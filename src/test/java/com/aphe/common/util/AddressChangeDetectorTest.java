package com.aphe.common.util;

import com.aphe.common.model.AddressVerifiable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AddressChangeDetectorTest {

    @InjectMocks
    private AddressChangeDetector detector;

    private AddressVerifiable mockEntity;

    @BeforeEach
    void setUp() {
        detector.clearAllSnapshots();
        mockEntity = mock(AddressVerifiable.class);
        // Note: We'll set up specific stubs in each test method to avoid unnecessary stubbing warnings
    }

    @Test
    void testTakeSnapshot() {
        when(mockEntity.getId()).thenReturn(1L);
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "City", "State", "US", "12345"});

        detector.takeSnapshot(mockEntity);
        assertEquals(1, detector.getCacheSize());
    }

    @Test
    void testHasAddressChanged_NoSnapshot() {
        when(mockEntity.getId()).thenReturn(1L);
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertTrue(changed, "Should return true when no snapshot exists");
    }

    @Test
    void testHasAddressChanged_NoChange() {
        when(mockEntity.getId()).thenReturn(1L);
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "City", "State", "US", "12345"});

        detector.takeSnapshot(mockEntity);
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertFalse(changed, "Should return false when address hasn't changed");
    }

    @Test
    void testHasAddressChanged_WithChange() {
        when(mockEntity.getId()).thenReturn(1L);
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "City", "State", "US", "12345"});

        detector.takeSnapshot(mockEntity);

        // Simulate address change
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"456 Oak St", "Apt 1", "City", "State", "US", "12345"});

        boolean changed = detector.hasAddressChanged(mockEntity);
        assertTrue(changed, "Should return true when address has changed");
    }

    @Test
    void testHasAddressChanged_NewEntity() {
        when(mockEntity.getId()).thenReturn(null);
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertFalse(changed, "Should return false for new entities");
    }

    @Test
    void testClearSnapshot() {
        when(mockEntity.getId()).thenReturn(1L);
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"A", "Apt 1", "City", "State", "US", "12345"});
        detector.takeSnapshot(mockEntity);
        assertEquals(1, detector.getCacheSize());
        
        detector.clearSnapshot(mockEntity);
        assertEquals(0, detector.getCacheSize());
    }

    @Test
    void testUpdateSnapshot() {
        // Have an initial address
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"A", "Apt 1", "City", "State", "US", "12345"});

        detector.takeSnapshot(mockEntity);
        
        // Change address
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"456 Oak St", "Apt 1", "City", "State", "US", "12345"});
        
        // Update snapshot
        detector.updateSnapshot(mockEntity);
        
        // Should not detect change now
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertFalse(changed, "Should not detect change after snapshot update");
    }

    @Test
    void testNullValues() {
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{null, null, "City", null, "US", null});
        detector.takeSnapshot(mockEntity);

        boolean changed = detector.hasAddressChanged(mockEntity);
        assertFalse(changed, "Should handle null values correctly");
    }

    @Test
    void testNewEntityPersistenceFlow() {
        // Simulate new entity (no ID initially)
        when(mockEntity.getId()).thenReturn(null);

        // No snapshot should be taken for new entity
        detector.takeSnapshot(mockEntity);
        assertEquals(0, detector.getCacheSize(), "Should not cache new entities without ID");

        // Simulate entity getting ID after persistence
        when(mockEntity.getId()).thenReturn(1L);
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"A St", "Apt 1", "City", "State", "US", "12345"});

        // Now update snapshot (simulating @PostPersist)
        detector.updateSnapshot(mockEntity);
        assertEquals(1, detector.getCacheSize(), "Should cache entity after it gets ID");

        // Subsequent changes should be detected
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"456 Oak St", "Apt 1", "City", "State", "US", "12345"});
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertTrue(changed, "Should detect changes after entity is persisted");
    }

    @Test
    void testPostPersistPostUpdateConsistency() {
        // Both postPersist and postUpdate should call updateSnapshot
        when(mockEntity.getId()).thenReturn(1L);

        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "City", "State", "US", "12345"});

        // Simulate postPersist
        detector.updateSnapshot(mockEntity);

        // Change address
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"456 Oak St", "Apt 1", "City", "State", "US", "12345"});

        // Should detect change
        assertTrue(detector.hasAddressChanged(mockEntity));

        // Simulate postUpdate
        detector.updateSnapshot(mockEntity);

        // Should not detect change after snapshot update
        assertFalse(detector.hasAddressChanged(mockEntity));
    }

    @Test
    void testCaseInsensitiveComparison() {
        // Take snapshot with mixed case
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "New York", "NY", "US", "12345"});
        detector.takeSnapshot(mockEntity);

        // Change to different case - should NOT detect as change
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 MAIN ST", "APT 1", "new york", "ny", "us", "12345"});
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertFalse(changed, "Should not detect case-only changes as modifications");
    }

    @Test
    void testWhitespaceNormalization() {
        // Take snapshot with extra whitespace
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{" 123 Main St ", "Apt 1", "New York", "NY", "US", "12345"});
        detector.takeSnapshot(mockEntity);

        // Change to trimmed version - should NOT detect as change
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "New York", "NY", "US", "12345"});
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertFalse(changed, "Should not detect whitespace-only changes as modifications");
    }

    @Test
    void testActualContentChange() {
        // Take snapshot
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "New York", "NY", "US", "12345"});
        detector.takeSnapshot(mockEntity);

        // Make actual content change (not just case/whitespace)
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"456 Oak Ave", "Apt 1", "New York", "NY", "US", "12345"});
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertTrue(changed, "Should detect actual content changes");
    }

    @Test
    void testEnumHandling() {
        // Test with enum values (like CountryCode)
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "New York", "NY", "US", "12345"});
        detector.takeSnapshot(mockEntity);

        // Same enum value should not trigger change
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "New York", "NY", "US", "12345"});
        boolean changed = detector.hasAddressChanged(mockEntity);
        assertFalse(changed, "Should handle enum values correctly");

        // Different enum value should trigger change
        when(mockEntity.getAddressFieldValues()).thenReturn(new Object[]{"123 Main St", "Apt 1", "New York", "NY", "CA", "12345"});
        changed = detector.hasAddressChanged(mockEntity);
        assertTrue(changed, "Should detect enum value changes");
    }

    @Test
    void testHashBasedKeyCollisionPrevention() {
        // Create actual instances instead of mocking getClass()
        MockContractorAddress entity1 = new MockContractorAddress();
        MockDomainAddress entity2 = new MockDomainAddress();

        // Both have same ID
        entity1.setId(123L);
        entity2.setId(123L);

        // Both have same address values
        Object[] sameAddressValues = {"123 Main St", "Apt 1", "City", "State", "US", "12345"};
        entity1.setAddressFieldValues(sameAddressValues);
        entity2.setAddressFieldValues(sameAddressValues);

        // Take snapshots for both
        detector.takeSnapshot(entity1);
        detector.takeSnapshot(entity2);

        // Should have 2 separate cache entries (different hash codes)
        assertEquals(2, detector.getCacheSize(), "Should cache entities separately despite same ID");

        // Verify different hash-based keys are generated
        int hash1 = MockContractorAddress.class.getName().hashCode();
        int hash2 = MockDomainAddress.class.getName().hashCode();
        assertNotEquals(hash1, hash2, "Different classes should have different hash codes");

        // Modify entity1 only
        entity1.setAddressFieldValues(new Object[]{"456 Oak St", "Apt 1", "City", "State", "US", "12345"});

        // Only entity1 should show change
        assertTrue(detector.hasAddressChanged(entity1), "Entity1 should show change");
        assertFalse(detector.hasAddressChanged(entity2), "Entity2 should NOT show change");
    }



    // Mock classes to simulate different package addresses
    static class MockContractorAddress implements AddressVerifiable {
        private Long id;
        private Object[] addressFieldValues = new Object[0];

        @Override
        public Enum<?> getAddressVerificationState() { return null; }

        @Override
        public void markAddressAsUnverified() { }

        @Override
        public Object[] getAddressFieldValues() { return addressFieldValues; }

        @Override
        public Long getId() { return id; }

        // Test helper methods
        public void setId(Long id) { this.id = id; }
        public void setAddressFieldValues(Object[] values) { this.addressFieldValues = values; }

        // This class name will be: com.aphe.common.util.AddressChangeDetectorTest$MockContractorAddress
    }

    static class MockDomainAddress implements AddressVerifiable {
        private Long id;
        private Object[] addressFieldValues = new Object[0];

        @Override
        public Enum<?> getAddressVerificationState() { return null; }

        @Override
        public void markAddressAsUnverified() { }

        @Override
        public Object[] getAddressFieldValues() { return addressFieldValues; }

        @Override
        public Long getId() { return id; }

        // Test helper methods
        public void setId(Long id) { this.id = id; }
        public void setAddressFieldValues(Object[] values) { this.addressFieldValues = values; }

        // This class name will be: com.aphe.common.util.AddressChangeDetectorTest$MockDomainAddress
    }
}
