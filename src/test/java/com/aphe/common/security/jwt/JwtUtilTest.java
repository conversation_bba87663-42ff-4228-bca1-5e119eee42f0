package com.aphe.common.security.jwt;

import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.D2DAccess;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for JwtUtil class
 * Tests JWT token generation, parsing, cookie management, and utility methods
 */
@ExtendWith(MockitoExtension.class)
class JwtUtilTest {

    @InjectMocks
    private JwtUtil jwtUtil;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    private static final String TEST_SECRET = "testSecretKey123456789012345678901234567890";
    private static final int TEST_EXPIRATION_MINUTES = 60;
    private static final String TEST_DOMAIN_NAME = "test.com";

    private ApheUserDetails testUserDetails;
    private String validToken;

    @BeforeEach
    void setUp() throws Exception {
        // Set up test properties using reflection
        ReflectionTestUtils.setField(jwtUtil, "secret", TEST_SECRET);
        ReflectionTestUtils.setField(jwtUtil, "expirationMinutes", TEST_EXPIRATION_MINUTES);
        ReflectionTestUtils.setField(jwtUtil, "domainName", TEST_DOMAIN_NAME);

        // Create test user details
        testUserDetails = createTestUserDetails();
        
        // Generate a valid token for testing
        validToken = jwtUtil.generateToken(testUserDetails);
    }

    private ApheUserDetails createTestUserDetails() {
        Collection<GrantedAuthority> authorities = Arrays.asList(
            new SimpleGrantedAuthority("ROLE_USER"),
            new SimpleGrantedAuthority("ROLE_ADMIN")
        );

        Collection<D2DAccess> domainRelations = new ArrayList<>();
        D2DAccess d2dAccess = new D2DAccess();
        d2dAccess.setRelType("VENDOR");
        d2dAccess.setSourceDomainId(100L);
        d2dAccess.setTargetDomainId(200L);
        d2dAccess.setSourceSubEntityId(1L);
        d2dAccess.setTargetSubEntityId(2L);
        domainRelations.add(d2dAccess);

        return new ApheUserDetails(
            1L, 123L, 456L, "TestDomain", "BUSINESS", 789L,
            authorities, "testuser", "password", true, true, true, true,
            true, domainRelations, "superadmin", false, true,
            "login123", "user456", "domain789", "parent012"
        );
    }

    // ========================================
    // TOKEN RETRIEVAL TESTS
    // ========================================

    @Test
    void testGetToken_FromAuthorizationHeader() {
        // Given
        String expectedToken = "test.jwt.token";
        when(request.getHeader(JwtUtil.AUTH_HEADER_NAME))
            .thenReturn(JwtUtil.TOKEN_PREFIX + expectedToken);

        // When
        String actualToken = jwtUtil.getToken(request);

        // Then
        assertEquals(expectedToken, actualToken);
        verify(request).getHeader(JwtUtil.AUTH_HEADER_NAME);
    }

    @Test
    void testGetToken_FromCookie() {
        // Given
        String expectedToken = "test.jwt.token";
        Cookie authCookie = new Cookie(JwtUtil.AUTH_COOKIE_NAME, expectedToken);
        Cookie[] cookies = {authCookie, new Cookie("other", "value")};
        
        when(request.getHeader(JwtUtil.AUTH_HEADER_NAME)).thenReturn(null);
        when(request.getCookies()).thenReturn(cookies);

        // When
        String actualToken = jwtUtil.getToken(request);

        // Then
        assertEquals(expectedToken, actualToken);
    }

    @Test
    void testGetToken_NoCookies() {
        // Given
        when(request.getHeader(JwtUtil.AUTH_HEADER_NAME)).thenReturn(null);
        when(request.getCookies()).thenReturn(null);

        // When
        String actualToken = jwtUtil.getToken(request);

        // Then
        assertNull(actualToken);
    }

    @Test
    void testGetToken_NoAuthCookie() {
        // Given
        Cookie[] cookies = {new Cookie("other", "value")};
        when(request.getHeader(JwtUtil.AUTH_HEADER_NAME)).thenReturn(null);
        when(request.getCookies()).thenReturn(cookies);

        // When
        String actualToken = jwtUtil.getToken(request);

        // Then
        assertNull(actualToken);
    }

    // ========================================
    // TOKEN GENERATION TESTS
    // ========================================

    @Test
    void testGenerateToken_Success() throws Exception {
        // When
        String token = jwtUtil.generateToken(testUserDetails);

        // Then
        assertNotNull(token);
        assertFalse(token.isEmpty());
        
        // Verify token can be parsed back
        Claims claims = Jwts.parser()
            .setSigningKey(TEST_SECRET.getBytes())
            .parseClaimsJws(token)
            .getBody();
        
        assertEquals("testuser", claims.getSubject());
        assertEquals("1", claims.get("loginId"));
        assertEquals("123", claims.get("userId"));
        assertEquals("456", claims.get("domainId"));
        assertEquals("BUSINESS", claims.get("domainType"));
        assertEquals("TestDomain", claims.get("domainName"));
        assertEquals("789", claims.get("parentDomainId"));
        assertEquals("true", claims.get("emailConfirmed"));
        assertEquals("superadmin", claims.get("superAdminLoginName"));
        assertEquals("false", claims.get("mfaRequired"));
        assertEquals("true", claims.get("mfaSuccess"));
        
        @SuppressWarnings("unchecked")
        List<String> roles = (List<String>) claims.get("roles");
        assertTrue(roles.contains("ROLE_USER"));
        assertTrue(roles.contains("ROLE_ADMIN"));
        
        assertNotNull(claims.get("domainRelations"));
        assertNotNull(claims.get("createTime"));
    }

    @Test
    void testGenerateToken_WithNullUserDetails() {
        // When & Then
        Exception exception = assertThrows(Exception.class, () -> {
            jwtUtil.generateToken(null);
        });
        
        assertEquals("Error creating token", exception.getMessage());
    }

    // ========================================
    // TOKEN PARSING TESTS
    // ========================================

    @Test
    void testParseToken_Success() throws Exception {
        // When
        ApheUserDetails parsedUser = jwtUtil.parseToken(validToken);

        // Then
        assertNotNull(parsedUser);
        assertEquals(testUserDetails.getLoginId(), parsedUser.getLoginId());
        assertEquals(testUserDetails.getUserId(), parsedUser.getUserId());
        assertEquals(testUserDetails.getDomainId(), parsedUser.getDomainId());
        assertEquals(testUserDetails.getDomainName(), parsedUser.getDomainName());
        assertEquals(testUserDetails.getDomainType(), parsedUser.getDomainType());
        assertEquals(testUserDetails.getParentDomainId(), parsedUser.getParentDomainId());
        assertEquals(testUserDetails.getUsername(), parsedUser.getUsername());
        assertEquals(testUserDetails.isEmailConfirmed(), parsedUser.isEmailConfirmed());
        assertEquals(testUserDetails.getSuperAdminLoginName(), parsedUser.getSuperAdminLoginName());
        assertEquals(testUserDetails.isMfaRequired(), parsedUser.isMfaRequired());
        assertEquals(testUserDetails.isMfaSuccess(), parsedUser.isMfaSuccess());
        
        // Verify authorities
        assertEquals(2, parsedUser.getAuthorities().size());
        assertTrue(parsedUser.getAuthorities().stream()
            .anyMatch(auth -> auth.getAuthority().equals("ROLE_USER")));
        assertTrue(parsedUser.getAuthorities().stream()
            .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN")));
        
        // Verify domain relations
        assertEquals(1, parsedUser.getDomainRelations().size());
        D2DAccess relation = parsedUser.getDomainRelations().iterator().next();
        assertEquals("VENDOR", relation.getRelType());
        assertEquals("100", relation.getSourceDomainId());
        assertEquals("200", relation.getTargetDomainId());
    }

    @Test
    void testParseToken_InvalidToken() {
        // Given
        String invalidToken = "invalid.jwt.token";

        // When & Then
        assertThrows(JwtTokenMalformedException.class, () -> {
            jwtUtil.parseToken(invalidToken);
        });
    }

    @Test
    void testParseToken_EmptyToken() {
        // When & Then
        assertThrows(JwtTokenMalformedException.class, () -> {
            jwtUtil.parseToken("");
        });
    }

    @Test
    void testParseToken_NullToken() {
        // When & Then
        assertThrows(JwtTokenMalformedException.class, () -> {
            jwtUtil.parseToken(null);
        });
    }

    // ========================================
    // TOKEN PARSING WITH SECRET TESTS
    // ========================================

    @Test
    void testParseTokenWithSecret_ValidSecret() throws Exception {
        // When
        Claims claims = jwtUtil.parseToken(validToken, TEST_SECRET);

        // Then
        assertNotNull(claims);
        assertEquals("testuser", claims.getSubject());
        assertEquals("123", claims.get("userId"));
    }

    @Test
    void testParseTokenWithSecret_NullSecret() throws Exception {
        // Given - Create token without signature for testing
        String tokenWithoutSignature = validToken.substring(0, validToken.lastIndexOf('.') + 1);

        // When
        Claims claims = jwtUtil.parseToken(tokenWithoutSignature, null);

        // Then
        assertNotNull(claims);
        // Note: This test verifies the parsing without signature verification
    }

    @Test
    void testParseTokenWithSecret_InvalidSecret() {
        // When & Then
        assertThrows(JwtTokenMalformedException.class, () -> {
            jwtUtil.parseToken(validToken, "wrongSecret");
        });
    }

    // ========================================
    // JSON PARSING TESTS
    // ========================================

    @Test
    void testParseTokenToJSON_Success() throws Exception {
        // When
        JSONObject jsonObject = jwtUtil.parseTokenToJSON(validToken, TEST_SECRET);

        // Then
        assertNotNull(jsonObject);
        assertEquals("testuser", jsonObject.get("sub"));
        assertEquals("123", jsonObject.get("userId"));
        assertEquals("456", jsonObject.get("domainId"));
    }

    @Test
    void testParseTokenToJSON_WithoutSecret() throws Exception {
        // When
        JSONObject jsonObject = jwtUtil.parseTokenToJSON(validToken);

        // Then
        assertNotNull(jsonObject);
        // This should work with the default secret
    }

    @Test
    void testParseTokenToJSON_InvalidToken() {
        // When & Then
        assertThrows(JwtTokenMalformedException.class, () -> {
            jwtUtil.parseTokenToJSON("invalid.token", TEST_SECRET);
        });
    }

    // ========================================
    // USER DETAILS RETRIEVAL TESTS
    // ========================================

    @Test
    void testGetUserDetails_Success() {
        // Given
        when(request.getHeader(JwtUtil.AUTH_HEADER_NAME))
            .thenReturn(JwtUtil.TOKEN_PREFIX + validToken);

        // When
        ApheUserDetails userDetails = jwtUtil.getUserDetails(request);

        // Then
        assertNotNull(userDetails);
        assertEquals(testUserDetails.getUsername(), userDetails.getUsername());
        assertEquals(testUserDetails.getUserId(), userDetails.getUserId());
    }

    @Test
    void testGetUserDetails_NoToken() {
        // Given
        when(request.getHeader(JwtUtil.AUTH_HEADER_NAME)).thenReturn(null);
        when(request.getCookies()).thenReturn(null);

        // When
        ApheUserDetails userDetails = jwtUtil.getUserDetails(request);

        // Then
        assertNull(userDetails);
    }

    @Test
    void testGetUserDetails_InvalidToken() {
        // Given
        when(request.getHeader(JwtUtil.AUTH_HEADER_NAME))
            .thenReturn(JwtUtil.TOKEN_PREFIX + "invalid.token");

        // When
        ApheUserDetails userDetails = jwtUtil.getUserDetails(request);

        // Then
        assertNull(userDetails);
    }

    // ========================================
    // COOKIE MANAGEMENT TESTS
    // ========================================

    @Test
    void testSetToken_Success() throws Exception {
        // Given
        when(request.getContextPath()).thenReturn("/app");
        when(request.getServerName()).thenReturn("subdomain.test.com");
        when(response.getHeaders("Set-Cookie")).thenReturn(Arrays.asList(
            "Authorization=token; Path=/app; Domain=test.com; HttpOnly; Secure",
            "UserId=123; Path=/app; Domain=test.com; Secure"
        ));

        // When
        jwtUtil.setToken(request, response, validToken);

        // Then
        verify(response).addHeader(JwtUtil.AUTH_HEADER_NAME, JwtUtil.TOKEN_PREFIX + validToken);
        verify(response).addHeader(JwtUtil.DOMAIN_HEADER_NAME, "456");
        verify(response).addHeader(JwtUtil.PARENT_DOMAIN_HEADER_NAME, "789");
        verify(response).addHeader(JwtUtil.USERID_HEADER_NAME, "123");

        // Verify cookies are added
        verify(response, times(8)).addCookie(any(Cookie.class));

        // Verify SameSite attribute is added
        verify(response).setHeader(eq("Set-Cookie"), contains("SameSite=Lax"));
        verify(response).addHeader(eq("Set-Cookie"), contains("SameSite=Lax"));
    }

    @Test
    void testSetToken_EmptyContextPath() throws Exception {
        // Given
        when(request.getContextPath()).thenReturn("");
        when(request.getServerName()).thenReturn("test.com");
        when(response.getHeaders("Set-Cookie")).thenReturn(Arrays.asList("cookie=value"));

        // When
        jwtUtil.setToken(request, response, validToken);

        // Then
        verify(response, times(8)).addCookie(any(Cookie.class));
    }

    @Test
    void testSetToken_NullContextPath() throws Exception {
        // Given
        when(request.getContextPath()).thenReturn(null);
        when(request.getServerName()).thenReturn("test.com");
        when(response.getHeaders("Set-Cookie")).thenReturn(Arrays.asList("cookie=value"));

        // When
        jwtUtil.setToken(request, response, validToken);

        // Then
        verify(response, times(8)).addCookie(any(Cookie.class));
    }

    @Test
    void testEraseToken_Success() {
        // Given
        when(request.getContextPath()).thenReturn("/app");
        when(request.getServerName()).thenReturn("subdomain.test.com");

        // When
        jwtUtil.eraseToken(request, response);

        // Then
        verify(response, times(8)).addCookie(any(Cookie.class));
    }

    @Test
    void testEraseToken_EmptyContextPath() {
        // Given
        when(request.getContextPath()).thenReturn("");
        when(request.getServerName()).thenReturn("test.com");

        // When
        jwtUtil.eraseToken(request, response);

        // Then
        verify(response, times(8)).addCookie(any(Cookie.class));
    }

    // ========================================
    // UTILITY METHOD TESTS
    // ========================================

    @Test
    void testSha256_Success() {
        // Given
        String input = "test input";
        String expectedHash = "8e5ca2a88d3d7b2e4d5f6c7a9b0e1f2d3c4b5a6978e5ca2a88d3d7b2e4d5f6c7a";

        // When
        String actualHash = jwtUtil.sha256(input);

        // Then
        assertNotNull(actualHash);
        assertEquals(64, actualHash.length()); // SHA-256 produces 64 character hex string
        assertTrue(actualHash.matches("[a-f0-9]+"));

        // Verify consistency
        assertEquals(actualHash, jwtUtil.sha256(input));
    }

    @Test
    void testSha256_EmptyString() {
        // Given
        String input = "";

        // When
        String hash = jwtUtil.sha256(input);

        // Then
        assertNotNull(hash);
        assertEquals(64, hash.length());
        assertEquals("e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", hash);
    }

    @Test
    void testSha256_NullInput() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            jwtUtil.sha256(null);
        });
    }

    @Test
    void testSha256_DifferentInputs() {
        // Given
        String input1 = "test1";
        String input2 = "test2";

        // When
        String hash1 = jwtUtil.sha256(input1);
        String hash2 = jwtUtil.sha256(input2);

        // Then
        assertNotEquals(hash1, hash2);
    }

    // ========================================
    // CONSTANTS TESTS
    // ========================================

    @Test
    void testConstants() {
        assertEquals("Authorization", JwtUtil.AUTH_COOKIE_NAME);
        assertEquals("DomainId", JwtUtil.DOMAIN_COOKIE_NAME);
        assertEquals("DomainType", JwtUtil.DOMAIN_TYPE_COOKIE_NAME);
        assertEquals("ParentDomainId", JwtUtil.PARENT_DOMAIN_COOKIE_NAME);
        assertEquals("IsLoggedIn", JwtUtil.ISLOGGED_IN_COOKIE_NAME);
        assertEquals("UserId", JwtUtil.USERID_COOKIE_NAME);
        assertEquals("mfaRequired", JwtUtil.MFA_REQUIRED_COOKIE_NAME);
        assertEquals("mfaSuccess", JwtUtil.MFA_SUCCESS_COOKIE_NAME);

        assertEquals("Authorization", JwtUtil.AUTH_HEADER_NAME);
        assertEquals("Bearer ", JwtUtil.TOKEN_PREFIX);
        assertEquals("UserId", JwtUtil.USERID_HEADER_NAME);
        assertEquals("DomainId", JwtUtil.DOMAIN_HEADER_NAME);
        assertEquals("ParentDomainId", JwtUtil.PARENT_DOMAIN_HEADER_NAME);
    }

    // ========================================
    // EDGE CASE TESTS
    // ========================================

    @Test
    void testGenerateToken_EmptyRoles() throws Exception {
        // Given
        ApheUserDetails userWithNoRoles = new ApheUserDetails(
            1L, 123L, 456L, "TestDomain", "BUSINESS", 789L,
            new ArrayList<>(), "testuser", "password", true, true, true, true,
            true, new ArrayList<>(), "superadmin", false, true,
            "login123", "user456", "domain789", "parent012"
        );

        // When
        String token = jwtUtil.generateToken(userWithNoRoles);

        // Then
        assertNotNull(token);

        ApheUserDetails parsed = jwtUtil.parseToken(token);
        assertTrue(parsed.getAuthorities().isEmpty());
    }

    @Test
    void testParseToken_EmptyDomainRelations() throws Exception {
        // Given
        ApheUserDetails userWithNoDomainRelations = new ApheUserDetails(
            1L, 123L, 456L, "TestDomain", "BUSINESS", 789L,
            Arrays.asList(new SimpleGrantedAuthority("ROLE_USER")),
            "testuser", "password", true, true, true, true,
            true, new ArrayList<>(), "superadmin", false, true,
            "login123", "user456", "domain789", "parent012"
        );

        // When
        String token = jwtUtil.generateToken(userWithNoDomainRelations);
        ApheUserDetails parsed = jwtUtil.parseToken(token);

        // Then
        assertNotNull(parsed);
        assertTrue(parsed.getDomainRelations().isEmpty());
    }
}

