package com.aphe.auth.service.d2d;

import com.aphe.auth.model.d2d.D2DInvitation;
import com.aphe.auth.model.d2d.D2DInvitationStatus;
import com.aphe.auth.model.d2d.D2DRelationType;
import com.aphe.auth.service.d2d.dto.CreateD2DInvitationDTO;
import com.aphe.auth.service.d2d.dto.D2DInvitationDTO;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for D2DInvitationManager class
 * Tests the orchestration service for managing D2D (Domain-to-Domain) invitations
 */
@ExtendWith(MockitoExtension.class)
class D2DInvitationManagerTest {

    @InjectMocks
    private D2DInvitationManager d2dInvitationManager;

    @Mock
    private D2DInvitationManagerTxn d2DInvitationManagerTxn;

    @Mock
    private Logger logger;

    private CreateD2DInvitationDTO createInvitationDTO;
    private D2DInvitation mockInvitation;
    private D2DInvitationDTO mockInvitationDTO;

    @BeforeEach
    void setUp() {
        // Set up the logger mock using reflection
        ReflectionTestUtils.setField(d2dInvitationManager, "logger", logger);

        // Create test data
        createInvitationDTO = createTestCreateInvitationDTO();
        mockInvitation = createTestD2DInvitation();
        mockInvitationDTO = createTestD2DInvitationDTO();
    }

    private CreateD2DInvitationDTO createTestCreateInvitationDTO() {
        CreateD2DInvitationDTO dto = new CreateD2DInvitationDTO();
        dto.domainId = 123L;
        dto.subEntityId = 456L;
        dto.relationType = D2DRelationType.Vendor;
        dto.emailAddress = "<EMAIL>";
        dto.name = "Test Company";
        return dto;
    }

    private D2DInvitation createTestD2DInvitation() {
        D2DInvitation invitation = new D2DInvitation();
        invitation.setDomainId(123L);
        invitation.setSubEntityId(456L);
        invitation.setRelationType(D2DRelationType.Vendor);
        invitation.setEmailAddress("<EMAIL>");
        invitation.setName("Test Company");
        invitation.setInvitationCode("test-invitation-code-12345");
        invitation.setStatus(D2DInvitationStatus.INVITED);
        invitation.setCreatedDate(new Date());
        
        // Mock the getId method since it's inherited from BaseEntity
        // We'll use reflection to set the ID
        ReflectionTestUtils.setField(invitation, "id", 1L);
        
        return invitation;
    }

    private D2DInvitationDTO createTestD2DInvitationDTO() {
        D2DInvitationDTO dto = new D2DInvitationDTO();
        dto.id = "1";
        dto.domainId = 123L;
        dto.subEntityId = 456L;
        dto.relationType = D2DRelationType.Vendor;
        dto.emailAddress = "<EMAIL>";
        dto.name = "Test Company";
        dto.invitationCode = "test-invitation-code-12345";
        dto.status = D2DInvitationStatus.INVITED;
        dto.createdDate = new Date();
        dto.existingUser = false;
        dto.domainName = "Test Domain";
        return dto;
    }

    // ========================================
    // ADD INVITATION TESTS
    // ========================================

    @Test
    void testAddInvitation_Success() throws ApheDataValidationException {
        // Given
        when(d2DInvitationManagerTxn.addInvitation(createInvitationDTO))
            .thenReturn(mockInvitation);
        when(d2DInvitationManagerTxn.getInvitation("1"))
            .thenReturn(mockInvitationDTO);

        // When
        D2DInvitationDTO result = d2dInvitationManager.addInvitation(createInvitationDTO);

        // Then
        assertNotNull(result);
        assertEquals(mockInvitationDTO.id, result.id);
        assertEquals(mockInvitationDTO.domainId, result.domainId);
        assertEquals(mockInvitationDTO.subEntityId, result.subEntityId);
        assertEquals(mockInvitationDTO.relationType, result.relationType);
        assertEquals(mockInvitationDTO.emailAddress, result.emailAddress);
        assertEquals(mockInvitationDTO.name, result.name);
        assertEquals(mockInvitationDTO.status, result.status);

        // Verify interactions
        verify(d2DInvitationManagerTxn).addInvitation(createInvitationDTO);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("1");
        verify(d2DInvitationManagerTxn).getInvitation("1");
    }

    @Test
    void testAddInvitation_NullInvitationReturned() throws ApheDataValidationException {
        // Given
        when(d2DInvitationManagerTxn.addInvitation(createInvitationDTO))
            .thenReturn(null);

        // When
        D2DInvitationDTO result = d2dInvitationManager.addInvitation(createInvitationDTO);

        // Then
        assertNull(result);

        // Verify interactions
        verify(d2DInvitationManagerTxn).addInvitation(createInvitationDTO);
        verify(d2DInvitationManagerTxn, never()).notifyUserNewD2DInvitation(anyString());
        verify(d2DInvitationManagerTxn, never()).getInvitation(anyString());
    }

    @Test
    void testAddInvitation_ValidationException() throws ApheDataValidationException {
        // Given
        ApheDataValidationException expectedException = new ApheDataValidationException("domainId", "Invalid domain ID");
        when(d2DInvitationManagerTxn.addInvitation(createInvitationDTO))
            .thenThrow(expectedException);

        // When & Then
        ApheDataValidationException exception = assertThrows(ApheDataValidationException.class, () -> {
            d2dInvitationManager.addInvitation(createInvitationDTO);
        });

        assertEquals(expectedException.getMessage(), exception.getMessage());

        // Verify interactions
        verify(d2DInvitationManagerTxn).addInvitation(createInvitationDTO);
        verify(d2DInvitationManagerTxn, never()).notifyUserNewD2DInvitation(anyString());
        verify(d2DInvitationManagerTxn, never()).getInvitation(anyString());
    }

    @Test
    void testAddInvitation_NotificationFails() throws ApheDataValidationException {
        // Given
        when(d2DInvitationManagerTxn.addInvitation(createInvitationDTO))
            .thenReturn(mockInvitation);
        doThrow(new ApheDataValidationException("notification", "Notification failed"))
            .when(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("1");

        // When & Then
        assertThrows(ApheDataValidationException.class, () -> {
            d2dInvitationManager.addInvitation(createInvitationDTO);
        });

        // Verify interactions
        verify(d2DInvitationManagerTxn).addInvitation(createInvitationDTO);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("1");
        verify(d2DInvitationManagerTxn, never()).getInvitation(anyString());
    }

    @Test
    void testAddInvitation_GetInvitationFails() throws ApheDataValidationException {
        // Given
        when(d2DInvitationManagerTxn.addInvitation(createInvitationDTO))
            .thenReturn(mockInvitation);
        doNothing().when(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("1");
        when(d2DInvitationManagerTxn.getInvitation("1"))
            .thenReturn(null);

        // When
        D2DInvitationDTO result = d2dInvitationManager.addInvitation(createInvitationDTO);

        // Then
        assertNull(result);

        // Verify interactions
        verify(d2DInvitationManagerTxn).addInvitation(createInvitationDTO);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("1");
        verify(d2DInvitationManagerTxn).getInvitation("1");
    }

    // ========================================
    // RESEND DOMAIN INVITATION TESTS
    // ========================================

    @Test
    void testResendDomainInvitation_Success() throws ApheDataValidationException {
        // Given
        String invitationId = "1";
        when(d2DInvitationManagerTxn.resetInvitationCode(invitationId))
            .thenReturn(true);

        // When
        boolean result = d2dInvitationManager.resendDomainInvitation(invitationId);

        // Then
        assertTrue(result);

        // Verify interactions
        verify(d2DInvitationManagerTxn).resetInvitationCode(invitationId);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation(invitationId);
    }

    @Test
    void testResendDomainInvitation_ResetFails() throws ApheDataValidationException {
        // Given
        String invitationId = "1";
        when(d2DInvitationManagerTxn.resetInvitationCode(invitationId))
            .thenReturn(false);

        // When
        boolean result = d2dInvitationManager.resendDomainInvitation(invitationId);

        // Then
        assertFalse(result);

        // Verify interactions
        verify(d2DInvitationManagerTxn).resetInvitationCode(invitationId);
        verify(d2DInvitationManagerTxn, never()).notifyUserNewD2DInvitation(anyString());
    }

    @Test
    void testResendDomainInvitation_ValidationException() throws ApheDataValidationException {
        // Given
        String invitationId = "invalid";
        ApheDataValidationException expectedException = new ApheDataValidationException("invitationId", "Invalid invitation ID");
        when(d2DInvitationManagerTxn.resetInvitationCode(invitationId))
            .thenThrow(expectedException);

        // When & Then
        ApheDataValidationException exception = assertThrows(ApheDataValidationException.class, () -> {
            d2dInvitationManager.resendDomainInvitation(invitationId);
        });

        assertEquals(expectedException.getMessage(), exception.getMessage());

        // Verify interactions
        verify(d2DInvitationManagerTxn).resetInvitationCode(invitationId);
        verify(d2DInvitationManagerTxn, never()).notifyUserNewD2DInvitation(anyString());
    }

    @Test
    void testResendDomainInvitation_NotificationFails() throws ApheDataValidationException {
        // Given
        String invitationId = "1";
        when(d2DInvitationManagerTxn.resetInvitationCode(invitationId))
            .thenReturn(true);
        doThrow(new ApheDataValidationException("notification", "Notification failed"))
            .when(d2DInvitationManagerTxn).notifyUserNewD2DInvitation(invitationId);

        // When & Then
        assertThrows(ApheDataValidationException.class, () -> {
            d2dInvitationManager.resendDomainInvitation(invitationId);
        });

        // Verify interactions
        verify(d2DInvitationManagerTxn).resetInvitationCode(invitationId);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation(invitationId);
    }

    // ========================================
    // EDGE CASE TESTS
    // ========================================

    @Test
    void testAddInvitation_WithNullDTO() throws ApheDataValidationException {
        // Given
        when(d2DInvitationManagerTxn.addInvitation(null))
            .thenThrow(new NullPointerException("CreateD2DInvitationDTO cannot be null"));

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            d2dInvitationManager.addInvitation(null);
        });

        verify(d2DInvitationManagerTxn).addInvitation(null);
    }

    @Test
    void testResendDomainInvitation_WithNullId() throws ApheDataValidationException {
        // Given
        when(d2DInvitationManagerTxn.resetInvitationCode(null))
            .thenThrow(new NumberFormatException("For input string: \"null\""));

        // When & Then
        assertThrows(NumberFormatException.class, () -> {
            d2dInvitationManager.resendDomainInvitation(null);
        });

        verify(d2DInvitationManagerTxn).resetInvitationCode(null);
    }

    @Test
    void testResendDomainInvitation_WithEmptyId() throws ApheDataValidationException {
        // Given
        String emptyId = "";
        when(d2DInvitationManagerTxn.resetInvitationCode(emptyId))
            .thenThrow(new NumberFormatException("For input string: \"\""));

        // When & Then
        assertThrows(NumberFormatException.class, () -> {
            d2dInvitationManager.resendDomainInvitation(emptyId);
        });

        verify(d2DInvitationManagerTxn).resetInvitationCode(emptyId);
    }

    // ========================================
    // DIFFERENT RELATION TYPE TESTS
    // ========================================

    @Test
    void testAddInvitation_EmployeeRelationType() throws ApheDataValidationException {
        // Given
        CreateD2DInvitationDTO employeeInvitation = createTestCreateInvitationDTO();
        employeeInvitation.relationType = D2DRelationType.Employee;
        
        D2DInvitation employeeMockInvitation = createTestD2DInvitation();
        employeeMockInvitation.setRelationType(D2DRelationType.Employee);
        
        D2DInvitationDTO employeeMockDTO = createTestD2DInvitationDTO();
        employeeMockDTO.relationType = D2DRelationType.Employee;

        when(d2DInvitationManagerTxn.addInvitation(employeeInvitation))
            .thenReturn(employeeMockInvitation);
        when(d2DInvitationManagerTxn.getInvitation("1"))
            .thenReturn(employeeMockDTO);

        // When
        D2DInvitationDTO result = d2dInvitationManager.addInvitation(employeeInvitation);

        // Then
        assertNotNull(result);
        assertEquals(D2DRelationType.Employee, result.relationType);

        // Verify interactions
        verify(d2DInvitationManagerTxn).addInvitation(employeeInvitation);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("1");
        verify(d2DInvitationManagerTxn).getInvitation("1");
    }

    // ========================================
    // INTEGRATION FLOW TESTS
    // ========================================

    @Test
    void testAddInvitation_CompleteFlow() throws ApheDataValidationException {
        // Given - Test the complete flow from creation to notification
        CreateD2DInvitationDTO invitation = createTestCreateInvitationDTO();
        D2DInvitation createdInvitation = createTestD2DInvitation();
        D2DInvitationDTO finalDTO = createTestD2DInvitationDTO();

        when(d2DInvitationManagerTxn.addInvitation(invitation))
            .thenReturn(createdInvitation);
        doNothing().when(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("1");
        when(d2DInvitationManagerTxn.getInvitation("1"))
            .thenReturn(finalDTO);

        // When
        D2DInvitationDTO result = d2dInvitationManager.addInvitation(invitation);

        // Then
        assertNotNull(result);
        assertEquals(finalDTO.id, result.id);
        assertEquals(finalDTO.domainId, result.domainId);
        assertEquals(finalDTO.subEntityId, result.subEntityId);
        assertEquals(finalDTO.relationType, result.relationType);
        assertEquals(finalDTO.emailAddress, result.emailAddress);
        assertEquals(finalDTO.name, result.name);
        assertEquals(finalDTO.invitationCode, result.invitationCode);
        assertEquals(finalDTO.status, result.status);
        assertEquals(finalDTO.existingUser, result.existingUser);
        assertEquals(finalDTO.domainName, result.domainName);

        // Verify the complete flow
        verify(d2DInvitationManagerTxn, times(1)).addInvitation(invitation);
        verify(d2DInvitationManagerTxn, times(1)).notifyUserNewD2DInvitation("1");
        verify(d2DInvitationManagerTxn, times(1)).getInvitation("1");
    }

    @Test
    void testResendDomainInvitation_CompleteFlow() throws ApheDataValidationException {
        // Given - Test the complete resend flow
        String invitationId = "123";

        when(d2DInvitationManagerTxn.resetInvitationCode(invitationId))
            .thenReturn(true);
        doNothing().when(d2DInvitationManagerTxn).notifyUserNewD2DInvitation(invitationId);

        // When
        boolean result = d2dInvitationManager.resendDomainInvitation(invitationId);

        // Then
        assertTrue(result);

        // Verify the complete flow
        verify(d2DInvitationManagerTxn, times(1)).resetInvitationCode(invitationId);
        verify(d2DInvitationManagerTxn, times(1)).notifyUserNewD2DInvitation(invitationId);
    }

    // ========================================
    // BOUNDARY VALUE TESTS
    // ========================================

    @Test
    void testAddInvitation_WithLongDomainId() throws ApheDataValidationException {
        // Given
        CreateD2DInvitationDTO invitation = createTestCreateInvitationDTO();
        invitation.domainId = 999999999999999999L; // Very long domain ID

        D2DInvitation mockInv = createTestD2DInvitation();
        mockInv.setDomainId(999999999999999999L);
        ReflectionTestUtils.setField(mockInv, "id", 999999999999999999L);

        D2DInvitationDTO mockDTO = createTestD2DInvitationDTO();
        mockDTO.id = "999999999999999999";
        mockDTO.domainId = 999999999999999999L;

        when(d2DInvitationManagerTxn.addInvitation(invitation))
            .thenReturn(mockInv);
        when(d2DInvitationManagerTxn.getInvitation("999999999999999999"))
            .thenReturn(mockDTO);

        // When
        D2DInvitationDTO result = d2dInvitationManager.addInvitation(invitation);

        // Then
        assertNotNull(result);
        assertEquals(999999999999999999L, result.domainId);

        verify(d2DInvitationManagerTxn).addInvitation(invitation);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation("999999999999999999");
        verify(d2DInvitationManagerTxn).getInvitation("999999999999999999");
    }

    @Test
    void testResendDomainInvitation_WithLongId() throws ApheDataValidationException {
        // Given
        String longInvitationId = "999999999999999999";

        when(d2DInvitationManagerTxn.resetInvitationCode(longInvitationId))
            .thenReturn(true);

        // When
        boolean result = d2dInvitationManager.resendDomainInvitation(longInvitationId);

        // Then
        assertTrue(result);

        verify(d2DInvitationManagerTxn).resetInvitationCode(longInvitationId);
        verify(d2DInvitationManagerTxn).notifyUserNewD2DInvitation(longInvitationId);
    }

    // ========================================
    // CONCURRENT ACCESS SIMULATION TESTS
    // ========================================

    @Test
    void testAddInvitation_MultipleInvitations() throws ApheDataValidationException {
        // Given - Simulate multiple invitations being created
        CreateD2DInvitationDTO invitation1 = createTestCreateInvitationDTO();
        invitation1.emailAddress = "<EMAIL>";
        invitation1.name = "Company 1";

        CreateD2DInvitationDTO invitation2 = createTestCreateInvitationDTO();
        invitation2.emailAddress = "<EMAIL>";
        invitation2.name = "Company 2";
        invitation2.relationType = D2DRelationType.Employee;

        D2DInvitation mockInv1 = createTestD2DInvitation();
        mockInv1.setEmailAddress("<EMAIL>");
        mockInv1.setName("Company 1");
        ReflectionTestUtils.setField(mockInv1, "id", 1L);

        D2DInvitation mockInv2 = createTestD2DInvitation();
        mockInv2.setEmailAddress("<EMAIL>");
        mockInv2.setName("Company 2");
        mockInv2.setRelationType(D2DRelationType.Employee);
        ReflectionTestUtils.setField(mockInv2, "id", 2L);

        D2DInvitationDTO mockDTO1 = createTestD2DInvitationDTO();
        mockDTO1.id = "1";
        mockDTO1.emailAddress = "<EMAIL>";
        mockDTO1.name = "Company 1";

        D2DInvitationDTO mockDTO2 = createTestD2DInvitationDTO();
        mockDTO2.id = "2";
        mockDTO2.emailAddress = "<EMAIL>";
        mockDTO2.name = "Company 2";
        mockDTO2.relationType = D2DRelationType.Employee;

        when(d2DInvitationManagerTxn.addInvitation(invitation1)).thenReturn(mockInv1);
        when(d2DInvitationManagerTxn.addInvitation(invitation2)).thenReturn(mockInv2);
        when(d2DInvitationManagerTxn.getInvitation("1")).thenReturn(mockDTO1);
        when(d2DInvitationManagerTxn.getInvitation("2")).thenReturn(mockDTO2);

        // When
        D2DInvitationDTO result1 = d2dInvitationManager.addInvitation(invitation1);
        D2DInvitationDTO result2 = d2dInvitationManager.addInvitation(invitation2);

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals("<EMAIL>", result1.emailAddress);
        assertEquals("<EMAIL>", result2.emailAddress);
        assertEquals(D2DRelationType.Vendor, result1.relationType);
        assertEquals(D2DRelationType.Employee, result2.relationType);

        // Verify all interactions
        verify(d2DInvitationManagerTxn, times(2)).addInvitation(any(CreateD2DInvitationDTO.class));
        verify(d2DInvitationManagerTxn, times(2)).notifyUserNewD2DInvitation(anyString());
        verify(d2DInvitationManagerTxn, times(2)).getInvitation(anyString());
    }

    // ========================================
    // LOGGER VERIFICATION TESTS
    // ========================================

    @Test
    void testLoggerIsInjected() {
        // Given & When
        Logger injectedLogger = (Logger) ReflectionTestUtils.getField(d2dInvitationManager, "logger");

        // Then
        assertNotNull(injectedLogger);
        assertEquals(logger, injectedLogger);
    }

    // ========================================
    // DEPENDENCY INJECTION TESTS
    // ========================================

    @Test
    void testD2DInvitationManagerTxnIsInjected() {
        // Given & When
        D2DInvitationManagerTxn injectedTxnManager =
            (D2DInvitationManagerTxn) ReflectionTestUtils.getField(d2dInvitationManager, "d2DInvitationManagerTxn");

        // Then
        assertNotNull(injectedTxnManager);
        assertEquals(d2DInvitationManagerTxn, injectedTxnManager);
    }
}
