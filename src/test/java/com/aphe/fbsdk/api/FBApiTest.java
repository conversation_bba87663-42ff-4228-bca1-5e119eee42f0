package com.aphe.fbsdk.api;


import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.exceptions.FBUserException;
import com.aphe.fbsdk.model.*;
import com.aphe.fbsdk.query.ExpandQueryBuilder;
import com.aphe.fbsdk.query.FilterQueryBuilder;
import com.aphe.fbsdk.query.PaginationQueryBuilder;
import com.aphe.fbsdk.query.QueryBuilder;
import org.junit.jupiter.api.Test;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class FBApiTest {

    public static final SimpleDateFormat ISO_8601_FORMAT = new SimpleDateFormat("YYYY-MM-dd'T'HH:mm:ss.sssZ");
    private static String baseURL = "https://api.freshbooks.com/";

    private String accountId = "6OG7az";
    private static String accessToken = "eyJraWQiOiJhMTlPSlR5aVlKOXhPM3FoWnhWeE1KZE5ZNXJ4cUhpQzBSTUY0TWRheGtjIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IbNjuim7HLhmWBq38UTB4Kc8vhUGhOrDVI5antsupTjqh1AwXFWFz0IBDf-kZEUQcJeZsBR1ANrsz57_U9hzx9gX_A-Ut57iNL67Q84WVLnJP6GLmHSFyMBA58Y8zIHN9VuNgp4EtXhbPeAjYvDgzJgNGzM0WJwX00S3nTltRGElmU5VekyCKh_9nFi4pGKIMco4lbpInJzpZwXCQEC6EOhY4SfqIT8bJ4Rz_UPK4RgzE-HiiTSSDJZQLEUFgn2xh7tMPvSrKv_R_EccuACWgmHr5pboQhMkFyGyMabHLZnFIlk0H7m9O9FGIK7qaWZuXh014NsGew28-1cQZb53RQ";

    private void printAccounts(List<ExpenseCategory> accounts) {
        accounts.stream().forEach(account -> {
            printAccount(account);
        });
    }

    private void printAccount(ExpenseCategory account) {
        System.out.printf("%s----%s----%s\n", account.id, account.parentid, account.category);
    }

    private void printIdentity(Identity identity) {
        System.out.printf("%s----%s----%s----%d\n", identity.id, identity.email, identity.first_name, identity.business_memberships.size());
    }

    private void printBill(Bill bill) {
        System.out.printf("%s----%s----%s----%s\n", bill.id, bill.vendor.vendorid, bill.amount.amount, bill.issue_date.toString());
    }

    private void printExpense(Expense expense) {
        System.out.printf("%s----%s----%s----%s\n", expense.id, expense.categoryid, expense.amount.amount, expense.date.toString());
    }

    private void printVendor(Vendor vendor) {
        System.out.printf("%s----%s----%s----%s---%s\n", vendor.vendorid, vendor.vendor_name, vendor.is_1099, vendor.getVisState(), vendor.city);
    }

    private void printBillPayment(BillPayment billPayment) {
        System.out.printf("%s----%s----%s----%s----%s\n",
                billPayment.id, billPayment.billid, billPayment.amount.amount, billPayment.getPaymentType(), billPayment.paid_date.toString());
    }


    @Test
    void testExpenseCategoriesGetAll() throws Exception {
        ExpenseCategoryManager accountManager = new ExpenseCategoryManager(baseURL, accessToken, accountId);
        List<ExpenseCategory> accounts = accountManager.getListAll(new ArrayList<>());
        printAccounts(accounts);
    }

    @Test
    void testExpenseCategoriesPageFilter() throws Exception {
        ExpenseCategoryManager accountManager = new ExpenseCategoryManager(baseURL, accessToken, accountId);
        PaginationQueryBuilder queryBuilder = new PaginationQueryBuilder().page(2).perPage(10);
        List<QueryBuilder> queryBuilders = Arrays.asList(queryBuilder);
        List<ExpenseCategory> accounts = accountManager.getList(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testExpenseCategoriesSearchById() throws Exception {
        ExpenseCategoryManager accountManager = new ExpenseCategoryManager(baseURL, accessToken, accountId);
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addEquals("parentid", "4860636");
        List<QueryBuilder> queryBuilders = Arrays.asList(filterQueryBuilder);
        List<ExpenseCategory> accounts = accountManager.getList(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testExpenseCategoriesSearchIn() throws Exception {
        ExpenseCategoryManager accountManager = new ExpenseCategoryManager(baseURL, accessToken, accountId);
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addIn("categoryid", Arrays.asList("4860639", "4860637"));
        List<QueryBuilder> queryBuilders = Arrays.asList(filterQueryBuilder);
        List<ExpenseCategory> accounts = accountManager.getList(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testExpenseCategory() throws Exception {
        ExpenseCategoryManager accountManager = new ExpenseCategoryManager(baseURL, accessToken, accountId);
        ExpenseCategory account = accountManager.getOne("4860636", null);
        printAccount(account);
    }

    @Test
    void testIdetity() throws Exception {
        IdentityManager identityManager = new IdentityManager(baseURL, accessToken, null);
        Identity identity = identityManager.getOne(null, null);
        printIdentity(identity);
    }

    @Test
    void testBills() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken, accountId);
        ExpandQueryBuilder expandQueryBuilder = new ExpandQueryBuilder().add("vendor");
        List<QueryBuilder> queryBuilders = Arrays.asList(expandQueryBuilder);
        List<Bill> bills = billManager.getListAll(queryBuilders);
        bills.stream().forEach(bill -> printBill(bill));
    }

    @Test
    void testBillsIdIn() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken, accountId);
        ExpandQueryBuilder expandQueryBuilder = new ExpandQueryBuilder().add("vendor");
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addIn("id", Arrays.asList("10218", "10217"));
        List<Bill> bills = null;
        try {
            bills = billManager.getList(Arrays.asList(expandQueryBuilder, filterQueryBuilder));
            bills.stream().forEach(bill -> printBill(bill));
        } catch (FBException e) {
            handlException(e);
        }
    }

    @Test
    void testBill() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken, accountId);
        ExpandQueryBuilder expandQueryBuilder = new ExpandQueryBuilder().add("vendor");
        Bill bill = billManager.getOne("10218", expandQueryBuilder);
        printBill(bill);
    }

    @Test
    void testExpenses() throws Exception {
        ExpenseManager expenseManager = new ExpenseManager(baseURL, accessToken, accountId);
//        ExpandQueryBuilder expandQueryBuilder = new ExpandQueryBuilder().add("category");
        List<Expense> expenses = expenseManager.getListAll(Arrays.asList());
        expenses.stream().forEach(expense -> printExpense(expense));
    }


    @Test
    void testExpense() throws Exception {
        ExpenseManager expenseManager = new ExpenseManager(baseURL, accessToken, accountId);
        Expense expense = expenseManager.getOne("1672397", null);
        printExpense(expense);
    }


    @Test
    void testExpensesDateRange() {
        ExpenseManager expenseManager = new ExpenseManager(baseURL, accessToken, accountId);

        LocalDate START_LOCAL_DATE = LocalDate.of(2021, 1, 1);
        LocalDate END_LOCAL_DATE = LocalDate.of(2021, 12, 31);

        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addBetween("date", START_LOCAL_DATE, END_LOCAL_DATE);
        List<Expense> expenses = null;
        try {
            expenses = expenseManager.getListAll(Arrays.asList(filterQueryBuilder));
            expenses.stream().forEach(expense -> printExpense(expense));
        } catch (FBException e) {
            handlException(e);
        }
    }

    private void handlException(FBException e) {
        if (e instanceof FBUserException) {
            FBUserException fbUserException = (FBUserException) e;
            fbUserException.getErrors().stream().forEach(error -> System.out.println(error.message));
        }
    }


    @Test
    void testExpensesUpdated() throws Exception {
        ExpenseManager expenseManager = new ExpenseManager(baseURL, accessToken, accountId);

        LocalDate START_LOCAL_DATE = LocalDate.of(2022, 4, 17);
        LocalDate END_LOCAL_DATE = LocalDate.of(2022, 12, 31);

        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addBetween("updated", START_LOCAL_DATE, END_LOCAL_DATE);
        List<Expense> expenses = null;
        try {
            expenses = expenseManager.getListAll(Arrays.asList(filterQueryBuilder));
            expenses.stream().forEach(expense -> printExpense(expense));
        } catch (FBException e) {
            handlException(e);
        }
    }

    @Test
    void testVendors() throws Exception {
        VendorManager vendorManager = new VendorManager(baseURL, accessToken, accountId);
        List<Vendor> vendors = null;
        try {
            vendors = vendorManager.getListAll(Arrays.asList());
            vendors.stream().forEach(vendor -> printVendor(vendor));
        } catch (FBException e) {
            handlException(e);
        }
    }

    @Test
    void testVendorOne() throws Exception {
        VendorManager vendorManager = new VendorManager(baseURL, accessToken, accountId);
        Vendor vendor = null;
        try {
            vendor = vendorManager.getOne("6377", null);
            printVendor(vendor);
        } catch (FBException e) {
            handlException(e);
        }
    }

    @Test
    void testVendorByIds() throws Exception {
        VendorManager vendorManager = new VendorManager(baseURL, accessToken, accountId);
        List<Vendor> vendors = null;
        try {
            FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addEquals("id", "6377");
            vendors = vendorManager.getListAll(Arrays.asList(filterQueryBuilder));
            vendors.stream().forEach(vendor -> printVendor(vendor));
        } catch (FBException e) {
            handlException(e);
        }
    }


    @Test
    void testBillPayments() throws Exception {
        BillPaymentManager billPaymentManager = new BillPaymentManager(baseURL, accessToken, accountId);
        List<BillPayment> billPayments = billPaymentManager.getListAll(new ArrayList<>());
        billPayments.stream().forEach(billPayment -> printBillPayment(billPayment));
    }

    @Test
    void testBillPaymentOne() throws Exception {
        //Not supported by FB

        BillPaymentManager billPaymentManager = new BillPaymentManager(baseURL, accessToken, accountId);
        BillPayment billPayment = null;
        try {
            billPayment = billPaymentManager.getOne("7992", null);
            printBillPayment(billPayment);
        } catch (FBException e) {
            handlException(e);
        }
    }


    @Test
    void testBillPaymentsByIds() throws Exception {

        //Not supported by FB
        BillPaymentManager billPaymentManager = new BillPaymentManager(baseURL, accessToken, accountId);
        List<BillPayment> billPayments = null;
        try {
            FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addEquals("id", "7992");
            billPayments = billPaymentManager.getList(Arrays.asList(filterQueryBuilder));
            billPayments.stream().forEach(billPayment -> printBillPayment(billPayment));
        } catch (FBException e) {
            handlException(e);
        }
    }

    @Test
    void testBillPaymentsByDateRange() throws Exception {
        //Not supported by FB
        ExpenseManager expenseManager = new ExpenseManager(baseURL, accessToken, accountId);
    }

    @Test
    void testDeletedVendors() throws Exception {
        VendorManager vendorManager = new VendorManager(baseURL, accessToken, accountId);
        List<Vendor> vendors = null;
        try {
            FilterQueryBuilder archivedQuery = new FilterQueryBuilder().addEquals("vis_state", "1");
            vendors = vendorManager.getListAll(Arrays.asList(archivedQuery));
            vendors.stream().forEach(vendor -> printVendor(vendor));
        } catch (FBException e) {
            handlException(e);
        }
    }

}


