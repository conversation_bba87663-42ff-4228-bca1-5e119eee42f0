CREATE TABLE clientaccessinvitations (
id BIGINT AUTO_INCREMENT NOT NULL,
globalid VARCHAR(255),
clientid VARCHAR(255),
globalrole VARCHAR(255),
localroles VARCHAR(255),
version BIGINT,
invitation_id BIGINT,
PRIMARY KEY (id));

CREATE INDEX clntccssnvitationsfkclntccssnvitationsinvitationid ON clientaccessinvitations (invitation_id);
ALTER TABLE clientaccessinvitations ADD CONSTRAINT fk_clientaccessinvitations_invitation_id FOREIGN KEY (invitation_id) REFERENCES invitations (id);