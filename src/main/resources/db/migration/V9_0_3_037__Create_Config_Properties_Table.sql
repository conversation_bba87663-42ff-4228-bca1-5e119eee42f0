-- Create table for dynamic configuration properties
-- This table stores application configuration that can be modified at runtime
--
DROP TABLE IF EXISTS config;

CREATE TABLE config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    globalid VARCHAR(255),
    name VARCHAR(500) NOT NULL COMMENT 'The property key (e.g., aphe.auth.maxUserFailedAttempts)',
    value TEXT COMMENT 'The property value',
    type VARCHAR(50) NOT NULL DEFAULT 'STRING' COMMENT 'Data type for validation and conversion',
    description VARCHAR(1000) COMMENT 'Description of what this property does',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether this property is currently active',
    is_encrypted BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this property value is encrypted',
    requires_restart BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether application restart is required for changes',
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last modification time',
    modified_by VARCHAR(100) COMMENT 'User who last modified this property',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
    version BIGINT,
    
    -- Constraints
    UNIQUE KEY uk_config_key (name),
    
    -- Indexes for performance
    INDEX idx_config_key (name),
    INDEX idx_config_active (is_active),
    INDEX idx_config_modified (last_modified)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Dynamic configuration properties that can be modified at runtime';
