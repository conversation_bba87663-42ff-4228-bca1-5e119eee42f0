{"void": "0", "corrected": "0", "payerNameAndAddress": "Payer Name \nPayer Address Street \nPayer City, State, Zip", "payerTin": "***********", "payeeTin": "***********", "payeeName": "Payee Name", "payeeAddressLine1Line2": "Payee Address Line1 \nPayee Address Line2", "payeeAddressCityStateZip": "Payee City, State, Zip", "payeeAccountNumber": "Accoount <PERSON>", "fatcaFilingRequirement": "Yes", "secondTinNotice": "Yes", "rents": "11.01", "royalties": "11.02", "otherIncome": "11.03", "federalTaxWithheld": "11.04", "fishingBoatProceeds": "11.05", "medicalPayments": "11.06", "directSalesIndicator": "No", "substitutePayments": "11.08", "cropInsuranceProceeds": "11.09", "attorneyPayments": "11.13", "box11": "11.10", "goldenParachuteAmount": "11.12", "nonQualifiedDeferredComp": "11.13", "four09ADeferrals": "11.14", "state1TaxWithheld": "11.16", "state2TaxWithheld": "11.17", "state1EIN": "CA / 11.18", "state2EIN": "MN / 11.19", "state1Income": "11.20", "state2Income": "11.21"}