{
"mappings":[
{
"valueProperty": "void",
"fieldNames": [
	"copy2.void"
]
},
{
"valueProperty": "corrected",
"fieldNames": [
	"copyb.corrected",
	"copy2.corrected"
]
},
{
"valueProperty": "payerNameAndAddress",
"fieldNames": [
	"copyb.payerNameAndAddress",
	"copy2.payerNameAndAddress"
]
},
{
"valueProperty": "payerTin",
"fieldNames": [
	"copyb.payerTin",
	"copy2.payerTin"
]
},
{
"valueProperty": "payeeTin",
"fieldNames": [
	"copyb.payeeTin",
	"copy2.payeeTin"
]
},
{
"valueProperty": "payeeName",
"fieldNames": [
	"copyb.payeeName",
	"copy2.payeeName"
]
},
{
"valueProperty": "payeeAddressLine1Line2",
"fieldNames": [
	"copyb.payeeAddressLine1Line2",
	"copy2.payeeAddressLine1Line2"
]
},
{
"valueProperty": "payeeAddressCityStateZip",
"fieldNames": [
	"copyb.payeeAddressCityStateZip",
	"copy2.payeeAddressCityStateZip"
]
},
{
"valueProperty": "payeeAccountNumber",
"fieldNames": [
	"copyb.accountNumber",
	"copy2.accountNumber"
]
},
{
"valueProperty": "calendarYear",
"fieldNames": [
	"copyb.calendarYear",
	"copy2.calendarYear"
]
},
{
"valueProperty": "nonEmployeeComp",
"fieldNames": [
	"copyb.nonEmployeeComp",
	"copy2.nonEmployeeComp"
]
},
{
"valueProperty": "directSalesIndicator",
"fieldNames": [
	"copyb.directSalesIndicator",
	"copy2.directSalesIndicator"
]
},
{
"valueProperty": "federalTaxWithheld",
"fieldNames": [
	"copyb.federalTaxWithheld",
	"copy2.federalTaxWithheld"
]
},
{
"valueProperty": "state1TaxWithheld",
"fieldNames": [
	"copyb.state1TaxWithheld",
	"copy2.state1TaxWithheld"
]
},
{
"valueProperty": "state1EIN",
"fieldNames": [
	"copyb.state1TaxId",
	"copy2.state1TaxId"
]
},
{
"valueProperty": "state1Income",
"fieldNames": [
	"copyb.state1Income",
	"copy2.state1Income"
]
},
{
"valueProperty": "state2TaxWithheld",
"fieldNames": [
	"copyb.state2TaxWithheld",
	"copy2.state2TaxWithheld"
]
},
{
"valueProperty": "state2EIN",
"fieldNames": [
	"copyb.state2TaxId",
	"copy2.state2TaxId"
]
},
{
"valueProperty": "state2Income",
"fieldNames": [
	"copyb.state2Income",
	"copy2.state2Income"
]
},
]}
