{"void": "1", "corrected": "2", "payerNameAndAddress": "Payer Name \nPayer Address Street \nPayer City, State, Zip", "payerTin": "***********", "payeeTin": "***********", "payeeName": "Payee Name", "payeeAddressLine1Line2": "Payee Address Line1 \nPayee Address Line2", "payeeAddressCityStateZip": "Payee City, State, Zip", "fatcaFilingRequirement": "1", "payeeAccountNumber": "Accoount <PERSON>", "secondTinNotice": "1", "payerRTN": "payer RTN", "interestIncome": "11.01", "earlyWithdrawalPenalty": "11.02", "interestOnUSSavingsBonds": "11.03", "federalTaxWithheld": "11.04", "investmentExpenses": "11.05", "foreignTaxPaid": "11.06", "foreignCountry": "INDIA", "taxExemptInterest": "11.08", "specifiedPrivateActivityBondInterest": "11.09", "marketDiscount": "11.13", "bondPremium": "11.10", "bondPremiumOnTreasuryObligations": "11.12", "bondPremiumOnTaxExemptBond": "11.14", "taxExemptTaxCreditBondCusipNo": "CUSIP Number", "state1Code": "CA", "state1EIN": "11.17", "state1TaxWithheld": "11.18", "state2Code": "MN", "state2EIN": "11.20", "state2TaxWithheld": "11.21"}