{
    "mappings":[
    {
    "valueProperty": "calendarYear",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].PgHeader[0].f1_1[0]",
        "topmostSubform[0].Copy1[0].Copy1Header[0].CalendarYear[0].f2_1[0]",
        "topmostSubform[0].CopyB[0].CopyBHeader[0].CalendarYear[0].f2_1[0]",
        "topmostSubform[0].Copy2[0].CopyCHeader[0].CalendarYear[0].f2_1[0]"
    ]
    },
    {
    "valueProperty": "void",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].PgHeader[0].c1_1[0]",
        "topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[0]",
        "topmostSubform[0].Copy2[0].<PERSON><PERSON>CHeader[0].c2_1[0]"
    ]
    },
    {
    "valueProperty": "corrected",
    "comment": "possible values are 2, null",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].PgHeader[0].c1_1[1]",
        "topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[1]",
        "topmostSubform[0].CopyB[0].CopyBHeader[0].c2_1[0]",
        "topmostSubform[0].Copy2[0].CopyCHeader[0].c2_1[1]"
    ]
    },
    {
    "valueProperty": "payerNameAndAddress",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_2[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_2[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_2[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_2[0]"
    ]
    },
    {
    "valueProperty": "payerTin",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_3[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_3[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_3[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_3[0]"
    ]
    },
    {
    "valueProperty": "payeeTin",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_4[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_4[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_4[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_4[0]"
    ]
    },
    {
    "valueProperty": "payeeName",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_5[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_5[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_5[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_5[0]"
    ]
    },
    {
    "valueProperty": "payeeAddressLine1Line2",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_6[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_6[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_6[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_6[0]"
    ]
    },
    {
    "valueProperty": "payeeAddressCityStateZip",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_7[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_7[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_7[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_7[0]"
    ]
    },
    {
    "valueProperty": "payeeAccountNumber",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].f1_8[0]",
        "topmostSubform[0].Copy1[0].LeftColumn[0].f2_8[0]",
        "topmostSubform[0].CopyB[0].LeftColumn[0].f2_8[0]",
        "topmostSubform[0].Copy2[0].LeftColumn[0].f2_8[0]"
    ]
    },
    {
    "valueProperty": "secondTinNotice",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].LeftColumn[0].c1_2[0]"
    ]
    },
    {
    "valueProperty": "nonEmployeeComp",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_9[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_9[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_9[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_9[0]"
    ]
    },
    {
    "valueProperty": "directSalesIndicator",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].c1_3[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].c2_3[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].c2_3[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].c2_3[0]"
    ]
    },
    {
    "valueProperty": "federalTaxWithheld",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_10[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_10[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_10[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_10[0]"
    ]
    },
    {
    "valueProperty": "state1TaxWithheld",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_11[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_11[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_11[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_11[0]"
    ]
    },
    {
    "valueProperty": "state2TaxWithheld",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_12[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_12[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_12[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_12[0]"
    ]
    },
    {
    "valueProperty": "state1EIN",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_13[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_13[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_13[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_13[0]"
    ]
    },
    {
    "valueProperty": "state2EIN",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_14[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_14[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_14[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_14[0]"
    ]
    },
    {
    "valueProperty": "state1Income",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_15[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_15[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_15[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_15[0]"
    ]
    },
    {
    "valueProperty": "state2Income",
    "fieldNames": [
        "topmostSubform[0].CopyA[0].RightColumn[0].f1_16[0]",
        "topmostSubform[0].Copy1[0].RightColumn[0].f2_16[0]",
        "topmostSubform[0].CopyB[0].RightColumn[0].f2_16[0]",
        "topmostSubform[0].Copy2[0].RightColumn[0].f2_16[0]"
    ]
    },
    ]}
    