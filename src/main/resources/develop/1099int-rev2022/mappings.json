{"mappings": [{"valueProperty": "calendarYear", "fieldNames": ["topmostSubform[0].CopyA[0].CopyHeader[0].CalendarYear1_1[0]", "topmostSubform[0].Copy1[0].CopyHeader[0].CalendarYear2_1[0]", "topmostSubform[0].CopyB[0].CopyBHeader[0].CalendarYear2_1[0]", "topmostSubform[0].Copy2[0].CopyHeader[0].CalendarYear2_1[0]"]}, {"valueProperty": "void", "comment": "possible values are 1,null", "fieldNames": ["topmostSubform[0].CopyA[0].CopyHeader[0].c1_1[0]", "topmostSubform[0].Copy1[0].CopyHeader[0].c2_1[0]", "topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[0]"]}, {"valueProperty": "corrected", "comment": "possible values are 2,null", "fieldNames": ["topmostSubform[0].CopyA[0].CopyHeader[0].c1_1[1]", "topmostSubform[0].Copy1[0].CopyHeader[0].c2_1[1]", "topmostSubform[0].CopyB[0].CopyBHeader[0].c2_1[0]", "topmostSubform[0].Copy2[0].CopyHeader[0].c2_1[0]", "topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[1]"]}, {"valueProperty": "payerNameAndAddress", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_1[0]", "topmostSubform[0].Copy1[0].LftColumn[0].f2_1[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].f2_1[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].f2_1[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].f2_1[0]"]}, {"valueProperty": "payerTin", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_2[0]", "topmostSubform[0].Copy1[0].LftColumn[0].f2_2[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].f2_2[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].f2_2[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].f2_2[0]"]}, {"valueProperty": "payeeTin", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_3[0]", "topmostSubform[0].Copy1[0].LftColumn[0].f2_3[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].f2_3[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].f2_3[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].f2_3[0]"]}, {"valueProperty": "payeeName", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_4[0]", "topmostSubform[0].Copy1[0].LftColumn[0].f2_4[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].f2_4[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].f2_4[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].f2_4[0]"]}, {"valueProperty": "payeeAddressLine1Line2", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_5[0]", "topmostSubform[0].Copy1[0].LftColumn[0].f2_5[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].f2_5[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].f2_5[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].f2_5[0]"]}, {"valueProperty": "payeeAddressCityStateZip", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_6[0]", "topmostSubform[0].Copy1[0].LftColumn[0].f2_6[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].f2_6[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].f2_6[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].f2_6[0]"]}, {"valueProperty": "fatcaFilingRequirement", "comment": "possible values are 1,null", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].c1_2[0]", "topmostSubform[0].Copy1[0].LftColumn[0].c2_2[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].c2_2[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].c2_2[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].c2_2[0]"]}, {"valueProperty": "payeeAccountNumber", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_7[0]", "topmostSubform[0].Copy1[0].LftColumn[0].f2_7[0]", "topmostSubform[0].CopyB[0].LeftColumn[0].f2_7[0]", "topmostSubform[0].Copy2[0].LeftColumn[0].f2_7[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].f2_7[0]"]}, {"valueProperty": "secondTinNotice", "comment": "possible values are 1,null", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].c1_3[0]", "topmostSubform[0].CopyC[0].LeftColumn[0].c2_3[0]"]}, {"valueProperty": "payerRTN", "fieldNames": ["topmostSubform[0].CopyA[0].LeftColumn[0].f1_8[0]", "topmostSubform[0].Copy1[0].RghtCol[0].f2_8[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].f2_8[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].f2_8[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].f2_8[0]"]}, {"valueProperty": "interestIncome", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box1[0].f1_9[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box1[0].f2_9[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box1[0].f2_9[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box1[0].f2_9[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box1[0].f2_9[0]"]}, {"valueProperty": "earlyWithdrawalPenalty", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box2[0].f1_10[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box2[0].f2_10[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box2[0].f2_10[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box2[0].f2_10[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box2[0].f2_10[0]"]}, {"valueProperty": "interestOnUSSavingsBonds", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box3[0].f1_11[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box3[0].f2_11[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box3[0].f2_11[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box3[0].f2_11[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box3[0].f2_11[0]"]}, {"valueProperty": "federalTaxWithheld", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box4[0].f1_12[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box4[0].f2_12[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box4[0].f2_12[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box4[0].f2_12[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box4[0].f2_12[0]"]}, {"valueProperty": "investmentExpenses", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box5[0].f1_13[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box5[0].f2_13[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box5[0].f2_13[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box5[0].f2_13[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box5[0].f2_13[0]"]}, {"valueProperty": "foreignTaxPaid", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box6[0].f1_14[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box6[0].f2_14[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box6[0].f2_14[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box6[0].f2_14[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box6[0].f2_14[0]"]}, {"valueProperty": "foreignCountry", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].f1_15[0]", "topmostSubform[0].Copy1[0].RghtCol[0].f2_15[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].f2_15[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].f2_15[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].f2_15[0]"]}, {"valueProperty": "taxExemptInterest", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box8[0].f1_16[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box8[0].f2_16[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box8[0].f2_16[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box8[0].f2_16[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box8[0].f2_16[0]"]}, {"valueProperty": "specifiedPrivateActivityBondInterest", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box9[0].f1_17[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box9[0].f2_17[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box9[0].f2_17[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box9[0].f2_17[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box9[0].f2_17[0]"]}, {"valueProperty": "marketDiscount", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box10[0].f1_18[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box10[0].f2_18[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box10[0].f2_18[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box10[0].f2_18[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box10[0].f2_18[0]"]}, {"valueProperty": "bondPremium", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box11[0].f1_19[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box11[0].f2_19[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box11[0].f2_19[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box11[0].f2_19[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box11[0].f2_19[0]"]}, {"valueProperty": "********************************", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box12[0].f1_20[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box12[0].f2_20[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box12[0].f2_20[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box12[0].f2_20[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box12[0].f2_20[0]"]}, {"valueProperty": "bondPremiumOnTaxExemptBond", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box13[0].f1_21[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box13[0].f2_21[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box13[0].f2_21[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box13[0].f2_21[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box13[0].f2_21[0]"]}, {"valueProperty": "taxExemptTaxCreditBondCusipNo", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box14[0].f1_22[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Box14[0].f2_22[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Box14[0].f2_22[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Box14[0].f2_22[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Box14[0].f2_22[0]"]}, {"valueProperty": "state1Code", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Boxes15_16_17[0].f1_23[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Boxes15_16_17[0].f2_23[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Boxes15_16_17[0].f2_23[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Boxes15_16_17[0].f2_23[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Boxes15_16_17[0].f2_23[0]"]}, {"valueProperty": "state1EIN", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Boxes15_16_17[0].f1_24[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Boxes15_16_17[0].f2_24[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Boxes15_16_17[0].f2_24[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Boxes15_16_17[0].f2_24[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Boxes15_16_17[0].f2_24[0]"]}, {"valueProperty": "state1TaxWithheld", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Boxes15_16_17[0].f1_25[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Boxes15_16_17[0].f2_25[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Boxes15_16_17[0].f2_25[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Boxes15_16_17[0].f2_25[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Boxes15_16_17[0].f2_25[0]"]}, {"valueProperty": "state2Code", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Boxes15_16_17[0].f1_26[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Boxes15_16_17[0].f2_26[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Boxes15_16_17[0].f2_26[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Boxes15_16_17[0].f2_26[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Boxes15_16_17[0].f2_26[0]"]}, {"valueProperty": "state2EIN", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Boxes15_16_17[0].f1_27[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Boxes15_16_17[0].f2_27[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Boxes15_16_17[0].f2_27[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Boxes15_16_17[0].f2_27[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Boxes15_16_17[0].f2_27[0]"]}, {"valueProperty": "state2TaxWithheld", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Boxes15_16_17[0].f1_28[0]", "topmostSubform[0].Copy1[0].RghtCol[0].Boxes15_16_17[0].f2_28[0]", "topmostSubform[0].CopyB[0].RghtColumn[0].Boxes15_16_17[0].f2_28[0]", "topmostSubform[0].Copy2[0].RghtColumn[0].Boxes15_16_17[0].f2_28[0]", "topmostSubform[0].CopyC[0].RghtColumn[0].Boxes15_16_17[0].f2_28[0]"]}]}