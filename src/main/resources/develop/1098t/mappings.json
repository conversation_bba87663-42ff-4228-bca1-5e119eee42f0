{"mappings": [{"valueProperty": "void", "comment": "Values are 1/0", "fieldNames": ["topmostSubform[0].CopyA[0].CopyAHeader[0].c1_1[0]", "topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[0]"]}, {"valueProperty": "corrected", "comment": "Values are 2/0", "fieldNames": ["topmostSubform[0].CopyA[0].CopyAHeader[0].c1_1[1]", "topmostSubform[0].CopyB[0].CopyBHeader[0].c2_1[0]", "topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[1]"]}, {"valueProperty": "filerNameAndAddress", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].f1_01[0]", "topmostSubform[0].CopyB[0].LeftCol[0].f2_01[0]", "topmostSubform[0].CopyC[0].LeftCol[0].f2_01[0]"]}, {"valueProperty": "filerTin", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].f1_02[0]", "topmostSubform[0].CopyB[0].LeftCol[0].f2_02[0]", "topmostSubform[0].CopyC[0].LeftCol[0].f2_02[0]"]}, {"valueProperty": "studentTin", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].f1_03[0]", "topmostSubform[0].CopyB[0].LeftCol[0].f2_03[0]", "topmostSubform[0].CopyC[0].LeftCol[0].f2_03[0]"]}, {"valueProperty": "requestStudentTinInWriting", "comment": "Values are 1/0", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].c1_2[0]", "topmostSubform[0].CopyC[0].LeftCol[0].c2_2[0]"]}, {"valueProperty": "studentName", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].f1_04[0]", "topmostSubform[0].CopyB[0].LeftCol[0].f2_04[0]", "topmostSubform[0].CopyC[0].LeftCol[0].f2_04[0]"]}, {"valueProperty": "studentAddressLine1Line2", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].f1_05[0]", "topmostSubform[0].CopyB[0].LeftCol[0].f2_05[0]", "topmostSubform[0].CopyC[0].LeftCol[0].f2_05[0]"]}, {"valueProperty": "studentAddressCityStateZip", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].f1_06[0]", "topmostSubform[0].CopyB[0].LeftCol[0].f2_06[0]", "topmostSubform[0].CopyC[0].LeftCol[0].f2_06[0]"]}, {"valueProperty": "studentAccountNumber", "fieldNames": ["topmostSubform[0].CopyA[0].LeftCol[0].f1_07[0]", "topmostSubform[0].CopyB[0].LeftCol[0].f2_07[0]", "topmostSubform[0].CopyC[0].LeftCol[0].f2_07[0]"]}, {"valueProperty": "paymentsReceived", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].f1_08[0]", "topmostSubform[0].CopyB[0].RightCol[0].f2_08[0]", "topmostSubform[0].CopyC[0].RightCol[0].f2_08[0]"]}, {"valueProperty": "adjustmentsForPriorYear", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box4_ReadOrder[0].f1_09[0]", "topmostSubform[0].CopyB[0].RightCol[0].Box4_ReadOrder[0].f2_09[0]", "topmostSubform[0].CopyC[0].RightCol[0].Box4_ReadOrder[0].f2_09[0]"]}, {"valueProperty": "scholarships", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].f1_10[0]", "topmostSubform[0].CopyB[0].RightCol[0].f2_10[0]", "topmostSubform[0].CopyC[0].RightCol[0].f2_10[0]"]}, {"valueProperty": "scholarshipAdjustmentsForPriorYear", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].Box6_ReadOrder[0].f1_11[0]", "topmostSubform[0].CopyB[0].RightCol[0].Box6_ReadOrder[0].f2_11[0]", "topmostSubform[0].CopyC[0].RightCol[0].Box6_ReadOrder[0].f2_11[0]"]}, {"valueProperty": "paymentForJanMarNextYear", "comment": "Values are 1/0", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].c1_3[0]", "topmostSubform[0].CopyB[0].RightCol[0].c2_3[0]", "topmostSubform[0].CopyC[0].RightCol[0].c2_3[0]"]}, {"valueProperty": "atleastHalfTimeStudent", "comment": "Values are 1/0", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].c1_4[0]", "topmostSubform[0].CopyB[0].RightCol[0].c2_4[0]", "topmostSubform[0].CopyC[0].RightCol[0].c2_4[0]"]}, {"valueProperty": "graduateStudent", "comment": "Values are 1/0", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].c1_5[0]", "topmostSubform[0].CopyB[0].RightCol[0].c2_5[0]", "topmostSubform[0].CopyC[0].RightCol[0].c2_5[0]"]}, {"valueProperty": "refund", "fieldNames": ["topmostSubform[0].CopyA[0].RightCol[0].f1_12[0]", "topmostSubform[0].CopyB[0].RightCol[0].f2_12[0]", "topmostSubform[0].CopyC[0].RightCol[0].f2_12[0]"]}]}