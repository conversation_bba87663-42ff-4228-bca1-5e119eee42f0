
void
comment=Possbile Values are 1, null
topmostSubform[0].CopyA[0].CopyAHeader[0].c1_1[0]			1			Checkbox			[1]
topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[0]			1			Checkbox			[1]
topmostSubform[0].Copy2[0].Copy2Header[0].c2_1[0]			1			Checkbox			[1]
topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[0]			1			Checkbox			[1]


corrected
comment=Possbile Values are 2, null
topmostSubform[0].CopyA[0].CopyAHeader[0].c1_1[1]			2			Checkbox			[2]
topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[1]			2			Checkbox			[2]
topmostSubform[0].CopyB[0].CopyBHeader[0].c2_1[0]			2			Checkbox			[2]
topmostSubform[0].Copy2[0].Copy2Header[0].c2_1[1]			2			Checkbox			[2]
topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[1]			2			Checkbox			[2]


payerNameAndAddress
topmostSubform[0].CopyA[0].LeftCol[0].f1_1[0]			1			TextBox			[]
topmostSubform[0].Copy1[0].LeftCol[0].f2_1[0]			16			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].LeftCol[0].f2_1[0]			31			TextBox			[]
topmostSubform[0].Copy2[0].LeftCol[0].f2_1[0]			46			TextBox			[]
topmostSubform[0].CopyC[0].LeftCol[0].f2_1[0]			61			TextBox			[]


payerTin
PAYERS TIN			2			TextBox			[]
PAYERS TIN 1			18			TextBox			[]


payeeTin
topmostSubform[0].CopyA[0].LeftCol[0].f1_3[0]			3			TextBox			[]
topmostSubform[0].Copy1[0].LeftCol[0].f2_3[0]			18			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].LeftCol[0].f2_3[0]			33			TextBox			[]
topmostSubform[0].Copy2[0].LeftCol[0].f2_3[0]			48			TextBox			[]
topmostSubform[0].CopyC[0].LeftCol[0].f2_3[0]			63			TextBox			[]


payeeName
topmostSubform[0].CopyA[0].LeftCol[0].f1_4[0]			4			TextBox			[]
topmostSubform[0].Copy1[0].LeftCol[0].f2_4[0]			19			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].LeftCol[0].f2_4[0]			34			TextBox			[]
topmostSubform[0].Copy2[0].LeftCol[0].f2_4[0]			49			TextBox			[]
topmostSubform[0].CopyC[0].LeftCol[0].f2_4[0]			64			TextBox			[]


payeeAddressLine1Line2
topmostSubform[0].CopyA[0].LeftCol[0].f1_5[0]			5			TextBox			[]
topmostSubform[0].Copy1[0].LeftCol[0].f2_5[0]			20			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].LeftCol[0].f2_5[0]			35			TextBox			[]
topmostSubform[0].Copy2[0].LeftCol[0].f2_5[0]			50			TextBox			[]
topmostSubform[0].CopyC[0].LeftCol[0].f2_5[0]			65			TextBox			[]


payeeAddressCityStateZip
topmostSubform[0].CopyA[0].LeftCol[0].f1_6[0]			6			TextBox			[]
topmostSubform[0].Copy1[0].LeftCol[0].f2_6[0]			21			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].LeftCol[0].f2_6[0]			36			TextBox			[]
topmostSubform[0].Copy2[0].LeftCol[0].f2_6[0]			51			TextBox			[]
topmostSubform[0].CopyC[0].LeftCol[0].f2_6[0]			66			TextBox			[]


fatcaFilingRequirement
comment=Possbile Values are 1, null
topmostSubform[0].CopyA[0].LeftCol[0].c1_2[0]			1			Checkbox			[1]
topmostSubform[0].Copy1[0].LeftCol[0].c2_2[0]			1			Checkbox			[1]
topmostSubform[0].CopyB[0].CopyBHeader[0].LeftCol[0].c2_2[0]			1			Checkbox			[1]
topmostSubform[0].Copy2[0].LeftCol[0].c2_2[0]			1			Checkbox			[1]
topmostSubform[0].CopyC[0].LeftCol[0].c2_2[0]			1			Checkbox			[1]


payeeAccountNumber
Account number see instructions			3			TextBox			[]
Account number see instructions 1			19			TextBox			[]


secondTinNotice
comment=Possbile Values are 1, null
topmostSubform[0].CopyA[0].LeftCol[0].c1_3[0]			1			Checkbox			[1]
topmostSubform[0].CopyC[0].LeftCol[0].c2_3[0]			1			Checkbox			[1]

nonEmployeeComp
Form 1099NEC			1			TextBox			[]
Form 1099NEC 1			17			TextBox			[]

federalTaxWithheld
topmostSubform[0].CopyA[0].RightCol[0].f1_9[0]			9			TextBox			[]
topmostSubform[0].Copy1[0].RightCol[0].f2_9[0]			24			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].RghtCol[0].f2_9[0]			39			TextBox			[]
topmostSubform[0].Copy2[0].RghtCol[0].f2_9[0]			54			TextBox			[]
topmostSubform[0].CopyC[0].RghtCol[0].f2_9[0]			69			TextBox			[]


state1TaxWithheld
5 State tax withheld			4			TextBox			[]
5 State tax withheld 3			20			TextBox			[]

state2TaxWithheld
topmostSubform[0].CopyA[0].RightCol[0].f1_11[0]			11			TextBox			[]
topmostSubform[0].Copy1[0].RightCol[0].f2_11[0]			26			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].RghtCol[0].f2_11[0]			41			TextBox			[]
topmostSubform[0].Copy2[0].RghtCol[0].f2_11[0]			56			TextBox			[]
topmostSubform[0].CopyC[0].RghtCol[0].f2_11[0]			71			TextBox			[]


state1EIN
topmostSubform[0].CopyA[0].RightCol[0].f1_12[0]			12			TextBox			[]
topmostSubform[0].Copy1[0].RightCol[0].f2_12[0]			27			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].RghtCol[0].f2_12[0]			42			TextBox			[]
topmostSubform[0].Copy2[0].RghtCol[0].f2_12[0]			57			TextBox			[]
topmostSubform[0].CopyC[0].RghtCol[0].f2_12[0]			72			TextBox			[]


state2EIN
topmostSubform[0].CopyA[0].RightCol[0].f1_13[0]			13			TextBox			[]
topmostSubform[0].Copy1[0].RightCol[0].f2_13[0]			28			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].RghtCol[0].f2_13[0]			43			TextBox			[]
topmostSubform[0].Copy2[0].RghtCol[0].f2_13[0]			58			TextBox			[]
topmostSubform[0].CopyC[0].RghtCol[0].f2_13[0]			73			TextBox			[]


state1Income
topmostSubform[0].CopyA[0].RightCol[0].f1_14[0]			14			TextBox			[]
topmostSubform[0].Copy1[0].RightCol[0].f2_14[0]			29			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].RghtCol[0].f2_14[0]			44			TextBox			[]
topmostSubform[0].Copy2[0].RghtCol[0].f2_14[0]			59			TextBox			[]
topmostSubform[0].CopyC[0].RghtCol[0].f2_14[0]			74			TextBox			[]


state2Income
topmostSubform[0].CopyA[0].RightCol[0].f1_15[0]			15			TextBox			[]
topmostSubform[0].Copy1[0].RightCol[0].f2_15[0]			30			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].RghtCol[0].f2_15[0]			45			TextBox			[]
topmostSubform[0].Copy2[0].RghtCol[0].f2_15[0]			60			TextBox			[]
topmostSubform[0].CopyC[0].RghtCol[0].f2_15[0]			75			TextBox			[]
