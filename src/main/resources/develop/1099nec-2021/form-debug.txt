Number of pages: 8
Size of page 1: [0.0,0.0,612.0,792.008]
Rotation of page 1: 0
Page size with rotation of page 1: Rectangle: 612.0x792.008 (rot: 0 degrees)
File length: 522549
Is rebuilt? false
Is encrypted? false
Form Type: XFA


Form Fields:

topmostSubform[0].CopyA[0].PgHeader[0].CalendarYear[0].f1_1[0]			1			TextBox			[]
topmostSubform[0].Copy1[0].Copy1Header[0].CalendarYear[0].f2_1[0]			17			TextBox			[]
topmostSubform[0].CopyB[0].CopyBHeader[0].CalendarYear[0].f2_1[0]			33			TextBox			[]
topmostSubform[0].Copy2[0].CopyCHeader[0].CalendarYear[0].f2_1[0]			49			TextBox			[]
topmostSubform[0].CopyC[0].CopyCHeader[0].CalendarYear[0].f2_1[0]			65			TextBox			[]


topmostSubform[0].CopyA[0].PgHeader[0].c1_1[0]			1			Checkbox			[1]
topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[0]			1			Checkbox			[1]
topmostSubform[0].Copy2[0].CopyCHeader[0].c2_1[0]			1			Checkbox			[1]
topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[0]			1			Checkbox			[1]

topmostSubform[0].CopyA[0].PgHeader[0].c1_1[1]			2			Checkbox			[2]
topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[1]			2			Checkbox			[2]
topmostSubform[0].CopyB[0].CopyBHeader[0].c2_1[0]			2			Checkbox			[2]
topmostSubform[0].Copy2[0].CopyCHeader[0].c2_1[1]			2			Checkbox			[2]
topmostSubform[0].CopyC[0].CopyCHeader[0].c2_1[1]			2			Checkbox			[2]


topmostSubform[0].CopyA[0].LeftColumn[0].f1_2[0]			2			TextBox			[]
topmostSubform[0].Copy1[0].LeftColumn[0].f2_2[0]			18			TextBox			[]
topmostSubform[0].CopyB[0].LeftColumn[0].f2_2[0]			34			TextBox			[]
topmostSubform[0].Copy2[0].LeftColumn[0].f2_2[0]			50			TextBox			[]
topmostSubform[0].CopyC[0].LeftColumn[0].f2_2[0]			66			TextBox			[]

topmostSubform[0].CopyA[0].LeftColumn[0].f1_3[0]			3			TextBox			[]
topmostSubform[0].Copy1[0].LeftColumn[0].f2_3[0]			19			TextBox			[]
topmostSubform[0].CopyB[0].LeftColumn[0].f2_3[0]			35			TextBox			[]
topmostSubform[0].Copy2[0].LeftColumn[0].f2_3[0]			51			TextBox			[]
topmostSubform[0].CopyC[0].LeftColumn[0].f2_3[0]			67			TextBox			[]

topmostSubform[0].CopyA[0].LeftColumn[0].f1_4[0]			4			TextBox			[]
topmostSubform[0].Copy1[0].LeftColumn[0].f2_4[0]			20			TextBox			[]
topmostSubform[0].CopyB[0].LeftColumn[0].f2_4[0]			36			TextBox			[]
topmostSubform[0].Copy2[0].LeftColumn[0].f2_4[0]			52			TextBox			[]
topmostSubform[0].CopyC[0].LeftColumn[0].f2_4[0]			68			TextBox			[]

topmostSubform[0].CopyA[0].LeftColumn[0].f1_5[0]			5			TextBox			[]
topmostSubform[0].Copy1[0].LeftColumn[0].f2_5[0]			21			TextBox			[]
topmostSubform[0].CopyB[0].LeftColumn[0].f2_5[0]			37			TextBox			[]
topmostSubform[0].Copy2[0].LeftColumn[0].f2_5[0]			53			TextBox			[]
topmostSubform[0].CopyC[0].LeftColumn[0].f2_5[0]			69			TextBox			[]

topmostSubform[0].CopyA[0].LeftColumn[0].f1_6[0]			6			TextBox			[]
topmostSubform[0].Copy1[0].LeftColumn[0].f2_6[0]			22			TextBox			[]
topmostSubform[0].CopyB[0].LeftColumn[0].f2_6[0]			38			TextBox			[]
topmostSubform[0].Copy2[0].LeftColumn[0].f2_6[0]			54			TextBox			[]
topmostSubform[0].CopyC[0].LeftColumn[0].f2_6[0]			70			TextBox			[]

topmostSubform[0].CopyA[0].LeftColumn[0].f1_7[0]			7			TextBox			[]
topmostSubform[0].Copy1[0].LeftColumn[0].f2_7[0]			23			TextBox			[]
topmostSubform[0].CopyB[0].LeftColumn[0].f2_7[0]			39			TextBox			[]
topmostSubform[0].Copy2[0].LeftColumn[0].f2_7[0]			55			TextBox			[]
topmostSubform[0].CopyC[0].LeftColumn[0].f2_7[0]			71			TextBox			[]

topmostSubform[0].CopyA[0].LeftColumn[0].f1_8[0]			8			TextBox			[]
topmostSubform[0].Copy1[0].LeftColumn[0].f2_8[0]			24			TextBox			[]
topmostSubform[0].CopyB[0].LeftColumn[0].f2_8[0]			40			TextBox			[]
topmostSubform[0].Copy2[0].LeftColumn[0].f2_8[0]			56			TextBox			[]
topmostSubform[0].CopyC[0].LeftColumn[0].f2_8[0]			72			TextBox			[]

topmostSubform[0].CopyA[0].LeftColumn[0].c1_2[0]			1			Checkbox			[1]
topmostSubform[0].CopyC[0].LeftColumn[0].c2_2[0]			1			Checkbox			[1]

topmostSubform[0].CopyA[0].RightColumn[0].f1_9[0]			9			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_9[0]			25			TextBox			[]
topmostSubform[0].CopyB[0].RightColumn[0].f2_9[0]			41			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_9[0]			57			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_9[0]			73			TextBox			[]

topmostSubform[0].CopyA[0].RightColumn[0].c1_3[0]			1			Checkbox			[1]
topmostSubform[0].Copy1[0].RightColumn[0].c2_3[0]			1			Checkbox			[1]
topmostSubform[0].CopyB[0].RightColumn[0].c2_3[0]			1			Checkbox			[1]
topmostSubform[0].Copy2[0].RightColumn[0].c2_3[0]			1			Checkbox			[1]
topmostSubform[0].CopyC[0].RightColumn[0].c2_3[0]			1			Checkbox			[1]

topmostSubform[0].CopyA[0].RightColumn[0].f1_10[0]			10			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_10[0]			26			TextBox			[]
topmostSubform[0].CopyB[0].RightColumn[0].f2_10[0]			42			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_10[0]			58			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_10[0]			74			TextBox			[]

topmostSubform[0].CopyA[0].RightColumn[0].f1_11[0]			11			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_11[0]			27			TextBox			[]
topmostSubform[0].CopyB[0].RightColumn[0].f2_11[0]			43			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_11[0]			59			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_11[0]			75			TextBox			[]

topmostSubform[0].CopyA[0].RightColumn[0].f1_12[0]			12			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_12[0]			28			TextBox			[]
topmostSubform[0].CopyB[0].RightColumn[0].f2_12[0]			44			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_12[0]			60			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_12[0]			76			TextBox			[]

topmostSubform[0].CopyA[0].RightColumn[0].f1_13[0]			13			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_13[0]			29			TextBox
topmostSubform[0].CopyB[0].RightColumn[0].f2_13[0]			45			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_13[0]			61			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_13[0]			77			TextBox			[]

topmostSubform[0].CopyA[0].RightColumn[0].f1_14[0]			14			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_14[0]			30			TextBox			[]
topmostSubform[0].CopyB[0].RightColumn[0].f2_14[0]			46			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_14[0]			62			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_14[0]			78			TextBox			[]

topmostSubform[0].CopyA[0].RightColumn[0].f1_15[0]			15			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_15[0]			31			TextBox			[]
topmostSubform[0].CopyB[0].RightColumn[0].f2_15[0]			47			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_15[0]			63			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_15[0]			79			TextBox			[]

topmostSubform[0].CopyA[0].RightColumn[0].f1_16[0]			16			TextBox			[]
topmostSubform[0].Copy1[0].RightColumn[0].f2_16[0]			32			TextBox			[]
topmostSubform[0].CopyB[0].RightColumn[0].f2_16[0]			48			TextBox			[]
topmostSubform[0].Copy2[0].RightColumn[0].f2_16[0]			64			TextBox			[]
topmostSubform[0].CopyC[0].RightColumn[0].f2_16[0]			80			TextBox			[]
