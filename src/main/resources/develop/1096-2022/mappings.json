{
"mappings":[
{
"valueProperty": "payerName",
"fieldNames": [
	"topmostSubform[0].Page1[0].NameAddress_ReadOrder[0].f1_1[0]"
]
},
{
"valueProperty": "payerAddressLine1Line2",
"fieldNames": [
	"topmostSubform[0].Page1[0].NameAddress_ReadOrder[0].f1_2[0]"
]
},
{
"valueProperty": "payerAddressCityStateZip",
"fieldNames": [
	"topmostSubform[0].Page1[0].NameAddress_ReadOrder[0].f1_3[0]"
]
},
{
"valueProperty": "contactName",
"fieldNames": [
	"topmostSubform[0].Page1[0].NameAddress_ReadOrder[0].f1_4[0]"
]
},
{
"valueProperty": "contactPhone",
"fieldNames": [
	"topmostSubform[0].Page1[0].NameAddress_ReadOrder[0].f1_5[0]"
]
},
{
"valueProperty": "contactEmail",
"fieldNames": [
	"topmostSubform[0].Page1[0].NameAddress_ReadOrder[0].f1_6[0]"
]
},
{
"valueProperty": "contactFax",
"fieldNames": [
	"topmostSubform[0].Page1[0].NameAddress_ReadOrder[0].f1_7[0]"
]
},
{
"valueProperty": "payerEIN",
"fieldNames": [
	"topmostSubform[0].Page1[0].f1_8[0]"
]
},
{
"valueProperty": "payerSSN",
"fieldNames": [
	"topmostSubform[0].Page1[0].f1_9[0]"
]
},
{
"valueProperty": "numberOfForms",
"fieldNames": [
	"topmostSubform[0].Page1[0].f1_10[0]"
]
},
{
"valueProperty": "federalTaxWithheld",
"fieldNames": [
	"topmostSubform[0].Page1[0].Box4_ReadOrder[0].f1_11[0]"
]
},
{
"valueProperty": "totalAmountReported",
"fieldNames": [
	"topmostSubform[0].Page1[0].f1_12[0]"
]
},
{
"valueProperty": "fw2g",
"comment": "possible values are 1,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].FW-2G[0].c1_1[0]"
]
},
{
"valueProperty": "f1097btc",
"comment": "possible values are 2,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1097-BTC[0].c1_1[0]"
]
},
{
"valueProperty": "f1098",
"comment": "possible values are 3,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1098[0].c1_1[0]"
]
},
{
"valueProperty": "f1098c",
"comment": "possible values are 4,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1098-C[0].c1_1[0]"
]
},
{
"valueProperty": "f1098e",
"comment": "possible values are 5,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1098-E[0].c1_1[0]"
]
},
{
"valueProperty": "f1098f",
"comment": "possible values are 6,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1098-F[0].c1_1[0]"
]
},
{
"valueProperty": "f1098q",
"comment": "possible values are 7,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1098-Q[0].c1_1[0]"
]
},
{
"valueProperty": "f1098t",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1098-T[0].c1_1[0]"
]
},
{
"valueProperty": "f1099a",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-A[0].c1_1[0]"
]
},
{
"valueProperty": "f1099b",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-B[0].c1_1[0]"
]
},
{
"valueProperty": "f1099c",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-C[0].c1_1[0]"
]
},
{
"valueProperty": "f1099cap",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-CAP[0].c1_1[0]"
]
},
{
"valueProperty": "f1099div",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-DIV[0].c1_1[0]"
]
},
{
"valueProperty": "f1099g",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-G[0].c1_1[0]"
]
},
{
"valueProperty": "f1099int",
"comment": "possible values are 15,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-INT[0].c1_1[0]"
]
},
{
"valueProperty": "f1099k",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-K[0].c1_1[0]"
]
},
{
"valueProperty": "f1099ls",
"fieldNames": [
	"topmostSubform[0].Page1[0].c1_1[0]"
]
},
{
"valueProperty": "f1099ltc",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-LTC[0].c1_1[0]"
]
},
{
"valueProperty": "f1099misc",
"comment": "possible values are 19,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-MISC[0].c1_1[0]"
]
},
{
"valueProperty": "f1099nec",
"comment": "possible values are 20,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-NEC[0].c1_1[0]"
]
},
{
"valueProperty": "f1099oid",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-OID[0].c1_1[0]"
]
},
{
"valueProperty": "f1099patr",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-PATR[0].c1_1[0]"
]
},
{
"valueProperty": "f1099q",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-Q[0].c1_1[0]"
]
},
{
"valueProperty": "f1099qa",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-QA[0].c1_1[0]"
]
},
{
"valueProperty": "f1099r",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-R[0].c1_1[0]"
]
},
{
"valueProperty": "f1099s",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-S[0].c1_1[0]"
]
},
{
"valueProperty": "f1099sa",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-SA[0].c1_1[0]"
]
},
{
"valueProperty": "f1099sb",
"fieldNames": [
	"topmostSubform[0].Page1[0].F1099-SB[0].c1_1[0]"
]
},
{
"valueProperty": "f3921",
"fieldNames": [
	"topmostSubform[0].Page1[0].F3921[0].c1_1[0]"
]
},
{
"valueProperty": "f3922",
"fieldNames": [
	"topmostSubform[0].Page1[0].F3922[0].c1_1[0]"
]
},
{
"valueProperty": "f5498",
"fieldNames": [
	"topmostSubform[0].Page1[0].F5498[0].c1_1[0]"
]
},
{
"valueProperty": "f5498esa",
"fieldNames": [
	"topmostSubform[0].Page1[0].F5498-ESA[0].c1_1[0]"
]
},
{
"valueProperty": "f5498qa",
"fieldNames": [
	"topmostSubform[0].Page1[0].F5498-QA[0].c1_1[0]"
]
},
{
"valueProperty": "f5498sa",
"comment": "possible values are 34,null",
"fieldNames": [
	"topmostSubform[0].Page1[0].c1_1[1]"
]
},
{
"valueProperty": "signerTitle",
"fieldNames": [
	"topmostSubform[0].Page1[0].f1_13[0]"
]
},
]}
