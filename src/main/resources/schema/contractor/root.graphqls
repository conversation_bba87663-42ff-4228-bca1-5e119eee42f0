
extend type Query {
    flush: Boolean

	payer: Payer

	payeeCount: Int!
	payees(pageSize: Int, pageNumber: Int): [Payee]
	payeesWithPageMetadata(pageSize: Int, pageNumber: Int): PayeeConnection
	payee(id:ID!) : Payee
	payeeByTin(tin: String!) : Payee
	payeeByPartnerId(partner: String!, partnerId: String!) : Payee

	filingCounts(filingYear: String): [FilingCount]
	filing (id:ID!): Filing
	filingByPayeeFormTypeFilingYear (payeeId: ID!, formType:String!, filingYear: String!): Filing
	filingsByIds(filingIds: [ID]!): [Filing]
	filingsByFormType (formType:String!, pageSize: Int, pageNumber: Int): [Filing]
	filingsByStatus (status:String!, year:String, pageSize: Int, pageNumber: Int): [Filing]
	filings(filingStatuses:[String], filingYears:[String], filingTypes:[String], pageSize: Int, pageNumber: Int): [Filing]
	filingsWithPageMetadata(filingStatuses:[String], filingYears:[String], filingTypes:[String], pageSize: Int, pageNumber: Int): FilingConnection

    selfEFileCount(filingYear: String): Int!
	selfEFileFilingsWithPageMetadata(filingStatuses:[String], filingYears:[String], filingTypes:[String], pageSize: Int, pageNumber: Int): FilingConnection

    printRequests(printStatus: String, filingYear:String): [PrintEmailRequest]
    emailRequests(emailStatus: String, filingYear:String): [PrintEmailRequest]

    w9s(payeeIds: [ID]!) : [W9Request]
    w9Request(requestId: ID!): W9Request

    tinMatches(payeeIds: [ID]!) : [TINMatchRequest]
    tinMatch(requestId: ID!): TINMatchRequest
}

extend type Mutation {
	updatePayer(input: AddEditPayerInput): Payer
	validatePayer(payerId: ID!, filingYear: String!) : ValidationMessages

    addEditPayeeLight(input: AddEditPayeeLightInput): Payee
	addEditPayee(input: AddEditPayeeInput): Payee
	deletePayee(payeeId: ID!) : Boolean
	deletePayees(payeeIds: [ID]!) : Boolean

    createTINMatches(payeeIds: [ID]!) : [TINMatchRequest]
	requestTINMatches(payeeIds: [ID]!) : Boolean
	cancelTINMatches(payeeIds: [ID]!) : Boolean
	updateTINMatch(payeeId: ID!, selected: Boolean!) : Boolean
	bulkUpdateTINMatches(payeeIds: [ID]!, selected: Boolean!) : Boolean
	ignoreTINValidation(payeeId: ID!) : Boolean

    createW9s(payeeIds: [ID]!) : [W9Request]
    requestW9s(payeeIds: [ID]!) : Boolean
    cancelW9Requests(payeeIds: [ID]!) : Boolean
    remindW9Requests(payeeIds: [ID]!) : Boolean
    applyW9Info(payeeIds: [ID]!) : Boolean

	addEditFiling1099MISC(input: AddEditFilingInput1099MISC): Filing!
	addEditFiling1099MISC2020(input: AddEditFilingInput1099MISC2020): Filing!
	addEditFiling1099MISC2021(input: AddEditFilingInput1099MISC2021): Filing!
	addEditFiling1099NEC(input: AddEditFilingInput1099NEC): Filing!
	addEditFiling1099NEC2021(input: AddEditFilingInput1099NEC2021): Filing!
	addEditFiling1099INT(input: AddEditFilingInput1099INT): Filing!
	addEditFiling1099OID(input: AddEditFilingInput1099OID): Filing!

	deleteFiling(filingId: ID!): Boolean
	deleteFilings(filingIds: [ID]!): Boolean
	submitFilings(filingIds: [ID]!): Boolean
	validateFilings(filingIds: [ID]!, ignoreWarnings: Boolean!): [MappedValidationMessages]
	updateFedSub(filingId: ID!, selected: Boolean!): [MappedValidationMessages]
	updateStateSub(filingId: ID!, selected: Boolean!, filingMethod:String): [MappedValidationMessages]
	updatePrintSub(filingId: ID!, selected: Boolean!): [MappedValidationMessages]
	updateEmailSub(filingId: ID!, selected: Boolean!): [MappedValidationMessages]
	updateTINMatchByFilingIds(filingIds: [ID]!, selected: Boolean!): [MappedValidationMessages]
	send1099Form(filingId: ID!): Boolean

	bulkUpdateFedSub(filingIds: [ID!], selected: Boolean!): [MappedValidationMessages]
	bulkUpdateStateSub(stateSubs: StateSubInputs!): [MappedValidationMessages]
	bulkUpdatePrintSub(filingIds: [ID!], selected: Boolean!): [MappedValidationMessages]
	bulkUpdateEmailSub(filingIds: [ID!], selected: Boolean!): [MappedValidationMessages]

    updatePrintRequests(input: UpdatePrintRequestsInput): Boolean
    updateEmailRequests(input: UpdateEmailRequestsInput): Boolean


    sendDraftFilingsReminder(domainIds:String!, reminderNumber:Int!, test:Boolean!): [CustomJobResponse]
    sendSeasonReminder(domainIds:String!, reminderNumber:Int!, test:Boolean!): [CustomJobResponse]
    runCustomJob(jobName:String!, ids:String!, test:Boolean!): [CustomJobResponse]


}