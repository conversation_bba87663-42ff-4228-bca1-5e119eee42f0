{
"mappings":[
{
"valueProperty": "calendarYear",
"fieldNames": [
	"topmostSubform[0].CopyA[0].CopyAHeader[0].CalendarYear[0].f1_1[0]",
	"topmostSubform[0].Copy1[0].Copy1Header[0].CalendarYear[0].f2_1[0]",
	"topmostSubform[0].CopyB[0].CopyBHeader[0].CalendarYear[0].f2_1[0]",
	"topmostSubform[0].Copy2[0].Copy2Header[0].CalendarYear[0].f2_1[0]"
]
},
{
"valueProperty": "void",
"fieldNames": [
	"topmostSubform[0].CopyA[0].CopyAHeader[0].c1_1[0]",
	"topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[0]"
]
},
{
"valueProperty": "corrected",
"fieldNames": [
	"topmostSubform[0].CopyA[0].CopyAHeader[0].c1_1[1]",
	"topmostSubform[0].Copy1[0].Copy1Header[0].c2_1[1]",
	"topmostSubform[0].CopyB[0].CopyBHeader[0].c2_1[0]",
	"topmostSubform[0].Copy2[0].Copy2Header[0].c2_1[0]"
]
},
{
"valueProperty": "payerNameAndAddress",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].f1_2[0]",
	"topmostSubform[0].Copy1[0].LeftColumn[0].f2_2[0]",
	"topmostSubform[0].CopyB[0].LeftColumn[0].f2_2[0]",
	"topmostSubform[0].Copy2[0].LeftColumn[0].f2_2[0]"
]
},
{
"valueProperty": "payerTin",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].f1_3[0]",
	"topmostSubform[0].Copy1[0].LeftColumn[0].f2_3[0]",
	"topmostSubform[0].CopyB[0].LeftColumn[0].f2_3[0]",
	"topmostSubform[0].Copy2[0].LeftColumn[0].f2_3[0]"
]
},
{
"valueProperty": "payeeTin",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].f1_4[0]",
	"topmostSubform[0].Copy1[0].LeftColumn[0].f2_4[0]",
	"topmostSubform[0].CopyB[0].LeftColumn[0].f2_4[0]",
	"topmostSubform[0].Copy2[0].LeftColumn[0].f2_4[0]"
]
},
{
"valueProperty": "payeeName",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].f1_5[0]",
	"topmostSubform[0].Copy1[0].LeftColumn[0].f2_5[0]",
	"topmostSubform[0].CopyB[0].LeftColumn[0].f2_5[0]",
	"topmostSubform[0].Copy2[0].LeftColumn[0].f2_5[0]"
]
},
{
"valueProperty": "payeeAddressLine1Line2",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].f1_6[0]",
	"topmostSubform[0].Copy1[0].LeftColumn[0].f2_6[0]",
	"topmostSubform[0].CopyB[0].LeftColumn[0].f2_6[0]",
	"topmostSubform[0].Copy2[0].LeftColumn[0].f2_6[0]"
]
},
{
"valueProperty": "payeeAddressCityStateZip",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].f1_7[0]",
	"topmostSubform[0].Copy1[0].LeftColumn[0].f2_7[0]",
	"topmostSubform[0].CopyB[0].LeftColumn[0].f2_7[0]",
	"topmostSubform[0].Copy2[0].LeftColumn[0].f2_7[0]"
]
},
{
"valueProperty": "payeeAccountNumber",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].f1_8[0]",
	"topmostSubform[0].Copy1[0].LeftColumn[0].f2_8[0]",
	"topmostSubform[0].CopyB[0].LeftColumn[0].f2_8[0]",
	"topmostSubform[0].Copy2[0].LeftColumn[0].f2_8[0]"
]
},
{
"valueProperty": "secondTinNotice",
"fieldNames": [
	"topmostSubform[0].CopyA[0].LeftColumn[0].c1_2[0]"
]
},
{
"valueProperty": "rents",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_9[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_9[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_9[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_9[0]"
]
},
{
"valueProperty": "royalties",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_10[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_10[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_10[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_10[0]"
]
},
{
"valueProperty": "otherIncome",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_11[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_11[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_11[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_11[0]"
]
},
{
"valueProperty": "federalTaxWithheld",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_12[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_12[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_12[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_12[0]"
]
},
{
"valueProperty": "fishingBoatProceeds",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_13[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_13[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_13[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_13[0]"
]
},
{
"valueProperty": "medicalPayments",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_14[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_14[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_14[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_14[0]"
]
},
{
"valueProperty": "directSalesIndicator",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].c1_4[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].c2_4[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].c2_4[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].c2_4[0]"
]
},
{
"valueProperty": "substitutePayments",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_15[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_15[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_15[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_15[0]"
]
},
{
"valueProperty": "cropInsuranceProceeds",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_16[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_16[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_16[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_16[0]"
]
},
{
"valueProperty": "attorneyPayments",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_17[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_17[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_17[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_17[0]"
]
},
{
"valueProperty": "fishPurchasedForResale",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_18[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_18[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_18[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_18[0]"
]
},
{
"valueProperty": "four09ADeferrals",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_19[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_19[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_19[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_19[0]"
]
},
{
"valueProperty": "fatcaFilingRequirement",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].TagCorrectingSubform[0].c1_3[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].TagCorrectingSubform[0].c2_3[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].TagCorrectingSubform[0].c2_3[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].TagCorrectingSubform[0].c2_3[0]"
]
},
{
"valueProperty": "goldenParachuteAmount",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].Box14_ReadOrder[0].f1_20[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].Box14_ReadOrder[0].f2_20[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].Box14_ReadOrder[0].f2_20[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].Box14_ReadOrder[0].f2_20[0]"
]
},
{
"valueProperty": "nonQualifiedDeferredComp",
"fieldNames": [
	"topmostSubform[0].CopyA[0].RightColumn[0].f1_21[0]",
	"topmostSubform[0].Copy1[0].RightColumn[0].f2_21[0]",
	"topmostSubform[0].CopyB[0].RightColumn[0].f2_21[0]",
	"topmostSubform[0].Copy2[0].RightColumn[0].f2_21[0]"
]
},
{
"valueProperty": "state1TaxWithheld",
"fieldNames": [
	"topmostSubform[0].CopyA[0].Box16_ReadOrder[0].f1_22[0]",
	"topmostSubform[0].Copy1[0].Box16_ReadOrder[0].f2_22[0]",
	"topmostSubform[0].CopyB[0].Box16_ReadOrder[0].f2_22[0]",
	"topmostSubform[0].Copy2[0].Box16_ReadOrder[0].f2_22[0]"
]
},
{
"valueProperty": "state2TaxWithheld",
"fieldNames": [
	"topmostSubform[0].CopyA[0].Box16_ReadOrder[0].f1_23[0]",
	"topmostSubform[0].Copy1[0].Box16_ReadOrder[0].f2_23[0]",
	"topmostSubform[0].CopyB[0].Box16_ReadOrder[0].f2_23[0]",
	"topmostSubform[0].Copy2[0].Box16_ReadOrder[0].f2_23[0]"
]
},
{
"valueProperty": "state1EIN",
"fieldNames": [
	"topmostSubform[0].CopyA[0].Box17_ReadOrder[0].f1_24[0]",
	"topmostSubform[0].Copy1[0].Box17_ReadOrder[0].f2_24[0]",
	"topmostSubform[0].CopyB[0].Box17_ReadOrder[0].f2_24[0]",
	"topmostSubform[0].Copy2[0].Box17_ReadOrder[0].f2_24[0]"
]
},
{
"valueProperty": "state2EIN",
"fieldNames": [
	"topmostSubform[0].CopyA[0].Box17_ReadOrder[0].f1_25[0]",
	"topmostSubform[0].Copy1[0].Box17_ReadOrder[0].f2_25[0]",
	"topmostSubform[0].CopyB[0].Box17_ReadOrder[0].f2_25[0]",
	"topmostSubform[0].Copy2[0].Box17_ReadOrder[0].f2_25[0]"
]
},
{
"valueProperty": "state1Income",
"fieldNames": [
	"topmostSubform[0].CopyA[0].f1_26[0]",
	"topmostSubform[0].Copy1[0].f2_26[0]",
	"topmostSubform[0].CopyB[0].f2_26[0]",
	"topmostSubform[0].Copy2[0].f2_26[0]"
]
},
{
"valueProperty": "state2Income",
"fieldNames": [
	"topmostSubform[0].CopyA[0].f1_27[0]",
	"topmostSubform[0].Copy1[0].f2_27[0]",
	"topmostSubform[0].CopyB[0].f2_27[0]",
	"topmostSubform[0].Copy2[0].f2_27[0]"
]
},
]}
