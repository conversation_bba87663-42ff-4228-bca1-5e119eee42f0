{"mappings": [{"valueProperty": "valueA", "fieldNames": ["topmostSubform[0].Page1[0].f1_01_0_[0]"]}, {"valueProperty": "valueB", "fieldNames": ["topmostSubform[0].Page1[0].f1_02_0_[0]"]}, {"valueProperty": "valueC", "fieldNames": ["topmostSubform[0].Page1[0].f1_03_0_[0]"]}, {"valueProperty": "valueD", "fieldNames": ["topmostSubform[0].Page1[0].f1_04_0_[0]"]}, {"valueProperty": "valueE", "fieldNames": ["topmostSubform[0].Page1[0].f1_05_0_[0]"]}, {"valueProperty": "valueF", "fieldNames": ["topmostSubform[0].Page1[0].f1_06_0_[0]"]}, {"valueProperty": "valueG", "fieldNames": ["topmostSubform[0].Page1[0].f1_07_0_[0]"]}, {"valueProperty": "valueH", "fieldNames": ["topmostSubform[0].Page1[0].f1_08_0_[0]"]}, {"valueProperty": "firstNameMiddleInitial", "fieldNames": ["topmostSubform[0].Page1[0].Line1[0].f1_09_0_[0]"]}, {"valueProperty": "lastName", "fieldNames": ["topmostSubform[0].Page1[0].Line1[0].f1_10_0_[0]"]}, {"valueProperty": "homeAddressLine1", "fieldNames": ["topmostSubform[0].Page1[0].Line1[0].f1_11_0_[0]"]}, {"valueProperty": "homeAddressCityStateZip", "fieldNames": ["topmostSubform[0].Page1[0].Line1[0].f1_12_0_[0]"]}, {"valueProperty": "ssn", "fieldNames": ["topmostSubform[0].Page1[0].f1_13_0_[0]"]}, {"valueProperty": "single", "fieldNames": ["topmostSubform[0].Page1[0].c1_01[0]"]}, {"valueProperty": "married", "fieldNames": ["topmostSubform[0].Page1[0].c1_01[1]"]}, {"valueProperty": "<PERSON><PERSON><PERSON>", "fieldNames": ["topmostSubform[0].Page1[0].c1_01[2]"]}, {"valueProperty": "<PERSON><PERSON><PERSON><PERSON>", "fieldNames": ["topmostSubform[0].Page1[0].c1_02[0]"]}, {"valueProperty": "totalAllowances", "fieldNames": ["topmostSubform[0].Page1[0].f1_14_0_[0]"]}, {"valueProperty": "additionalAmount", "fieldNames": ["topmostSubform[0].Page1[0].f1_15_0_[0]"]}, {"valueProperty": "exempt", "fieldNames": ["topmostSubform[0].Page1[0].f1_16_0_[0]"]}, {"valueProperty": "employerNameAndAddress", "fieldNames": ["topmostSubform[0].Page1[0].f1_17_0_[0]"]}, {"valueProperty": "employerOfficeCode", "fieldNames": ["topmostSubform[0].Page1[0].f1_18_0_[0]"]}, {"valueProperty": "empoyerEIN", "fieldNames": ["topmostSubform[0].Page1[0].f1_19_0_[0]"]}]}