<?xml version="1.0" encoding="UTF-8"?>


<!-- <configuration> <include resource="org/springframework/boot/logging/logback/defaults.xml" 
	/> <include resource="org/springframework/boot/logging/logback/file-appender.xml" 
	/> <root level="INFO"> <appender-ref ref="FILE" /> </root> </configuration> -->


<configuration scan="true" scanPeriod="15 seconds">

	<springProperty scope="context" name="hostname" source="HOSTNAME" defaultValue="localhost"/>

	<!-- Configure the Sentry appender, overriding the logging threshold to the WARN level -->
    <appender name="Sentry" class="io.sentry.logback.SentryAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <!-- Optionally add an encoder -->
        <encoder>
			<pattern>%d [%thread] %-5level %logger{36} - is_logged_in=%X{is_logged_in:-} user_id=%X{user_id:-} domain_id=%X{domain_id:-} domain_type=%X{domain_type:-} parent_domain_id=%X{parent_domain_id:-} ssa_tid=%X{ssa_tid:-} client_tid=%X{client_tid:-} _ga=%X{_ga:-} _gid=%X{_gid:-} gai=%X{gai:-} gav=%X{gav:-} %msg%n</pattern>
        </encoder>
    </appender>
    
    
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d [%thread] %-5level %logger{36} - is_logged_in=%X{is_logged_in:-} user_id=%X{user_id:-} domain_id=%X{domain_id:-} domain_type=%X{domain_type:-} parent_domain_id=%X{parent_domain_id:-} ssa_tid=%X{ssa_tid:-} client_tid=%X{client_tid:-} _ga=%X{_ga:-} _gid=%X{_gid:-} gai=%X{gai:-} gav=%X{gav:-} %msg%n</pattern>
		</encoder>
	</appender>
	
	<appender name="FILEOUT" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_FILE}-${hostname}.log</file>
		<!-- encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder 
			by default -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_FILE}-${hostname}-%d{yyyy-MM-dd}.log</fileNamePattern>
		</rollingPolicy>
		<encoder>
			<pattern>%d [%thread] %-5level %logger{36} - is_logged_in=%X{is_logged_in:-} user_id=%X{user_id:-} domain_id=%X{domain_id:-} domain_type=%X{domain_type:-} parent_domain_id=%X{parent_domain_id:-} ssa_tid=%X{ssa_tid:-} client_tid=%X{client_tid:-} _ga=%X{_ga:-} _gid=%X{_gid:-} gai=%X{gai:-} gav=%X{gav:-} %msg%n</pattern>
		</encoder>
	</appender>
	
	
	<springProfile name="dev">
		<root level="INFO">
			<appender-ref ref="STDOUT" />
			<appender-ref ref="Sentry" />
		</root>

		<logger name="com.aphe" level="DEBUG" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>
	</springProfile>

	<springProfile name="qa,e2e,prod,sandbox">
		<include resource="org/springframework/boot/logging/logback/defaults.xml" />
		<include resource="org/springframework/boot/logging/logback/file-appender.xml" />
		
		<root level="INFO">
			<appender-ref ref="FILEOUT" />
			<appender-ref ref="Sentry" />
		</root>
		<logger name="com.aphe" level="DEBUG" additivity="false">
			<appender-ref ref="FILEOUT" />
			<appender-ref ref="Sentry" />
		</logger>
	</springProfile>
</configuration>
