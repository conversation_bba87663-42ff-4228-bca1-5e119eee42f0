query {
  availableCredits
  billingTransactions{
    id
    domainId
    transactionTime
    transactionType
    transactionAmount
    description
    runningTotal
  }
}



mutation chargeWithNonce($chargeWithNonceInput: ChargeWithNonceInput){
   chargeWithNonce(input: $chargeWithNonceInput)
}

{
  "chargeWithNonceInput": {
    "nonce": "cnon:CBASEKFyRM2qQKh5lItcgkKBamY",
    "token": "verf:aacdd10b-89f2-494b-9e0a-01cff4e86fe7",
    "amount": 1000
  }
}

query getInvoice($product: String!, $quantity: Long){
  invoice(product:$product, quantity:$quantity){
    product
    previouslyFiled
    totalAmount
    lineItems {
      product
      description
      quantity
      itemPrice
      lineAmount
    }
  }
}

{
  "product": "EFILE_PRINT",
  "quantity":9967865
}