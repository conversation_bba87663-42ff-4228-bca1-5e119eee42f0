query($businessId: ID!, $page: Int!, $pageSize: Int!) {
    business(id: $businessId) {
        id
        name
        accounts(page: $page, pageSize: $pageSize) {
            edges {
                node {
                    id
                    classicId
                    name
                    description
                    displayId
                    type {
                        name
                        value
                        normalBalanceType
                    }
                    isArchived
                    sequence
                    balanceInBusinessCurrency
                }
            }
        }
    }
}