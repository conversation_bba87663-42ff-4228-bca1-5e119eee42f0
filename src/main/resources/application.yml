# App specific properties.
app: domain
spring:
    main:
        allow-circular-references: true
    flyway.enabled: false
    jpa:
        hibernate:
            cache:
                use_second_level_cache: false
                use_query_cache: false
                region.factory_class: org.hibernate.cache.ehcache.EhCacheRegionFactory
        properties:
            jakarta.persistence.sharedCache.mode: ALL
    mvc:
        view:
            prefix: /WEB-INF/jsp/
            suffix: .jsp        
    jmx:
        default-domain: domain
    
    mail:
        default-encoding: UTF-8
        host: smtp.gmail.com
        username: <EMAIL>
        password: ENC(Amj0ESmfeABTvI4wG7ZnYGVVWH+DvsgTteObcbrsEARi5khPVeQEFWwoS1dlCO1oKZCb+f5efib/0Grv/+bM6Q==)

        postmark:
            username: E<PERSON>(o460KLznaXKGkwKqyX/+JbUaY+UvvCMYHIq8kxJcoBN0l9KaHJ4P66i+8E6oWJQfI9ZURQgOc+LdGzmYBMAkYv1vMyA5qw5q3XFJhbERdT8=)
            streamName: smartfiletxnemails

        port: 587
        properties:
            mail:
                smtp:
                    auth: true
                    starttls:
                        enable: true
        protocol: smtp
        test-connection: false
    servlet:
        multipart:
            max-file-size: 5MB
            max-request-size: 5MB
    data:
        redis:
            repositories:
                enabled: true

aphe:
    keyStorePass: ENC(JRT0svZBJP3UnECAVI/yumoX25siy03//ULjM4fGZjoi6RvT+thC2tjnD3ExjB8g)
    keyStorePath: /keystore.p12
    fileUploadDir: /tmp/${appName}
    hashSalt: ENC(CowXdAro+HoNdVUcUjpGP/sRu2L0kTs1fj8oGGImDojLsllC94IM/oSntnvuVLtbI6pyvZpIWMTGBU2qx7SanUPv5dvCRsa7uZY8HzwF47I=)
    product:
        appId: domain
        appName: 1099SmartFile
        url: https://app.1099smartfile.com
        filingsPath: /file1099/filings
        w9RequestPath: /w9
    sentry:
        dsn: https://<EMAIL>/2558504
        stacktrace.app.packages: com.aphe
    auth:
        tokenValidationPath: /rs/api/accounts/tokens/
        tokenGenerationPath: /rs/api/sessions/tokens/
        domainUpdatePath: /rs/api/domains/
        oAuthAccessTokenPath: /rs/api/domains/oauth/
        domainUsersPath: /rs/api/domains/users/
        domainAccountantsPath: /rs/api/domains/accountants/
        domainClientsPath: /rs/api/clients/
        loginPath: /access/login
        logoutPath: /access/logout
    sendInBlue:
        submittedFilingsTemplateId: 3
        acceptedFilingsTemplateId: 4
        rejectedFilingsTemplateId: 5
        your1099FormTemplateId: 6
    billing:
        paypal:
            apiKey: AR823haD_2E1-uuLA_fK-3alYbMzRsWb7XpbOfzp8W2AsMnMoP3yaWKKK3BUuI92EJPk8z8AnXSHOiKB
            secret: EJTZdGcpqb8EfUOs5C0I4R83lGsX72QzR9OvPn0NkIYTooq4ZPXMP1a6ShTxpSwlm8HL_PByHeaFM-b1
            basePath: https://api-m.sandbox.paypal.com
            username: <EMAIL>
            password: 9XYx0]_@
    support:
        email: <EMAIL>
        bccEmail: <EMAIL>
        name: 1099SmartFile Team
        address:
            line1: 412 Arlewood Ct
            city: San Ramon
            state: CA
            zip: 94582
        phone: (*************
        hours: 8 AM PST to 8 PM PST (M-F)
    config:
        enableTINMatch: true
        tasks:
            pause: false
    contractor:
        blockFilingSubmissionOnTINCheck: true
        submitFilingsTask:
            initialDelay: 300000
            fixedDelay: 60000
        updateFilingStatusTask:
            initialDelay: 360000
            fixedDelay: 30000
        submitTINMatchRequestsTask:
            initialDelay: 300000
            fixedDelay: 60000
        updateTINMatchRequestStatusTask:
            initialDelay: 300000
            fixedDelay: 60000
        printTask:
            initialDelay: 420000
            fixedDelay: 600000
        emailTask:
            initialDelay: 480000
            fixedDelay: 60000
        emailPrintNotificationTask:
            initialDelay: 540000
            fixedDelay: 14400000
    efs:
        responseDir: "responses"
        queueFilingsTask:
            initialDelay: 600000
            fixedDelay: 1800000
        generateTransmissionsTask:
            initialDelay: 144000000000
            fixedDelay: 144000000000
        irs:
            eServicesURL: https://api.alt.www4.irs.gov
        tin:
            pauseTINCheck: false
            queueRequestsTask:
                initialDelay: 60000
                fixedDelay: 60000
    trclients:
        companyName: SSA Technologies LLC
        companyEmail: <EMAIL>
        companyPhone: 5106831444
        companyContact: Shashikanth Nallu
        companyAddress:
            line1: 412 Arlewood Ct
            city: San Ramon
            state: CA
            state1: CA - CALIFORNIA
            zip: 94582-2887
        graphqlEndpoint: /graphql
        tempDownloadDir: /var/efs/tmp
        fire:
            url: https://test.fire.irs.gov
            tcc: ENC(M7FPdhixvAB/WkZF48c2cKgYJqkbEkzYs5Hjuo79ueDpym5lbbFBjzg25eXBpFae)
            ein: ENC(L71Vh0iybf/RRfEQGFrGSYQfZJAydetsbeOeLqbw4SpiZuQ9Bxg3JqiX7+1DXmC9)
            companyName: SSA Technologies LLC
            username: ssatechca
            password: ENC(Ogu8gQazn90m3Fr3A9KXNQ6neUj46X3tPiThR4MYSPQgW+AOMT4thzjSxmu2w5wJ)
            pin: ENC(ESKX3vnJWn0aRBauYQZgXEegge0x1IluXbt0VKhF91TtO+FvaU0jXBWe7kF5yXYe)
        iris:
            url: https://api.alt.www4.irs.gov
        nc:
            url: https://www.ncdor.gov/taxes-forms/withholding-tax/enc3-web-file-upload
        nd:
            wsURL: https://test.apps.nd.gov/tax/ElectronicUpload
            wsdlPackage: com.aphe.trclients.nd.wsdl
            username: 1099SmartFileEU
            password: ENC(MWVWQTyXm7fOsjCAY7UfpLkS9AJfMqTL32vIsbP4/bB8q2gMcxZ+lkSSfcBtmvcM)
        ma:
            url: secft.dor.state.ma.us
            port: 444
            username: ssatechca
            password: ENC(POyTaxkQ4fUmQhf8iQx8i3NgENUAQj1syUX0CN4/RpdajdCJPHef1IPwG2dGFBVD)
        vt:
            url: https://www.myvtax.vermont.gov
            username: ssatechca
            password: ENC(Sm7qYW9AHOVRreNYVxjf35Ky8A3K88d/kRMH9tX1m0E1P+FTvQn3lq+anQbnYivI)
        nj:
            url: mft-sonj.axwaycloud.com
            port: 22
            username: A1384
            password: ENC(uG9pQ/OulsXXuLo4j6dLJkCMP5I5ELG85X2usqX9jeQM4WP4jkF0T/oRTiReP8la)
        pa:
            url: https://mypath.pa.gov
            username: ssatechca
            password: ENCV8eWqR0V53qpPKXfio9u/gABdLpff5RhN4lUCvUXoocZG/uSHkDJoJ5KXkaBVgpw)
        de:
            url: https://dorweb.revenue.delaware.gov/w2send/
        dc:
            url: https://mytax.dc.gov
            username: ssatechca
            password: ENC(qBuz8cM001EePGTAWKjlbG5Gr7cvud2Z230oVnUJ7aKY3Xzh45YoGe86eeSAIGB1)
        ok:
            url: https://oktap.tax.ok.gov/OkTAP/Web
            username: ssatechca
            password: ENC(KuofJWet2H1oJxtsoa4NlJ6TBWwI39yGBwe2ITE9MRNVJujsCzdIkklvHoUG4BzY)
        mt:
            url: https://tap.dor.mt.gov/
            username: ssatechca
            password: ENC(5y2D0weglxlJG5NCsul4kthtb4vyK920a+95+pdLoLtmez8o8M5l1+1d5kjvWOqg)
        or:
            url: https://revenueonline.dor.oregon.gov/tap/_/
        me:
#            url: https://portal.maine.gov/meetrs
            url: https://revenue.maine.gov
            username: ssatechca
            password: ENC(FKhjDHkNcltp7FooO/n4wgcpOkcRwTDP7G5NpU1bjCeH1cgmTluPS4ZM5aZKmprH)
            totpSecret: ENC(ldGKYo+VsStccKazH9pwK0NooEnBE5XNNam53y2cySjMQEY55ZsuPXCArezVkv7yI9o/3m0bS7fH+QJz6jT61A==)


    email:
        draftFilings:
            templateName: contractor/draftFilings
        submittedFilings:
            templateName: contractor/submittedFilings
        systemRejectedFilings:
            templateName: contractor/systemRejectedFilings
        sentToAgencyFilings:
            templateName: contractor/sentToAgencyFilings
        acceptedFilings:
            templateName: contractor/acceptedFilings
        rejectedFilings:
            templateName: contractor/rejectedFilings
        your1099Form:
            templateName: contractor/your1099Form
        emailBounced:
            templateName: contractor/emailBounced
        printReturned:
            templateName: contractor/printReturned
        submittedTINMatches:
            templateName: contractor/tinMatchesSubmitted
        systemRejectedTINMatches:
            templateName: contractor/tinMatchesSystemRejected
        sentToAgencyTINMatches:
            templateName: contractor/tinMatchesSentToAgency
        verifiedTINMatches:
            templateName: contractor/tinMatchesVerified
        failedTINMatches:
            templateName: contractor/tinMatchesFailed
        review:
            templateName: contractor/review
        w9Request:
            templateName: contractor/w9request
        w9Applied:
            templateName: contractor/w9applied

        emailQueueTask:
            initialDelay: 30000
            fixedDelay: 30000
    freshbooks:
        clientId: 99b3a346c6e34eaed1a75481b5869647f54ccfc665dbb3a5ed47a1a3e7712925
        baseURL: https://api.freshbooks.com/
    zoho:
        clientId: 1000.GXD24UC77HDB4DZMQ9KSGOETIY0VWO
        baseURL: https://books.zoho.com/
    wave:
        clientId: 8aXrMKfwOLX.dOAMfgvfIW78zR2zSuQ80XFgFC2X
        baseURL: https://gql.waveapps.com/graphql/public
    bqecore:
        clientId: BXoSNaatS_0Q1HxMoq3X87n1FCvxf9cw.apps.bqe.com
    bdc:
        devKey: ENC(AogEyv5AqKvhiiz/egXjQ1I4Kfr4OBZLM/ktFNAsAiPr5LxdFrU4EN/rQgugJ41lHKCCtq5ZvCi1xSaSzl7QuA==)
        baseURL: https://api.bill.com/api/v2/

management:
    endpoints:
        web:
            base-path: /badbadbadfeed
            exposure:
                include: flyway,loggers,health,info,env,scheduledtasks,threaddump
    endpoint:
        health:
            show-details: always
            probes:
              enabled: true
        loggers:
            enabled: true
    info.git.mode: full
    health:
        livenessstate:
            enabled: true
        readinessstate:
            enabled: true

springdoc:
    packagesToScan: com.aphe.contractor
    swagger-ui:
        path: /apidoc.html    
    
zuul:
  routes:
    restServices:
      path: /rs/api/**
      url: forward:/rs/api
  ratelimit:
    enabled: true
    repository: JPA
    policy-list:
      restServices:
        - limit: 1200
          refresh-interval: 60
          type:
            - user
  strip-prefix: true

dgs:
    graphql:
        introspection:
            enabled: false


org:
    jobrunr:
      background-job-server:
          enabled: true
          worker-count: 20
          poll-interval-in-seconds: 5
      jobs:
          retry-back-off-time-seed: 3
          default-retry-back-off-time-seed: 3
      dashboard:
          enabled: true
          port: 8000
#      metrics:
#          enabled: true
jasypt:
    encryptor:
        password: ${jasypt-password}