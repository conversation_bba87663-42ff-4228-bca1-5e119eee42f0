<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
	<body>
		<div th:fragment="filingstable">
			<table>
				<tr>
					<th>Payee Name</th>
					<th>Form Type</th>
					<th>Payee Address</th>
					<th>Payee Email</th>
					<th>Email Status</th>
					<th>Notes</th>
				</tr>
				<tr th:each="filing : ${filings}">
					<td th:text="${filing.payeeName}">Philips Masonary</td>
					<td th:text="${filing.formName}">1099-NEC</td>
					<td>
						<div th:text="${filing.addressLine1}">412 Arlewood Ct</div>
						<div th:text="${filing.addressLine2}">San Ramon CA 98434</div>
					</td>
					<td th:text="${filing.emailAddress}"><EMAIL></td>
					<td>
						<div th:text="${filing.emailStatus}">Received</div>
						<div th:if="${!#strings.isEmpty(filing.get('emailStatusDate'))}" th:text="${#dates.format(filing.emailStatusDate, 'MM-dd-yyyy HH:mm z')}">02/01/2001 8:55 PM PST</div>
					</td>
					<td th:text="${filing.emailStatusDesc}">Mail box is full</td>
				</tr>
			</table>
		</div>
	</body>
</html>


