<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

	<head>
		<base href="../" th:if="false"/>
		<script src="https://code.jquery.com/jquery-2.1.4.min.js" th:if="true"></script>
		<script src="http://blackpeppersoftware.github.io/thymeleaf-fragment.js/thymeleaf-fragment.js"
				defer="defer" th:if="true"></script>

		<meta charset="UTF-8" />
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
		<link href='http://fonts.googleapis.com/css?family=Roboto' rel='stylesheet' type='text/css'/>
		<link href='fragments/email.css' rel='stylesheet' type='text/css'/>
	</head>


  <body>

  	<div th:replace="~{fragments/title}"></div>
  	
  	<div class="pageBody pageBodyLeft">
		<br>
		<span th:utext="${STATUS_MESSAGE}">There are prints in the errored state</span><br>
  	</div>
  	<br/>

  	<div th:replace="~{fragments/footer}"></div>

  </body>
</html>