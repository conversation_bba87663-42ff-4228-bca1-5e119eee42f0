package org.hibernate.validator.constraints;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class CheckEntityValidator implements ConstraintValidator<CheckEntity, Object> {

	@Autowired
	private EntityManagerFactory entityManagerFactory;

	private Class<?> entityClass;
	private String entityIdPropertyName;
	private String propertyName;
	private String message;

	@Override
	public void initialize(CheckEntity constraintAnnotation) {
		this.entityClass = constraintAnnotation.entityClass();
		this.entityIdPropertyName = constraintAnnotation.entityIdPropertyName();
		this.propertyName = constraintAnnotation.propertyName();
		this.message = constraintAnnotation.message();

	}

	@Override
	public boolean isValid(Object target, ConstraintValidatorContext context) {
		EntityManager entityManager = entityManagerFactory.createEntityManager();
		CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();

		CriteriaQuery<Object> criteriaQuery = criteriaBuilder.createQuery();

		Root<?> root = criteriaQuery.from(entityClass);

		Class<?> targetClass = target.getClass();

		List<Predicate> predicates = new ArrayList<Predicate>(1);

		try {
			PropertyDescriptor desc = new PropertyDescriptor(propertyName, targetClass);
			Method readMethod = desc.getReadMethod();
			Object propertyValue = readMethod.invoke(target);
			Predicate predicate = criteriaBuilder.equal(root.get(entityIdPropertyName), propertyValue);
			predicates.add(predicate);
		} catch (Exception e) {
			e.printStackTrace();
		}
		criteriaQuery.select(root);
		criteriaQuery.where(predicates.toArray(new Predicate[predicates.size()]));
		TypedQuery<Object> typedQuery = entityManager.createQuery(criteriaQuery);

		List<Object> resultSet = typedQuery.getResultList();

		boolean entityExists = resultSet.size() > 0;
		if (!entityExists) {
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate(message).addPropertyNode(propertyName).addConstraintViolation();
		}
		return entityExists;
	}

}