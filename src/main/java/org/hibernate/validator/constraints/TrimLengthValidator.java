package org.hibernate.validator.constraints;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class TrimLengthValidator implements ConstraintValidator<TrimLength, CharSequence> {

//	private static final Log log = LoggerFactory.make();

	private int min;
	private int max;

	public void initialize(TrimLength parameters) {
		min = parameters.min();
		max = parameters.max();
		validateParameters();
	}

	public boolean isValid(CharSequence value, ConstraintValidatorContext constraintValidatorContext) {
		if (value == null) {
			return true;
		}else{
			value = value.toString().trim();
		}
		int length = value.length();
		return length >= min && length <= max;
	}

	private void validateParameters() {
		if (min < 0) {
//			throw log.getMinCannotBeNegativeException();
		}
		if (max < 0) {
//			throw log.getMaxCannotBeNegativeException();
		}
		if (max < min) {
//			throw log.getLengthCannotBeNegativeException();
		}
	}

}
