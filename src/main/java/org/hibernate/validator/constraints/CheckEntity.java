package org.hibernate.validator.constraints;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import com.aphe.common.model.BaseEntity;

@Constraint(validatedBy = { CheckEntityValidator.class })
@Target({ ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface CheckEntity {

	Class<? extends BaseEntity> entityClass();
	
	String entityIdPropertyName() default "id";
	String propertyName();

	String message() default "{CheckEntity.message}";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};

	@Target({ ElementType.TYPE })
	@Retention(RetentionPolicy.RUNTIME)
	@Documented
	@interface List {
		CheckEntity[] value();
	}
}