package com.aphe.util.pdfgen.f941;

import java.math.BigDecimal;
import java.util.Date;



public class F941Data {
	
	String ein;
	String filingName;
	String tradeName;
	Address address;
	FilingQuarter filingQuarter;
	int numberOfEmployees;
	BigDecimal wages;
	BigDecimal incomeTaxWithheld;
	boolean noSSMCTaxes;
	BigDecimal ssWages;
	BigDecimal ssWagesTaxes;
	BigDecimal ssTips;
	BigDecimal ssTipsTaxes;
	BigDecimal mcWagesTips;
	BigDecimal mcWagesTipsTaxes;
	
	BigDecimal ssmcTotalTaxes;
	BigDecimal taxDueOnUnreportedTips; //Seciton 3121q
	BigDecimal totalTaxes; //calculated --- totalTaxes + ssmcTaxes + taxes on unreported tips
	BigDecimal fractionAdjustments;
	BigDecimal sickPayAdjustments;
	BigDecimal tipsGTLAdjusments;
	BigDecimal totalTaxesAfterAdjustments; //calculated --- sum of totalTaxes + 3 adjustments.
	BigDecimal totalDeposits; //include overpayments applied from previous quarters
	BigDecimal cobraPayments; 
	BigDecimal cobraPaymentIndividuals;
	BigDecimal totalDepositsAfterCobra;
	BigDecimal balanceDue; //--- Calculated
	BigDecimal overPayment; //--- Calcualed 
	
	OverpaymentTreatment overPaymentTreatment;
	
	DepositScheduleStatus depositScheduleStatus;
	
	BigDecimal month1TaxLiability;
	BigDecimal month2TaxLiability;
	BigDecimal month3TaxLiability; 
	BigDecimal totalQuarterLiability; //--- Calcualed 
	
	boolean stoppedWages;
	Date wageStopDate;
	boolean isSeasonalEmployer;
	
	CanTalkToDesignee canTalkToDesignee;
	String designeeName;
	String designeePhone;
	String designeePin; //5 digits.
	
	String filerName;
	String filerTitle;
	String filterPhone;
	
	
	
	
	
	
}
