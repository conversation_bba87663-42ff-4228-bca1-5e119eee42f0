package com.aphe.util.pdfgen;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;

public class ItextMerge {
	public static void main(String[] args) {
		List<InputStream> list = new ArrayList<InputStream>();
		try {
			// Source pdfs
			String folder = "2021-01-18 00-24-18-762";
			String rootPath = "/Users/<USER>/Projects/common/domain-service/target/1099Forms/";
			
			
			list.add(new FileInputStream(new File(rootPath + File.separator + folder + File.separator + "SSA Technoloiges Inc 0-1099misc-rev2020-2020-54397.pdf")));
			list.add(new FileInputStream(new File(rootPath + File.separator + folder + File.separator + "SSA Technoloiges Inc 1-1099misc-rev2020-2020-54400.pdf")));
			list.add(new FileInputStream(new File(rootPath + File.separator + folder + File.separator + "SSA Technoloiges Inc 2-1099misc-rev2020-2020-54399.pdf")));
			list.add(new FileInputStream(new File(rootPath + File.separator + folder + File.separator + "SSA Technoloiges Inc 3-1099misc-rev2020-2020-54398.pdf")));
			
			// Resulting pdf
			OutputStream out = new FileOutputStream(new File(rootPath + File.separator + folder + File.separator + "result.pdf"));

			doMerge(list, out);

		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (DocumentException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * Merge multiple pdf into one pdf
	 * 
	 * @param list
	 *            of pdf input stream
	 * @param outputStream
	 *            output file output stream
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static void doMerge(List<InputStream> list, OutputStream outputStream) throws DocumentException, IOException {
		Document document = new Document();
		PdfWriter writer = PdfWriter.getInstance(document, outputStream);
		document.open();
		PdfContentByte cb = writer.getDirectContent();

		for (InputStream in : list) {
			PdfReader reader = new PdfReader(in);
			for (int i = 1; i <= reader.getNumberOfPages(); i++) {
				document.newPage();
				//import the page from source pdf
				PdfImportedPage page = writer.getImportedPage(reader, i);
				//add the page to the destination pdf
				cb.addTemplate(page, 0, 0);
			}
		}

		outputStream.flush();
		document.close();
		outputStream.close();
	}
}