package com.aphe.util.pdfgen;

import com.itextpdf.text.*;
import com.itextpdf.text.Font.FontFamily;
import com.itextpdf.text.pdf.*;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/*  
 * Rest API to list all supported forms.
 * 
 * Rest API to get the input format to fill a given form
 * Eg: For W4:
 * { 
 * allowances : {{ a : "1"}, {b : "0"}},
 * firstName : "Keerthi",
 * middleInitial : "K",
 * lastName : "Arutla",
 * SSN : " ",
 * Married : "Single, Married, Married but with holding at higher rate",
 * Name Different from SSN Card : "Yes/No",
 * totalAllowances " "",
 * additionalAmount : " ",
 * isExempt : "Yes/No",
 * }
 * Rest API to get the filled form for given data.
 * Rest API to sign a filled form...Signature Name/Signature free form & Signature Date.
 * 
 * How do we add new forms.
 * 
 * Specify the PDF filename and its revision number.
 * 
 * Express the model in JSON schema.
 * Edit the PDF and give human readable names
 * Enter the mappings in the mappings
 * 
 * Option 1 : Can the value extraction happen with jsut regular expressions or do we need dedicated object model internally so that we can write functions to extra this data.
 * Option 2 : Expose the intricate details of the various forms fields so that users can send right values. Very dumb pdf filling service.
 * 
 * 
 * Open Questions:
 * How to Model some of the check boxes as radio buttons. For Eg: filing quarter is an enum with values (Q1/Q2/Q3/Q4)
 * how to treat calculations part. Should we ask those from the user?
 * If we calculate, we are making this tool form aware.
 * If we model it that way, even groupings of checkboxes will be same..
 * If we make this tool form aware, we might need to enforce strict validation of the input.
 * 
 * Should model file contains JSON schema of the model and mappings or will they be stored in different files?
 * 
 * TODO: 1 - Add infrastructure to add metadata about forms and their revisions.
 * TODO: 2 - Add Rest or other API to query for forms and corresponding metadata.
 * 

 * Forms table : FormId, FormName, Revision, effectiveStartEnd, effectiveEndDate, description, shortDescription, detailedDesc, officialFormLocation. (
 * Can this be in stored in files too, no need for a relational database, may be an excel file, read as csv)
 * 
 * FormsModelMappings:
 * FormId, modelFile.
 * 
 * <AUTHOR>
 *
 */

public class Generator {

	public static void main(String[] args) {
		try {
			Generator generator = new Generator();
			String formName = "1096-2024";
			try {
//				 generator.analyzePDF(formName);
//				 generator.generateMappingsJson(formName);
				generator.testPDF(formName);
			} catch (Exception e) {
				e.printStackTrace();
			}
			// testGetValue();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static final String developFolderAbs = "../utils/pdfgenerator/src/main/resources/develop";
	private static final String developFolderRel = "/develop";
	private static final String supportedFormFolderRel = "/supported";

	public void generateMappingsJson(String formName) throws DocumentException, IOException, JSONException {
		String developFolderPathAbs = developFolderAbs + File.separator + formName + File.separator;
		String developFolderPathRel = developFolderRel + File.separator + formName + File.separator;

		PrintWriter mappingsWriter = new PrintWriter(new FileOutputStream(developFolderPathAbs + "mappings-generated.json"));
		mappingsWriter.println("{");
		mappingsWriter.println("\"mappings\":[");

		String mappingTextFile = developFolderPathRel + "mappings.txt";

		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(this.getClass().getResourceAsStream(mappingTextFile)));

			String line = br.readLine();
			while (line != null) {
				while (line != null && line.length() > 0) {
					String valueProperty = line;
					List<String> fieldNames = new ArrayList<String>();
					String regEx = null;
					String regExGroup = null;
					String comment = null;
					line = br.readLine();

					while (line != null && line.length() > 0) {
						if (line.startsWith("comment=")) {
							comment = line.substring("comment=".length());
						} else if (line.startsWith("regEx=")) {
							regEx = line.substring("regEx=".length());
						} else if (line.startsWith("regExGroup=")) {
							regExGroup = line.substring("regExGroup=".length());
						} else {
							fieldNames.add(line);
						}
						line = br.readLine();
					}

					// Group is done.. write this mapping.
					mappingsWriter.println("{");
					mappingsWriter.println("\"valueProperty\": \"" + valueProperty + "\",");
					if (regEx != null) {
						mappingsWriter.println("\"regEx\": \"" + regEx + "\",");
					}
					if (comment != null) {
						mappingsWriter.println("\"comment\": \"" + comment + "\",");
					}
					if (regExGroup != null) {
						mappingsWriter.println("\"regExGroup\": \"" + regExGroup + "\",");
					}
					if (fieldNames.size() > 0) {
						mappingsWriter.println("\"fieldNames\": [");

						for (int i = 0; i < fieldNames.size(); i++) {
							String fieldName = fieldNames.get(i);
							fieldName = fieldName.substring(0, fieldName.indexOf('\t'));
							mappingsWriter.print("\t\"" + fieldName + "\"");
							if (i < fieldNames.size() - 1) {
								mappingsWriter.println(",");
							} else {
								mappingsWriter.println();
							}
						}
						mappingsWriter.println("]");
					}
					mappingsWriter.println("},");
				}
				line = br.readLine();
			}

			mappingsWriter.println("]}");
			mappingsWriter.flush();
			mappingsWriter.close();

			br.close();

		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public void analyzePDF(String formName) throws DocumentException, IOException, JSONException {
		
//		String password = "ka6753";
		String password = null;

		String developFolderPathAbs = developFolderAbs + File.separator + formName + File.separator;
		String developFolderPathRel = developFolderRel + File.separator + formName + File.separator;

		PrintWriter debugWriter = new PrintWriter(new FileOutputStream(developFolderPathAbs + "form-debug.txt"));
		

		PdfReader reader = new PdfReader(developFolderPathAbs + "form.pdf");

		debugWriter.print("Number of pages: ");
		debugWriter.println(reader.getNumberOfPages());
		Rectangle mediabox = reader.getPageSize(1);
		debugWriter.print("Size of page 1: [");
		debugWriter.print(mediabox.getLeft());
		debugWriter.print(',');
		debugWriter.print(mediabox.getBottom());
		debugWriter.print(',');
		debugWriter.print(mediabox.getRight());
		debugWriter.print(',');
		debugWriter.print(mediabox.getTop());
		debugWriter.println("]");
		debugWriter.print("Rotation of page 1: ");
		debugWriter.println(reader.getPageRotation(1));
		debugWriter.print("Page size with rotation of page 1: ");
		debugWriter.println(reader.getPageSizeWithRotation(1));
		debugWriter.print("File length: ");
		debugWriter.println(reader.getFileLength());
		debugWriter.print("Is rebuilt? ");
		debugWriter.println(reader.isRebuilt());
		debugWriter.print("Is encrypted? ");
		debugWriter.println(reader.isEncrypted());

		AcroFields form = reader.getAcroFields();
		XfaForm xfaForm = form.getXfa();
		debugWriter.print("Form Type: ");
		debugWriter.println(xfaForm.isXfaPresent() ? "XFA" : "Arco");

		debugWriter.println();
		debugWriter.println();
		debugWriter.println("Form Fields:");
		debugWriter.println();

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		PdfStamper stamper = new PdfStamper(reader, outputStream);
		if(password != null)
			stamper.setEncryption(password.getBytes(), "1099SF".getBytes(),PdfWriter.ALLOW_COPY, PdfWriter.ENCRYPTION_AES_256);
		AcroFields outFormFields = stamper.getAcroFields();

		int value = 1;
		Set<String> fields = outFormFields.getFields().keySet();
		for (String fieldName : fields) {

			String valueWritten = null;

			String fieldType = "";
			List<String> fieldValues = new ArrayList<String>();

			switch (form.getFieldType(fieldName)) {
			case AcroFields.FIELD_TYPE_CHECKBOX:
				fieldType = "Checkbox";
				for (String state : form.getAppearanceStates(fieldName)) {
					outFormFields.setField(fieldName, state);
					valueWritten = state;
					fieldValues.add(state);
				}
				break;
			case AcroFields.FIELD_TYPE_COMBO:
				fieldType = "Combo";
				break;
			case AcroFields.FIELD_TYPE_LIST:
				fieldType = "List";
				break;
			case AcroFields.FIELD_TYPE_NONE:
				fieldType = "None";
				break;
			case AcroFields.FIELD_TYPE_PUSHBUTTON:
				fieldType = "PushButton";
				break;
			case AcroFields.FIELD_TYPE_RADIOBUTTON:
				fieldType = "RadioButton";
				for (String state : form.getAppearanceStates(fieldName)) {
					outFormFields.setField(fieldName, state);
					valueWritten = state;
					fieldValues.add(state);
				}
				break;
			case AcroFields.FIELD_TYPE_SIGNATURE:
				fieldType = "Signature";
				break;
			case AcroFields.FIELD_TYPE_TEXT:
				fieldType = "TextBox";
				
				outFormFields.setFieldProperty(fieldName, "textsize", new Float(7), null);
				final BaseFont font = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.EMBEDDED);
				outFormFields.setFieldProperty(fieldName, "textfont", font, null);
				
				valueWritten = Integer.toString(value);
				outFormFields.setField(fieldName, valueWritten);
				value += 1;
				break;
			default:
				fieldType = "Unknown";
				break;
			}

			debugWriter.println(fieldName + "\t\t\t" + valueWritten + "\t\t\t" + fieldType + "\t\t\t" + fieldValues);

		}
		stamper.setFormFlattening(true);

		debugWriter.flush();
		debugWriter.close();

		stamper.close();

		OutputStream fileOutputStream = new FileOutputStream(developFolderPathAbs + "form-debug.pdf");
		outputStream.writeTo(fileOutputStream);
		outputStream.close();
	}

	public void testPDF(String formName) throws DocumentException, IOException, JSONException {

		String folderPath = developFolderRel + File.separator + formName + File.separator;
		String folderPathAbs = developFolderAbs + File.separator + formName + File.separator;

		PdfReader reader = new PdfReader(folderPath + "form.pdf");

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		PdfStamper stamper = new PdfStamper(reader, outputStream);
		AcroFields outFormFields = stamper.getAcroFields();
		
		// Read from the mappings file.
		String testData = readFile(folderPath + "form-testdata.json");
		JSONObject testDataObj = new JSONObject(testData);

		// Read from the mappings file.
		String mappingsData = readFile(folderPath + "mappings.json");
		JSONObject mappingsObj = new JSONObject(mappingsData);
		JSONArray mappings = mappingsObj.getJSONArray("mappings");

		for (int i = 0; i < mappings.length(); i++) {
			JSONObject mapping = mappings.getJSONObject(i);

			JSONArray fieldNames = mapping.getJSONArray("fieldNames");
			String valueProperty = null;
			if (mapping.has("valueProperty")) {
				valueProperty = mapping.getString("valueProperty");
			}
			JSONArray valueProperties = null;
			if (mapping.has("valueProperties")) {
				valueProperties = mapping.getJSONArray("valueProperties");
			}
			String regEx = null;
			if (mapping.has("regEx")) {
				regEx = mapping.getString("regEx");
			}
			int regExGroup = 1;
			if (mapping.has("regExGroup")) {
				regExGroup = mapping.getInt("regExGroup");
			}
			String value = null;
			if (valueProperties != null) {
				value = getValue(testDataObj, valueProperties, regEx, regExGroup);
			} else if (valueProperty != null) {
				value = getValue(testDataObj, valueProperty, regEx, regExGroup);
			}
			if (value != null) {
				for (int j = 0; j < fieldNames.length(); j++) {
					String fieldName = fieldNames.getString(j);
					
					outFormFields.setFieldProperty(fieldName, "textsize", new Float(7), null);
					final BaseFont font = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.EMBEDDED);
					outFormFields.setFieldProperty(fieldName, "textfont", font, null);

					
					outFormFields.setField(fieldName, value);
				}
			}
		}
		
		Font f = new Font(FontFamily.HELVETICA, 60);
        Phrase p =  new Phrase("TEST FORM", f);
        
		
		String payerAddress = getValue(testDataObj, "payerNameAndAddress", null, 0);
		String payeeName = getValue(testDataObj, "payeeName", null, 0);
		String payeeAddressLine1Line2 = getValue(testDataObj, "payeeAddressLine1Line2", null, 0);
		String payeeAddressCityStateZip = getValue(testDataObj, "payeeAddressCityStateZip", null, 0);
		String payeeAddress = payeeName + "\n" + payeeAddressLine1Line2 + "\n" + payeeAddressCityStateZip;
		
		addAddressPage(reader, stamper, payerAddress, payeeAddress);

        int n = reader.getNumberOfPages();
        PdfGState gs1 = new PdfGState();
        gs1.setFillOpacity(0.5f);
        PdfContentByte over;
        
        for (int i = 1; i <= n; i++) {
            over = stamper.getOverContent(i);
            over.saveState();
            over.setGState(gs1);
            ColumnText.showTextAligned(over, Element.ALIGN_CENTER, p, 297, 450, 45);
            over.restoreState();
        }
        
        stamper.setFormFlattening(true);
		stamper.close();

		OutputStream fileOutputStream = new FileOutputStream(folderPathAbs + "form-testdata.pdf");
		outputStream.writeTo(fileOutputStream);
		outputStream.close();

	}

	public ByteArrayOutputStream getFilledPDF(String formName, HashMap<String, Object> data, String pagesToKeep, String password) throws DocumentException, IOException, JSONException {
		return getFilledPDF(formName, data, pagesToKeep, null, false, password);
	}

	public ByteArrayOutputStream getFilledPDF(String formName, HashMap<String, Object> data, String pagesToKeep, String overlayText, boolean addAddressPage, String password) throws DocumentException, IOException, JSONException {

		String supportedFormFolderPathRel = supportedFormFolderRel + File.separator + formName + File.separator;

		PdfReader reader = new PdfReader(supportedFormFolderPathRel + "form.pdf");

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		PdfStamper stamper = new PdfStamper(reader, outputStream);
		if(pagesToKeep != null) {
			stamper.getReader().selectPages(pagesToKeep);	
		}
		if(password != null)
			stamper.setEncryption(password.getBytes(), password.getBytes(),PdfWriter.ALLOW_COPY, PdfWriter.ENCRYPTION_AES_256);
		
		AcroFields outFormFields = stamper.getAcroFields();

		// Read from the mappings file.
		String mappingsData = readFile(supportedFormFolderPathRel + "mappings.json");
		JSONObject mappingsObj = new JSONObject(mappingsData);
		JSONArray mappings = mappingsObj.getJSONArray("mappings");

		for (int i = 0; i < mappings.length(); i++) {
			JSONObject mapping = mappings.getJSONObject(i);
			JSONArray fieldNames = mapping.getJSONArray("fieldNames");

			String valueProperty = null;
			if (mapping.has("valueProperty")) {
				valueProperty = mapping.getString("valueProperty");
			}
			JSONArray valueProperties = null;
			if (mapping.has("valueProperties")) {
				valueProperties = mapping.getJSONArray("valueProperties");
			}
			String regEx = null;
			if (mapping.has("regEx")) {
				regEx = mapping.getString("regEx");
			}
			int regExGroup = 1;
			if (mapping.has("regExGroup")) {
				regExGroup = mapping.getInt("regExGroup");
			}
			String value = null;
			if (valueProperties != null) {
				value = getValue(data, valueProperties, regEx, regExGroup);
			} else if (valueProperty != null) {
				value = getValue(data, valueProperty, regEx, regExGroup);
			}
			if (value != null) {
				for (int j = 0; j < fieldNames.length(); j++) {
					String fieldName = fieldNames.getString(j);
					
					outFormFields.setFieldProperty(fieldName, "textsize", new Float(7), null);
					final BaseFont font = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.EMBEDDED);
					outFormFields.setFieldProperty(fieldName, "textfont", font, null);
					
					outFormFields.setField(fieldName, value);
				}
			}
		}
		
		if(addAddressPage) {
			
			String payerAddress = getValue(data, "payerNameAndAddress", null, 0);
			String payeeName = getValue(data, "payeeName", null, 0);
			String payeeAddressLine1Line2 = getValue(data, "payeeAddressLine1Line2", null, 0);
			String payeeAddressCityStateZip = getValue(data, "payeeAddressCityStateZip", null, 0);
			String payeeAddress = payeeName + "\n" + payeeAddressLine1Line2 + "\n" + payeeAddressCityStateZip;
			addAddressPage(reader, stamper, payerAddress, payeeAddress);	
		}
		
			
		if(overlayText != null) {
	        Font f = new Font(FontFamily.HELVETICA, 60);
	        Phrase p =  new Phrase(overlayText, f);
	        
	        int n = reader.getNumberOfPages();
	        PdfGState gs1 = new PdfGState();
	        gs1.setFillOpacity(0.5f);
	        PdfContentByte over;
	        
	        for (int i = 1; i <= n; i++) {
	            over = stamper.getOverContent(i);
	            over.saveState();
	            over.setGState(gs1);
	            ColumnText.showTextAligned(over, Element.ALIGN_CENTER, p, 297, 450, 45);
	            over.restoreState();
	        }
		}
		
		stamper.setFormFlattening(true);
		stamper.close();
		return outputStream;
	}
	
	private void addAddressPage(PdfReader reader, PdfStamper stamper, String payerNameAndAddress, String payeeNameAndAddress) throws JSONException, DocumentException {
		stamper.insertPage(1, reader.getPageSize(1));
		Font addressFont = new Font(FontFamily.HELVETICA, 8);
		PdfContentByte content = stamper.getOverContent(1);
		//Address box
		ColumnText ct = new ColumnText( content );
		String block1 =  "important tax document" + "\n" + payerNameAndAddress;
		String address =  block1.toUpperCase() + "\n\n\n\n\n\n\n\n" + payeeNameAndAddress.toUpperCase();
		ct.setSimpleColumn(60,576,270,790);
		
		
		Paragraph para = new Paragraph(10);
		para.setFont(addressFont);
		para.add(address);
		ct.setLeading(10);
		ct.setText(para);
		ct.go();
		
		//Folder lines..
		ColumnText line1a = new ColumnText( content );
		ColumnText line1b = new ColumnText( content );
		ColumnText line2a = new ColumnText( content );
		ColumnText line2b = new ColumnText( content );
		
		line1a.setSimpleColumn(0, 255, 50, 295);
		line1a.setText(new Phrase("_________", addressFont));
		line1a.go();

		line1b.setSimpleColumn(570, 255, 670, 295);
		line1b.setText(new Phrase("_________", addressFont));
		line1b.go();

		
		line2a.setSimpleColumn(0, 528, 50, 585);
		line2a.setText(new Phrase("_________", addressFont));
		line2a.go();

		line2b.setSimpleColumn(570, 528, 670, 585);
		line2b.setText(new Phrase("_________", addressFont));
		line2b.go();
	}

	
	public void mergePDFs(String folderPath, String fileName) throws Exception {
		List<InputStream> list = new ArrayList<InputStream>();
		try {
			File fileFolder = new File(folderPath);
			for (File f : fileFolder.listFiles()) {
				list.add(new FileInputStream(f));
			}
			OutputStream out = new FileOutputStream(new File(fileName));
			doMerge(list, out);
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}
	
	private void doMerge(List<InputStream> list, OutputStream outputStream) throws DocumentException, IOException {
		Document document = new Document();
		PdfWriter writer = PdfWriter.getInstance(document, outputStream);
		document.open();
		PdfContentByte cb = writer.getDirectContent();

		for (InputStream in : list) {
			PdfReader reader = new PdfReader(in);
			for (int i = 1; i <= reader.getNumberOfPages(); i++) {
				document.newPage();
				//import the page from source pdf
				PdfImportedPage page = writer.getImportedPage(reader, i);
				//add the page to the destination pdf
				cb.addTemplate(page, 0, 0);
			}
		}

		outputStream.flush();
		document.close();
		outputStream.close();
	}	
	
	private String getValue(HashMap<String, Object> data, JSONArray valueProperties, String regEx, int regExGroup) throws JSONException {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < valueProperties.length(); i++) {
			String valueProperty = valueProperties.getString(i);
			if (valueProperty.startsWith("&^&")) {
				sb.append(valueProperty.substring("&^&".length()));
			} else {
				sb.append(getValue(data, valueProperty));
			}
		}
		return extractRegExValue(regEx, regExGroup, sb.toString());
	}

	private String getValue(HashMap<String, Object> data, String valueProperty, String regEx, int regExGroup) throws JSONException {
		String value = getValue(data, valueProperty);
		return extractRegExValue(regEx, regExGroup, value);
	}

	private String getValue(HashMap<String, Object> data, String valueProperty) {
		int index = valueProperty.indexOf(".");
		if (index >= 0) {
			String key = valueProperty.substring(0, index);
			Object value = null;
			try {
				value = data.get(key);
			} catch (Exception e) {
			}
			if (value != null) {
				String restOfTheKey = valueProperty.substring(index + 1);
				HashMap<String, Object> subMap = (HashMap<String, Object>) value;
				return getValue(subMap, restOfTheKey);
			} else {
				return "";
			}
		} else {
			String value = "";
			try {
				value = data.get(valueProperty).toString();
			} catch (Exception e) {
			}
			return value;
		}
	}

	private String getValue(JSONObject json, JSONArray valueProperties, String regEx, int regExGroup) throws JSONException {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < valueProperties.length(); i++) {
			String valueProperty = valueProperties.getString(i);
			if (valueProperty.startsWith("&^&")) {
				sb.append(valueProperty.substring("&^&".length()));
			} else {
				sb.append(getValue(json, valueProperty));
			}
		}
		return extractRegExValue(regEx, regExGroup, sb.toString());
	}

	private String getValue(JSONObject json, String valueProperty, String regEx, int regExGroup) throws JSONException {
		String value = getValue(json, valueProperty);
		return extractRegExValue(regEx, regExGroup, value);
	}

	private String getValue(JSONObject json, String valueProperty) throws JSONException {
		int index = valueProperty.indexOf(".");
		if (index >= 0) {
			String key = valueProperty.substring(0, index);
			JSONObject value = null;
			try {
				value = json.getJSONObject(key);
			} catch (Exception e) {
			}
			if (value != null) {
				String restOfTheKey = valueProperty.substring(index + 1);
				return getValue(value, restOfTheKey);
			} else {
				return "";
			}
		} else {
			String value = "";
			try {
				value = json.getString(valueProperty);
			} catch (Exception e) {
			}
			return value;
		}
	}

	private String extractRegExValue(String regEx, int regExGroup, String value) {
		if (value != null && value.length() > 0 && regEx != null) {
			Pattern pattern = Pattern.compile(regEx);
			Matcher matcher = pattern.matcher(value);
			if (matcher.matches()) {
				return matcher.group(regExGroup);
			} else {
				return "";
			}
		} else {
			return value;
		}
	}

	public String readFile(String fileName) {
		String result = "";
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(this.getClass().getResourceAsStream(fileName)));
			StringBuilder sb = new StringBuilder();
			String line = br.readLine();
			while (line != null) {
				sb.append(line);
				line = br.readLine();
			}
			result = sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	public void testGetValue() throws JSONException {
		String data = "{ 'ein' : '999999998', 'filingName' : 'Aphelion Technologies, Inc'," + "'address' : {" + "'line1' : '798 Praderia Cir'," + "'line2' : 'POBox 15576',"
				+ "'city' : 'Fremont'," + "'zip' : '94539'" + "}" + "}";
		JSONObject json = new JSONObject(data);

		System.out.println("Line 1 = " + getValue(json, "address.line1", null, 1));
		System.out.println("Line 2 = " + getValue(json, "address.line2", null, 1));
		System.out.println("City = " + getValue(json, "address.city", null, 1));
		System.out.println("Zip = " + getValue(json, "address.zip", null, 1));

		System.out.println("EIN = " + getValue(json, "ein", null, 1));

		System.out.println("EIN [1] = " + getValue(json, "ein", "(^.)(.*$)", 1));
		System.out.println("EIN [2] = " + getValue(json, "ein", "(^.)(.)(.*$)", 2));
		System.out.println("EIN [3] = " + getValue(json, "ein", "(^..)(.)(.*$)", 2));
		System.out.println("EIN [4] = " + getValue(json, "ein", "(^...)(.)(.*$)", 2));
		System.out.println("EIN [4] = " + getValue(json, "ein", "(^.{3})(.)(.*$)", 2));
		System.out.println("EIN [5] = " + getValue(json, "ein", "(^.{4})(.)(.*$)", 2));
		System.out.println("EIN [6] = " + getValue(json, "ein", "(^.{5})(.)(.*$)", 2));
		System.out.println("EIN [7] = " + getValue(json, "ein", "(^.{6})(.)(.*$)", 2));
		System.out.println("EIN [8] = " + getValue(json, "ein", "(^.{7})(.)(.*$)", 2));
		System.out.println("EIN [9] = " + getValue(json, "ein", "(^.{8})(.)(.*$)", 2));

	}

	private static void createHelloPDF() throws DocumentException, FileNotFoundException {
		Document document = new Document();
		PdfWriter.getInstance(document, new FileOutputStream("hello.pdf"));
		document.open();
		document.add(new Paragraph("Hello Pdf"));
		document.close();
	}

	private static void createPageSizePDF() throws DocumentException, FileNotFoundException {
		Document document = new Document(PageSize.A4, 0, 0, 10, 10);
		PdfWriter.getInstance(document, new FileOutputStream("pageSizePDF.pdf"));
		document.open();
		document.add(new Paragraph("A4 page Size--->>roseinia.net"));
		document.close();
	}

}