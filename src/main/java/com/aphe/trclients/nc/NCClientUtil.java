package com.aphe.trclients.nc;

import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import java.time.format.DateTimeFormatter;

public class NCClientUtil {

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    public void goToMainPage(WebDriver driver, String url) {
        driver.get(url);
        pause(5);
    }

    public void goTo1099UploadPage(WebDriver driver) {
        String upload1099LinkId = "//*[text()='File NC-3s, 1099s and W-2s']";
        WebElement upload1099LinkEle = driver.findElement(By.xpath(upload1099LinkId));
        upload1099LinkEle.click();
        pause(15);
    }


    public void attachFile(WebDriver driver, String filePath, String justFileName, boolean isCorrection,
                           String companyName, String companyEmail, String companyPhone,
                           String companyContact, String addressLine1, String addressCity,
                           String addressState, String addressZip
                           ) throws Exception {

        String newSubmissionButtonLabel = isCorrection ? "New Amended Submission" : "New Submission";
        String newSubmissionButtonId = "//*[text()='" + newSubmissionButtonLabel + "']";
        WebElement newSubmissionEle = driver.findElement(By.xpath(newSubmissionButtonId));
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", newSubmissionEle);
        newSubmissionEle.click();
        pause(2);


        String companyContactId = "//*[@id='Submitter--name-inner']";
        WebElement companyContactEle = driver.findElement(By.xpath(companyContactId));
        companyContactEle.sendKeys(companyContact);
        pause(1);

        String companyNameId = "//*[@id='Submitter--company-inner']";
        WebElement companyNameEle = driver.findElement(By.xpath(companyNameId));
        companyNameEle.sendKeys(companyName);
        pause(1);

        String line1Id = "//*[@id='Submitter--street-inner']";
        WebElement line1Ele = driver.findElement(By.xpath(line1Id));
        line1Ele.sendKeys(addressLine1);
        pause(1);

        String cityId = "//*[@id='Submitter--city-inner']";
        WebElement cityEle = driver.findElement(By.xpath(cityId));
        cityEle.sendKeys(addressCity);
        pause(1);

        String stateId = "//*[@id='Submitter--state-label']";
        WebElement stateEle = driver.findElement(By.xpath(stateId));
//        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", stateEle);
        stateEle.click();
        pause(2);

        String caId = "//*[@id=\"__item217-Submitter--state-4\"]";
        WebElement caEle = driver.findElement(By.xpath(caId));
        caEle.click();
        pause(2);

        String zipId = "//*[@id='Submitter--zip-inner']";
        WebElement zipEle = driver.findElement(By.xpath(zipId));
        zipEle.sendKeys(addressZip);
        pause(1);


        String titleId = "//*[@id='Submitter--title-inner']";
        WebElement titleEle = driver.findElement(By.xpath(titleId));
        titleEle.sendKeys("President");
        pause(1);


        String phoneId = "//*[@id='Submitter--phone-inner']";
        WebElement phoneEle = driver.findElement(By.xpath(phoneId));
        phoneEle.sendKeys(companyPhone);
        pause(1);


        String emailId = "//*[@id='Submitter--email-inner']";
        WebElement emailEle = driver.findElement(By.xpath(emailId));
        emailEle.sendKeys(companyEmail);
        pause(1);


        String emailId1 = "//*[@id='Submitter--email2-inner']";
        WebElement emailEle1 = driver.findElement(By.xpath(emailId1));
        emailEle1.sendKeys(companyEmail);
        pause(1);

        String nextButtonId = "//*[text()='Next']";
        WebElement nextButtonEle = driver.findElement(By.xpath(nextButtonId));
        nextButtonEle.click();
        pause(5);


        String attachmentFileInputIdName = isCorrection ? "AmendedHomeNC3--1099_filer" : "HomeNC3--1099_filer";
        String attachmentFileId = "//*[@id='" + attachmentFileInputIdName + "']";
        WebElement fileSel = driver.findElement(By.xpath(attachmentFileId));
        fileSel.sendKeys(filePath);
        pause(10);

//        String nextButtonId1 = "//*[text()='Next']";
//        WebElement nextButtonEle1 = driver.findElement(By.xpath(nextButtonId1));
//        nextButtonEle1.click();
//        pause(5);
//
        String nextButtonId1 = isCorrection ? "//*[@id='AmendedHomeNC3--HomeNC3Next-inner']" : "//*[@id='HomeNC3--HomeNC3Next-inner']";
        WebElement nextButtonEle1 = driver.findElement(By.xpath(nextButtonId1));
        nextButtonEle1.click();
        pause(5);
    }


    public String submitFile(WebDriver driver, boolean isCorrection) throws Exception {

        String labelName = isCorrection ? "AmendedSubmitNC3--AgreeCB" : "SubmitNC3--AgreeCB";
        String checkBoxId = "//*[@id='" + labelName + "']";
        WebElement checkBoxEle = driver.findElement(By.xpath(checkBoxId));
        checkBoxEle.click();
        pause(1);

        String submitButtonId = "//*[text()='Submit']";
        WebElement submitButtonEle = driver.findElement(By.xpath(submitButtonId));
        submitButtonEle.click();
        pause(5);

//        String okButtonId = "//*[@id='ConfirmationForm']/div[2]/button[2]";
//        WebElement okButtonEle = driver.findElement(By.xpath(okButtonId));
//        okButtonEle.click();
//        pause(5);
//
//        String confirmationId = "//*[@id='caption2_Dc-9']/strong";
//        WebElement confirmationEle = driver.findElement(By.xpath(confirmationId));
//        String confirmationNubmer = confirmationEle.getText();

//        return confirmationNubmer;
        return null;
    }

    public void pause(int seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (InterruptedException e) {
        }
    }
}
