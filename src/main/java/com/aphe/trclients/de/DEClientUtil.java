package com.aphe.trclients.de;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import java.time.format.DateTimeFormatter;

public class DEClientUtil {

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    public void goToMainPage(WebDriver driver, String url) {
        driver.get(url);
        pause(10);
    }

    public String submitFile(WebDriver driver, String filePath, String companyName, String companyEmail, String companyPhone) throws Exception {

        String nameElementName = "//*[@name='user_name']";
        String emailElementName = "//*[@name='email']";
        String phoneElementName = "//*[@name='phone']";
        String fileElementName = "//*[@name='doc[]']";

        String submitButtonId = "//*[@value='Submit']";
        String resetButtonId = "//*[@value='Reset']";

        WebElement nameElement = driver.findElement(By.xpath(nameElementName));
        WebElement emailElement = driver.findElement(By.xpath(emailElementName));
        WebElement phoneElement = driver.findElement(By.xpath(phoneElementName));
        WebElement fileSel = driver.findElement(By.xpath(fileElementName));

        nameElement.sendKeys(companyName);
        emailElement.sendKeys(companyEmail);
        phoneElement.sendKeys(companyPhone);
        fileSel.sendKeys(filePath);

        WebElement submitButton = driver.findElement(By.xpath(submitButtonId));
        //For delaware do not submit if you are connecting to dev efs server.
        submitButton.click();
        pause(5);

        String confirmationId = "//*[@id=\"mainBody\"]/b[2]";
        WebElement confirmationEle = driver.findElement(By.xpath(confirmationId));
        String confirmationNumber = confirmationEle.getText();

        return confirmationNumber;
    }

    public void pause(int seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (InterruptedException e) {
        }
    }
}
