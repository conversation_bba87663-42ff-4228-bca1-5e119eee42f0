package com.aphe.trclients.graphql;

import com.aphe.trclients.common.tnn.TransmissionAutoProcessOrchestrator;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class TRClientsRootMutation {

    private static final Logger logger = LoggerFactory.getLogger(TRClientsRootMutation.class);

    @Autowired
    TransmissionAutoProcessOrchestrator transmissionAutoProcessOrchestrator;

    @DgsMutation
    public boolean generateFiles(@InputArgument("jurisdiction") String jurisdiction) throws Exception {
        try {
            transmissionAutoProcessOrchestrator.generateFiles(jurisdiction);
            return true;
        } catch (Exception e) {
            logger.error("Error generating files. " + e.getMessage(), e);
            throw e;
        }
    }

    @DgsMutation
    public boolean testLoginLogout(@InputArgument("jurisdiction") String jurisdiction,
                                   @InputArgument("useRemoteDriver") boolean useRemoteDriver) throws Exception {
        try {
            transmissionAutoProcessOrchestrator.testLoginLogout(jurisdiction, useRemoteDriver);
            return true;
        } catch (Exception e) {
            logger.error("Error testing loginLogout " + e.getMessage(), e);
            throw e;
        }
    }


    @DgsMutation
    public boolean uploadFiles(@InputArgument("jurisdiction") String jurisdiction,
                               @InputArgument("includeDownloaded") boolean includeDownloaded,
                               @InputArgument("useRemoteDriver") boolean useRemoteDriver) throws Exception {
        try {
            transmissionAutoProcessOrchestrator.uploadFiles(
                    jurisdiction, includeDownloaded, useRemoteDriver);
            return true;
        } catch (Exception e) {
            logger.error("Error uploading files. " + e.getMessage(), e);
            throw e;
        }
    }

    @DgsMutation
    public boolean checkStatus(@InputArgument("jurisdiction") String jurisdiction,
                               @InputArgument("useRemoteDriver") boolean useRemoteDriver) throws Exception {
        try {
            transmissionAutoProcessOrchestrator.checkFileStatus(jurisdiction, useRemoteDriver);
            return true;
        } catch (Exception e) {
            logger.error("Error getting file status " + e.getMessage(), e);
            throw e;
        }
    }

}