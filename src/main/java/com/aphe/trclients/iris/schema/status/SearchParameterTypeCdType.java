
package com.aphe.trclients.iris.schema.status;

import javax.xml.bind.annotation.*;

/**
 * <p>Java class for SearchParameterTypeCdType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <pre>
 * &lt;simpleType name="SearchParameterTypeCdType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="RID"/&gt;
 *     &lt;enumeration value="UTID"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "SearchParameterTypeCdType", namespace = "urn:us:gov:treasury:irs:ir")
@XmlEnum
public enum SearchParameterTypeCdType {


    /**
     * Receipt ID
     * 
     */
    RID,

    /**
     * Unique Transmission ID
     * 
     */
    UTID;

    public String value() {
        return name();
    }

    public static SearchParameterTypeCdType fromValue(String v) {
        return valueOf(v);
    }

}
