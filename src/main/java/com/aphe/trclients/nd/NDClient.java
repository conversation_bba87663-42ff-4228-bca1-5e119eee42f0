package com.aphe.trclients.nd;

import com.aphe.trclients.common.TransmissionClient;
import com.aphe.trclients.common.tnn.TNNTransmissionServer;
import com.aphe.trclients.common.tnn.model.ProcessingStatus;
import com.aphe.trclients.common.tnn.model.ProcessingStatusDTO;
import com.aphe.trclients.common.tnn.model.UploadStatusDTO;
import com.aphe.trclients.nd.wsdl.*;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;
import org.w3c.dom.Node;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

@Component
public class NDClient extends WebServiceGatewaySupport implements TransmissionClient {
    private Logger logger = LoggerFactory.getLogger(NDClient.class);

    @Value("${aphe.trclients.nd.username}")
    private String username;

    @Value("${aphe.trclients.nd.password}")
    private String password;

    @Value("${aphe.trclients.nd.wsURL}")
    private String wsURL;

    private String ping = "/Ping";
    private String upload = "/ElectronicUpload";

    @Autowired
    TNNTransmissionServer tnnTransmissionServer;

    public String ping(String pingString) throws NDClientException {
        String pingAction = wsURL + ping;
        ObjectFactory objectFactory = new ObjectFactory();
        Ping ping = objectFactory.createPing();
        ping.setPstrPing(objectFactory.createPingPstrPing(pingString));
        PingResponse response = (PingResponse) getWebServiceTemplate().marshalSendAndReceive(ping, new SoapActionCallback(pingAction));
        return response.getPstrPing().getValue();
    }

    @Override
    public void testLoginLogout(boolean useRemote) throws Exception {

    }

    @Override
    public List<UploadStatusDTO> uploadFiles(String authToken, boolean useRemote, List<UploadStatusDTO> transmissionFiles) throws Exception {

        ND_REQUEST_TYPE requestType = ND_REQUEST_TYPE.TNN;

        String uploadAction = wsURL + upload;
        ObjectFactory objectFactory = new ObjectFactory();

        for (UploadStatusDTO transmissionFile : transmissionFiles) {

            String decryptedFileLocation = tnnTransmissionServer.downloadFile(authToken, transmissionFile.transmissionId, transmissionFile.fileName);

            if(decryptedFileLocation != null) {

                ElectronicUpload uploadRequest = objectFactory.createElectronicUpload();
                uploadRequest.setUserName(objectFactory.createElectronicUploadUserName(username));
                uploadRequest.setPassword(objectFactory.createElectronicUploadPassword(password));

                byte[] fileBytes = getFileBytes(decryptedFileLocation);
                uploadRequest.setFimgFile(objectFactory.createElectronicUploadFimgFile(fileBytes));
                uploadRequest.setFstrRequestType(objectFactory.createElectronicUploadFstrRequestType(requestType.getValue()));

                ElectronicUploadResponse response = (ElectronicUploadResponse) getWebServiceTemplate().marshalSendAndReceive(uploadRequest, new SoapActionCallback(uploadAction));

                boolean uploadValid = response.isFblnValidated().booleanValue();
                JAXBElement<ElectronicUploadResponse.ErrorResults> errorResults = response.getErrorResults();
                if (uploadValid) {
                    logger.info("File uploaded to ND successfully");
                    String uniqueFileName = response.getFstrFileName().getValue();
                    transmissionFile.confirmationFileName = uniqueFileName;
                } else {
                    transmissionFile.uploadError = true;
                    NewDataSet parsedErrorResults = parseErrorResults(errorResults);
                    String errorMessage = extractErrorDetails(parsedErrorResults);
                    logger.error("Error uploading file to ND: " + errorMessage);
                    transmissionFile.errorMessage = errorMessage;
                }
            } else {
                transmissionFile.uploadError = true;
                transmissionFile.errorMessage = "Unable to download file from TNN";
            }
        }
        return transmissionFiles;
    }

    @Override
    public List<ProcessingStatusDTO> getProcessingStatus(String authToken, boolean useRemote, List<ProcessingStatusDTO> processingFiles) throws Exception {
        String uploadAction = wsURL + upload;
        ObjectFactory objectFactory = new ObjectFactory();

        for (ProcessingStatusDTO processingFile : processingFiles) {
            ElectronicUpload uploadRequest = objectFactory.createElectronicUpload();
            uploadRequest.setUserName(objectFactory.createElectronicUploadUserName(username));
            uploadRequest.setPassword(objectFactory.createElectronicUploadPassword(password));
            uploadRequest.setFstrFileName(objectFactory.createElectronicUploadFstrFileName(processingFile.ackNumer));
            uploadRequest.setFstrRequestType(objectFactory.createElectronicUploadFstrRequestType(ND_REQUEST_TYPE.ACK.getValue()));

            ElectronicUploadResponse response = (ElectronicUploadResponse) getWebServiceTemplate().marshalSendAndReceive(uploadRequest, new SoapActionCallback(uploadAction));

            boolean fileValidated = response.isFblnValidated().booleanValue();
            String responseFileName = response.getFstrFileName().getValue();
            boolean fileNameMatches = processingFile.ackNumer.equals(responseFileName);

            if(fileValidated && fileNameMatches) {
                processingFile.fileStatus = ProcessingStatus.Accepted;
            } else {
                processingFile.fileStatus = ProcessingStatus.Processing;
                JAXBElement<ElectronicUploadResponse.ErrorResults> errorResults = response.getErrorResults();
                if (!errorResults.isNil()) {
                    NewDataSet parsedErrorResults = parseErrorResults(errorResults);
                    String errorMessage = extractErrorDetails(parsedErrorResults);
                    boolean isRejected = isRejected(parsedErrorResults);
                    if (isRejected) {
                        logger.error("ND file got rejected with errors: " + errorMessage);
                        processingFile.errorMessage = errorMessage;
                        processingFile.fileStatus = ProcessingStatus.Rejected;
                    }
                }
            }
        }
        return processingFiles;
    }


    @NotNull
    private byte[] getFileBytes(String fileLocation) {
        try {

            File file = new File(fileLocation);
            byte[] fileBytes = new byte[(int) file.length()];
            FileInputStream fis = null;
            try {
                fis = new FileInputStream(file);
                fis.read(fileBytes);
            } catch (FileNotFoundException e) {
                logger.error("File not found", e);
                throw new RuntimeException("File not found", e);
            } catch (IOException e) {
                logger.error("IO Exception", e);
                throw new RuntimeException("IO Exception", e);
            } finally {
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        logger.error("IO Exception", e);
                    }
                }
            }
            return fileBytes;
        } catch (Exception e) {
            logger.error("Error decrypting file", e);
            throw new RuntimeException("Error decrypting file", e);
        } finally {
            File file = new File(fileLocation);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    private String extractErrorDetails(NewDataSet dataset) {
        StringBuilder errorMessage = new StringBuilder();

        if (dataset != null) {
            for (Results results : dataset.errorResults) {
                errorMessage.append(results.tbl1ResponseDetail);
                errorMessage.append("\n");
            }
        } else {
            errorMessage.append("Error while unmarshalling error details from ND: message=");
        }
        return errorMessage.toString();
    }

    private boolean isRejected(NewDataSet dataset) {
        if (dataset != null) {
            for (Results results : dataset.errorResults) {
                if (results.tbl1Reject) {
                    return true;
                }
            }
        }
        return false;
    }


    private NewDataSet parseErrorResults(JAXBElement<ElectronicUploadResponse.ErrorResults> errorResults) {
        JAXBContext jaxbContext;
        Unmarshaller unmarshaller;
        try {
            Node any = (Node) errorResults.getValue().getAny();
            //This is schema element.
            Node schemaNode = any.getFirstChild();
            //Remove this schema element from any
            Node deletedSchemaNode = any.removeChild(schemaNode);

            jaxbContext = JAXBContext.newInstance(NewDataSet.class);
            unmarshaller = jaxbContext.createUnmarshaller();
            NewDataSet dataset = (NewDataSet) unmarshaller.unmarshal(any);
            return dataset;
        } catch (JAXBException e) {
            logger.error("Error while unmarshalling error details from ND", e);
        }
        return null;
    }

}
