package com.aphe.trclients.ma;

import com.aphe.trclients.common.SSHClientFactory;
import com.aphe.trclients.common.TransmissionClient;
import com.aphe.trclients.common.tnn.TNNTransmissionServer;
import com.aphe.trclients.common.tnn.model.ProcessingStatus;
import com.aphe.trclients.common.tnn.model.ProcessingStatusDTO;
import com.aphe.trclients.common.tnn.model.UploadStatusDTO;
import net.schmizz.sshj.SSHClient;
import net.schmizz.sshj.sftp.SFTPClient;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Component
public class MABulkFileUploadClient implements TransmissionClient {

    private Logger logger = LoggerFactory.getLogger(MABulkFileUploadClient.class);

    @Autowired
    TNNTransmissionServer tnnTransmissionServer;

    @Value("${aphe.trclients.ma.url}")
    private String host;

    @Value("${aphe.trclients.ma.port}")
    private int port;

    @Value("${aphe.trclients.ma.username}")
    private String username;

    @Value("${aphe.trclients.ma.password}")
    private String password;

    @Value("${aphe.efs.transmissionFileDir}")
    public String transmissionFileDir;

    @Value("${aphe.trclients.tempDownloadDir}")
    String tempDownloadDir;

    @Autowired
    SSHClientFactory sshClientFactory;

    private String remoteDownloadPath = "/home/<USER>/ssatechca/download";
    private String remoteUploadPath = "/home/<USER>/ssatechca/upload";

    @Override
    public void testLoginLogout(boolean useRemote) throws Exception {

    }

    @Override
    public List<UploadStatusDTO> uploadFiles(String authToken, boolean useRemote, List<UploadStatusDTO> transmissionFiles) throws Exception {
        SSHClient sshClient = sshClientFactory.getSSHClient(host, port, username, password);
        SFTPClient sftpClient = sshClient.newSFTPClient();
        sftpClient.getFileTransfer().setPreserveAttributes(false);

        try {
            for (UploadStatusDTO uploadFile : transmissionFiles) {
                //Decrypt the file.
                String decryptedFileLocation = null;
                try {

                    decryptedFileLocation = tnnTransmissionServer.downloadFile(authToken, uploadFile.transmissionId, uploadFile.fileName);

                    if (decryptedFileLocation != null) {
                        boolean isValid = true;
                        //TODO: do file validations..
                        //TODO: Reuse the regular file validations.
                        //TODO: Make sure all the records only contain MA records.

                        String fileName = uploadFile.fileName;
                        String fileLocalPath = decryptedFileLocation;
                        String fileRemotePath = remoteUploadPath + File.separator + fileName;

                        logger.debug("Uploading file  " + fileLocalPath + " to remote location: " + fileRemotePath);

                        sftpClient.put(fileLocalPath, fileRemotePath);

                        uploadFile.confirmationFileName = fileName;
                    } else {
                        uploadFile.uploadError = true;
                        uploadFile.errorMessage = "Errro downloading file";
                    }
                } catch (Exception e) {
                    uploadFile.uploadError = true;
                    uploadFile.errorMessage = e.getMessage();
                } finally {
                    //Delete the file...
                    File file = new File(decryptedFileLocation);
                    if (file.exists()) {
                        file.delete();
                    }
                }
            }
        } finally {
            if (sftpClient != null) {
                sftpClient.close();
            }
            if (sshClient != null) {
                sshClient.disconnect();
            }
        }
        return transmissionFiles;
    }

    @Override
    public List<ProcessingStatusDTO> getProcessingStatus(String authToken, boolean useRemote, List<ProcessingStatusDTO> filesToBeChecked) throws Exception {
        SSHClient sshClient = sshClientFactory.getSSHClient(host, port, username, password);
        SFTPClient sftpClient = sshClient.newSFTPClient();
        try {
            for (ProcessingStatusDTO file : filesToBeChecked) {
                String ackNumer = file.ackNumer;
                String remoteFileName = ackNumer + ".ack";

                String remoteFilePath = remoteDownloadPath + File.separator + remoteFileName;

                File responsesDir = new File(tempDownloadDir);
                responsesDir.mkdirs();

                String localFilePath = tempDownloadDir + File.separator + remoteFileName;

                if (sftpClient.statExistence(remoteFilePath) == null) {
                    file.fileStatus = ProcessingStatus.Processing;
                } else {
                    //Download the file and save it to responses files.
                    sftpClient.get(remoteFilePath, localFilePath);

                    //validate the file.
                    File responseFile = new File(localFilePath);
                    String syntaxString = "<SyntaxValid>true</SyntaxValid>";
                    String fileNameString = "<TransmissionName>" + ackNumer + ".txt" + "</TransmissionName>";
                    String fileContents = FileUtils.readFileToString(responseFile, StandardCharsets.UTF_8.name());
                    //check for presence of this string -- <SyntaxValid>true</SyntaxValid>
                    //check for presence of file name -- <TransmissionName>1099SFApp-PP89q-TY2022-MA.txt.txt</TransmissionName>
                    boolean syntaxValid = fileContents.contains(syntaxString);
                    boolean fileNameMatch = fileContents.contains(fileNameString);

                    if (fileNameMatch && syntaxValid) {
                        //Upload the file.
                        boolean uploaded = tnnTransmissionServer.uploadResponseFile(authToken, file.transmissionId, localFilePath, remoteFileName);
                        if (uploaded) {
                            file.fileStatus = ProcessingStatus.Accepted;
                        } else {
                            file.fileStatus = ProcessingStatus.Rejected;
                        }
                    } else {
                        file.fileStatus = ProcessingStatus.Rejected;
                    }
                }
            }
        } finally {
            if (sftpClient != null) {
                sftpClient.close();
            }
            if (sshClient != null) {
                sshClient.disconnect();
            }
        }
        return filesToBeChecked;
    }

}
