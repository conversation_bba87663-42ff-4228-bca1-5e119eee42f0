package com.aphe.trclients.common.tnn.model;

public class UploadStatusDTO {
    public long transmissionId;
    public String fileName;
    public String fileLocation;
    public boolean isCorrection;
    public String formType;
    public String year;

    public int numberOfARecords;
    public int numberOfBRecords;
    public String totalWithHoldingAmount = "0.00";

    public String confirmationFileName;
    public boolean uploadError = false;
    public String errorMessage;

    public UploadStatusDTO(long transmissionId, String fileName, String fileLocation, boolean isCorrection, String formType, String year,
                           int numberOfARecords, int numberOfBRecords, String totalWithholdingAmount) {
        this.transmissionId = transmissionId;
        this.fileName = fileName;
        this.fileLocation = fileLocation;
        this.isCorrection = isCorrection;
        this.formType = formType;
        this.year = year;

        this.numberOfARecords = numberOfARecords;
        this.numberOfBRecords = numberOfBRecords;
        this.totalWithHoldingAmount = totalWithholdingAmount;
    }
}
