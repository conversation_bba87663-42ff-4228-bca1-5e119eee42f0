package com.aphe.trclients.common;

import com.aphe.trclients.common.tnn.model.ProcessingStatusDTO;
import com.aphe.trclients.common.tnn.model.UploadStatusDTO;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.List;

@Component
public interface TransmissionClient {

    ZoneId pacificTimeZone = ZoneId.of("America/Los_Angeles");

    default void pause(int seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (InterruptedException e) {
        }
    }

    void testLoginLogout(boolean useRemote) throws Exception;

    List<UploadStatusDTO> uploadFiles(String authToken, boolean useRemote, List<UploadStatusDTO> filesToBeUploaded) throws Exception;

    List<ProcessingStatusDTO> getProcessingStatus(String authToken, boolean useRemote, List<ProcessingStatusDTO> filesToBeChecked) throws Exception;

}
