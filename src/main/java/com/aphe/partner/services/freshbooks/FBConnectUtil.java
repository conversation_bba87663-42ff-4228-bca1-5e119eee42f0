package com.aphe.partner.services.freshbooks;

import com.aphe.common.util.StringUtil;
import com.aphe.contractor.model.enums.CountryCode;
import com.aphe.domain.dto.AddEditDomainInput;
import com.aphe.domain.dto.UpdateAddressInput;
import com.aphe.domain.model.Address;
import com.aphe.domain.model.TinType;
import com.aphe.fbsdk.api.*;
import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.*;
import com.aphe.fbsdk.model.enums.VisState;
import com.aphe.fbsdk.query.ExpandQueryBuilder;
import com.aphe.fbsdk.query.FilterQueryBuilder;
import com.aphe.fbsdk.query.PaginationQueryBuilder;
import com.aphe.partner.dto.AccountingIntegrationDTO;
import com.aphe.partner.dto.BoxMappingsInput;
import com.aphe.partner.dto.ChartOfAccountDTO;
import com.aphe.partner.dto.OAuthAccessTokenDTO;
import com.aphe.partner.model.PAccount;
import com.aphe.partner.model.PTransaction;
import com.aphe.partner.model.PVendor;
import com.aphe.partner.services.OAuthConnectionUtil;
import com.aphe.partner.services.transform.TNNBill;
import com.aphe.partner.services.transform.TNNBillPayment;
import com.aphe.partner.services.transform.TNNExpense;
import com.intuit.ipp.data.AccountTypeEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Transactional
public class FBConnectUtil extends OAuthConnectionUtil {

    public static final int PER_PAGE = 100;
    public static final int BILLS_PER_PAGE = 50;

    @Autowired
    private FBMapper fbMapper;


    final static Logger logger = LogManager.getLogger(FBConnectUtil.class);
    public static final String UNKNOWN_VENDOR = "Unknown Vendor";

    @Value("${aphe.freshbooks.clientId}")
    private String clientId;

    @Value("${aphe.freshbooks.baseURL}")
    private String baseURL;

    public String getAccountName(String realmId, String accessToken) {
        IdentityManager identityManager = new IdentityManager(baseURL, accessToken, realmId);
        try {
            Identity currentUser = identityManager.getOne(null, null);
            List<BusinessMembership> businessMemberships = currentUser.business_memberships;
            if (businessMemberships != null && businessMemberships.size() > 0) {
                for(BusinessMembership membership : businessMemberships) {
                    Business business = membership.business;
                    if(business.account_id.equalsIgnoreCase(realmId)) {
                        String bizName = business.name;
                        if(StringUtil.isEmpty(bizName)) {
                            bizName = "No Business Name";
                        }
                        return bizName;
                    }
                }
            }
        } catch (FBException e) {
            e.printStackTrace();
        }
        return null;
    }

    public AddEditDomainInput getCompanyInfo(AccountingIntegrationDTO dto,  OAuthAccessTokenDTO tokenDTO) {
        IdentityManager identityManager = new IdentityManager(baseURL, tokenDTO.accessToken, tokenDTO.accountId);
        try {
            Identity currentUser = identityManager.getOne(null, null);
            List<BusinessMembership> businessMemberships = currentUser.business_memberships;
            if (businessMemberships != null && businessMemberships.size() > 0) {

                for(BusinessMembership membership : businessMemberships) {
                    Business business = membership.business;
                    if(business.account_id.equalsIgnoreCase(tokenDTO.accountId)) {
                        String bizName = business.name;
                        if(StringUtil.isEmpty(bizName)) {
                            bizName = "No Business Name";
                        }
                        AddEditDomainInput addEditDomainInput = new AddEditDomainInput();
                        addEditDomainInput.id = dto.domainId;
                        addEditDomainInput.name = clean(bizName);
                        //TODO: NO API TO GET TIN
                        addEditDomainInput.tin = clean("");
                        addEditDomainInput.tinType = TinType.NA;

                        addEditDomainInput.address = getAddress(business);

//                        if(PropertiesManager.isProd() || PropertiesManager.isSandbox()) {
//                            String email = currentUser.email;
//                            addEditDomainInput.emailAddress = email != null ? clean(email) : null;
//                        }

                        PhoneNumber thePhoneNumber = business.phone_number;
                        String thePhone = null;
                        if (thePhoneNumber != null) {
                            thePhone = clean(thePhoneNumber.phone_number);
                            thePhone = getPhoneNumber(thePhone);
                        }

                        addEditDomainInput.phoneNumber = thePhone != null ? thePhone : null;

                        return addEditDomainInput;
                    }
                }
            }
        } catch (FBException e) {
            e.printStackTrace();
        }
        return null;
    }

    private UpdateAddressInput getAddress(Business business) {
        UpdateAddressInput addressInput = null;
        addressInput = getAddressInput(business.address);
        return addressInput;
    }

    private UpdateAddressInput getAddressInput(com.aphe.fbsdk.model.Address theAddress) {
        if (theAddress != null) {
            String line1 = theAddress.street;
            String city = theAddress.city;
            String province = theAddress.province;
            String postalCode = theAddress.postal_code;
            String country = theAddress.country;
            return getUpdateAddressInput(line1, null, city, province, postalCode, country);
        }
        return null;
    }

    public List<PAccount> getAccounts(AccountingIntegrationDTO dto,  OAuthAccessTokenDTO tokenDTO) throws Exception {

        List<PAccount> accounts = new ArrayList<>();
        ExpenseCategoryManager accountManager = new ExpenseCategoryManager(baseURL, tokenDTO.accessToken, tokenDTO.accountId);
        List<ExpenseCategory> expenseCategoryList = accountManager.getListAll(new ArrayList<>());
        for(ExpenseCategory exp: expenseCategoryList) {
            PAccount pAccount = new PAccount();
            pAccount.setIntegrationId(dto.id);
            pAccount.setAccountId(exp.categoryid);
            pAccount.setName(exp.category);
            pAccount.setFullyQualifiedName(exp.category);
            pAccount.setAccountSubType("Expense");
            pAccount.setAccountType(AccountTypeEnum.EXPENSE.value());
            if(exp.is_cogs) {
                pAccount.setAccountType(AccountTypeEnum.COST_OF_GOODS_SOLD.value());
            }
            pAccount.setParentId(exp.parentid != null ? exp.parentid.toString() : null);
            accounts.add(pAccount);
        }
        return accounts;
    }

    public List<PVendor> getPayees(AccountingIntegrationDTO currentIntegration,  OAuthAccessTokenDTO tokenDTO, String filingYear) throws Exception {

        VendorManager vendorManager = new VendorManager(baseURL, tokenDTO.accessToken, tokenDTO.accountId);
        List<PVendor> pVendors = new ArrayList<PVendor>();
        List<Vendor> vendorArrayList = vendorManager.getListAll(new ArrayList<>());
        List<Vendor> deletedVendors = vendorManager.getListAll(Arrays.asList(new FilterQueryBuilder().addEquals("vis_state", VisState.DELETED.getValue())));
        vendorArrayList.addAll(deletedVendors);

        for(Vendor v : vendorArrayList) {
            PVendor pVendor = new PVendor();
            pVendor.setIntegrationId(currentIntegration.id);
            pVendor.setVendorId(v.vendorid);
            pVendor.setDisplayName(clean(v.vendor_name));
            pVendor.setCompanyName(clean(v.vendor_name));
            pVendor.setFirstName(clean(v.primary_contact_first_name));
            pVendor.setLastName(clean(v.primary_contact_last_name));
            pVendor.setEmailAddress(v.primary_contact_email != null ? clean(v.primary_contact_email) : null);
            pVendor.setPrimaryPhone(v.phone != null ? clean(v.phone) : null);
            pVendor.setVendor1099(v.is_1099);
            Address vendorAddress = new Address();
            vendorAddress.setLine1(clean(v.street));
            vendorAddress.setLine2(clean(v.street2));
            vendorAddress.setCity(clean(v.city));
            vendorAddress.setState(clean(v.province));
            vendorAddress.setCountry(CountryCode.getCountryByName(clean(v.country)));
            vendorAddress.setPostalCode(clean(v.postal_code));
            pVendor.setAddress(vendorAddress);
            pVendors.add(pVendor);
        }

        HashMap<String, PVendor> expenseVendors = new HashMap<>();
        List<Expense> allExpenses = getAllExpenses(tokenDTO, filingYear);
        for(Expense expense : allExpenses) {
            String vendorName = clean(expense.vendor);
            if(StringUtil.isEmpty(vendorName)) {
                vendorName = UNKNOWN_VENDOR;
            }
            if(expenseVendors.get(vendorName) == null) {
                PVendor pVendor = new PVendor();
                pVendor.setIntegrationId(currentIntegration.id);
                pVendor.setVendorId(vendorName);
                pVendor.setDisplayName(vendorName);
                pVendor.setCompanyName(vendorName);
                pVendor.setVendor1099(false);
                expenseVendors.put(vendorName, pVendor);
            }
        }
        pVendors.addAll(expenseVendors.values());
        return pVendors;
    }

    public List<PTransaction> getTransactions(AccountingIntegrationDTO dto,  OAuthAccessTokenDTO tokenDTO, String filingYear,
                                              List<PVendor> vendors,
                                              List<PAccount> accounts) throws Exception {
        List<Expense> fbExpenses = getAllExpenses(tokenDTO, filingYear);
        List<BillPayment> fbBillPayments = getAllBillPayments(tokenDTO);
        List<String> fbBillIds = fbBillPayments.stream().map(billPayment -> billPayment.billid).collect(Collectors.toList());
        Map<String, Bill> fbBills = getBillsByBillIds(tokenDTO, fbBillIds);

        List<TNNExpense> tnnExpenses = fbExpenses.stream().map((fbExpense) -> fbMapper.toTNNExpense(fbExpense, accounts)).collect(Collectors.toList());
        List<TNNBillPayment> tnnBillPayments = fbBillPayments.stream().map((billPayment) -> fbMapper.toTNNBillPayment(billPayment, accounts, fbBills)).collect(Collectors.toList());
        Map<String, TNNBill> tnnBillMap = fbBills.values().stream().map(fbMapper::toTNNBill).collect(Collectors.toMap(bill -> bill.id, o -> o, (bill1, bill2) -> bill2));
        List<String> coaIds = accounts.stream().map(PAccount::getAccountId).collect(Collectors.toList());

        List<PTransaction> pTransactions = transactionBuilder.buildTransactionLines(tnnExpenses, tnnBillPayments, tnnBillMap, coaIds, filingYear);
        return pTransactions;
    }

    @NotNull
    private List<Expense> getAllExpenses(OAuthAccessTokenDTO tokenDTO, String filingYear) throws FBException {
        List<Expense> allExpenses = new ArrayList<>();
        ExpenseManager expenseManager = new ExpenseManager(baseURL, tokenDTO.accessToken, tokenDTO.accountId);

        FilterQueryBuilder dateFilter = new FilterQueryBuilder().addBetween("date", getStartLocalDate(filingYear), getEndLocalDate(filingYear));
        List<Expense> expenseList = expenseManager.getListAll(Arrays.asList(dateFilter));
        for(Expense expense : expenseList) {
            allExpenses.add(expense);
        }
        return allExpenses;
    }

    private List<BillPayment> getAllBillPayments(OAuthAccessTokenDTO tokenDTO) throws Exception {
        List<BillPayment> allBillPayments = new ArrayList<>();
        BillPaymentManager billPaymentManager = new BillPaymentManager(baseURL, tokenDTO.accessToken, tokenDTO.accountId);
        List<BillPayment> billPayments = billPaymentManager.getListAll(new ArrayList<>());
        for (BillPayment billPayment : billPayments) {
            allBillPayments.add(billPayment);
        }
        return allBillPayments;
    }

    private Map<String, Bill> getBillsByBillIds(OAuthAccessTokenDTO tokenDTO, List<String> billIds) throws Exception {
        HashMap<String, Bill> allBills = new HashMap<>();
        int currentPage = 1;
        boolean hasMorePages = false;
        BillManager billManager = new BillManager(baseURL, tokenDTO.accessToken, tokenDTO.accountId);
        do{
            PaginationQueryBuilder paginator = new PaginationQueryBuilder(currentPage, BILLS_PER_PAGE);
            ExpandQueryBuilder inclduesFilter = new ExpandQueryBuilder().add("vendor");
            hasMorePages =  (currentPage)*BILLS_PER_PAGE < billIds.size();
            int beginIndex = (currentPage-1)*BILLS_PER_PAGE;
            int endIndex = hasMorePages ? (currentPage)*BILLS_PER_PAGE  : billIds.size();
            List<String> thisPageBillIds = billIds.subList(beginIndex, endIndex);
            FilterQueryBuilder idFilter = new FilterQueryBuilder().addIn("id", thisPageBillIds);
            List<Bill> bills = billManager.getList(Arrays.asList(paginator, inclduesFilter, idFilter));
            for(Bill bill : bills){
                allBills.put(bill.id, bill);
            }
            currentPage++;
        }while(hasMorePages);
        return allBills;
    }

    public void updateAccountBoxData(AccountingIntegrationDTO currentIntegration, BoxMappingsInput newMappings,
                                     List<ChartOfAccountDTO> allAccounts,
                                     OAuthAccessTokenDTO tokenDTO) {
        throw new RuntimeException("Not implemented");
    }

}
