package com.aphe.partner.services.wave;

import com.aphe.wavesdk.WaveClient;
import com.aphe.wavesdk.WaveResponse;
import com.aphe.wavesdk.model.Account;
import com.aphe.wavesdk.model.Business;
import com.aphe.wavesdk.model.Vendor;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Component
public class WaveUtil {

    public static final String BIZ_QUERY = "queries/wave/business.graphql";
    public static final String COA_QUERY = "queries/wave/coa.graphql";
    public static final String VENDORS_QUERY = "queries/wave/vendors.graphql";
    public static final int PAGE_SIZE = 100;

    @Autowired
    WaveClient waveClient;

    public Business getBusiness(String baseURL, String accessToken, String id) throws Exception {

        String queryString = getQueryString(BIZ_QUERY);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", id);
        String variablesString = jsonObject.toString();

        WaveResponse businessesResponse = waveClient.getWaveResponse(baseURL, accessToken, queryString, variablesString);
        Business business = businessesResponse.getData().getBusiness();
        return business;
    }

    public List<Account> getAccounts(String baseURL, String accessToken, String id) throws Exception {
        List<Account> allAccounts = new ArrayList<>();

        String queryString = getQueryString(COA_QUERY);

        int page = 1;
        boolean hasMorePages = false;
        do {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("businessId", id);
            jsonObject.put("page", page);
            jsonObject.put("pageSize", PAGE_SIZE);
            String variablesString = jsonObject.toString();
            WaveResponse businessesResponse = waveClient.getWaveResponse(baseURL, accessToken, queryString, variablesString);
            Business business = businessesResponse.getData().getBusiness();
            List<Account> pageOfAccounts = business.getAccounts();
            allAccounts.addAll(pageOfAccounts);
            hasMorePages = business.hasMoreAccounts();
            page++;
        } while (hasMorePages);
        return allAccounts;
    }

    public List<Vendor> getVendors(String baseURL, String accessToken, String id) throws Exception {
        List<Vendor> allVendors = new ArrayList<>();

        String queryString = getQueryString(VENDORS_QUERY);

        int page = 1;
        boolean hasMorePages = false;
        do {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("businessId", id);
            jsonObject.put("page", page);
            jsonObject.put("pageSize", PAGE_SIZE);
            String variablesString = jsonObject.toString();
            WaveResponse businessesResponse = waveClient.getWaveResponse(baseURL, accessToken, queryString, variablesString);
            Business business = businessesResponse.getData().getBusiness();
            List<Vendor> pageOfVendors = business.getVendors();
            allVendors.addAll(pageOfVendors);
            hasMorePages = business.hasMoreVendors();
            page++;
        } while (hasMorePages);
        return allVendors;
    }

    private String getQueryString(String queryFileName) throws Exception {
        String queryString = "";
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream(queryFileName);
        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
                sb.append('\n');
            }
            queryString = sb.toString();
        }
        return queryString;
    }
}
