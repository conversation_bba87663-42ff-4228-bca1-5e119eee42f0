package com.aphe.partner.services.transform;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;


/**
 * Bill Payment
 * -- Date, Amount, From Account(isBankAccount or not)
 * -- Bill Payments covered for and the amount covered for each of those bills
 * -- For each bill, the bill line items.
 * -- Bill Line Items:
 * -- Account, Amount.
 * <p>
 * Expense
 * -- Date, Amount, From Account(isBankAccount or not), Chart of Account.
 */

public class TNNBill {

    final static Logger logger = LogManager.getLogger(TNNBill.class);

    public String id;
    public LocalDate date;
    public BigDecimal amount;
    public String description;
    public List<TNNBillLine> billLines;

}


