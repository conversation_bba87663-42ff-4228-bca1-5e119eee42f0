package com.aphe.partner.services.transform;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public class TNNExpense {

    final static Logger logger = LogManager.getLogger(TNNExpense.class);

    public String id;
    public String vendorId;
    public LocalDate date;
    public BigDecimal amount;
    public String description;
    public boolean fromBankAccount;
    public List<TNNExpenseLine> expenseLines;

}
