package com.aphe.partner.services;

import com.aphe.common.service.CommonBaseManager;
import com.aphe.partner.model.AccountingIntegration;

public class CommonAccountingIntegrationManager extends CommonBaseManager {

	protected boolean isAccessible(AccountingIntegration accountingIntegration) {
		if (accountingIntegration != null && accountingIntegration.getDomainId() != null) {
			Long loggedInDomainId = getCurrentDomainId();
			return loggedInDomainId != null && loggedInDomainId == accountingIntegration.getDomainId();
		}
		return false;
	}

}
