package com.aphe.partner.services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aphe.partner.dto.AccountingIntegrationDTO;
import com.aphe.partner.model.AccountingIntegration;

@Component
public class AccountingIntegrationConvertUtil {

	@Autowired
	AccountingIntegrationMapper mapper;

	public AccountingIntegration convertAccountingIntegrationDTOToEntity(AccountingIntegrationDTO dto) {
		return mapper.toEntity(dto);
	}

	public AccountingIntegrationDTO convertAccountingIntegrationToDTO(AccountingIntegration entity) {
		return mapper.toDTO(entity);
	}

}
