package com.aphe.partner.services;

import com.aphe.common.security.jwt.ApheAuthContext;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.SSLUtil;
import com.aphe.partner.dto.AccountingIntegrationDTO;
import com.aphe.partner.dto.OAuthAccessTokenDTO;
import com.aphe.partner.model.AccountingIntegrationPartner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

@Component
public class AcctIntUitl {

	private static Logger logger = LoggerFactory.getLogger(AcctIntUitl.class);

	@Value("${aphe.auth.tokenValidationURLLocal}")
	private String authServiceURL;

	@Value("${aphe.auth.oAuthAccessTokenPath}")
	private String accessTokenPath;

	@Autowired
	AccountingIntegrationMgr acctIntMgr;

	public OAuthAccessTokenDTO createOrUpdateIntegration(String partner) throws Exception {
		OAuthAccessTokenDTO oAuthAccessTokenDTO = getOAuthAccessToken(partner);
		if (oAuthAccessTokenDTO != null) {
			Long domainId = acctIntMgr.getCurrentDomainId();
			AccountingIntegrationDTO acctIntDTO = acctIntMgr.getAccountingIntegrationDTO(domainId, partner);
			if (acctIntDTO == null) {
				acctIntDTO = new AccountingIntegrationDTO();
				acctIntDTO.domainId = oAuthAccessTokenDTO.domainId;
				acctIntDTO.partner = AccountingIntegrationPartner.valueOf(partner);
				acctIntMgr.addOrUpdateIntegration(acctIntDTO);
			}
			return oAuthAccessTokenDTO;
		}
		return null;
	}

	/**
	 * if any one makes calls or attempts to use accounting intgration with a partner.
	 * 
	 * 1. Call auth to see if this is connected. get the latest access token.
	 * 2. If we get an access token, this is good. Create an acocunting if it doesn;t exist.
	 * 3. Go on about doing the job.
	 * 2. If connected, create a record on our side, if that doesn't exisit.
	 * 
	 * 
	 */

	public OAuthAccessTokenDTO getOAuthAccessToken(String partner) {
		try {
			if (PropertiesManager.isDev()) {
				SSLUtil.turnOffSslChecking();
			}

			RestTemplate restTemplate = new RestTemplate();
			restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));
			String token = ApheAuthContext.getToken();

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.add(JwtUtil.AUTH_HEADER_NAME, JwtUtil.TOKEN_PREFIX + token);

			String resourceUrl = authServiceURL + accessTokenPath + "/" + partner + "/";
			HttpEntity<String> httpEntity = new HttpEntity<>(partner, headers);
			ResponseEntity<OAuthAccessTokenDTO> response = restTemplate.exchange(resourceUrl, HttpMethod.GET, httpEntity, OAuthAccessTokenDTO.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				OAuthAccessTokenDTO responseDTO = response.getBody();
				return responseDTO;
			}
		} catch (Exception e) {
			logger.error("Error getting oAuthCredentials from auth service", e);
			return null;
		} finally {
			try {
				// SSLUtil.turnOnSslChecking();
			} catch (Exception e) {
				logger.error("Error turning on SSL cert checking", e);
			}
		}
		return null;
	}

}
