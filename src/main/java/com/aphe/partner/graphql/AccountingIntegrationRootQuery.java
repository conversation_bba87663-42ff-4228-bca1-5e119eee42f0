package com.aphe.partner.graphql;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.graphql.BaseGraphQL;
import com.aphe.common.util.DateUtil;
import com.aphe.common.util.StringUtil;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import com.aphe.insights.service.InsightsManager;
import com.aphe.partner.dto.AccountingIntegrationDTO;
import com.aphe.partner.dto.BoxMappingDTO;
import com.aphe.partner.dto.ChartOfAccountDTO;
import com.aphe.partner.dto.OAuthAccessTokenDTO;
import com.aphe.partner.dto.actimport.ImportSummaryDTO;
import com.aphe.partner.dto.actimport.PVendorImportSummaryDTO;
import com.aphe.partner.services.AccountingIntegrationMgr;
import com.aphe.partner.services.AccountingIntegrationOrchestrator;
import com.aphe.partner.services.AcctIntUitl;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsQuery;
import com.netflix.graphql.dgs.InputArgument;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@DgsComponent
public class AccountingIntegrationRootQuery extends BaseGraphQL {

	protected Logger logger = LoggerFactory.getLogger(AccountingIntegrationRootQuery.class);

	@Autowired
	AccountingIntegrationMgr acctIntMgr;

	@Autowired
	AccountingIntegrationOrchestrator accountingIntegrationOrchestrator;

	@Autowired
	DomainMgr domainMgr;

	@Value("${aphe.efs.tnnFormsDir}")
	public String tnnFormsDir;


	@Autowired
	AcctIntUitl acctUtil;

	@Autowired
	InsightsManager insightsManager;

	@DgsQuery
	public String accountingIntegrationPartner() throws ApheException {
		try {
			return acctIntMgr.getAccountingIntegrationPartner(acctIntMgr.getCurrentDomainId());
		} catch (Exception e) {
			handleException(e, "Error getting accounting integration data");
		}
		return null;
	}

	@DgsQuery
	public AccountingIntegrationDTO accountingIntegration(@InputArgument("partner") String partner) throws ApheException {
		try {
			OAuthAccessTokenDTO tokenDTO = acctUtil.createOrUpdateIntegration(partner);
			return acctIntMgr.getAccountingIntegrationDTO(acctIntMgr.getCurrentDomainId(), partner);
		} catch (Exception e) {
			handleException(e, "Error getting accounting integration data");
		}
		return null;
	}

	@DgsQuery
	public List<ChartOfAccountDTO> chartOfAccounts(@InputArgument("id") String id, @InputArgument("refresh") boolean refresh) throws Exception {
		try {
			AccountingIntegrationDTO integrationDTO = acctIntMgr.getAccountingIntegrationDTOById(id);
			if(integrationDTO != null) {
				String partner = integrationDTO.partner.name();
				boolean isMoreThan24HoursSinceLastRefresh = isMoreThan24HoursSinceLastRefresh(integrationDTO.accountsLastSyncDate);
				if(refresh || isMoreThan24HoursSinceLastRefresh) {
					OAuthAccessTokenDTO tokenDTO = acctUtil.getOAuthAccessToken(partner);
					if(tokenDTO != null) {
						accountingIntegrationOrchestrator.syncAccounts(acctIntMgr.getCurrentDomainId(), partner, tokenDTO);
					}
				}
				return acctIntMgr.getChartOfAccounts(acctIntMgr.getCurrentDomainId(), partner);
			}
		} catch (Exception e) {
			handleException(e, "Error getting chart of accounts");
		}
		return null;
	}

	@DgsQuery
	public List<BoxMappingDTO> boxMappings(@InputArgument("id") String id) throws Exception {
		try {
			AccountingIntegrationDTO integrationDTO = acctIntMgr.getAccountingIntegrationDTOById(id);
			if(integrationDTO != null) {
				String partner = integrationDTO.partner.name();
				return acctIntMgr.getBoxMappings(acctIntMgr.getCurrentDomainId(), partner);
			}
		} catch (Exception e) {
			handleException(e, "Error getting box mappings");
		}
		return null;
	}

	@DgsQuery
	public ImportSummaryDTO importSummary(
			@InputArgument("integrationId") String integrationId,
			@InputArgument("filingYear") String filingYear,
			@InputArgument("include1099VendorsOnly") boolean include1099VendorOnly,
			@InputArgument("refreshData") boolean refreshData) throws Exception {
		try {
			AccountingIntegrationDTO integrationDTO = acctIntMgr.getAccountingIntegrationDTOById(integrationId);
			if(integrationDTO != null) {
				String partner = integrationDTO.partner.name();
				OAuthAccessTokenDTO tokenDTO = acctUtil.getOAuthAccessToken(partner);
				if(tokenDTO != null) {
					DomainDTO domainDTO = domainMgr.getDomain();
					if (domainDTO != null && StringUtil.isEmpty(domainDTO.tin)) {
						acctIntMgr.syncPayerInfo(acctIntMgr.getCurrentDomainId(), partner, tokenDTO);

						insightsManager.updateTINInsight(acctIntMgr.getCurrentDomainId());
					}
					boolean isMoreThan24HoursSinceLastRefresh = isMoreThan24HoursSinceLastRefresh(integrationDTO.transactionsLastSyncDate);
					if(refreshData || isMoreThan24HoursSinceLastRefresh) {
						boolean payeesSynced = acctIntMgr.syncPayees(acctIntMgr.getCurrentDomainId(), partner, tokenDTO, filingYear);
						boolean accountsSynced = accountingIntegrationOrchestrator.syncAccounts(acctIntMgr.getCurrentDomainId(), partner, tokenDTO);
						boolean transactionsSynced = acctIntMgr.syncTransactions(acctIntMgr.getCurrentDomainId(), partner, tokenDTO, filingYear);
						integrationDTO = acctIntMgr.getAccountingIntegrationDTOById(integrationId);
					}

					List<PVendorImportSummaryDTO> importData = acctIntMgr.getImportSummary(acctIntMgr.getCurrentDomainId(), partner, tokenDTO, filingYear, include1099VendorOnly);
					Long domainId = integrationDTO.domainId;
					String realmId = tokenDTO.accountId;
					String randomString = RandomStringUtils.random(10, true, true);

					ImportSummaryDTO importSummaryDTO = new ImportSummaryDTO();
					importSummaryDTO.lastSyncTime = integrationDTO.transactionsLastSyncDate == null ? new Date() : integrationDTO.transactionsLastSyncDate;
					importSummaryDTO.vendorImportSummaryList = importData;
					return importSummaryDTO;
				}
			}
		} catch (Throwable e) {
			handleException(e, "Error importing data from the accounting software");
		}
		return null;
	}

	private boolean isMoreThan24HoursSinceLastRefresh(Date lastSyncDate) {
		if(lastSyncDate == null)
			return true;
		return DateUtil.addHours(new Date(), -24).after(lastSyncDate);
	}

}