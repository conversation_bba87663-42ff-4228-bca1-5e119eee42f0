package com.aphe.partner.graphql;

import com.aphe.common.graphql.BaseGraphQL;
import com.aphe.insights.service.InsightsManager;
import com.aphe.partner.dto.BoxMappingsInput;
import com.aphe.partner.dto.OAuthAccessTokenDTO;
import com.aphe.partner.services.AccountingIntegrationMgr;
import com.aphe.partner.services.AccountingIntegrationOrchestrator;
import com.aphe.partner.services.AcctIntUitl;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class AccountingIntegrationRootMutation extends BaseGraphQL  {

	private static final Logger logger = LoggerFactory.getLogger(AccountingIntegrationRootMutation.class);

	@Autowired
	AccountingIntegrationMgr acctIntMgr;

	@Autowired
	AccountingIntegrationOrchestrator accountingIntegrationOrchestrator;


	@Autowired
	AcctIntUitl acctUtil;

	@Autowired
	InsightsManager insightsManager;

	@DgsMutation
	public boolean syncAccounts(@InputArgument("partner") String partner) throws Exception {
		try {
			OAuthAccessTokenDTO tokenDTO = acctUtil.createOrUpdateIntegration(partner);
			return accountingIntegrationOrchestrator.syncAccounts(acctIntMgr.getCurrentDomainId(), partner, tokenDTO);
		} catch (Exception e) {
			handleException(e, "Error syncing accounts");
		}
		return false;
	}

	@DgsMutation
	public boolean syncPayerInfo(@InputArgument("partner") String partner) throws Exception {
		try {
			OAuthAccessTokenDTO tokenDTO = acctUtil.createOrUpdateIntegration(partner);
			boolean retVal = acctIntMgr.syncPayerInfo(acctIntMgr.getCurrentDomainId(), partner, tokenDTO);
			insightsManager.updateTINInsight(acctIntMgr.getCurrentDomainId());

			return retVal;

		} catch (Exception e) {
			handleException(e, "Error syncing payer info");
		}
		return false;
	}

	@DgsMutation
	public boolean syncPayees(@InputArgument("partner") String partner, @InputArgument("filingYear") String filingYear) throws Exception {
		try {
			OAuthAccessTokenDTO tokenDTO = acctUtil.createOrUpdateIntegration(partner);
			return acctIntMgr.syncPayees(acctIntMgr.getCurrentDomainId(), partner, tokenDTO, filingYear);
		} catch (Exception e) {
			handleException(e, "Error syncing payees");
		}
		return false;
	}

	@DgsMutation
	public boolean updateBoxMappings(@InputArgument("input") BoxMappingsInput newMappings) throws Exception {
		OAuthAccessTokenDTO tokenDTO = acctUtil.createOrUpdateIntegration(newMappings.partner);
		try {
			return accountingIntegrationOrchestrator.updateBoxMappings(acctIntMgr.getCurrentDomainId(), newMappings, tokenDTO);
		} catch (Exception e) {
			handleException(e, "Error updating box mappings");
		}
		return false;
	}

	@DgsMutation
	public boolean deleteAccountingIntegration(@InputArgument("partner") String partner) throws Exception {
		try {
			return acctIntMgr.deleteAccountingIntegration(acctIntMgr.getCurrentDomainId(), partner);
		} catch (Exception e) {
			handleException(e, "Error deleting accounting integration");
		}
		return false;
	}

}