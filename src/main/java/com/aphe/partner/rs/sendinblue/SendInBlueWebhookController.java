package com.aphe.partner.rs.sendinblue;

import java.util.Date;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aphe.contractor.model.enums.EmailStatus;
import com.aphe.contractor.services.FilingsStatusManager;

@RestController
@Validated
@RequestMapping(path = "/webhooks", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
public class SendInBlueWebhookController {

	private static Logger logger = LoggerFactory.getLogger(SendInBlueWebhookController.class);

	@Autowired
	FilingsStatusManager filingsStatusMgr;

	
	/**
	 * CURL command to simulate a webhook event 
curl -k -d '{"event":"opened","email":"<EMAIL>","id":271494,"date":"2020-09-26 01:47:39","ts":1601077659,"message-id":"<<EMAIL>>","ts_event":1601077659,"subject":"Important tax document","sending_ip":"*************","ts_epoch":1601110059728,"template_id":6}' \
-H "Content-Type: application/json" \
"https://api-local.1099smartfile.com:8083/webhooks/sendinblue"

	 */
	
	
	
	@PostMapping(path = "/sendinblue")
	public void ackSendInBlueEvent(HttpServletRequest request, HttpServletResponse response, @RequestBody String json) throws Exception {
		try {
			JSONObject payload = new JSONObject(json);
			String messageId = payload.getString("message-id");
			String evetntType = payload.getString("event");

			EmailStatus newStatus = null;

			if ("opened".equalsIgnoreCase(evetntType) || "unique_opened".equalsIgnoreCase(evetntType)) {
				newStatus = EmailStatus.Opened;
			} else if ("delivered".equalsIgnoreCase(evetntType)) {
				newStatus = EmailStatus.Delivered;
			} else if ("soft_bounce".equalsIgnoreCase(evetntType) || "hard_bounce".equalsIgnoreCase(evetntType)) {
				newStatus = EmailStatus.Bounced;
			} else if ("error".equalsIgnoreCase(evetntType) || "blocked".equalsIgnoreCase(evetntType) || "invalid_email".equalsIgnoreCase(evetntType)
					|| "unsubscribed".equalsIgnoreCase(evetntType)) {
				newStatus = EmailStatus.Errored;
			}
			if (newStatus != null && messageId != null) {
				filingsStatusMgr.updateEmailStatus(messageId, newStatus, "Update from email system", new Date());
			}
		} catch (Exception e) {
			logger.error("Attention Required: Error processing email tracking event. " + e.getMessage(), e);
			throw e;
		}
	}
}
