package com.aphe.partner.model;

import com.aphe.common.model.BaseEntity;
import org.eclipse.persistence.annotations.Cache;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "p_accountingintegrations")
@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true)
public class AccountingIntegration extends BaseEntity {

	@Column(name = "DOMAIN_ID")
	private String domainId;

	@Enumerated(EnumType.STRING)
	@Column(name = "PARTNER")
	private AccountingIntegrationPartner partner;

	@Column(name = "ACCOUNTS_LAST_SYNC_DATE")
	private Date accountsLastSyncDate;

	@Column(name = "VENDORS_LAST_SYNC_DATE")
	private Date vendorsLastSyncDate;

	@Column(name = "TRANSACTIONS_LAST_SYNC_DATE")
	private Date transactionsLastSyncDate;

	public String getDomainId() {
		return domainId;
	}

	public void setDomainId(String domainId) {
		this.domainId = domainId;
	}

	public AccountingIntegrationPartner getPartner() {
		return partner;
	}

	public void setPartner(AccountingIntegrationPartner partner) {
		this.partner = partner;
	}

	public Date getAccountsLastSyncDate() {
		return accountsLastSyncDate;
	}

	public void setAccountsLastSyncDate(Date accountsLastSyncDate) {
		this.accountsLastSyncDate = accountsLastSyncDate;
	}

	public Date getVendorsLastSyncDate() {
		return vendorsLastSyncDate;
	}

	public void setVendorsLastSyncDate(Date vendorsLastSyncDate) {
		this.vendorsLastSyncDate = vendorsLastSyncDate;
	}

	public Date getTransactionsLastSyncDate() {
		return transactionsLastSyncDate;
	}

	public void setTransactionsLastSyncDate(Date transactionsLastSyncDate) {
		this.transactionsLastSyncDate = transactionsLastSyncDate;
	}
}
