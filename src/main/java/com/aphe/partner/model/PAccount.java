package com.aphe.partner.model;

import com.aphe.common.model.BaseEntity;
import org.eclipse.persistence.annotations.Cache;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "p_accounts")
@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true)
public class PAccount extends BaseEntity {

	@Column(name = "INTEGRATION_ID")
	private long integrationId;

	@Column(name = "ACCOUNT_ID")
	private String accountId;

	@Column(name = "NAME")
	private String name;

	@Column(name = "FULLY_QUALIFIED_NAME")
	private String fullyQualifiedName;

	@Column(name = "ACCT_NUM")
	private String acctNum;

	@Column(name = "DESCRIPTION")
	private String description;

	@Column(name = "ACTIVE")
	private boolean isActive;

	@Column(name = "CREATE_TIME")
	private Date createTime;

	@Column(name = "LAST_UPDATED_TIME")
	private Date lastUpdatedTime;

	@Column(name = "PARENT_ID")
	private String parentId;

	@Column(name = "SUB_ACCOUNT")
	private boolean isSubAccount;

	@Column(name = "CLASSIFICATION")
	private String classification;

	@Column(name = "ACCOUNT_TYPE")
	private String accountType;

	@Column(name = "ACCOUNT_SUB_TYPE")
	private String accountSubType;

	@Column(name = "CURRENT_BALANCE")
	private BigDecimal currentBalance;

	@Column(name = "CURRENT_BALANCE_WITH_SUB_ACCOUNTS")
	private BigDecimal currentBalanceWithSubAccounts;

	@Column(name = "SYNC_TOKEN")
	private String syncToken;

	@Column(name = "FROM_TYPE")
	private String formType;

	@Column(name = "BOX_TYPE")
	private String boxType;

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFullyQualifiedName() {
		return fullyQualifiedName;
	}

	public void setFullyQualifiedName(String fullyQualifiedName) {
		this.fullyQualifiedName = fullyQualifiedName;
	}

	public String getAcctNum() {
		return acctNum;
	}

	public void setAcctNum(String acctNum) {
		this.acctNum = acctNum;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean isActive) {
		this.isActive = isActive;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getLastUpdatedTime() {
		return lastUpdatedTime;
	}

	public void setLastUpdatedTime(Date lastUpdatedTime) {
		this.lastUpdatedTime = lastUpdatedTime;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public boolean isSubAccount() {
		return isSubAccount;
	}

	public void setSubAccount(boolean isSubAccount) {
		this.isSubAccount = isSubAccount;
	}

	public String getClassification() {
		return classification;
	}

	public void setClassification(String classification) {
		this.classification = classification;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getAccountSubType() {
		return accountSubType;
	}

	public void setAccountSubType(String accountSubType) {
		this.accountSubType = accountSubType;
	}

	public BigDecimal getCurrentBalance() {
		return currentBalance;
	}

	public void setCurrentBalance(BigDecimal currentBalance) {
		this.currentBalance = currentBalance;
	}

	public BigDecimal getCurrentBalanceWithSubAccounts() {
		return currentBalanceWithSubAccounts;
	}

	public void setCurrentBalanceWithSubAccounts(BigDecimal currentBalanceWithSubAccounts) {
		this.currentBalanceWithSubAccounts = currentBalanceWithSubAccounts;
	}

	public String getSyncToken() {
		return syncToken;
	}

	public void setSyncToken(String syncToken) {
		this.syncToken = syncToken;
	}

	public long getIntegrationId() {
		return integrationId;
	}

	public void setIntegrationId(long integrationId) {
		this.integrationId = integrationId;
	}

	public String getFormType() {
		return formType;
	}

	public void setFormType(String formType) {
		this.formType = formType;
	}

	public String getBoxType() {
		return boxType;
	}

	public void setBoxType(String boxType) {
		this.boxType = boxType;
	}
}
