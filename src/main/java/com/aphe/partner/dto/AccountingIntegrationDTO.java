package com.aphe.partner.dto;

import com.aphe.partner.model.AccountingIntegrationPartner;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

public class AccountingIntegrationDTO {

	public Long id;

	@Min(1)
	public String domainId;

	@NotNull
	public AccountingIntegrationPartner partner;

	public Date accountsLastSyncDate;
	public Date vendorsLastSyncDate;
	public Date transactionsLastSyncDate;

}
