package com.aphe.partner.dto.actimport;

import java.math.BigDecimal;

public class PTransactionLineDTO {
    public String lineId;
    public String desc;
    public String accountId;
    public BigDecimal amount = new BigDecimal("0.00");

    public boolean excluded = false;
    public String exclusionReason;
    public boolean errored = false;
    public String errorMessage;


    public PTransactionLineDTO(String lineId, String accountId, BigDecimal amount, String desc) {
        this.lineId = lineId;
        this.accountId = accountId;
        this.amount = amount != null ? amount : BigDecimal.ZERO;
        this.desc = desc;
    }
}
