package com.aphe.contractor.tasks.email;

import com.aphe.common.mail.Email;
import com.aphe.common.mail.MailService;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.jobrunr.scheduling.BackgroundJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EmailQueueDispatcher {

    @Autowired
    private MailService mailService;

    public void dispatchEmails() {
        CronJobAuthenticationUtil.configureAuthentication("superadmin");
        List<Email> queue = mailService.getQueuedEmails();
        for (Email email : queue) {
            BackgroundJob.enqueue(() -> mailService.sendEmail(email.getId()));
        }
        CronJobAuthenticationUtil.cleanAuthentication();
    }
}