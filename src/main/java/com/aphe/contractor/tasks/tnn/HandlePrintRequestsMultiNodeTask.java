package com.aphe.contractor.tasks.tnn;

import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.PrintSub;
import com.aphe.contractor.model.enums.CountryCode;
import com.aphe.contractor.model.enums.PrintStatus;
import com.aphe.contractor.services.*;
import com.aphe.contractor.tasks.TaskUtil;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.model.EFSFilingAddress;
import com.aphe.efs.model.enums.PrintCopyType;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.efs.services.filings.FormGenerationUtil;
import com.aphe.efs.services.filings.PrintCopyFileAndPages;
import com.aphe.print.lob.LobClientFactory;
import com.lob.model.Address;
import com.lob.model.Letter;
import com.lob.net.LobResponse;
import com.lob.net.RequestOptions;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to take print requests that are ready to be processed and send them to lob.
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class HandlePrintRequestsMultiNodeTask extends ApheBackgroundTask {

	Logger logger = LoggerFactory.getLogger(HandlePrintRequestsMultiNodeTask.class);
	public static final String PRINT_SUBS = "printSubs";
	public static final String PRINT_SUBS_SENT = "printSubsSent";
	public static final String PRINT_SUBS_FAILED = "printSubsFailed";
	public static final String FAILURE_REASONS = "failureReasons";


	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Autowired
	FilingsStatusManager filingsStatusMgr;

	@Autowired
	FormGenerationUtil formGenerationUtil;

	@Value("${aphe.efs.tnnFormsDir}")
	public String tnnFormsDir;

	@Autowired
	LobClientFactory lobClientFactory;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	MailManager mailManager;
    @Autowired
    private TaskUtil taskUtil;

	//TODO: This should probably run, but only after filing status update job runs.
	@Recurring(id = "domainservice-print-forms-job", interval = "PT4H")
	@Override
	public void startTask() {
		jobName = "domainservice-print-forms-job";
		chunkSize = 1;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		//		//logger.info("Start of executing the taskName=HandlePrintRequestsTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");
		Set<Long> domainsWithPrintSubs = new HashSet<>();
		List<PrintSub> printSubsToBePrinted = subManager.findByPrintStatus(PrintStatus.Queued);
		for (PrintSub printSub : printSubsToBePrinted) {
			domainsWithPrintSubs.add(printSub.getFiling().getPayer().getDomainId());
		}
		CronJobAuthenticationUtil.cleanAuthentication();
		return domainsWithPrintSubs.stream().map(String::valueOf).collect(Collectors.toList());
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
		Long domainId = Long.parseLong(unitOfWork);
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		try {
			List<PrintSub> printSubs = subManager.findByDomainIdAndPrintStatus(domainId, PrintStatus.Queued);
			List<Long> efsFilingIds = printSubs.stream().map(printSub -> {
				Filing f = printSub.getFiling();
				FedSub fedSub = f.getFedSub();
				return fedSub.getFilingRequestId();
			}).collect(Collectors.toList());
			List<EFSFiling> efsFilings = efsFilingsMgr.getFilings(efsFilingIds);

			int printSubsCount = printSubs.size();
			int printSubsSent = 0;
			int printSubsFailed = 0;
			Map<String, String> failureReasons = new HashMap<>();
			for (EFSFiling efsFiling : efsFilings) {
				String failureReason = submitToPrintSystem(efsFiling);
				if (failureReason == null) {
					printSubsSent++;
				} else {
					failureReasons.put(efsFiling.getId().toString(), failureReason);
					printSubsFailed++;
				}
			}

			Map<String, String> values = new HashMap<>();
			values.put(PRINT_SUBS, String.valueOf(printSubsCount));
			values.put(PRINT_SUBS_SENT, String.valueOf(printSubsSent));
			values.put(PRINT_SUBS_FAILED, String.valueOf(printSubsFailed));
			values.put(FAILURE_REASONS, taskUtil.buildFailureReasons(failureReasons));
			if(printSubsFailed > 0) {
				return new ApheExecutionData(ApheExecutionResult.FAILED, "Some print requests could not be sent.", values);
			} else {
				return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
			}
		} catch (Exception e) {
			logger.error("Error processing print requests: ", e);
			return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
		} finally {
			CronJobAuthenticationUtil.cleanAuthentication();
		}
	}

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of(PRINT_SUBS, PRINT_SUBS_SENT, PRINT_SUBS_FAILED, FAILURE_REASONS);
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
		if (errored == 0) {
			return;
		}
		long emailStartTime = System.currentTimeMillis();
		mailManager.sendSystemStatusEmail(subject, emailHTML);
		long emailTime = System.currentTimeMillis() - emailStartTime;
		logger.info("Sent summary email in {}ms", emailTime);
	}


	private String submitToPrintSystem(EFSFiling efsFiling) throws ApheDataValidationException {
		String filePath = null;
		try {
			RequestOptions mailRequestOptions = lobClientFactory.getRequestOptionsForMail();
			Map<String, String> letterMetaData = lobClientFactory.getLetterMetadata();

			if(mailRequestOptions == null) {
				return "Not available print keys. Cannot proceed with printing.";
			}

			//get the from address..
			String fromName = efsFiling.getPayerName1();
			EFSFilingAddress fromAddress = efsFiling.getPayerAddress();

			//get the to address..
			String toName = efsFiling.getPayeeName1();
			EFSFilingAddress toAddress = efsFiling.getPayeeAddress();

			//get the PDF
			List<Long> efsFilingIdArray = new ArrayList<>();
			efsFilingIdArray.add(efsFiling.getId());
			PrintCopyFileAndPages printCopyFileAndPages = formGenerationUtil.getPrintCopyFileNameAndPages(efsFiling.getFilingReturnType(), efsFiling.getFilingYear(), PrintCopyType.RecipientMailCopy);
			String fileName = efsFilingsMgr.generateForms(efsFilingIdArray, false, PrintCopyType.RecipientMailCopy.name(), false);
			String folderPath = tnnFormsDir + File.separator + fileName;
			File f = new File(folderPath);
			if (f.isDirectory()) {
				for (File entryFile : f.listFiles()) {
					filePath = entryFile.getAbsolutePath();
					break;
				}
			}
			final File file = new File(filePath);

			//create request to lob.
			String toZipCode = toAddress.getZipCode();
			toZipCode = StringUtil.formatZipCode(toZipCode);

			String fromZipCode = fromAddress.getZipCode();
			fromZipCode = StringUtil.formatZipCode(fromZipCode);

			String payeeCity = toAddress.getCity();
			String payeeCountry = toAddress.getCountry();
			String payeeState = toAddress.getState();
			//If payee country is one of US territories, then set the state to the country.
			if (payeeCountry != null && CountryCode.USTerritories.contains(payeeCountry)) {
				payeeCity = (payeeCity + " " + payeeState).trim();
				payeeState = payeeCountry;
				payeeCountry = "US";
			}

			String payerCity = fromAddress.getCity();
			String payerState = fromAddress.getState();
			String payerCountry = fromAddress.getCountry();
			if (payerCountry != null && CountryCode.USTerritories.contains(payerCountry)) {
				payerCity = (payerCity + " " + payerState).trim();
				payerState = payerCountry;
				payerCountry = "US";
			}

			LobResponse<Letter> response = new Letter.RequestBuilder()
					.setDescription(efsFiling.getFilingReturnType().getFormName() + "-" + efsFiling.getFilingYear() + "-" + efsFiling.getPayeeName1() + "-"
							+ efsFiling.getClientRefId())
					.setFile(file)
					.setColor(false)
					.setDoubleSided(false)
					.setAddressPlacement(printCopyFileAndPages.includeAddressPage ? "insert_blank_page" : "top_first_page")
					.setMailType("usps_first_class")
					.setReturnEnvelope(false)
					.setMetadata(letterMetaData)
					.setTo(
							new Address.RequestBuilder().setName(toName)
									.setLine1(toAddress.getStreetName1())
									.setLine2(toAddress.getStreetName2() != null ? toAddress.getStreetName2() : "")
									.setCity(payeeCity)
									.setState(payeeState)
									.setZip(toZipCode))
					.setFrom(
							new Address.RequestBuilder().setName(fromName)
									.setLine1(fromAddress.getStreetName1())
									.setLine2(fromAddress.getStreetName2() != null ? fromAddress.getStreetName2() : "")
									.setCity(payerCity)
									.setState(payerState)
									.setZip(fromZipCode))
					.create(mailRequestOptions);


			//store tracking id on tnn side.
			int responseCode = response.getResponseCode();
			if (responseCode >= 200 || responseCode <= 300) {
				Letter letter = response.getResponseBody();
				String letterId = letter.getId();
				LocalDate expectedDeliveryLocalDate = letter.getExpectedDeliveryDate();
				Date expectedDeliveryDate = Date.from(expectedDeliveryLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
				localFilingMgr.updatePrintCopyTrackingId(efsFiling.getClientRefId(), expectedDeliveryDate, letterId);

				//delete the address ids.
				Address lobAddressFrom = letter.getFrom();
				Address lobAddressTo = letter.getTo();
				Address.delete(lobAddressFrom.getId(), mailRequestOptions);
				Address.delete(lobAddressTo.getId(), mailRequestOptions);

				//For Sandbox and test accounts delete the print request also.
				if (efsFiling.isTestFiling() || !PropertiesManager.isProd()) {
					LobResponse<Letter> deleteLetterResponse = Letter.delete(letterId, mailRequestOptions);
					Letter deletedLetter = deleteLetterResponse.getResponseBody();
					if (!deletedLetter.isDeleted()) {
						logger.error("Error deleting a lob print request for test filing or sandbox request");
					} else {
						filingsStatusMgr.updatePrintStatus(letterId, PrintStatus.Cancelled, "Cancelled the print request for test filing or a sandbox environment.",
								new Date());
					}
				} else {
					lobClientFactory.incrementMailCount(mailRequestOptions.getApiKey());
				}
			} else {
				localFilingMgr.updatePrintStatusToFail(efsFiling.getClientRefId(), "A non 200 response code from print system: " + responseCode);
				return "A non 200 response code from print system: " + responseCode;
			}
		} catch (Exception e) {
			logger.error("Attention Required: Error sending the print request for clientRefId=" + efsFiling.getClientRefId(), e);
			localFilingMgr.updatePrintStatusToFail(efsFiling.getClientRefId(), "Error submitting to print system");
			return e.getMessage();
		} finally {
			//delete the un-encrypted PDF file from local dir in finally block
			if (filePath != null) {
				try {
					File f = new File(filePath);
					f.delete();
				} catch (Exception e) {
					logger.error("Attention Required: Failed to delete the file filePath=" + filePath);
				}
			}
		}
		return null;
	}

}
