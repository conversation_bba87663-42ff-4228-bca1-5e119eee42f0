package com.aphe.contractor.tasks.tnn;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.EmailSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.PrintSub;
import com.aphe.contractor.model.enums.EmailStatus;
import com.aphe.contractor.model.enums.PrintStatus;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.SubManager;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to notify the user about update on email and print delivery notification statues...
 */

@Component
@Profile({"test"})
public class HandleEmailPrintNotificationTask extends GenericBackgroundTask {

    @Autowired
    FilingManager localFilingMgr;

    @Autowired
    SubManager subManager;

    @Autowired
    AuthManager authManager;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    MailUtil mailUtil;

    @Autowired
    ContractorConvertUtil contractorConvertUtil;

    Logger logger = LoggerFactory.getLogger(HandleEmailPrintNotificationTask.class);

//    @Scheduled(initialDelayString = "${aphe.contractor.emailPrintNotificationTask.initialDelay}", fixedDelayString = "${aphe.contractor.emailPrintNotificationTask.fixedDelay}")
    public void sendEmailsToPayees() {

        boolean pauseTasks = isPauseTasks("domainservice-emailprintnotification-job");
        if(pauseTasks) {
            return;
        }

//        //logger.info("Start of executing the taskName=HandleEmailPrintNotificationTask status=Begin");
        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        List<EmailSub> emailSubsToBeEmailNotified = subManager.getFilingsEmailNotifiable();

        Map<Long, Set<Filing>> emailSuccessByDomain = new HashMap<>();
        Map<Long, Set<Filing>> emailErroredByDomain = new HashMap<>();
        Map<Long, Set<Filing>> emailBouncedByDomain = new HashMap<>();

        for (EmailSub emailSub : emailSubsToBeEmailNotified) {
            if (EmailStatus.Emailed == emailSub.getEmailStatus()) {
                Set<Filing> domainFilings = emailSuccessByDomain.get(emailSub.getFiling().getPayer().getDomainId());
                if (domainFilings == null) {
                    domainFilings = new HashSet<>();
                    emailSuccessByDomain.put(emailSub.getFiling().getPayer().getDomainId(), domainFilings);
                }
                domainFilings.add(emailSub.getFiling());
            } else if (EmailStatus.Errored == emailSub.getEmailStatus()) {
                Set<Filing> domainFilings = emailErroredByDomain.get(emailSub.getFiling().getPayer().getDomainId());
                if (domainFilings == null) {
                    domainFilings = new HashSet<>();
                    emailErroredByDomain.put(emailSub.getFiling().getPayer().getDomainId(), domainFilings);
                }
                domainFilings.add(emailSub.getFiling());
            } else if (EmailStatus.Bounced == emailSub.getEmailStatus()) {
                Set<Filing> domainFilings = emailBouncedByDomain.get(emailSub.getFiling().getPayer().getDomainId());
                if (domainFilings == null) {
                    domainFilings = new HashSet<>();
                    emailBouncedByDomain.put(emailSub.getFiling().getPayer().getDomainId(), domainFilings);
                }
                domainFilings.add(emailSub.getFiling());
            }
        }

        List<PrintSub> printSubsToBePrintNotified = subManager.getFilingsPrintNotifiable();
        Map<Long, Set<Filing>> printMailedByDomain = new HashMap<>();
        Map<Long, Set<Filing>> printErroredByDomain = new HashMap<>();
        Map<Long, Set<Filing>> printDeliveryByDomain = new HashMap<>();
        Map<Long, Set<Filing>> printReturnedByDomain = new HashMap<>();

        for (PrintSub printSub : printSubsToBePrintNotified) {
            if (PrintStatus.Mailing == printSub.getPrintStatus()) {
                Set<Filing> domainFilings = printMailedByDomain.get(printSub.getFiling().getPayer().getDomainId());
                if (domainFilings == null) {
                    domainFilings = new HashSet<>();
                    printMailedByDomain.put(printSub.getFiling().getPayer().getDomainId(), domainFilings);
                }
                domainFilings.add(printSub.getFiling());
            } else if (PrintStatus.Errored == printSub.getPrintStatus()) {
                Set<Filing> domainFilings = printErroredByDomain.get(printSub.getFiling().getPayer().getDomainId());
                if (domainFilings == null) {
                    domainFilings = new HashSet<>();
                    printErroredByDomain.put(printSub.getFiling().getPayer().getDomainId(), domainFilings);
                }
                domainFilings.add(printSub.getFiling());
            } else if (PrintStatus.ProcessedForDelivery == printSub.getPrintStatus()) {
                Set<Filing> domainFilings = printDeliveryByDomain.get(printSub.getFiling().getPayer().getDomainId());
                if (domainFilings == null) {
                    domainFilings = new HashSet<>();
                    printDeliveryByDomain.put(printSub.getFiling().getPayer().getDomainId(), domainFilings);
                }
                domainFilings.add(printSub.getFiling());
            } else if (PrintStatus.ReturnedToSender == printSub.getPrintStatus()) {
                Set<Filing> domainFilings = printReturnedByDomain.get(printSub.getFiling().getPayer().getDomainId());
                if (domainFilings == null) {
                    domainFilings = new HashSet<>();
                    printReturnedByDomain.put(printSub.getFiling().getPayer().getDomainId(), domainFilings);
                }
                domainFilings.add(printSub.getFiling());
            }
        }


        Set<Long> allDomainIds = new HashSet<>();
//        allDomainIds.addAll(emailSuccessByDomain.keySet());
//        allDomainIds.addAll(emailErroredByDomain.keySet());
        allDomainIds.addAll(emailBouncedByDomain.keySet());
//        allDomainIds.addAll(printMailedByDomain.keySet());
//        allDomainIds.addAll(printErroredByDomain.keySet());
//        allDomainIds.addAll(printDeliveryByDomain.keySet());
        allDomainIds.addAll(printReturnedByDomain.keySet());

        Map<Long, String> domainNames = new HashMap<>();

        if (allDomainIds.size() > 0) {
            // First get all the users of these domainIds.
            Map<Long, List<UserDTO>> usersByDomain = getUsersByDomain(allDomainIds);

            // First for all domain, add payer's email address if there is one...
            for (Long domainId : allDomainIds) {
                List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
                try {
                    DomainDTO domainDTO = domainMgr.getDomain(domainId);
                    String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
                    domainNames.put(domainId, displayName);
                    if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
                        UserDTO userDto = new UserDTO();
                        userDto.setEmail(domainDTO.emailAddress);
                        userDto.setFirstName(displayName);
                        userDto.setLastName("");
                        domainEmailUsers.add(userDto);
                    }
                } catch (NumberFormatException | ApheForbiddenException e2) {
                }
            }


            // Now send the emails by each domainId for bounced emails.
            sendEmailStatusUpdates(emailBouncedByDomain, usersByDomain, domainNames);

            // Now send the emails by each domainId for returned to sender filings
            sendPrintStatusUpdates(printReturnedByDomain, usersByDomain, domainNames);
        }


        CronJobAuthenticationUtil.cleanAuthentication();
//        //logger.info("Done executing the taskName=HandleEmailPrintNotificationTask status=End");
    }

    private Map<Long, List<UserDTO>> getUsersByDomain(Set<Long> allDomainIds) {
        String token = authManager.getTokenForSystemUser();
        Map<Long, List<UserDTO>> usersByDomain = authManager.getDomainUsersRemote(token, allDomainIds);
        authManager.deleteToken(token);
        return usersByDomain;
    }

    private void sendEmailStatusUpdates(Map<Long, Set<Filing>> byDomain, Map<Long, List<UserDTO>> usersByDomain, Map<Long, String> domainNames) {
        for (Long domainId : byDomain.keySet()) {
            try {
                Set<Filing> filings = byDomain.get(domainId);
                List<Long> filingIds = filings.stream().map(v -> v.getId()).collect(Collectors.toList());
                List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
                String displayName = domainNames.get(domainId);
                localFilingMgr.updateEmailNotify(filingIds, false);
                mailUtil.sendEmailBouncedEmail(filings, domainEmailUsers, displayName);
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void sendPrintStatusUpdates(Map<Long, Set<Filing>> byDomain, Map<Long, List<UserDTO>> usersByDomain, Map<Long, String> domainNames) {
        for (Long domainId : byDomain.keySet()) {
            try {
                Set<Filing> filings = byDomain.get(domainId);
                List<Long> filingIds = filings.stream().map(v -> v.getId()).collect(Collectors.toList());
                List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
                String displayName = domainNames.get(domainId);
                localFilingMgr.updatePrintNotify(filingIds, false);
                mailUtil.sendPrintReturnedEmail(filings, domainEmailUsers, displayName);
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
