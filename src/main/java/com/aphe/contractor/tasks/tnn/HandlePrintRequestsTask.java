package com.aphe.contractor.tasks.tnn;

import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.PrintSub;
import com.aphe.contractor.model.enums.CountryCode;
import com.aphe.contractor.model.enums.PrintStatus;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.FilingsStatusManager;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.SubManager;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.model.EFSFilingAddress;
import com.aphe.efs.model.enums.PrintCopyType;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.efs.services.filings.FormGenerationUtil;
import com.aphe.efs.services.filings.PrintCopyFileAndPages;
import com.aphe.print.lob.LobClientFactory;
import com.lob.model.Address;
import com.lob.model.Letter;
import com.lob.net.LobResponse;
import com.lob.net.RequestOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Auto task to take print requests that are ready to be processed and send them to lob.
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class HandlePrintRequestsTask extends GenericBackgroundTask {

	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Autowired
	FilingsStatusManager filingsStatusMgr;

	@Autowired
	FormGenerationUtil formGenerationUtil;

	@Value("${aphe.efs.tnnFormsDir}")
	public String tnnFormsDir;

	@Autowired
	LobClientFactory lobClientFactory;

	@Autowired
	MailUtil mailUtil;

	Logger logger = LoggerFactory.getLogger(HandlePrintRequestsTask.class);

	@Scheduled(initialDelayString = "${aphe.contractor.printTask.initialDelay}", fixedDelayString = "${aphe.contractor.printTask.fixedDelay}")
	public void handlePrintRequests() {

		boolean pauseTasks = isPauseTasks("domainservice-submit-prints-job");
		if(pauseTasks) {
			return;
		}

//		//logger.info("Start of executing the taskName=HandlePrintRequestsTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		List<PrintSub> printSubsToBePrinted = subManager.findByPrintStatus(PrintStatus.Queued);
		List<Long> efsFilingIds = printSubsToBePrinted.stream().map(printSub -> {
			Filing f = printSub.getFiling();
			FedSub fedSub = f.getFedSub();
			return fedSub.getFilingRequestId();
		}).collect(Collectors.toList());

		try {
			List<EFSFiling> efsFilings = efsFilingsMgr.getFilings(efsFilingIds);

			for (EFSFiling efsFiling : efsFilings) {

				String filePath = null;
				//Protect with try/catch.
				try {
					RequestOptions mailRequestOptions = lobClientFactory.getRequestOptionsForMail();
					Map<String, String> letterMetaData = lobClientFactory.getLetterMetadata();

					if(mailRequestOptions == null) {
						logger.error("Not available print keys. Cannot proceed with printing.");
						break;
					}



					//get the from address..
					String fromName = efsFiling.getPayerName1();
					EFSFilingAddress fromAddress = efsFiling.getPayerAddress();

					//get the to address..
					String toName = efsFiling.getPayeeName1();
					EFSFilingAddress toAddress = efsFiling.getPayeeAddress();

					//get the PDF
					List<Long> efsFilingIdArray = new ArrayList<>();
					efsFilingIdArray.add(efsFiling.getId());
					PrintCopyFileAndPages printCopyFileAndPages = formGenerationUtil.getPrintCopyFileNameAndPages(efsFiling.getFilingReturnType(), efsFiling.getFilingYear(), PrintCopyType.RecipientMailCopy);
					String fileName = efsFilingsMgr.generateForms(efsFilingIdArray, false, PrintCopyType.RecipientMailCopy.name(), false);
					String folderPath = tnnFormsDir + File.separator + fileName;
					File f = new File(folderPath);
					if (f.isDirectory()) {
						for (File entryFile : f.listFiles()) {
							filePath = entryFile.getAbsolutePath();
							break;
						}
					}
					final File file = new File(filePath);

					//create request to lob.
					String toZipCode = toAddress.getZipCode();
					toZipCode = StringUtil.formatZipCode(toZipCode);
					
					String fromZipCode = fromAddress.getZipCode();
					fromZipCode = StringUtil.formatZipCode(fromZipCode);

					String payeeCity = toAddress.getCity();
					String payeeCountry = toAddress.getCountry();
					String payeeState = toAddress.getState();
					//If payee country is one of US territories, then set the state to the country.
					if (payeeCountry != null && CountryCode.USTerritories.contains(payeeCountry)) {
						payeeCity = (payeeCity + " " + payeeState).trim();
						payeeState = payeeCountry;
						payeeCountry = "US";
					}

					String payerCity = fromAddress.getCity();
					String payerState = fromAddress.getState();
					String payerCountry = fromAddress.getCountry();
					if (payerCountry != null && CountryCode.USTerritories.contains(payerCountry)) {
						payerCity = (payerCity + " " + payerState).trim();
						payerState = payerCountry;
						payerCountry = "US";
					}



					LobResponse<Letter> response = new Letter.RequestBuilder()
							.setDescription(efsFiling.getFilingReturnType().getFormName() + "-" + efsFiling.getFilingYear() + "-" + efsFiling.getPayeeName1() + "-"
									+ efsFiling.getClientRefId())
							.setFile(file)
							.setColor(false)
							.setDoubleSided(false)
							.setAddressPlacement(printCopyFileAndPages.includeAddressPage ? "insert_blank_page" : "top_first_page")
							.setMailType("usps_first_class")
							.setReturnEnvelope(false)
							.setMetadata(letterMetaData)
							.setTo(
									new Address.RequestBuilder().setName(toName)
									.setLine1(toAddress.getStreetName1())
									.setLine2(toAddress.getStreetName2() != null ? toAddress.getStreetName2() : "")
									.setCity(payeeCity)
									.setState(payeeState)
									.setZip(toZipCode))
							.setFrom(
									new Address.RequestBuilder().setName(fromName)
									.setLine1(fromAddress.getStreetName1())
									.setLine2(fromAddress.getStreetName2() != null ? fromAddress.getStreetName2() : "")
									.setCity(payerCity)
									.setState(payerState)
									.setZip(fromZipCode))
							.create(mailRequestOptions);


					//store tracking id on tnn side.
					int responseCode = response.getResponseCode();
					if (responseCode >= 200 || responseCode <= 300) {
						Letter letter = response.getResponseBody();
						String letterId = letter.getId();
						LocalDate expectedDeliveryLocalDate = letter.getExpectedDeliveryDate();
						Date expectedDeliveryDate = Date.from(expectedDeliveryLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
						localFilingMgr.updatePrintCopyTrackingId(efsFiling.getClientRefId(), expectedDeliveryDate, letterId);

						//delete the address ids.
						Address lobAddressFrom = letter.getFrom();
						Address lobAddressTo = letter.getTo();
						Address.delete(lobAddressFrom.getId(), mailRequestOptions);
						Address.delete(lobAddressTo.getId(), mailRequestOptions);

						//For Sandbox and test accounts delete the print request also.
						if (efsFiling.isTestFiling() || !PropertiesManager.isProd()) {
							LobResponse<Letter> deleteLetterResponse = Letter.delete(letterId, mailRequestOptions);
							Letter deletedLetter = deleteLetterResponse.getResponseBody();
							if (!deletedLetter.isDeleted()) {
								logger.error("Error deleting a lob print request for test filing or sandbox request");
							} else {
								filingsStatusMgr.updatePrintStatus(letterId, PrintStatus.Cancelled, "Cancelled the print request for test filing or a sandbox environment.",
										new Date());
							}
						} else {
							lobClientFactory.incrementMailCount(mailRequestOptions.getApiKey());
						}

					} else {
						logger.error("Attention Required: Error sending the print request for clientRefId=" + efsFiling.getClientRefId());
						localFilingMgr.updatePrintStatusToFail(efsFiling.getClientRefId(), "A non 200 response code from print system: " + responseCode);
					}
				} catch (Exception e) {
					logger.error("Attention Required: Error sending the print request for clientRefId=" + efsFiling.getClientRefId());
					logger.error("Error queueing the print copy", e);
					logger.error(e.getMessage());
					localFilingMgr.updatePrintStatusToFail(efsFiling.getClientRefId(), "Error submitting to print system");
				} finally {
					//delete the un-encrypted PDF file from local dir in finally block
					if (filePath != null) {
						try {
							File f = new File(filePath);
							f.delete();
						} catch (Exception e) {
							logger.error("Attention Required: Failed to delete the file filePath=" + filePath);
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error("Attention Required: Error processing print requests: ", e);
		}

		List<PrintSub> failedPrints = subManager.findByPrintStatus(PrintStatus.Errored);
		if(failedPrints.size() > 0) {
			String subject = "Attention Required: PRINT COPY ISSUE";
			String errorMessage = String.format("There are %d print requests in errored state", failedPrints.size());
			mailUtil.sendSystemStatusEmail(subject, errorMessage);
		}

		CronJobAuthenticationUtil.cleanAuthentication();
//		//logger.info("Done executing the taskName=HandlePrintRequestsTask status=End");

	}
}
