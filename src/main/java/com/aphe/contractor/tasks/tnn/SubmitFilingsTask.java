package com.aphe.contractor.tasks.tnn;

import com.aphe.AppUtil;
import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.MappedValidationErros;
import com.aphe.common.error.exceptions.ApheDataListValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.DateUtil;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.Payee;
import com.aphe.contractor.model.TINMatchRequest;
import com.aphe.contractor.model.enums.CorrectionType;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.TINMatchResult;
import com.aphe.contractor.model.enums.TINMatchStatus;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.SubManager;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.dto.FilingDataDTO;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.insights.service.InsightsManager;
import org.hibernate.validator.constraints.ValidationErrors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to take submitted filings by the user and submit to the EFS 
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class SubmitFilingsTask extends  GenericBackgroundTask {

	@Autowired
	Environment env;

	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Autowired
	ContractorConvertUtil conversionUtil;

	@Autowired
	AuthManager authManager;

	@Autowired
	DomainMgr domainMgr;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	@Autowired
	InsightsManager insightsManager;

	private String blockOnTINCheckPropertyName = "aphe.contractor.blockFilingSubmissionOnTINCheck";

	Logger logger = LoggerFactory.getLogger(SubmitFilingsTask.class);

	@Scheduled(initialDelayString = "${aphe.contractor.submitFilingsTask.initialDelay}", fixedDelayString = "${aphe.contractor.submitFilingsTask.fixedDelay}")
	public void submitFilingsTask() {

		boolean pauseTasks = isPauseTasks("domainservice-submit-filings-job");
		if(pauseTasks) {
			return;
		}

		//logger.info("Start of executing the taskName=SubmitFilingsTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		//Fed and CFSF submissions.
		processSubmissions();

		//State self file submissions.
		//They would start with a Submitted status...
		//Change them to reminded and put up a todo task on dashboard, and when customer marks them taken care of, change the status to filed.

		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=SubmitFilingsTask status=End");

	}

	private void buildCommunicationData(HashMap<Long, List<Filing>> filingsByDomain,
											   Map<Long, String> domainNames,
											   Map<Long, List<UserDTO>> usersByDomain,
											   Map<Long, List<Long>> accountantsByDomain) {
		String token = authManager.getTokenForSystemUser();

		Set<Long> domainIds = filingsByDomain.keySet();
		usersByDomain.putAll(authManager.getDomainUsersRemote(token, domainIds));
		accountantsByDomain.putAll(authManager.getDomainAccountantsRemote(token, domainIds));
		authManager.deleteToken(token);

		for (Long domainId : domainIds) {

			List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);

			String domainName = "";

			try {
				DomainDTO domainDTO = domainMgr.getDomain(domainId);
				domainName = contractorConvertUtil.getDomainDisplayName(domainDTO);
				domainNames.put(domainId, domainName);
				//TODO: Make sure this is a valid email address. Here and where domainDTO email is being used.
				if (domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
					UserDTO userDto = new UserDTO();
					userDto.setEmail(domainDTO.emailAddress);
					userDto.setFirstName(domainName);
					userDto.setLastName("");
					domainEmailUsers.add(userDto);
				}
			} catch (NumberFormatException | ApheForbiddenException e2) {
			}
		}
	}

	//TODO: First look up all the domains that have filings to be submitted.
	// Create a task for each domain that could get run on any node.
	// The main node doesn't need to wait for any thing..
	// Each background task submits the filings and creates the respetive emails..

	private void processSubmissions() {
		// Find all the filings that have been in FilingStatus=Submitted and send them to EFS.
		// List<Filing> filingsToBeProcessed = localFilingMgr.findByStatus(FilingStatus.Submitted);
		List<FedSub> filingsToBeProcessed = subManager.findByFedSubStatusIn(Arrays.asList(FilingStatus.Submitted, FilingStatus.WaitingOnTINMatch));

		// Group them by domainId and generate the EFSFiling payload. Do for each domain in a separate transaction.
		HashMap<Long, List<Filing>> filingsByDomain = new HashMap<>();
		for (FedSub fedSub : filingsToBeProcessed) {
			Long domainId = fedSub.getFiling().getPayer().getDomainId();
			List<Filing> domainFilings = filingsByDomain.get(domainId);
			if (domainFilings == null) {
				domainFilings = new ArrayList<Filing>();
				filingsByDomain.put(domainId, domainFilings);
			}
			domainFilings.add(fedSub.getFiling());
		}

		if (filingsByDomain.keySet().size() > 0) {

			// First get all the users and domain names of these domainIds.
			Map<Long, String> domainNames = new HashMap<>();
			Map<Long, List<UserDTO>> usersByDomain =  new HashMap<>();
			Map<Long, List<Long>> accountantsByDomain =  new HashMap<>();
			buildCommunicationData(filingsByDomain, domainNames, usersByDomain, accountantsByDomain);

			for (Long domainId : filingsByDomain.keySet()) {

				List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);
				String domainName = domainNames.get(domainId);

				List<Filing> filings = filingsByDomain.get(domainId);

				Map<Long, String> conversionErrors = new HashMap<>();
				Map<Long, String> tinMatchErrors = new HashMap<>();
				Map<String, String> submissionErrors = new HashMap<>();
				Map<Long, Filing> successfulFilings = new HashMap<>();
				Map<Long, Filing> failedFilings = new HashMap<>();

				Map<FilingDataDTO, Filing> efsFilingsMap = new HashMap<>();


				//First separate out correction filings, regardless of they are ready to submit or waiting on tin match
				List<Filing> correctionFilings = filings.stream().filter(f->f.getCorrectionType() != CorrectionType.Original).collect(Collectors.toList());
				filings.removeAll(correctionFilings);


				//They are all convertible to EFSFiling. All the validation checks have passed.
				List<Filing> filingsThatCanBeSubmitted = new ArrayList<>();
				List<Filing> filingsToMoveWaitOnTINMatch = new ArrayList<>();
				for (Filing f : filings) {
					if(f.getFedSub().getStatus() == FilingStatus.Submitted) {
						boolean isTINCheckInProgress = isTINCheckInProgress(f);
						boolean skipTINCheck = isSkipTINCheck();
						if(skipTINCheck || !isTINCheckInProgress) {
							filingsThatCanBeSubmitted.add(f);
						} else {
							filingsToMoveWaitOnTINMatch.add(f);
						}
					} else if(f.getFedSub().getStatus() == FilingStatus.WaitingOnTINMatch) {
						boolean skipTINCheck = isSkipTINCheck();
						int tinMatchStatus = getTINMatchStatus(f);
						if(tinMatchStatus == 0 || skipTINCheck) {
							filingsThatCanBeSubmitted.add(f);
						} else if(tinMatchStatus == 1) {
							tinMatchErrors.put(f.getId(), getTINMatchFailure(f));
						}
					}
				}

				handleCorrectionSubmissions(correctionFilings, filingsThatCanBeSubmitted, filingsToMoveWaitOnTINMatch, tinMatchErrors);

				if(filingsToMoveWaitOnTINMatch.size() > 0) {
					List<Long> waitOnTINMatchFilingIds = filingsToMoveWaitOnTINMatch.stream().map(f->f.getId()).collect(Collectors.toList());
					List<Filing> movedToWaitingOnTINMatchFilings = localFilingMgr.waitOnTINMatchByIds(waitOnTINMatchFilingIds);
					for(Filing f : movedToWaitingOnTINMatchFilings){
//						successfulFilings.put(f.getId(), f);
					}
				}


				for (Filing f : filingsThatCanBeSubmitted) {
					if(f.getCorrectionType() == CorrectionType.Second) {
						continue;
					}

					Filing secondFiling = null;
					Filing originalFiling = null;
					if(f.getCorrectionType() == CorrectionType.First) {
						//Find second correction type.
						secondFiling = filingsThatCanBeSubmitted.stream()
								.filter(f2-> f2.getCorrectionType() == CorrectionType.Second && f2.getOriginalFilingId().longValue() == f.getOriginalFilingId().longValue())
								.findFirst().orElse(null);
						originalFiling = localFilingMgr.getOriginalFiling(f.getId());
					}

					try {
						List<FilingDataDTO> efsFilings = conversionUtil.convertToEFSFilingDTOs(f, domainId, true, secondFiling, originalFiling);
						for(FilingDataDTO dto : efsFilings) {
							efsFilingsMap.put(dto, f);
						}
					} catch (Exception e) {
						logger.error("Error converting a filings to EFS DTO ", e);
						conversionErrors.put(f.getId(), "Error processing your data. Please contact us.");
					}
				}

				boolean submitted = false;
				List<EFSFiling> efsFilings = null;
				try {
					List<FilingDataDTO> filingDTOs = new ArrayList<>();
					filingDTOs.addAll(efsFilingsMap.keySet());
					if (filingDTOs.size() > 0) {
						efsFilings = efsFilingsMgr.addFilings(filingDTOs);
						submitted = true;
					}
				} catch (ApheDataListValidationException e) {
					MappedValidationErros errors = e.getMappedErrors();
					logger.error("Error submitting filings: ", e);
					logger.error("Error submitting filings: " + errors.getErrors().toString());
					Map<String, ValidationErrors> mappedErrors = errors.getErrors();
					for (String id : mappedErrors.keySet()) {
						submissionErrors.put(id, errors.getErrors().toString());
					}
				} catch (Exception e) {
					logger.error("Error submitting filings: ", e);
				}


				if (submitted && efsFilings != null && efsFilings.size() > 0) {
					for (EFSFiling filing : efsFilings) {
						Filing f = localFilingMgr.receiveFilingBySubRefId(filing.getClientRefId(), filing.getId());
						//Filing could be null, because we do not track the PA second filing on our side.
						//Also this allows rest of the filings to be in good status, rather than the task exiting with error.
						if(f != null) {
							successfulFilings.put(f.getId(), f);
						} else {
							logger.error("Failed to update the filing status to received for filing refId=" + filing.getClientRefId());
						}
					}
				}

				if (conversionErrors.size() > 0) {
					try {
						List<Filing> systemRejectedFilings = localFilingMgr.systemRejectFilingsByIds(conversionErrors);
						for(Filing f : systemRejectedFilings) {
							failedFilings.put(f.getId(), f);
						}
					} catch (Exception e1) {
						logger.error("Error changing the status of failed filings to Draft ", e1);
					}
				}
				if (tinMatchErrors.size() > 0) {
					try {
						List<Filing> tinMatchErrorFilings = localFilingMgr.systemRejectFilingsByIds(tinMatchErrors);
						for(Filing f : tinMatchErrorFilings) {
							failedFilings.put(f.getId(), f);
						}
					} catch (Exception e1) {
						logger.error("Error changing the status of failed filings to Draft ", e1);
					}
				}

				if (submissionErrors.size() > 0) {
					try {
						List<Filing> submissionErrorFilings = localFilingMgr.systemRejectFilingsBySubmissionRefIds(submissionErrors);
						for(Filing f : submissionErrorFilings) {
							failedFilings.put(f.getId(), f);
						}
					} catch (Exception e1) {
						logger.error("Error changing the status of failed filings to Draft ", e1);
					}
				}

				if(successfulFilings.size() > 0) {
					// Do not send to GS and also not to the regular customers.
					if(!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
						mailUtil.sendFilingsSubmittedEmail(new HashSet<>(successfulFilings.values()), domainEmailUsers, domainName);
					}
				}

				if (failedFilings.size() > 0) {
					mailUtil.sendFilingsSystemRejectedEmail(new HashSet<>(failedFilings.values()), domainEmailUsers, domainName);
				}

				//Update insights for this domain.
				insightsManager.updateFilingCounts(domainId);


			}
		}
	}

	private void handleCorrectionSubmissions(List<Filing> correctionFilings, List<Filing> filingsThatCanBeSubmitted, List<Filing> filingsToMoveWaitOnTINMatch, Map<Long, String> tinMatchErrors) {
		if(correctionFilings.size() == 0) {
			return;
		}
		//Group them by original filing id.
		Map<Long, List<Filing>> correctionsByOriginalFilingId = correctionFilings.stream()
				.collect(Collectors.groupingBy(filing -> filing.getOriginalFilingId()));

		//For each original filing, if either first or second can not be submitted, do not submit.
		for(Long originalFilingId: correctionsByOriginalFilingId.keySet()) {
			List<Filing> corrections = correctionsByOriginalFilingId.get(originalFilingId);

			boolean canSubmit = true;
			boolean hasTinMatchError = false;
			for(Filing correctionFiling : corrections) {
				if(correctionFiling.getFedSub().getStatus() == FilingStatus.Submitted) {
					boolean isTINCheckInProgress = isTINCheckInProgress(correctionFiling);
					boolean skipTINCheck = isSkipTINCheck();
					if( skipTINCheck || !isTINCheckInProgress) {
						canSubmit = canSubmit && true;
					} else {
						filingsToMoveWaitOnTINMatch.add(correctionFiling);
						canSubmit = canSubmit && false;
					}
				} else if(correctionFiling.getFedSub().getStatus() == FilingStatus.WaitingOnTINMatch) {
					boolean skipTINCheck = isSkipTINCheck();
					int tinMatchStatus = getTINMatchStatus(correctionFiling);

					if(tinMatchStatus == 0 || skipTINCheck) {
						canSubmit = canSubmit && true;
					} else if(tinMatchStatus == 1) {
						canSubmit = canSubmit && false;
						hasTinMatchError = true;
						tinMatchErrors.put(correctionFiling.getId(), getTINMatchFailure(correctionFiling));
					} else {
						canSubmit = canSubmit && false;
					}
				}
			}
			if(hasTinMatchError) {
				for(Filing correctionFiling : corrections) {
					if(tinMatchErrors.get(correctionFiling.getId()) == null) {
						tinMatchErrors.put(correctionFiling.getId(), "Correction filing can not be submitted because of TIN match failure of it's correction pair.");
					}
				}
			}
			if(canSubmit) {
				for(Filing correctionFiling : corrections) {
					filingsThatCanBeSubmitted.add(correctionFiling);
				}
			}
		}
	}

	private boolean isSkipTINCheck() {
		boolean blockOnTINCheck = env.getProperty(blockOnTINCheckPropertyName, Boolean.class, true);
		boolean isPeakDay = DateUtil.is1099PeakDay();
		boolean skipTINCheck = !blockOnTINCheck || isPeakDay;
		return skipTINCheck;
	}

	private String getTINMatchFailure(Filing f) {
		Payee p = f.getPayee();
		TINMatchRequest tinMatchRequest = p.getTinMatchRequest();
		if(tinMatchRequest != null) {
			TINMatchResult tinMatchResult = tinMatchRequest.getTinMatchResult();
			if(tinMatchResult != null) {
				return tinMatchResult.getResultString();
			}
		}
		return "TIN Match verification failed";
	}

	private boolean isTINCheckInProgress(Filing f) {
		Payee p = f.getPayee();
		if(TINMatchStatus.InProgress == p.getTinMatchStatus()) {
			return true;
		}
		return false;
	}

	private int getTINMatchStatus(Filing f) {
		Payee p = f.getPayee();
		TINMatchStatus tinMatchStatus = p.getTinMatchStatus();
		if(tinMatchStatus == null) {
			return 0;
		}

		if(TINMatchStatus.InvalidTINType == tinMatchStatus || TINMatchStatus.Valid == tinMatchStatus) {
			return 0;
		} else if(TINMatchStatus.NotVerified == tinMatchStatus || TINMatchStatus.InValid == tinMatchStatus) {
			return 1;
		} else {
			return 2;
		}
	}

}
