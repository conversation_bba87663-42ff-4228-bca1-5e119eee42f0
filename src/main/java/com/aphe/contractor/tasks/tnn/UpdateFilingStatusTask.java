package com.aphe.contractor.tasks.tnn;

import com.aphe.AppUtil;
import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.StateSub;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.StateFilingStatus;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.SubManager;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.dto.FilingStatusDTO;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.insights.service.InsightsManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Auto task to update the status of all the filings sent to EFS.
 */

@Component
@Profile({"test"})
public class UpdateFilingStatusTask extends GenericBackgroundTask {

	Logger logger = LoggerFactory.getLogger(UpdateFilingStatusTask.class);

	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Autowired
	AuthManager authManager;

	@Autowired
	DomainMgr domainMgr;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	@Autowired
	InsightsManager insightsManager;

	//TODO: this should run only once a day.. only pick up files that are sent to EFS more than 3 days ago or something like that..

	// This should actually be a listener job that listens to EFSFilingStatusChange events.

//	@Scheduled(initialDelayString = "${aphe.contractor.updateFilingStatusTask.initialDelay}", fixedDelayString = "${aphe.contractor.updateFilingStatusTask.fixedDelay}")
	public void updateFilingsStatusTask() {

		boolean pauseTasks = isPauseTasks("domainservice-update-filing-status-job");
		if(pauseTasks) {
			return;
		}

		//logger.info("Start of executing the taskName=UpdateFilingStatusTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		// Find all the filings that have been in FilingStatus=Processing and
		// ProcessingStatus != null
		List<FilingStatus> filingsStatuses = new ArrayList<>();
		filingsStatuses.add(FilingStatus.Received);
		filingsStatuses.add(FilingStatus.Queued);
		filingsStatuses.add(FilingStatus.Processing);
		filingsStatuses.add(FilingStatus.Sent_To_Agency);

		List<Long> efsFilingIdsToBeUpdated = new ArrayList<>();

		Map<String, FedSub> fedSubsBySubRefId = new HashMap<>();
		Map<String, StateSub> stateSubsBySubRefId = new HashMap<>();


		List<FedSub> fedSubsInProcessing = subManager.findByFedSubStatusIn(filingsStatuses);
		for (FedSub f : fedSubsInProcessing) {
			efsFilingIdsToBeUpdated.add(f.getFilingRequestId());
			fedSubsBySubRefId.put(f.getSubmissionRefId(), f);
		}

		List<StateFilingStatus> stateFilingsStatuses = new ArrayList<>();
		stateFilingsStatuses.add(StateFilingStatus.Received);
		stateFilingsStatuses.add(StateFilingStatus.Queued);
		stateFilingsStatuses.add(StateFilingStatus.Processing);
		stateFilingsStatuses.add(StateFilingStatus.Sent_To_Agency);

		List<StateSub> stateSubsInProcessing = subManager.findByStateSubStatusIn(stateFilingsStatuses);
		for (StateSub f : stateSubsInProcessing) {
			efsFilingIdsToBeUpdated.add(f.getFilingRequestId());
			stateSubsBySubRefId.put(f.getSubmissionRefId(), f);
		}


		HashMap<Long, Filing> updatedFilings = new HashMap<>();
		try {
			List<FilingStatusDTO> statuses = efsFilingsMgr.getFilingStatus(efsFilingIdsToBeUpdated);

			for (FilingStatusDTO status : statuses) {
				FilingStatus newStatus = FilingStatus.getFromEFSFilingStatus(status.status.name());
				if(newStatus == null) {
					if(status.status == com.aphe.efs.model.enums.FilingStatus.System_Rejected || status.status == com.aphe.efs.model.enums.FilingStatus.Agency_Rejected) {
						newStatus = FilingStatus.Draft;
					}
				}
				if(newStatus == null) {
					logger.error("Attention Required: Unknown filing status from EFS filingRequestId=" + status.filingId);
					continue;
				}
				//Ignore EFS internal statuses of Queued and processing. This is also to prevent multiple email notification we send replacement files to IRS because of any issues on our side.
				if(newStatus == FilingStatus.Queued || newStatus == FilingStatus.Processing) {
					continue;
				}

				FedSub fedSubToBeUpdated = fedSubsBySubRefId.get(status.clientRefId);
				if (fedSubToBeUpdated != null && fedSubToBeUpdated.getStatus() != newStatus) {
					Filing updatedFiling = localFilingMgr.updateFedFilingStatusByRefId(status.clientRefId, newStatus, status.statusChangeDesc, new Date(), status.uniqueRecordId);
					if (updatedFiling != null) {
						updatedFilings.put(updatedFiling.getId(), updatedFiling);
					}
				}

				StateSub stateSubToBeUpdated = stateSubsBySubRefId.get(status.clientRefId);
				if (stateSubToBeUpdated != null) {
					StateFilingStatus newStateFilingStatus = StateFilingStatus.getStateFilingStatusByName(newStatus.name());
					if(newStateFilingStatus == null) {
						if(newStatus == FilingStatus.Draft) {
							newStateFilingStatus = StateFilingStatus.None;
						} else {
							logger.error("Attention Required: Invalid state filing status: " + newStatus.name());
						}
					}
					if (stateSubToBeUpdated.getStatus() != newStateFilingStatus) {
						Filing updatedFiling = localFilingMgr.updateStateFilingStatusByRefId(status.clientRefId, newStatus, status.statusChangeDesc, new Date(), status.uniqueRecordId);
						if (updatedFiling != null) {
							updatedFilings.put(updatedFiling.getId(), updatedFiling);
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error updating the filing status: ", e);
		}

		// Now group them by domainId for rejected and accepted filings.
		Map<Long, Set<Filing>> acceptedFilingsByDomain = new HashMap<>();
		Map<Long, Set<Filing>> systemRejectedFilingsByDomain = new HashMap<>();
		Map<Long, Set<Filing>> rejectedFilingsByDomain = new HashMap<>();
		Map<Long, Set<Filing>> sentToAgencyFilingsByDomain = new HashMap<>();
		for (Filing f : updatedFilings.values()) {
			if (FilingStatus.Draft == f.getFedSub().getStatus()) {
				Set<Filing> domainFailedFilings = systemRejectedFilingsByDomain.get(f.getPayer().getDomainId());
				if (domainFailedFilings == null) {
					domainFailedFilings = new HashSet<>();
					systemRejectedFilingsByDomain.put(f.getPayer().getDomainId(), domainFailedFilings);
				}
				domainFailedFilings.add(f);
			} else if (FilingStatus.Accepted == f.getFedSub().getStatus() || StateFilingStatus.Accepted == f.getStateSub().getStatus()) {
				Set<Filing> domainSuccessfulFilings = acceptedFilingsByDomain.get(f.getPayer().getDomainId());
				if (domainSuccessfulFilings == null) {
					domainSuccessfulFilings = new HashSet<>();
					acceptedFilingsByDomain.put(f.getPayer().getDomainId(), domainSuccessfulFilings);
				}
				domainSuccessfulFilings.add(f);
			} else if (FilingStatus.Sent_To_Agency == f.getFedSub().getStatus() || StateFilingStatus.Sent_To_Agency == f.getStateSub().getStatus()) {
				Set<Filing> domainSentToAgencyFilings = sentToAgencyFilingsByDomain.get(f.getPayer().getDomainId());
				if (domainSentToAgencyFilings == null) {
					domainSentToAgencyFilings = new HashSet<>();
					sentToAgencyFilingsByDomain.put(f.getPayer().getDomainId(), domainSentToAgencyFilings);
				}
				domainSentToAgencyFilings.add(f);
			}
		}
		Set<Long> allDomainIds = new HashSet<>();
		allDomainIds.addAll(systemRejectedFilingsByDomain.keySet());
		allDomainIds.addAll(sentToAgencyFilingsByDomain.keySet());
		allDomainIds.addAll(acceptedFilingsByDomain.keySet());
		allDomainIds.addAll(rejectedFilingsByDomain.keySet());

		if (allDomainIds.size() > 0) {
			// First get all the users of these domainIds.
			String token = authManager.getTokenForSystemUser();
			Map<Long, List<UserDTO>> usersByDomain = authManager.getDomainUsersRemote(token, allDomainIds);
			Map<Long, List<Long>> accountantsByDomain = authManager.getDomainAccountantsRemote(token, allDomainIds);
			authManager.deleteToken(token);

			// Now send the emails by each domainId.
			for (Long domainId : systemRejectedFilingsByDomain.keySet()) {

				List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
				try {
					DomainDTO domainDTO = domainMgr.getDomain(domainId);
					String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
					if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
						UserDTO userDto = new UserDTO();
						userDto.setEmail(domainDTO.emailAddress);
						userDto.setFirstName(displayName);
						userDto.setLastName("");
						domainEmailUsers.add(userDto);
					}
					Set<Filing> failedFilings = systemRejectedFilingsByDomain.get(domainId);
					mailUtil.sendFilingsSystemRejectedEmail(failedFilings, domainEmailUsers, displayName);
				} catch (NumberFormatException | ApheForbiddenException e2) {
				}
			}

			for (Long domainId : rejectedFilingsByDomain.keySet()) {
				List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
				try {
					DomainDTO domainDTO = domainMgr.getDomain(domainId);
					String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
					if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
						UserDTO userDto = new UserDTO();
						userDto.setEmail(domainDTO.emailAddress);
						userDto.setFirstName(displayName);
						userDto.setLastName("");
						domainEmailUsers.add(userDto);
					}
					Set<Filing> failedFilings = rejectedFilingsByDomain.get(domainId);
					mailUtil.sendFilingsRejectedEmail(failedFilings, domainEmailUsers, displayName);
				} catch (NumberFormatException | ApheForbiddenException e2) {
				}
			}

			for (Long domainId : acceptedFilingsByDomain.keySet()) {
				List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
				try {
					DomainDTO domainDTO = domainMgr.getDomain(domainId);
					String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
					//TODO: Make sure this is a valid email address. Here and where domainDTO email is being used.
					if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
						UserDTO userDto = new UserDTO();
						userDto.setEmail(domainDTO.emailAddress);
						userDto.setFirstName(displayName);
						userDto.setLastName("");
						domainEmailUsers.add(userDto);
					}
					Set<Filing> acceptedFilings = acceptedFilingsByDomain.get(domainId);
					mailUtil.sendFilingsAcceptedEmail(acceptedFilings, domainEmailUsers, displayName);
				} catch (NumberFormatException | ApheForbiddenException e2) {
				}
			}

			for (Long domainId : sentToAgencyFilingsByDomain.keySet()) {

				List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
				try {
					DomainDTO domainDTO = domainMgr.getDomain(domainId);
					String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
					//TODO: Make sure this is a valid email address. Here and where domainDTO email is being used.
					if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
						UserDTO userDto = new UserDTO();
						userDto.setEmail(domainDTO.emailAddress);
						userDto.setFirstName(displayName);
						userDto.setLastName("");
						domainEmailUsers.add(userDto);
					}
					Set<Filing> sentToAgencyFilings = sentToAgencyFilingsByDomain.get(domainId);

					//Do not send to GS clients.
					if(!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
						mailUtil.sendFilingsSentToAgencyEmail(sentToAgencyFilings, domainEmailUsers, displayName);
					}
				} catch (NumberFormatException | ApheForbiddenException e2) {
				}

			}

			for(Long domainId : allDomainIds) {
				insightsManager.updateFilingCounts(domainId);
			}

		}

		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=UpdateFilingStatusTask status=End");

	}

}
