package com.aphe.contractor.tasks.tnn;

import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.dto.read.PayeeDTO;
import com.aphe.contractor.model.EmailSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.enums.EmailStatus;
import com.aphe.contractor.model.enums.PrintCopyType;
import com.aphe.contractor.services.*;
import com.aphe.contractor.tasks.TaskUtil;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to take submitted filings by the user and submit to the EFS. This run every minute or so??
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class HandleEmailRequestsMultiNodeTask extends ApheBackgroundTask {

    Logger logger = LoggerFactory.getLogger(HandleEmailRequestsMultiNodeTask.class);
    public static final String EMAILS_SUBS = "emailSubs";
    public static final String EMAIL_SUBS_SENT = "emailSubsSent";
    public static final String EMAIL_SUBS_FAILED = "emailSubsFailed";
    public static final String FAILURE_REASONS = "failureReasons";

    @Autowired
    PayeeManager payeeManager;

    @Autowired
    FilingManager localFilingMgr;

    @Autowired
    SubManager subManager;

    @Value("${aphe.efs.tnnFormsDir}")
    public String tnnFormsDir;

    @Autowired
    MailUtil mailUtil;

    @Autowired
    MailManager mailManager;

    @Autowired
    TaskUtil taskUtil;

    @Recurring(id = "domainservice-email-forms-job", interval = "PT4H")
    @Override
    public void startTask() {
        jobName = "domainservice-email-forms-job";
        chunkSize = 1;
        executeTask();
    }

    @Override
    public List<String> getUnitsOfWork() {
        CronJobAuthenticationUtil.configureAuthentication("superadmin");
        List<EmailSub> emailSubsQueued = subManager.findByEmailStatus(EmailStatus.Queued);
        Set<Long> domainsWithEmailSubs = new HashSet<>();
        for (EmailSub emailSub : emailSubsQueued) {
            domainsWithEmailSubs.add(emailSub.getFiling().getPayer().getDomainId());
        }
        CronJobAuthenticationUtil.cleanAuthentication();
        return domainsWithEmailSubs.stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
        Long domainId = Long.parseLong(unitOfWork);
        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        List<EmailSub> emailSubs = subManager.findByDomainIdAndEmailStatus(domainId, EmailStatus.Queued);
        int emialSubsCount = emailSubs.size();
        int emailSubsSent = 0;
        int emailSubsFailed = 0;
        Map<String, String> failureReasons = new HashMap<>();

        try {

            for (EmailSub emailSub : emailSubs) {
                String filePath = null;
                //Protect with try/catch.
                try {
                    Filing filing = emailSub.getFiling();
                    PayeeDTO payeeDTO = payeeManager.getPayee(filing.getPayee().getId());
                    String payeeEmail = payeeDTO.emailAddress;
                    if (!StringUtil.isValidEmailAddress(payeeEmail)) {
                        emailSubsFailed++;
                        failureReasons.put(emailSub.getId().toString(), "Invalid email address");
                        localFilingMgr.updateEmailStatusToBounced(emailSub.getId(), "Invalid email address");
                        continue;
                    }


                    //get the PDF
                    List<String> filingIds = new ArrayList<>();
                    filingIds.add(Long.toString(filing.getId()));
                    String fileName = localFilingMgr.generateForms(filingIds, false, PrintCopyType.RecipientEMailCopy);
                    String folderPath = tnnFormsDir + File.separator + fileName;
                    File f = new File(folderPath);
                    if (f.isDirectory()) {
                        for (File entryFile : f.listFiles()) {
                            filePath = entryFile.getAbsolutePath();
                            break;
                        }
                    }
                    final File file = new File(filePath);

                    //create request to SendInBlue
                    String messageId = mailUtil.send1099Form(filing, file);

                    //store tracking id on tnn side.
                    if (messageId != null) {
                        localFilingMgr.updateEmailTrackingId(emailSub.getId(), messageId);
                    } else {
                        emailSubsFailed++;
                        localFilingMgr.updateEmailStatusToFail(emailSub.getId(), "Error sending email. No message id returned.");
                        logger.error("Attention Required: Error sending the email request for emailSubId=" + emailSub.getId());
                    }
                } catch (Exception e) {
                    emailSubsFailed++;
                    failureReasons.put(emailSub.getId().toString(), e.getMessage());

                    logger.error("Attention Required: Error sending the email request for emailSubId=" + emailSub.getId());
                    logger.error(e.getMessage());
                    localFilingMgr.updateEmailStatusToFail(emailSub.getId(), e.getMessage());
                } finally {
                    //delete the un-encrypted PDF file from local dir in finally block
                    if (filePath != null) {
                        try {
                            File f = new File(filePath);
                            f.delete();
                        } catch (Exception e) {
                            logger.error("Attention Required: Failed to delete the file filePath=" + filePath);
                        }
                    }
                }
            }
        } catch (Exception e) {
            emailSubsFailed++;
            logger.error("Attention Required: Error processing email requests: ", e);
        } finally {
            CronJobAuthenticationUtil.cleanAuthentication();

            Map<String, String> values = new HashMap<>();
            values.put(EMAILS_SUBS, String.valueOf(emialSubsCount));
            values.put(EMAIL_SUBS_SENT, String.valueOf(emailSubsSent));
            values.put(EMAIL_SUBS_FAILED, String.valueOf(emailSubsFailed));
            values.put(FAILURE_REASONS, taskUtil.buildFailureReasons(failureReasons));
            if(emailSubsFailed > 0) {
                return new ApheExecutionData(ApheExecutionResult.FAILED, "Some email requests could not be sent.", values);
            } else {
                return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
            }
        }
    }

    @Override
    public List<String> getAdditionalEmailColumns() {
        return List.of(EMAILS_SUBS, EMAIL_SUBS_SENT, EMAIL_SUBS_FAILED, FAILURE_REASONS);
    }

    @Override
    public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
        logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
        long emailStartTime = System.currentTimeMillis();
        if (errored == 0 && skipped == 0) {
            return;
        }
        mailManager.sendSystemStatusEmail(subject, emailHTML);
        long emailTime = System.currentTimeMillis() - emailStartTime;
        logger.info("Email sent in {}ms", emailTime);
    }
}
