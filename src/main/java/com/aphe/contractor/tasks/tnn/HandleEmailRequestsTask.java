 package com.aphe.contractor.tasks.tnn;

 import com.aphe.common.tasks.GenericBackgroundTask;
 import com.aphe.common.util.CronJobAuthenticationUtil;
 import com.aphe.common.util.StringUtil;
 import com.aphe.contractor.dto.read.PayeeDTO;
 import com.aphe.contractor.model.EmailSub;
 import com.aphe.contractor.model.Filing;
 import com.aphe.contractor.model.enums.EmailStatus;
 import com.aphe.contractor.model.enums.PrintCopyType;
 import com.aphe.contractor.services.FilingManager;
 import com.aphe.contractor.services.MailUtil;
 import com.aphe.contractor.services.PayeeManager;
 import com.aphe.contractor.services.SubManager;
 import org.slf4j.Logger;
 import org.slf4j.LoggerFactory;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.beans.factory.annotation.Value;
 import org.springframework.context.annotation.Profile;
 import org.springframework.stereotype.Component;

 import java.io.File;
 import java.util.ArrayList;
 import java.util.List;

/**
 * Auto task to take submitted filings by the user and submit to the EFS. This run every minute or so??
 */

@Component
@Profile({"test"})
public class HandleEmailRequestsTask extends GenericBackgroundTask {

	@Autowired
	PayeeManager payeeManager;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Value("${aphe.efs.tnnFormsDir}")
	public String tnnFormsDir;

	@Autowired
	MailUtil mailUtil;

	Logger logger = LoggerFactory.getLogger(HandleEmailRequestsTask.class);

//	@Scheduled(initialDelayString = "${aphe.contractor.emailTask.initialDelay}", fixedDelayString = "${aphe.contractor.emailTask.fixedDelay}")
	public void sendEmailsToFormRecepients() {

		boolean pauseTasks = isPauseTasks("domainservice-form-email-job");
		if(pauseTasks) {
			return;
		}

//		//logger.info("Start of executing the taskName=HandleEmailRequestsTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		List<EmailSub> emailSubsQueued = subManager.findByEmailStatus(EmailStatus.Queued);
//		List<EmailSub> emailSubsQueued = new ArrayList<Filing>();
		try {
			for (EmailSub emailSub : emailSubsQueued) {
				String filePath = null;
				//Protect with try/catch.
				try {
					Filing filing = emailSub.getFiling();
					PayeeDTO payeeDTO = payeeManager.getPayee(filing.getPayee().getId());
					String payeeEmail = payeeDTO.emailAddress;
					if(!StringUtil.isValidEmailAddress(payeeEmail)) {
						localFilingMgr.updateEmailStatusToBounced(emailSub.getId(), "Invalid email address");
						continue;
					}


					//get the PDF
					List<String> filingIds = new ArrayList<>();
					filingIds.add(Long.toString(filing.getId()));
					String fileName = localFilingMgr.generateForms(filingIds, false, PrintCopyType.RecipientEMailCopy);
					String folderPath = tnnFormsDir + File.separator + fileName;
					File f = new File(folderPath);
					if (f.isDirectory()) {
						for (File entryFile : f.listFiles()) {
							filePath = entryFile.getAbsolutePath();
							break;
						}
					}
					final File file = new File(filePath);

					//create request to SendInBlue
					String messageId = mailUtil.send1099Form(filing, file);

					//store tracking id on tnn side.
					if (messageId != null) {
						localFilingMgr.updateEmailTrackingId(emailSub.getId(), messageId);
					} else {
						logger.error("Attention Required: Error sending the email request for emailSubId=" + emailSub.getId());
					}
				} catch (Exception e) {
					logger.error("Attention Required: Error sending the email request for emailSubId=" + emailSub.getId());
					logger.error(e.getMessage());
					localFilingMgr.updateEmailStatusToFail(emailSub.getId(), e.getMessage());
				} finally {
					//delete the un-encrypted PDF file from local dir in finally block
					if (filePath != null) {
						try {
							File f = new File(filePath);
							f.delete();
						} catch (Exception e) {
							logger.error("Attention Required: Failed to delete the file filePath=" + filePath);
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error("Attention Required: Error processing email requests: ", e);
		}

		CronJobAuthenticationUtil.cleanAuthentication();
//		//logger.info("Done executing the taskName=HandleEmailRequestsTask status=End");

	}

}
