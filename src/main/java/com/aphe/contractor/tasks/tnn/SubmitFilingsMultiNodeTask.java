package com.aphe.contractor.tasks.tnn;

import com.aphe.AppUtil;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.MappedValidationErros;
import com.aphe.common.error.exceptions.ApheDataListValidationException;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.DateUtil;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.Payee;
import com.aphe.contractor.model.TINMatchRequest;
import com.aphe.contractor.model.enums.CorrectionType;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.TINMatchResult;
import com.aphe.contractor.model.enums.TINMatchStatus;
import com.aphe.contractor.services.*;
import com.aphe.contractor.tasks.TaskUtil;
import com.aphe.efs.dto.FilingDataDTO;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.insights.service.InsightsManager;
import org.hibernate.validator.constraints.ValidationErrors;
import org.jetbrains.annotations.NotNull;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to take submitted filings by the user and submit to the EFS 
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class SubmitFilingsMultiNodeTask extends ApheBackgroundTask {

	Logger logger = LoggerFactory.getLogger(SubmitFilingsMultiNodeTask.class);

	@Autowired
	Environment env;

	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Autowired
	ContractorConvertUtil conversionUtil;

	@Autowired
	TaskUtil taskUtil;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	MailManager mailManager;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	@Autowired
	InsightsManager insightsManager;

	private String blockOnTINCheckPropertyName = "aphe.contractor.blockFilingSubmissionOnTINCheck";


	//TODO: Waking up every 60 seconds is too often. We should trigger this job asynchronously when a filing is submitted by the user, with some sort debounce.
	//TODO: We should invoke this task every hour or so, not to miss out any filings, just for safety.
	//TODO: Check when we need to throow errors on execution.
	@Recurring(id = "domainservice-submit-filings-job", interval = "PT60S")
	@Override
	public void startTask() {
		jobName = "domainservice-submit-filings-job";
		chunkSize = 1;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		//logger.info("Start of executing the taskName=SubmitFilingsTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");
		List<FedSub> filingsToBeProcessed = subManager.findByFedSubStatusIn(Arrays.asList(FilingStatus.Submitted, FilingStatus.WaitingOnTINMatch));

		//Get all domainIds from the filings.
		Set<String> domainIds = new HashSet<>();
		for (FedSub fedSub : filingsToBeProcessed) {
			Long domainId = fedSub.getFiling().getPayer().getDomainId();
			domainIds.add(domainId.toString());
		}
		CronJobAuthenticationUtil.cleanAuthentication();
		return new ArrayList<>(domainIds);
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
		Long domainId = Long.parseLong(unitOfWork);
		List<FedSub> filingsToBeProcessed = subManager.findByDomainIdAndFedSubStatusIn(domainId, Arrays.asList(FilingStatus.Submitted, FilingStatus.WaitingOnTINMatch));
		List<Filing> filings = filingsToBeProcessed.stream().map(fedSub -> fedSub.getFiling()).collect(Collectors.toList());
		if (filings.size() > 0) {
			// Get some require info.
			Map<Long, String> domainNames = new HashMap<>();
			Map<Long, List<UserDTO>> usersByDomain =  new HashMap<>();
			Map<Long, List<Long>> accountantsByDomain =  new HashMap<>();
			taskUtil.buildCommunicationData(domainId, domainNames, usersByDomain, accountantsByDomain);

			//Process the filings.
			List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);
			String domainName = domainNames.get(domainId);

			Map<Long, String> tinMatchErrors = new HashMap<>();
			Map<Long, String> conversionErrors = new HashMap<>();
			Map<String, String> submissionErrors = new HashMap<>();
			Map<Long, Filing> successfulFilings = new HashMap<>();
			Map<Long, Filing> failedFilings = new HashMap<>();

			Map<FilingDataDTO, Filing> efsFilingsMap = new HashMap<>();

			//First separate out correction filings, regardless of they are ready to submit or waiting on tin match
			List<Filing> correctionFilings = filings.stream().filter(f->f.getCorrectionType() != CorrectionType.Original).collect(Collectors.toList());
			filings.removeAll(correctionFilings);


			//Divide the filings into two buckets.
			List<Filing> filingsThatCanBeSubmitted = new ArrayList<>();
			List<Filing> filingsToMoveWaitOnTINMatch = new ArrayList<>();
			groupRegularFilings(filings, filingsThatCanBeSubmitted, filingsToMoveWaitOnTINMatch, tinMatchErrors);
			groupCorrectionFilings(correctionFilings, filingsThatCanBeSubmitted, filingsToMoveWaitOnTINMatch, tinMatchErrors);


			//Update the filing status on the filings that are waiting on tin match.
			if(filingsToMoveWaitOnTINMatch.size() > 0) {
				List<Long> waitOnTINMatchFilingIds = filingsToMoveWaitOnTINMatch.stream().map(f->f.getId()).collect(Collectors.toList());
				List<Filing> movedToWaitingOnTINMatchFilings = localFilingMgr.waitOnTINMatchByIds(waitOnTINMatchFilingIds);
//				for(Filing f : movedToWaitingOnTINMatchFilings){
//						successfulFilings.put(f.getId(), f);
//				}
			}

			//Convert the filings to EFSFilingDTO. and handle special cases of corrections.
			convertToEFSFilings(filingsThatCanBeSubmitted, domainId, efsFilingsMap, conversionErrors);

			//Submit to EFS
			EFSSubmissionResult result = submitFilingsToEFS(efsFilingsMap, submissionErrors, filingsThatCanBeSubmitted);

			//Process the submission response. Mark the filings as received, send emails, update insights etc.
			processSubmissionRespons(result, successfulFilings, conversionErrors, failedFilings, tinMatchErrors, submissionErrors, domainId, accountantsByDomain, domainEmailUsers, domainName);

			return new ApheExecutionData(ApheExecutionResult.SUCCESS, null);

		} else {
			return new ApheExecutionData(ApheExecutionResult.SKIPPED, "No filings to be processed");
		}
	}

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of();
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
		if (errored == 0) {
			return;
		}
		long emailStartTime = System.currentTimeMillis();
		mailManager.sendSystemStatusEmail(subject, emailHTML);
		long emailTime = System.currentTimeMillis() - emailStartTime;
		logger.info("Email sent in {}ms", emailTime);
	}


	private void processSubmissionRespons(EFSSubmissionResult result, Map<Long, Filing> successfulFilings, Map<Long, String> conversionErrors, Map<Long, Filing> failedFilings, Map<Long, String> tinMatchErrors, Map<String, String> submissionErrors, Long domainId, Map<Long, List<Long>> accountantsByDomain, List<UserDTO> domainEmailUsers, String domainName) {
		if (result.submitted() && result.efsFilings() != null && result.efsFilings().size() > 0) {
			for (EFSFiling filing : result.efsFilings()) {
				Filing f = localFilingMgr.receiveFilingBySubRefId(filing.getClientRefId(), filing.getId());
				//Filing could be null, because we do not track the PA second filing on our side.
				//Also this allows rest of the filings to be in good status, rather than the task exiting with error.
				if(f != null) {
					successfulFilings.put(f.getId(), f);
				} else {
					logger.error("Failed to update the filing status to received for filing refId=" + filing.getClientRefId());
				}
			}
		}

		if (conversionErrors.size() > 0) {
			try {
				List<Filing> systemRejectedFilings = localFilingMgr.systemRejectFilingsByIds(conversionErrors);
				for(Filing f : systemRejectedFilings) {
					failedFilings.put(f.getId(), f);
				}
			} catch (Exception e1) {
				logger.error("Error changing the status of failed filings to Draft ", e1);
			}
		}
		if (tinMatchErrors.size() > 0) {
			try {
				List<Filing> tinMatchErrorFilings = localFilingMgr.systemRejectFilingsByIds(tinMatchErrors);
				for(Filing f : tinMatchErrorFilings) {
					failedFilings.put(f.getId(), f);
				}
			} catch (Exception e1) {
				logger.error("Error changing the status of failed filings to Draft ", e1);
			}
		}

		if (submissionErrors.size() > 0) {
			try {
				List<Filing> submissionErrorFilings = localFilingMgr.systemRejectFilingsBySubmissionRefIds(submissionErrors);
				for(Filing f : submissionErrorFilings) {
					failedFilings.put(f.getId(), f);
				}
			} catch (Exception e1) {
				logger.error("Error changing the status of failed filings to Draft ", e1);
			}
		}

		if(successfulFilings.size() > 0) {
			// Do not send to GS and also not to the regular customers.
			if(!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
				mailUtil.sendFilingsSubmittedEmail(new HashSet<>(successfulFilings.values()), domainEmailUsers, domainName);
			}
		}

		if (failedFilings.size() > 0) {
			mailUtil.sendFilingsSystemRejectedEmail(new HashSet<>(failedFilings.values()), domainEmailUsers, domainName);
		}

		//Update insights for this domain.
		insightsManager.updateFilingCounts(domainId);
	}

	private @NotNull SubmitFilingsMultiNodeTask.EFSSubmissionResult submitFilingsToEFS(Map<FilingDataDTO, Filing> efsFilingsMap, Map<String, String> submissionErrors, List<Filing> filingsThatCanBeSubmitted) {
		boolean submitted = false;
		List<EFSFiling> efsFilings = null;
		try {
			List<FilingDataDTO> filingDTOs = new ArrayList<>();
			filingDTOs.addAll(efsFilingsMap.keySet());
			if (filingDTOs.size() > 0) {
				efsFilings = efsFilingsMgr.addFilings(filingDTOs);
				submitted = true;
			}
		} catch (ApheDataListValidationException e) {
			MappedValidationErros errors = e.getMappedErrors();
			logger.error("Error submitting filings: ", e);
			logger.error("Error submitting filings: " + errors.getErrors().toString());
			Map<String, ValidationErrors> mappedErrors = errors.getErrors();
			for (String id : mappedErrors.keySet()) {
				submissionErrors.put(id, errors.getErrors().toString());
			}
		} catch (Exception e) {
			logger.error("Error submitting filings: ", e);
			for (Filing filing : filingsThatCanBeSubmitted) {
				submissionErrors.put(filing.getId().toString(), "Error submitting filing to EFS.");
			}
		}
		EFSSubmissionResult result = new EFSSubmissionResult(submitted, efsFilings);
		return result;
	}

	private record EFSSubmissionResult(boolean submitted, List<EFSFiling> efsFilings) {
	}

	private void convertToEFSFilings(List<Filing> filingsThatCanBeSubmitted, Long domainId, Map<FilingDataDTO, Filing> efsFilingsMap, Map<Long, String> conversionErrors) {
		for (Filing f : filingsThatCanBeSubmitted) {
			if(f.getCorrectionType() == CorrectionType.Second) {
				continue;
			}

			Filing secondFiling = null;
			Filing originalFiling = null;
			if(f.getCorrectionType() == CorrectionType.First) {
				//Find second correction type.
				secondFiling = filingsThatCanBeSubmitted.stream()
						.filter(f2-> f2.getCorrectionType() == CorrectionType.Second && f2.getOriginalFilingId().longValue() == f.getOriginalFilingId().longValue())
						.findFirst().orElse(null);
				originalFiling = localFilingMgr.getOriginalFiling(f.getId());
				if(originalFiling == null) {
					conversionErrors.put(f.getId(), "Original filing is null");
					if(secondFiling != null) {
						conversionErrors.put(secondFiling.getId(), "Original filing is null");
					}
				}
			}

			try {
				List<FilingDataDTO> efsFilings = conversionUtil.convertToEFSFilingDTOs(f, domainId, true, secondFiling, originalFiling);
				for(FilingDataDTO dto : efsFilings) {
					efsFilingsMap.put(dto, f);
				}
			} catch (Exception e) {
				logger.error("Error converting a filings to EFS DTO ", e);
				conversionErrors.put(f.getId(), "Error processing your data. Please contact us.");
			}
		}
	}

	private void groupRegularFilings(List<Filing> filings, List<Filing> filingsThatCanBeSubmitted, List<Filing> filingsToMoveWaitOnTINMatch, Map<Long, String> tinMatchErrors) {
		for (Filing f : filings) {
			if(f.getFedSub().getStatus() == FilingStatus.Submitted) {
				boolean isTINCheckInProgress = isTINCheckInProgress(f);
				boolean skipTINCheck = isSkipTINCheck();
				if(skipTINCheck || !isTINCheckInProgress) {
					filingsThatCanBeSubmitted.add(f);
				} else {
					filingsToMoveWaitOnTINMatch.add(f);
				}
			} else if(f.getFedSub().getStatus() == FilingStatus.WaitingOnTINMatch) {
				boolean skipTINCheck = isSkipTINCheck();
				int tinMatchStatus = getTINMatchStatus(f);
				if(tinMatchStatus == 0 || skipTINCheck) {
					filingsThatCanBeSubmitted.add(f);
				} else if(tinMatchStatus == 1) {
					tinMatchErrors.put(f.getId(), getTINMatchFailure(f));
				}
			}
		}
	}

	private void groupCorrectionFilings(List<Filing> correctionFilings, List<Filing> filingsThatCanBeSubmitted, List<Filing> filingsToMoveWaitOnTINMatch, Map<Long, String> tinMatchErrors) {
		if(correctionFilings.size() == 0) {
			return;
		}
		//Group them by original filing id.
		Map<Long, List<Filing>> correctionsByOriginalFilingId = correctionFilings.stream()
				.collect(Collectors.groupingBy(filing -> filing.getOriginalFilingId()));

		//For each original filing, if either first or second can not be submitted, do not submit.
		for(Long originalFilingId: correctionsByOriginalFilingId.keySet()) {
			List<Filing> corrections = correctionsByOriginalFilingId.get(originalFilingId);

			boolean canSubmit = true;
			boolean hasTinMatchError = false;
			for(Filing correctionFiling : corrections) {
				if(correctionFiling.getFedSub().getStatus() == FilingStatus.Submitted) {
					boolean isTINCheckInProgress = isTINCheckInProgress(correctionFiling);
					boolean skipTINCheck = isSkipTINCheck();
					if( skipTINCheck || !isTINCheckInProgress) {
						canSubmit = canSubmit && true;
					} else {
						filingsToMoveWaitOnTINMatch.add(correctionFiling);
						canSubmit = canSubmit && false;
					}
				} else if(correctionFiling.getFedSub().getStatus() == FilingStatus.WaitingOnTINMatch) {
					boolean skipTINCheck = isSkipTINCheck();
					int tinMatchStatus = getTINMatchStatus(correctionFiling);

					if(tinMatchStatus == 0 || skipTINCheck) {
						canSubmit = canSubmit && true;
					} else if(tinMatchStatus == 1) {
						canSubmit = canSubmit && false;
						hasTinMatchError = true;
						tinMatchErrors.put(correctionFiling.getId(), getTINMatchFailure(correctionFiling));
					} else {
						canSubmit = canSubmit && false;
					}
				}
			}
			if(hasTinMatchError) {
				for(Filing correctionFiling : corrections) {
					if(tinMatchErrors.get(correctionFiling.getId()) == null) {
						tinMatchErrors.put(correctionFiling.getId(), "Correction filing can not be submitted because of TIN match failure of it's correction pair.");
					}
				}
			}
			if(canSubmit) {
				for(Filing correctionFiling : corrections) {
					filingsThatCanBeSubmitted.add(correctionFiling);
				}
			}
		}
	}

	private boolean isSkipTINCheck() {
		boolean blockOnTINCheck = env.getProperty(blockOnTINCheckPropertyName, Boolean.class, true);
		boolean isPeakDay = DateUtil.is1099PeakDay();
		boolean skipTINCheck = !blockOnTINCheck || isPeakDay;
		return skipTINCheck;
	}

	private String getTINMatchFailure(Filing f) {
		Payee p = f.getPayee();
		TINMatchRequest tinMatchRequest = p.getTinMatchRequest();
		if(tinMatchRequest != null) {
			TINMatchResult tinMatchResult = tinMatchRequest.getTinMatchResult();
			if(tinMatchResult != null) {
				return tinMatchResult.getResultString();
			}
		}
		return "TIN Match verification failed";
	}

	private boolean isTINCheckInProgress(Filing f) {
		Payee p = f.getPayee();
		if(TINMatchStatus.InProgress == p.getTinMatchStatus()) {
			return true;
		}
		return false;
	}

	private int getTINMatchStatus(Filing f) {
		Payee p = f.getPayee();
		TINMatchStatus tinMatchStatus = p.getTinMatchStatus();
		if(tinMatchStatus == null) {
			return 0;
		}

		if(TINMatchStatus.InvalidTINType == tinMatchStatus || TINMatchStatus.Valid == tinMatchStatus) {
			return 0;
		} else if(TINMatchStatus.NotVerified == tinMatchStatus || TINMatchStatus.InValid == tinMatchStatus) {
			return 1;
		} else {
			return 2;
		}
	}

}
