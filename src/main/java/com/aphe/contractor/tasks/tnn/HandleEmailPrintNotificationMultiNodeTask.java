package com.aphe.contractor.tasks.tnn;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.EmailSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.PrintSub;
import com.aphe.contractor.model.enums.EmailStatus;
import com.aphe.contractor.model.enums.PrintStatus;
import com.aphe.contractor.services.*;
import com.aphe.contractor.tasks.TaskUtil;
import com.aphe.domain.service.DomainMgr;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to notify the user about update on email and print delivery notification statues...
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class HandleEmailPrintNotificationMultiNodeTask extends ApheBackgroundTask {

    Logger logger = LoggerFactory.getLogger(HandleEmailPrintNotificationMultiNodeTask.class);

    public static final String DOMAIN_NAME = "domainName";
    public static final String EMAILS_BOUNCED = "emailsBounced";
    public static final String PRINTS_RETURNED = "printsReturned";

    @Autowired
    FilingManager localFilingMgr;

    @Autowired
    SubManager subManager;

    @Autowired
    AuthManager authManager;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    MailUtil mailUtil;

    @Autowired
    ContractorConvertUtil contractorConvertUtil;

    @Autowired
    MailManager mailManager;

    @Autowired
    TaskUtil taskUtil;


    //Run every 4 hours.
    @Recurring(id = "domainservice-emailprintnotification-job", interval = "PT4H")
    @Override
    public void startTask() {
        jobName = "domainservice-emailprintnotification-job";
        chunkSize = 1;
        executeTask();
    }

    @Override
    public List<String> getUnitsOfWork() {

        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        List<EmailSub> emailSubsToBeEmailNotified = subManager.getFilingsEmailNotifiable();

        Set<Long> domainsWithEmailPrintNotifiableEvents = new HashSet<>();

        for (EmailSub emailSub : emailSubsToBeEmailNotified) {
            domainsWithEmailPrintNotifiableEvents.add(emailSub.getFiling().getPayer().getDomainId());
        }

        List<PrintSub> printSubsToBePrintNotified = subManager.getFilingsPrintNotifiable();

        for (PrintSub printSub : printSubsToBePrintNotified) {
            domainsWithEmailPrintNotifiableEvents.add(printSub.getFiling().getPayer().getDomainId());
        }

        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        return domainsWithEmailPrintNotifiableEvents.stream().map(String::valueOf).collect(Collectors.toList());

    }

    @Override
    public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
        Long domainId = Long.parseLong(unitOfWork);

        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        try {
            List<EmailSub> emailSubsToBeEmailNotified = subManager.getFilingsEmailNotifiable(domainId);

            Set<Filing> emailSuccessByDomain = new HashSet<>();
            Set<Filing> emailErroredByDomain = new HashSet<>();
            Set<Filing> emailBouncedByDomain = new HashSet<>();

            for (EmailSub emailSub : emailSubsToBeEmailNotified) {
                if (EmailStatus.Emailed == emailSub.getEmailStatus()) {
                    emailSuccessByDomain.add(emailSub.getFiling());
                } else if (EmailStatus.Errored == emailSub.getEmailStatus()) {
                    emailErroredByDomain.add(emailSub.getFiling());
                } else if (EmailStatus.Bounced == emailSub.getEmailStatus()) {
                    emailBouncedByDomain.add(emailSub.getFiling());
                }
            }

            List<PrintSub> printSubsToBePrintNotified = subManager.getFilingsPrintNotifiable(domainId);
            Set<Filing> printMailedByDomain = new HashSet<>();
            Set<Filing> printErroredByDomain = new HashSet<>();
            Set<Filing> printDeliveryByDomain = new HashSet<>();
            Set<Filing> printReturnedByDomain = new HashSet<>();

            for (PrintSub printSub : printSubsToBePrintNotified) {
                if (PrintStatus.Mailing == printSub.getPrintStatus()) {
                    printMailedByDomain.add(printSub.getFiling());
                } else if (PrintStatus.Errored == printSub.getPrintStatus()) {
                    printErroredByDomain.add(printSub.getFiling());
                } else if (PrintStatus.ProcessedForDelivery == printSub.getPrintStatus()) {
                    printDeliveryByDomain.add(printSub.getFiling());
                } else if (PrintStatus.ReturnedToSender == printSub.getPrintStatus()) {
                    printReturnedByDomain.add(printSub.getFiling());
                }
            }


//        allDomainIds.addAll(emailSuccessByDomain.keySet());
//        allDomainIds.addAll(emailErroredByDomain.keySet());
//            allDomainIds.addAll(emailBouncedByDomain.keySet());
//        allDomainIds.addAll(printMailedByDomain.keySet());
//        allDomainIds.addAll(printErroredByDomain.keySet());
//        allDomainIds.addAll(printDeliveryByDomain.keySet());
//            allDomainIds.addAll(printReturnedByDomain.keySet());

            boolean hasNotifiableEvent = emailBouncedByDomain.size() > 0 || printReturnedByDomain.size() > 0;
            String domainName = "";

            if (hasNotifiableEvent) {

                Map<Long, String> domainNames = new HashMap<>();
                Map<Long, List<UserDTO>> usersByDomain = new HashMap<>();
                Map<Long, List<Long>> accountantsByDomain = new HashMap<>();
                taskUtil.buildCommunicationData(domainId, domainNames, usersByDomain, accountantsByDomain);

                List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);
                domainName = domainNames.get(domainId);


                // Now send the emails by each domainId for bounced emails.
                sendEmailStatusUpdates(emailBouncedByDomain, domainEmailUsers, domainName);

                // Now send the emails by each domainId for returned to sender filings
                sendPrintStatusUpdates(printReturnedByDomain, domainEmailUsers, domainName);

                Map<String, String> values = new HashMap<>();
                values.put(DOMAIN_NAME, domainName);
                values.put(EMAILS_BOUNCED, String.valueOf(emailBouncedByDomain.size()));
                values.put(PRINTS_RETURNED, String.valueOf(printReturnedByDomain.size()));
                return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
            } else {
                return new ApheExecutionData(ApheExecutionResult.SKIPPED, "No notifiable events");
            }
        } catch (Exception e) {
            return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
        } finally {
            CronJobAuthenticationUtil.cleanAuthentication();
        }
    }

    @Override
    public List<String> getAdditionalEmailColumns() {
        return List.of(DOMAIN_NAME, EMAILS_BOUNCED, PRINTS_RETURNED);
    }

    @Override
    public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
        logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
        if (errored == 0 && skipped == 0) {
            return;
        }
        long emailStartTime = System.currentTimeMillis();
        mailManager.sendSystemStatusEmail(subject, emailHTML);
        long emailTime = System.currentTimeMillis() - emailStartTime;
        logger.info("Email sent in {}ms", emailTime);
    }


    private Map<Long, List<UserDTO>> getUsersByDomain(Set<Long> allDomainIds) {
        String token = authManager.getTokenForSystemUser();
        Map<Long, List<UserDTO>> usersByDomain = authManager.getDomainUsersRemote(token, allDomainIds);
        authManager.deleteToken(token);
        return usersByDomain;
    }

    private void sendEmailStatusUpdates(Set<Filing> filings, List<UserDTO> users, String domainName) {
        try {
            List<Long> filingIds = filings.stream().map(v -> v.getId()).collect(Collectors.toList());
            localFilingMgr.updateEmailNotify(filingIds, false);
            mailUtil.sendEmailBouncedEmail(filings, users, domainName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sendPrintStatusUpdates(Set<Filing> filings, List<UserDTO> users, String domainName) {
        try {
            List<Long> filingIds = filings.stream().map(v -> v.getId()).collect(Collectors.toList());
            localFilingMgr.updatePrintNotify(filingIds, false);
            mailUtil.sendPrintReturnedEmail(filings, users, domainName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
