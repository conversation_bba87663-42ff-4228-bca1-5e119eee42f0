package com.aphe.contractor.tasks.tnn;

import com.aphe.AppUtil;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.StateSub;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.StateFilingStatus;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.MailManager;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.SubManager;
import com.aphe.contractor.tasks.TaskUtil;
import com.aphe.efs.dto.FilingStatusDTO;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.insights.service.InsightsManager;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to update the status of all the filings sent to EFS.
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class UpdateFilingStatusMultiNodeTask extends ApheBackgroundTask {

	Logger logger = LoggerFactory.getLogger(UpdateFilingStatusMultiNodeTask.class);
	public static final String DOMAIN_NAME = "domainName";
	public static final String TOTAL_SUBS = "totalSubs";
	public static final String SUBS_NO_UPDATE = "subsNoUpdate";
	public static final String SUBS_UPDATED = "subsUpdated";
	public static final String SUBS_ERRORS = "subsErrors";
	public static final String FAILURE_REASONS = "failureReasons";


	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Autowired
	MailManager mailManager;

	@Autowired
	TaskUtil taskUtil;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	InsightsManager insightsManager;

	//TODO: Long term, we should let this run once a day, but for now, we need to run this more often.
	//TODO: This task will be invoked by EFS, whenever EFS updates the status of any filing, either system rejected, agaent rejcted, or an update from the agency.
	@Recurring(id = "domainservice-update-filing-status-job", interval = "PT60S")
	@Override
	public void startTask() {
		jobName = "domainservice-update-filing-status-job";
		chunkSize = 1;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		List<FilingStatus> fedFilingsStatuses = List.of(FilingStatus.Received, FilingStatus.Queued, FilingStatus.Processing, FilingStatus.Sent_To_Agency);
		Set<Long> domainIdsWithFilingsToBeUpdated = new HashSet<>();
		List<FedSub> fedSubsInProcessing = subManager.findByFedSubStatusIn(fedFilingsStatuses);
		for (FedSub f : fedSubsInProcessing) {
			domainIdsWithFilingsToBeUpdated.add(f.getFiling().getPayer().getDomainId());
		}

		List<StateFilingStatus> stateFilingsStatuses = List.of(StateFilingStatus.Received, StateFilingStatus.Queued, StateFilingStatus.Processing, StateFilingStatus.Sent_To_Agency);
		List<StateSub> stateSubsInProcessing = subManager.findByStateSubStatusIn(stateFilingsStatuses);
		for (StateSub f : stateSubsInProcessing) {
			domainIdsWithFilingsToBeUpdated.add(f.getFiling().getPayer().getDomainId());
		}

		CronJobAuthenticationUtil.cleanAuthentication();
		return domainIdsWithFilingsToBeUpdated.stream().map(String::valueOf).collect(Collectors.toList());
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
		Long domainId = Long.parseLong(unitOfWork);

		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		try {
			List<FilingStatus> fedFilingsStatuses = List.of(FilingStatus.Received, FilingStatus.Queued, FilingStatus.Processing, FilingStatus.Sent_To_Agency);
			List<StateFilingStatus> stateFilingsStatuses = List.of(StateFilingStatus.Received, StateFilingStatus.Queued, StateFilingStatus.Processing, StateFilingStatus.Sent_To_Agency);

			List<FedSub> fedSubsInProcessing = subManager.findByDomainIdAndFedSubStatusIn(domainId, fedFilingsStatuses);
			List<StateSub> stateSubsInProcessing = subManager.findByDomainIdAndStateSubStatusIn(domainId, stateFilingsStatuses);

			List<Long> efsFilingIdsToBeUpdated = new ArrayList<>();
			Map<String, FedSub> fedSubsBySubRefId = new HashMap<>();
			Map<String, StateSub> stateSubsBySubRefId = new HashMap<>();

			for (FedSub f : fedSubsInProcessing) {
				efsFilingIdsToBeUpdated.add(f.getFilingRequestId());
				fedSubsBySubRefId.put(f.getSubmissionRefId(), f);
			}
			for (StateSub f : stateSubsInProcessing) {
				efsFilingIdsToBeUpdated.add(f.getFilingRequestId());
				stateSubsBySubRefId.put(f.getSubmissionRefId(), f);
			}

			HashMap<Long, Filing> updatedFilings = new HashMap<>();
			int totalSubs = 0;
			int subsNoUpdate = 0;
			int subsUpdated = 0;
			int subsErrors = 0;
			Map<String, String> failureReasons = new HashMap<>();
			totalSubs = efsFilingIdsToBeUpdated.size();


			List<FilingStatusDTO> efsFilingStauses = efsFilingsMgr.getFilingStatus(efsFilingIdsToBeUpdated);
			for (FilingStatusDTO efsFileNameStatus : efsFilingStauses) {

				boolean foundFedOrStateSub = false;

				FilingStatus newFedSubStatus = FilingStatus.getFromEFSFilingStatus(efsFileNameStatus.status.name());
				if(newFedSubStatus == null) {
					if (efsFileNameStatus.status == com.aphe.efs.model.enums.FilingStatus.System_Rejected || efsFileNameStatus.status == com.aphe.efs.model.enums.FilingStatus.Agency_Rejected) {
						newFedSubStatus = FilingStatus.Draft;
					}
				}
				//If the new status is still nul, treat this as an error and move on to the next filing.
				if(newFedSubStatus == null) {
					logger.error("Attention Required: Unknown filing efsFileNameStatus from EFS filingRequestId=" + efsFileNameStatus.filingId);
					subsErrors++;
					failureReasons.put(efsFileNameStatus.clientRefId, "Unknown filing efsFileNameStatus from EFS filingRequestId=" + efsFileNameStatus.filingId);
					continue;
				}

				//Ignore EFS internal efsFilingStauses of Queued and processing. This is also to prevent multiple email notification we send replacement files to IRS because of any issues on our side.
				if(newFedSubStatus == FilingStatus.Queued || newFedSubStatus == FilingStatus.Processing) {
					subsNoUpdate++;
					continue;
				}

				FedSub fedSubToBeUpdated = fedSubsBySubRefId.get(efsFileNameStatus.clientRefId);
				if (fedSubToBeUpdated != null) {
					foundFedOrStateSub = true;
					if(fedSubToBeUpdated.getStatus() != newFedSubStatus) {
						Filing updatedFiling = localFilingMgr.updateFedFilingStatusByRefId(efsFileNameStatus.clientRefId, newFedSubStatus, efsFileNameStatus.statusChangeDesc, new Date(), efsFileNameStatus.uniqueRecordId);
						if (updatedFiling != null) {
							subsUpdated++;
							updatedFilings.put(updatedFiling.getId(), updatedFiling);
						} else {
							subsErrors++;
							failureReasons.put(efsFileNameStatus.clientRefId, "Failed to update the filing status to " + newFedSubStatus.name() + " for clientRefId=" + efsFileNameStatus.clientRefId);
						}
					} else{
						subsNoUpdate++;
					}
				}

				//See if there is state sub to be updated. Not all EFS Filings may have corresponding state sub.
				StateSub stateSubToBeUpdated = stateSubsBySubRefId.get(efsFileNameStatus.clientRefId);
				//StateSub filingStateSub = fedSubToBeUpdated.getFiling().getStateSub();
				//filingStateSub should be same as stateSubToBeUpdated. both could be null or both should have same id.
				if(stateSubToBeUpdated != null) {
					StateFilingStatus newStateFilingStatus = StateFilingStatus.getStateFilingStatusByName(newFedSubStatus.name());
					if(newStateFilingStatus == null) {
						if(newFedSubStatus == FilingStatus.Draft) {
							newStateFilingStatus = StateFilingStatus.None;
						}
					}

					//If the new status is still nul, treat this as an error and move on to the next filing.
					if(newStateFilingStatus == null) {
						subsErrors++;
						failureReasons.put(efsFileNameStatus.clientRefId, "Invalid state filing efsFileNameStatus: " + newFedSubStatus.name());
						continue;
					}

					if (stateSubToBeUpdated.getStatus() != newStateFilingStatus) {
						Filing updatedFiling = localFilingMgr.updateStateFilingStatusByRefId(efsFileNameStatus.clientRefId, newFedSubStatus, efsFileNameStatus.statusChangeDesc, new Date(), efsFileNameStatus.uniqueRecordId);
						if (updatedFiling != null) {
							updatedFilings.put(updatedFiling.getId(), updatedFiling);
						} else {
							subsErrors++;
							failureReasons.put(efsFileNameStatus.clientRefId, "Failed to update the filing status to " + newFedSubStatus.name() + " for clientRefId=" + efsFileNameStatus.clientRefId);
						}
					} else {
						subsNoUpdate++;
					}
				}
			}


			//Now notify the users about the status of the filings.
			Set<Filing> acceptedFilingsByDomain = new HashSet<>();
			Set<Filing> systemRejectedFilingsByDomain = new HashSet<>();
			Set<Filing> sentToAgencyFilingsByDomain = new HashSet<>();
			for (Filing f : updatedFilings.values()) {
				if (FilingStatus.Draft == f.getFedSub().getStatus()) {
					systemRejectedFilingsByDomain.add(f);
				} else if (FilingStatus.Accepted == f.getFedSub().getStatus() || StateFilingStatus.Accepted == f.getStateSub().getStatus()) {
					acceptedFilingsByDomain.add(f);
				} else if (FilingStatus.Sent_To_Agency == f.getFedSub().getStatus() || StateFilingStatus.Sent_To_Agency == f.getStateSub().getStatus()) {
					sentToAgencyFilingsByDomain.add(f);
				}
			}

			String domainName = "";
			if(acceptedFilingsByDomain.size() > 0 || systemRejectedFilingsByDomain.size() > 0 || sentToAgencyFilingsByDomain.size() > 0) {

				Map<Long, String> domainNames = new HashMap<>();
				Map<Long, List<UserDTO>> usersByDomain = new HashMap<>();
				Map<Long, List<Long>> accountantsByDomain = new HashMap<>();
				taskUtil.buildCommunicationData(domainId, domainNames, usersByDomain, accountantsByDomain);

				List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);
				domainName = domainNames.get(domainId);

				// Now send the emails by each domainId.
				mailUtil.sendFilingsSystemRejectedEmail(systemRejectedFilingsByDomain, domainEmailUsers, domainName);
				mailUtil.sendFilingsAcceptedEmail(acceptedFilingsByDomain, domainEmailUsers, domainName);
				//Do not send to GS clients.
				if(!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
					mailUtil.sendFilingsSentToAgencyEmail(sentToAgencyFilingsByDomain, domainEmailUsers, domainName);
				}
				insightsManager.updateFilingCounts(domainId);
			}


			Map<String, String> values = new HashMap<>();
			values.put(DOMAIN_NAME, domainName);
			values.put(TOTAL_SUBS, String.valueOf(totalSubs));
			values.put(SUBS_NO_UPDATE, String.valueOf(subsNoUpdate));
			values.put(SUBS_UPDATED, String.valueOf(subsUpdated));
			values.put(SUBS_ERRORS, String.valueOf(subsErrors));
			values.put(FAILURE_REASONS, taskUtil.buildFailureReasons(failureReasons));
			return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
		} catch (Exception e) {
			logger.error("Error updating the filing status: ", e);
			return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
		} finally {
			CronJobAuthenticationUtil.cleanAuthentication();
		}
	}

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of(DOMAIN_NAME, TOTAL_SUBS, SUBS_NO_UPDATE, SUBS_UPDATED, SUBS_ERRORS, FAILURE_REASONS);
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
		// Send email only if errored or skipped
		if (errored == 0) {
			return;
		}
		long emailStartTime = System.currentTimeMillis();
		mailManager.sendSystemStatusEmail(subject, emailHTML);
		long emailTime = System.currentTimeMillis() - emailStartTime;
		logger.info("Email sent in {}ms", emailTime);
	}
}
