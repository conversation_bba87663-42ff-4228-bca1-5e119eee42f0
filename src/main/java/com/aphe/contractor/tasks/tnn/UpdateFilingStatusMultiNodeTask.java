package com.aphe.contractor.tasks.tnn;

import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.StateSub;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.StateFilingStatus;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.MailManager;
import com.aphe.contractor.services.SubManager;
import com.aphe.contractor.tasks.TaskUtil;
import com.aphe.efs.dto.FilingStatusDTO;
import com.aphe.efs.services.filings.FilingsManager;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to update the status of all the filings sent to EFS.
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class UpdateFilingStatusMultiNodeTask extends ApheBackgroundTask {

	Logger logger = LoggerFactory.getLogger(UpdateFilingStatusMultiNodeTask.class);
	public static final String TOTAL_SUBS = "totalSubs";
	public static final String SUBS_NO_UPDATE = "subsNoUpdate";
	public static final String SUBS_UPDATED = "subsUpdated";
	public static final String SUBS_ERRORS = "subsErrors";
	public static final String FAILURE_REASONS = "failureReasons";


	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	SubManager subManager;

	@Autowired
	MailManager mailManager;

	@Autowired
	TaskUtil taskUtil;


	//TODO: this should run only once a day.. only pick up files that are sent to EFS more than 3 days ago or something like that..
	//TODO: This should actually be a listener job that listens to EFSFilingStatusChange events.
	@Recurring(id = "domainservice-update-filing-status-job", interval = "PT4H")
	@Override
	public void startTask() {
		jobName = "domainservice-update-filing-status-job";
		chunkSize = 1;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		List<FilingStatus> filingsStatuses = List.of(FilingStatus.Received, FilingStatus.Queued, FilingStatus.Processing, FilingStatus.Sent_To_Agency);
		Set<Long> domainIdsWithFilingsToBeUpdated = new HashSet<>();
		List<FedSub> fedSubsInProcessing = subManager.findByFedSubStatusIn(filingsStatuses);
		for (FedSub f : fedSubsInProcessing) {
			domainIdsWithFilingsToBeUpdated.add(f.getFiling().getPayer().getDomainId());
		}

		List<StateFilingStatus> stateFilingsStatuses = List.of(StateFilingStatus.Received, StateFilingStatus.Queued, StateFilingStatus.Processing, StateFilingStatus.Sent_To_Agency);
		List<StateSub> stateSubsInProcessing = subManager.findByStateSubStatusIn(stateFilingsStatuses);
		for (StateSub f : stateSubsInProcessing) {
			domainIdsWithFilingsToBeUpdated.add(f.getFiling().getPayer().getDomainId());
		}

		CronJobAuthenticationUtil.cleanAuthentication();
		return domainIdsWithFilingsToBeUpdated.stream().map(String::valueOf).collect(Collectors.toList());
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
		Long domainId = Long.parseLong(unitOfWork);

		List<FilingStatus> filingsStatuses = List.of(FilingStatus.Received, FilingStatus.Queued, FilingStatus.Processing, FilingStatus.Sent_To_Agency);
		List<StateFilingStatus> stateFilingsStatuses = List.of(StateFilingStatus.Received, StateFilingStatus.Queued, StateFilingStatus.Processing, StateFilingStatus.Sent_To_Agency);

		List<FedSub> fedSubsInProcessing = subManager.findByDomainIdAndFedSubStatusIn(domainId, filingsStatuses);
		List<StateSub> stateSubsInProcessing = subManager.findByDomainIdAndStateSubStatusIn(domainId, stateFilingsStatuses);

		List<Long> efsFilingIdsToBeUpdated = new ArrayList<>();
		Map<String, FedSub> fedSubsBySubRefId = new HashMap<>();
		Map<String, StateSub> stateSubsBySubRefId = new HashMap<>();

		for (FedSub f : fedSubsInProcessing) {
			efsFilingIdsToBeUpdated.add(f.getFilingRequestId());
			fedSubsBySubRefId.put(f.getSubmissionRefId(), f);
		}
		for (StateSub f : stateSubsInProcessing) {
			efsFilingIdsToBeUpdated.add(f.getFilingRequestId());
			stateSubsBySubRefId.put(f.getSubmissionRefId(), f);
		}

		HashMap<Long, Filing> updatedFilings = new HashMap<>();
		int totalSubs = 0;
		int subsNoUpdate = 0;
		int subsUpdated = 0;
		int subsErrors = 0;
		Map<String, String> failureReasons = new HashMap<>();
		totalSubs = efsFilingIdsToBeUpdated.size();
		try {
			List<FilingStatusDTO> efsFilingStauses = efsFilingsMgr.getFilingStatus(efsFilingIdsToBeUpdated);
			for (FilingStatusDTO efsFileNameStatus : efsFilingStauses) {

				FilingStatus newFedSubStatus = FilingStatus.getFromEFSFilingStatus(efsFileNameStatus.status.name());
				if(newFedSubStatus == null) {
					if (efsFileNameStatus.status == com.aphe.efs.model.enums.FilingStatus.System_Rejected || efsFileNameStatus.status == com.aphe.efs.model.enums.FilingStatus.Agency_Rejected) {
						newFedSubStatus = FilingStatus.Draft;
					}
				}
				//If the new status is still nul, treat this as an error and move on to the next filing.
				if(newFedSubStatus == null) {
					logger.error("Attention Required: Unknown filing efsFileNameStatus from EFS filingRequestId=" + efsFileNameStatus.filingId);
					subsErrors++;
					failureReasons.put(efsFileNameStatus.clientRefId, "Unknown filing efsFileNameStatus from EFS filingRequestId=" + efsFileNameStatus.filingId);
					continue;
				}

				//Ignore EFS internal efsFilingStauses of Queued and processing. This is also to prevent multiple email notification we send replacement files to IRS because of any issues on our side.
				if(newFedSubStatus == FilingStatus.Queued || newFedSubStatus == FilingStatus.Processing) {
					subsNoUpdate++;
					continue;
				}

				FedSub fedSubToBeUpdated = fedSubsBySubRefId.get(efsFileNameStatus.clientRefId);
				if (fedSubToBeUpdated != null) {
					if(fedSubToBeUpdated.getStatus() != newFedSubStatus) {
						Filing updatedFiling = localFilingMgr.updateFedFilingStatusByRefId(efsFileNameStatus.clientRefId, newFedSubStatus, efsFileNameStatus.statusChangeDesc, new Date(), efsFileNameStatus.uniqueRecordId);
						if (updatedFiling != null) {
							subsUpdated++;
							updatedFilings.put(updatedFiling.getId(), updatedFiling);
						} else {
							subsErrors++;
							failureReasons.put(efsFileNameStatus.clientRefId, "Failed to update the filing status to " + newFedSubStatus.name() + " for clientRefId=" + efsFileNameStatus.clientRefId);
						}
					} else{
						subsNoUpdate++;
					}
				} else {
					subsErrors++;
					failureReasons.put(efsFileNameStatus.clientRefId, "Fed sub not found for clientRefId=" + efsFileNameStatus.clientRefId);
				}

				//See if there is state sub to be updated. Not EFS Filings may have corresonding state sub.
				StateSub stateSubToBeUpdated = stateSubsBySubRefId.get(efsFileNameStatus.clientRefId);
				StateSub filingStateSub = fedSubToBeUpdated.getFiling().getStateSub();
				//filingStateSub should be same as stateSubToBeUpdated. both could be null or both should have same id.
				if(filingStateSub != null && stateSubToBeUpdated != null) {
					if(!filingStateSub.getId().equals(stateSubToBeUpdated.getId())) {
						subsErrors++;
						failureReasons.put(efsFileNameStatus.clientRefId, "State sub id mismatch for clientRefId=" + efsFileNameStatus.clientRefId + ". filingStateSub.id=" + filingStateSub.getId() + " stateSubToBeUpdated.id=" + stateSubToBeUpdated.getId());
						logger.error("Attention Required: State sub id mismatch for clientRefId=" + efsFileNameStatus.clientRefId + ". filingStateSub.id=" + filingStateSub.getId() + " stateSubToBeUpdated.id=" + stateSubToBeUpdated.getId());
					} else {
						StateFilingStatus newStateFilingStatus = StateFilingStatus.getStateFilingStatusByName(newFedSubStatus.name());
						if(newStateFilingStatus == null) {
							if(newFedSubStatus == FilingStatus.Draft) {
								newStateFilingStatus = StateFilingStatus.None;
							}
						}

						//If the new status is still nul, treat this as an error and move on to the next filing.
						if(newStateFilingStatus == null) {
							subsErrors++;
							failureReasons.put(efsFileNameStatus.clientRefId, "Invalid state filing efsFileNameStatus: " + newFedSubStatus.name());
							continue;
						}

						if (stateSubToBeUpdated.getStatus() != newStateFilingStatus) {
							Filing updatedFiling = localFilingMgr.updateStateFilingStatusByRefId(efsFileNameStatus.clientRefId, newFedSubStatus, efsFileNameStatus.statusChangeDesc, new Date(), efsFileNameStatus.uniqueRecordId);
							if (updatedFiling != null) {
								updatedFilings.put(updatedFiling.getId(), updatedFiling);
 							} else {
								subsErrors++;
								failureReasons.put(efsFileNameStatus.clientRefId, "Failed to update the filing status to " + newFedSubStatus.name() + " for clientRefId=" + efsFileNameStatus.clientRefId);
							}
						} else {
							subsNoUpdate++;
						}
					}
				} else if(filingStateSub == null && stateSubToBeUpdated != null) {
					subsErrors++;
					failureReasons.put(efsFileNameStatus.clientRefId, "State sub not found for clientRefId=" + efsFileNameStatus.clientRefId + ". filingStateSub.id=" + filingStateSub + " stateSubToBeUpdated.id=" + stateSubToBeUpdated.getId());
					logger.error("Attention Required: State sub not found for clientRefId=" + efsFileNameStatus.clientRefId + ". filingStateSub.id=" + filingStateSub + " stateSubToBeUpdated.id=" + stateSubToBeUpdated.getId());
				}
			}
			Map<String, String> values = new HashMap<>();
			values.put(TOTAL_SUBS, String.valueOf(totalSubs));
			values.put(SUBS_NO_UPDATE, String.valueOf(subsNoUpdate));
			values.put(SUBS_UPDATED, String.valueOf(subsUpdated));
			values.put(SUBS_ERRORS, String.valueOf(subsErrors));
			values.put(FAILURE_REASONS, taskUtil.buildFailureReasons(failureReasons));
			return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
		} catch (Exception e) {
			logger.error("Error updating the filing status: ", e);
			return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
		}
	}

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of(TOTAL_SUBS, SUBS_NO_UPDATE, SUBS_UPDATED, SUBS_ERRORS, FAILURE_REASONS);
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
		// Send email only if errored or skipped
		if (errored == 0) {
			return;
		}
		long emailStartTime = System.currentTimeMillis();
		mailManager.sendSystemStatusEmail(subject, emailHTML);
		long emailTime = System.currentTimeMillis() - emailStartTime;
		logger.info("Email sent in {}ms", emailTime);
	}
}
