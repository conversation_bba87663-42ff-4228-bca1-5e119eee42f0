package com.aphe.contractor.tasks.tinm;

import com.aphe.AppUtil;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.DateUtil;
import com.aphe.contractor.model.TINMatchRequest;
import com.aphe.contractor.model.enums.TINMatchRequestStatus;
import com.aphe.contractor.model.enums.TINMatchResult;
import com.aphe.contractor.services.MailManager;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.TINMatchRequestManager;
import com.aphe.contractor.tasks.TaskUtil;
import com.aphe.efs.tinm.dto.TINMatchRequestStatusDTO;
import com.aphe.efs.tinm.services.EFSTINMatchRequestManager;
import org.jetbrains.annotations.NotNull;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Auto task to update the status of all the filings sent to EFS.
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class UpdateTINMatchRequestStatusMultiNodeTask extends ApheBackgroundTask {

	Logger logger = LoggerFactory.getLogger(UpdateTINMatchRequestStatusMultiNodeTask.class);

	public static final String DOMAIN_NAME = "domainName";
	public static final String REQUESTS_TO_BE_UPDATED = "requestsToBeUpdated";
	public static final String REQUESTS_FAILED = "requestsFailed";
	public static final String REQUESTS_SUCCESSFUL = "requestsSuccessful";
	public static final String REQUESTS_SENT_TO_AGENCY = "requestsSentToAgency";

	@Autowired
	EFSTINMatchRequestManager efstinMatchRequestManager;

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;

	@Autowired
	TaskUtil taskUtil;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	MailManager mailManager;

	@Recurring(id = "domainservice-update-tinmatches-job", interval = "PT60S")
	@Override
	public void startTask() {
		jobName = "domainservice-update-tinmatches-job";
		chunkSize = 1;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		if(DateUtil.is1099PeakDay()) {
			logger.info("1099 Peak Day. Not processing the TIN Match update status job");
			return List.of();
		} else {
			CronJobAuthenticationUtil.configureAuthentication("superadmin");
			List<TINMatchRequestStatus> tinMatchRequestStatuses = getTinMatchRequestStatuses();
			List<TINMatchRequest> requestToBeProcessed = tinMatchRequestManager.findByStatusIn(tinMatchRequestStatuses);
			// Group them by domainId and extract domainIds.
			Set<String> domainIds = new HashSet<>();
			for (TINMatchRequest tinMatchRequest : requestToBeProcessed) {
				Long domainId = tinMatchRequest.getPayee().getPayer().getDomainId();
				domainIds.add(domainId.toString());
			}
			CronJobAuthenticationUtil.cleanAuthentication();
			return new ArrayList<>(domainIds);
		}
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
		Long domainId = Long.parseLong(unitOfWork);

		CronJobAuthenticationUtil.configureAuthentication("superadmin");
		try {
			//First get all the tin match requests for this domain.
			List<TINMatchRequestStatus> tinMatchRequestStatuses = getTinMatchRequestStatuses();
			List<TINMatchRequest> tinMatchRequests = tinMatchRequestManager.findByDomainIdAndStatusIn(domainId, tinMatchRequestStatuses);
			List<String> tinMatchRequestsToBeUpdated = tinMatchRequests.stream().map(TINMatchRequest::getTinMatchRequestId).collect(Collectors.toList());


			List<TINMatchRequestStatusDTO> statuses = efstinMatchRequestManager.getRequestStatus(tinMatchRequestsToBeUpdated);

			HashMap<Long, TINMatchRequest> updatedTINMatchRequests = new HashMap<>();

			for (TINMatchRequestStatusDTO status : statuses) {
				TINMatchRequestStatus newStatus = TINMatchRequestStatus.valueOf(status.status.name());
				//Ignore EFS internal statuses of Queued and processing. This is also to prevent multiple email notification we send replacement files to IRS because of any issues on our side.
				if(newStatus == TINMatchRequestStatus.Queued || newStatus == TINMatchRequestStatus.Processing) {
					continue;
				}
				TINMatchRequest updatedFiling = tinMatchRequestManager.updateStatusByRefId(status.clientRefId, newStatus, status.statusChangeDesc, new Date(), status.tinMatchResult);
				if (updatedFiling != null) {
					updatedTINMatchRequests.put(updatedFiling.getId(), updatedFiling);
				}
			}

			Set<TINMatchRequest> verifiedRequests = new HashSet<>();
			Set<TINMatchRequest> unverifiedRequests = new HashSet<>();
			Set<TINMatchRequest> sentToAgencyRequest = new HashSet<>();
			for (TINMatchRequest tinMatchRequest : updatedTINMatchRequests.values()) {
				if (TINMatchRequestStatus.Response_Received == tinMatchRequest.getTinMatchStatus()) {

					if (TINMatchResult.INVALID_RESULTS.contains(tinMatchRequest.getTinMatchResult().getResultCode())
							|| TINMatchResult.UNKNOWN_RESULTS.contains(tinMatchRequest.getTinMatchResult().getResultCode())) {
						unverifiedRequests.add(tinMatchRequest);
					} else if (TINMatchResult.VALID_RESULTS.contains(tinMatchRequest.getTinMatchResult().getResultCode())) {
						verifiedRequests.add(tinMatchRequest);
					}
				} else if (TINMatchRequestStatus.Sent_To_Agency == tinMatchRequest.getTinMatchStatus()) {
					sentToAgencyRequest.add(tinMatchRequest);
				}
			}

			boolean hasUpdate = verifiedRequests.size() > 0 || unverifiedRequests.size() > 0 || sentToAgencyRequest.size() > 0;

			if (hasUpdate) {

				Map<Long, String> domainNames = new HashMap<>();
				Map<Long, List<UserDTO>> usersByDomain = new HashMap<>();
				Map<Long, List<Long>> accountantsByDomain = new HashMap<>();
				taskUtil.buildCommunicationData(domainId, domainNames, usersByDomain, accountantsByDomain);

				List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);
				String domainName = domainNames.get(domainId);


				if(!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
					mailUtil.sendTINMatchFailedEmail(unverifiedRequests, domainEmailUsers, domainName);
				}
				// mailUtil.sendTINMatchVerifiedEmail(verifiedRequests, domainEmailUsers, displayName);
				//mailUtil.sendTINMatchSentToAgencyEmail(sentToAgencyRequests, domainEmailUsers, displayName);
				Map<String, String> values = new HashMap<>();
				values.put(DOMAIN_NAME, domainName);
				values.put(REQUESTS_TO_BE_UPDATED, String.valueOf(tinMatchRequestsToBeUpdated.size()));
				values.put(REQUESTS_SENT_TO_AGENCY, String.valueOf(sentToAgencyRequest.size()));
				values.put(REQUESTS_FAILED, String.valueOf(unverifiedRequests.size()));
				values.put(REQUESTS_SUCCESSFUL, String.valueOf(sentToAgencyRequest.size()));

				return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);

			} else {
				logger.info("No tin match requests to be updated for domainId={}", domainId);
				return new ApheExecutionData(ApheExecutionResult.SKIPPED, "No tin match requests to be updated");
			}
		}catch (Exception e) {
			logger.error("Error updating tin match request status: ", e);
			return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
		} finally {
			CronJobAuthenticationUtil.cleanAuthentication();
		}
	}

	private static @NotNull List<TINMatchRequestStatus> getTinMatchRequestStatuses() {
		List<TINMatchRequestStatus> tinMatchRequestStatuses = new ArrayList<>();
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Received);
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Queued);
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Processing);
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Sent_To_Agency);
		return tinMatchRequestStatuses;
	}

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of(DOMAIN_NAME, REQUESTS_TO_BE_UPDATED, REQUESTS_SENT_TO_AGENCY, REQUESTS_FAILED, REQUESTS_SUCCESSFUL);
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
		if (errored == 0) {
			return;
		}
		long emailStartTime = System.currentTimeMillis();
		mailManager.sendSystemStatusEmail(subject, emailHTML);
		long emailTime = System.currentTimeMillis() - emailStartTime;
		logger.info("Email sent in {}ms", emailTime);
	}
}
