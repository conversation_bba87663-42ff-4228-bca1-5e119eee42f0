package com.aphe.contractor.tasks.tinm;

import com.aphe.AppUtil;
import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.MappedValidationErros;
import com.aphe.common.error.exceptions.ApheDataListValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.TINMatchRequest;
import com.aphe.contractor.model.enums.TINMatchRequestStatus;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.TINMatchRequestManager;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.tinm.dto.TINMatchRequestDTO;
import com.aphe.efs.tinm.model.EFSTINMatchRequest;
import com.aphe.efs.tinm.services.EFSTINMatchRequestManager;
import org.hibernate.validator.constraints.ValidationErrors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Auto task to take submitted filings by the user and submit to the EFS 
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class SubmitTINMatchRequestsTask extends GenericBackgroundTask {

	@Autowired
	EFSTINMatchRequestManager efsTINMatchRequestManager;

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;

	@Autowired
	ContractorConvertUtil conversionUtil;

	@Autowired
	AuthManager authManager;

	@Autowired
	DomainMgr domainMgr;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	Logger logger = LoggerFactory.getLogger(SubmitTINMatchRequestsTask.class);

	@Scheduled(initialDelayString = "${aphe.contractor.submitTINMatchRequestsTask.initialDelay}", fixedDelayString = "${aphe.contractor.submitTINMatchRequestsTask.fixedDelay}")
	public void submitTINMatchRequestsTask() {

		boolean pauseTasks = isPauseTasks("domainservice-submit-tinmatches-job");
		if(pauseTasks) {
			return;
		}

		//logger.info("Start of executing the taskName=SubmitFilingsTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		processTINMatchSubmissions();

		//State self file submissions.
		//They would start with a Submitted status...
		//Change them to reminded and put up a todo task on dashboard, and when customer marks them taken care of, change the status to filed.

		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=SubmitTINMatchRequests status=End");

	}

	private void buildCommunicationData(HashMap<Long, List<TINMatchRequest>> tinMatchRequests,
											   Map<Long, String> domainNames,
											   Map<Long, List<UserDTO>> usersByDomain,
										       Map<Long, List<Long>> accountantsByDomain) {
		String token = authManager.getTokenForSystemUser();

		Set<Long> domainIds = tinMatchRequests.keySet();
		usersByDomain.putAll(authManager.getDomainUsersRemote(token, domainIds));
		accountantsByDomain.putAll(authManager.getDomainAccountantsRemote(token, domainIds));
		authManager.deleteToken(token);

		for (Long domainId : domainIds) {

			List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);

			String domainName = "";

			try {
				DomainDTO domainDTO = domainMgr.getDomain(domainId);
				domainName = contractorConvertUtil.getDomainDisplayName(domainDTO);
				domainNames.put(domainId, domainName);
				//TODO: Make sure this is a valid email address. Here and where domainDTO email is being used.
				if (domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
					UserDTO userDto = new UserDTO();
					userDto.setEmail(domainDTO.emailAddress);
					userDto.setFirstName(domainName);
					userDto.setLastName("");
					domainEmailUsers.add(userDto);
				}
			} catch (NumberFormatException | ApheForbiddenException e2) {
			}
		}
	}

	//TODO: We could the same thing as Submit Filings task. Each worker thread could take a domain to process.

	private void processTINMatchSubmissions() {
		List<TINMatchRequest> requestToBeProcessed = tinMatchRequestManager.findByStatusIn(TINMatchRequestStatus.Submitted);

		// Group them by domainId and generate the efs payload. Do for each domain in a separate transaction.
		HashMap<Long, List<TINMatchRequest>> requestsByDomain = new HashMap<>();
		for (TINMatchRequest tinMatchRequest : requestToBeProcessed) {
			Long domainId = tinMatchRequest.getPayee().getPayer().getDomainId();
			List<TINMatchRequest> domainRequests = requestsByDomain.get(domainId);
			if (domainRequests == null) {
				domainRequests = new ArrayList<TINMatchRequest>();
				requestsByDomain.put(domainId, domainRequests);
			}
			domainRequests.add(tinMatchRequest);
		}

		if (requestsByDomain.keySet().size() > 0) {

			// First get all the users and domain names of these domainIds.
			Map<Long, String> domainNames = new HashMap<>();
			Map<Long, List<UserDTO>> usersByDomain =  new HashMap<>();
			Map<Long, List<Long>> accountantsByDomain =  new HashMap<>();
			buildCommunicationData(requestsByDomain, domainNames, usersByDomain, accountantsByDomain);

			for (Long domainId : requestsByDomain.keySet()) {

				List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);
				String domainName = domainNames.get(domainId);

				List<TINMatchRequest> tinMatchRequests = requestsByDomain.get(domainId);

				Map<Long, String> conversionErrorsById = new HashMap<>();
				Map<Long, String> submissionErrorsById = new HashMap<>();
				Set<TINMatchRequest> successfulRequests = new HashSet<>();
				Set<TINMatchRequest> failedRequests = new HashSet<>();

				Map<TINMatchRequestDTO, TINMatchRequest> convertedFilings = new HashMap<>();

				for (TINMatchRequest tinMatchRequest : tinMatchRequests) {
					try {
						TINMatchRequestDTO efsTINMatchRequestDTO = conversionUtil.convertToEFSTINMatchRequestDTO(tinMatchRequest, domainId);
						convertedFilings.put(efsTINMatchRequestDTO, tinMatchRequest);
					} catch (Exception e) {
						logger.error("Error converting a tinMatchRequests to EFS DTO ", e);
						conversionErrorsById.put(tinMatchRequest.getId(), "Error processing your data. Please contact us.");
						failedRequests.add(tinMatchRequest);
					}
				}

				boolean submitted = false;
				List<EFSTINMatchRequest> efsTINMatchRequests = null;
				try {
					List<TINMatchRequestDTO> requestDTOS = new ArrayList<>();
					requestDTOS.addAll(convertedFilings.keySet());
					if (requestDTOS.size() > 0) {
						efsTINMatchRequests = efsTINMatchRequestManager.addRequests(requestDTOS);
						submitted = true;
//						successfulRequests.addAll(convertedFilings.values());
					}
				} catch (ApheDataListValidationException e) {
					MappedValidationErros errors = e.getMappedErrors();
					logger.error("Error submitting tinMatchRequests: ", e);
					logger.error("Error submitting tinMatchRequests: " + errors.getErrors().toString());
					Map<String, ValidationErrors> mappedErrors = errors.getErrors();
					for (String id : mappedErrors.keySet()) {
						submissionErrorsById.put(Long.parseLong(id), errors.getErrors().toString());
					}
					failedRequests.addAll(convertedFilings.values());

				} catch (Exception e) {
					logger.error("Error submitting tinMatchRequests: ", e);
				}

				if (submitted && efsTINMatchRequests != null && efsTINMatchRequests.size() > 0) {
					for (EFSTINMatchRequest efstinMatchRequest : efsTINMatchRequests) {
						tinMatchRequestManager.receiveTINMatchRequest(efstinMatchRequest.getClientRefId(), efstinMatchRequest.getId());
					}
//					mailUtil.sendTINMatchSubmittedEmail(successfulRequests, domainEmailUsers, domainName);
				}

				if (conversionErrorsById.size() > 0) {
					try {
						tinMatchRequestManager.cancelTINMatchRequestsByIds(conversionErrorsById);
					} catch (Exception e1) {
						logger.error("Error changing the status of failed tinMatchRequests to Draft ", e1);
					}
				}

				if (submissionErrorsById.size() > 0) {
					try {
						tinMatchRequestManager.cancelTINMatchRequestsByIds(submissionErrorsById);
					} catch (Exception e1) {
						logger.error("Error changing the status of failed tinMatchRequests to Draft ", e1);
					}
				}

				if (failedRequests.size() > 0) {
					// Do not send for GS.
					if(!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
						mailUtil.sendTINMatchSystemRejectedEmail(failedRequests, domainEmailUsers, domainName);
					}
				}
			}
		}
	}

}
