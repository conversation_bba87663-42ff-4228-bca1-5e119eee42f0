package com.aphe.contractor.tasks.tinm;

import com.aphe.AppUtil;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.MappedValidationErros;
import com.aphe.common.error.exceptions.ApheDataListValidationException;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.TINMatchRequest;
import com.aphe.contractor.model.enums.TINMatchRequestStatus;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.contractor.services.MailManager;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.TINMatchRequestManager;
import com.aphe.contractor.tasks.TaskUtil;
import com.aphe.efs.tinm.dto.TINMatchRequestDTO;
import com.aphe.efs.tinm.model.EFSTINMatchRequest;
import com.aphe.efs.tinm.services.EFSTINMatchRequestManager;
import org.hibernate.validator.constraints.ValidationErrors;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Auto task to take submitted filings by the user and submit to the EFS 
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class SubmitTINMatchRequestsMultiNodeTask extends ApheBackgroundTask {

	Logger logger = LoggerFactory.getLogger(SubmitTINMatchRequestsMultiNodeTask.class);

	@Autowired
	EFSTINMatchRequestManager efsTINMatchRequestManager;

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;

	@Autowired
	ContractorConvertUtil conversionUtil;

	@Autowired
	TaskUtil taskUtil;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	MailManager mailManager;

	@Recurring(id = "domainservice-submit-tinmatches-job", interval = "PT60S")
	@Override
	public void startTask() {
		jobName = "domainservice-submit-tinmatches-job";
		chunkSize = 1;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		List<TINMatchRequest> requestToBeProcessed = tinMatchRequestManager.findByStatusIn(TINMatchRequestStatus.Submitted);
		// Group them by domainId and extract domainids.
		Set<String> domainIds = new HashSet<>();
		for (TINMatchRequest tinMatchRequest : requestToBeProcessed) {
			Long domainId = tinMatchRequest.getPayee().getPayer().getDomainId();
			domainIds.add(domainId.toString());
		}
		return new ArrayList<>(domainIds);
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {

		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		try {
			// First get all the users and domain names of these domainIds.
			Map<Long, String> domainNames = new HashMap<>();
			Map<Long, List<UserDTO>> usersByDomain = new HashMap<>();
			Map<Long, List<Long>> accountantsByDomain = new HashMap<>();
			taskUtil.buildCommunicationData(Long.parseLong(unitOfWork), domainNames, usersByDomain, accountantsByDomain);

			Long domainId = Long.parseLong(unitOfWork);

			List<UserDTO> domainEmailUsers = usersByDomain.get(domainId);
			String domainName = domainNames.get(domainId);

			List<TINMatchRequestStatus> statuses = List.of(TINMatchRequestStatus.Submitted);

			List<TINMatchRequest> tinMatchRequests = tinMatchRequestManager.findByDomainIdAndStatusIn(domainId, statuses);

			Map<Long, String> conversionErrorsById = new HashMap<>();
			Map<Long, String> submissionErrorsById = new HashMap<>();
			Set<TINMatchRequest> successfulRequests = new HashSet<>();
			Set<TINMatchRequest> failedRequests = new HashSet<>();

			Map<TINMatchRequestDTO, TINMatchRequest> convertedFilings = new HashMap<>();

			for (TINMatchRequest tinMatchRequest : tinMatchRequests) {
				try {
					TINMatchRequestDTO efsTINMatchRequestDTO = conversionUtil.convertToEFSTINMatchRequestDTO(tinMatchRequest, domainId);
					convertedFilings.put(efsTINMatchRequestDTO, tinMatchRequest);
				} catch (Exception e) {
					logger.error("Error converting a tinMatchRequests to EFS DTO ", e);
					conversionErrorsById.put(tinMatchRequest.getId(), "Error processing your data. Please contact us.");
					failedRequests.add(tinMatchRequest);
				}
			}

			boolean submitted = false;
			List<EFSTINMatchRequest> efsTINMatchRequests = null;
			try {
				List<TINMatchRequestDTO> requestDTOS = new ArrayList<>();
				requestDTOS.addAll(convertedFilings.keySet());
				if (requestDTOS.size() > 0) {
					efsTINMatchRequests = efsTINMatchRequestManager.addRequests(requestDTOS);
					submitted = true;
//						successfulRequests.addAll(convertedFilings.values());
				}
			} catch (ApheDataListValidationException e) {
				MappedValidationErros errors = e.getMappedErrors();
				logger.error("Error submitting tinMatchRequests: ", e);
				logger.error("Error submitting tinMatchRequests: " + errors.getErrors().toString());
				Map<String, ValidationErrors> mappedErrors = errors.getErrors();
				for (String id : mappedErrors.keySet()) {
					submissionErrorsById.put(Long.parseLong(id), errors.getErrors().toString());
				}
				failedRequests.addAll(convertedFilings.values());

			} catch (Exception e) {
				logger.error("Error submitting tinMatchRequests: ", e);
			}

			if (submitted && efsTINMatchRequests != null && efsTINMatchRequests.size() > 0) {
				for (EFSTINMatchRequest efstinMatchRequest : efsTINMatchRequests) {
					tinMatchRequestManager.receiveTINMatchRequest(efstinMatchRequest.getClientRefId(), efstinMatchRequest.getId());
				}
//					mailUtil.sendTINMatchSubmittedEmail(successfulRequests, domainEmailUsers, domainName);
			}

			if (conversionErrorsById.size() > 0) {
				try {
					tinMatchRequestManager.cancelTINMatchRequestsByIds(conversionErrorsById);
				} catch (Exception e1) {
					logger.error("Error changing the status of failed tinMatchRequests to Draft ", e1);
				}
			}

			if (submissionErrorsById.size() > 0) {
				try {
					tinMatchRequestManager.cancelTINMatchRequestsByIds(submissionErrorsById);
				} catch (Exception e1) {
					logger.error("Error changing the status of failed tinMatchRequests to Draft ", e1);
				}
			}

			if (failedRequests.size() > 0) {
				// Do not send for GS.
				if (!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
					mailUtil.sendTINMatchSystemRejectedEmail(failedRequests, domainEmailUsers, domainName);
				}
			}
			return new ApheExecutionData(ApheExecutionResult.SUCCESS, null);
		} catch (Exception e) {
			logger.error("Error submitting tinMatchRequests: ", e);
			return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
		} finally {
			CronJobAuthenticationUtil.cleanAuthentication();
		}




	}

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of();
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
		if (errored == 0) {
			return;
		}
		long emailStartTime = System.currentTimeMillis();
		mailManager.sendSystemStatusEmail(subject, emailHTML);
		long emailTime = System.currentTimeMillis() - emailStartTime;
		logger.info("Email sent in {}ms", emailTime);
	}
}
