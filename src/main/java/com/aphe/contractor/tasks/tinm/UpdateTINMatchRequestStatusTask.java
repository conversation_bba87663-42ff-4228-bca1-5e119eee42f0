package com.aphe.contractor.tasks.tinm;

import com.aphe.AppUtil;
import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.DateUtil;
import com.aphe.contractor.model.TINMatchRequest;
import com.aphe.contractor.model.enums.TINMatchRequestStatus;
import com.aphe.contractor.model.enums.TINMatchResult;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.contractor.services.MailUtil;
import com.aphe.contractor.services.TINMatchRequestManager;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.tinm.dto.TINMatchRequestStatusDTO;
import com.aphe.efs.tinm.services.EFSTINMatchRequestManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Auto task to update the status of all the filings sent to EFS.
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class UpdateTINMatchRequestStatusTask extends GenericBackgroundTask {

	Logger logger = LoggerFactory.getLogger(UpdateTINMatchRequestStatusTask.class);

	@Autowired
	EFSTINMatchRequestManager efstinMatchRequestManager;

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;

	@Autowired
	AuthManager authManager;
	
	@Autowired
	DomainMgr domainMgr;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	//TODO: This should be as update Filing status task...

	@Scheduled(initialDelayString = "${aphe.contractor.updateTINMatchRequestStatusTask.initialDelay}", fixedDelayString = "${aphe.contractor.updateTINMatchRequestStatusTask.fixedDelay}")
	public void updateFilingsStatusTask() {

		boolean pauseTasks = isPauseTasks("domainservice-update-filings-job");
		if(pauseTasks) {
			return;
		}

		if(DateUtil.is1099PeakDay()) {
			//logger.info("1099 Peak Day. Not processing the TIN Match Request Status");
			return;
		}

		//logger.info("Start of executing the taskName=updateTINMatchRequestStatusTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		// Find all the filings that have been in FilingStatus=Processing and
		// ProcessingStatus != null
		List<TINMatchRequestStatus> tinMatchRequestStatuses = new ArrayList<>();
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Received);
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Queued);
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Processing);
		tinMatchRequestStatuses.add(TINMatchRequestStatus.Sent_To_Agency);

		List<String> tinMatchRequestsToBeUpdated = new ArrayList<>();


		List<TINMatchRequest> tinMatchRequestsInProcessing = tinMatchRequestManager.findByStatusIn(tinMatchRequestStatuses);
		for (TINMatchRequest tinMatchRequest : tinMatchRequestsInProcessing) {
			tinMatchRequestsToBeUpdated.add(tinMatchRequest.getTinMatchRequestId());
		}

		if(tinMatchRequestsToBeUpdated.size() > 0) {
			updateTINMatchRequestStatus(tinMatchRequestsToBeUpdated);
		}

		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=updateTINMatchRequestStatusTask status=End");

	}

	private void updateTINMatchRequestStatus(List<String> tinMatchRequestsToBeUpdated) {
		HashMap<Long, TINMatchRequest> updatedTINMatchRequests = new HashMap<>();
		try {
			List<TINMatchRequestStatusDTO> statuses = efstinMatchRequestManager.getRequestStatus(tinMatchRequestsToBeUpdated);

			for (TINMatchRequestStatusDTO status : statuses) {
				TINMatchRequestStatus newStatus = TINMatchRequestStatus.valueOf(status.status.name());
				//Ignore EFS internal statuses of Queued and processing. This is also to prevent multiple email notification we send replacement files to IRS because of any issues on our side.
				if(newStatus == TINMatchRequestStatus.Queued || newStatus == TINMatchRequestStatus.Processing) {
					continue;
				}
				TINMatchRequest updatedFiling = tinMatchRequestManager.updateStatusByRefId(status.clientRefId, newStatus, status.statusChangeDesc, new Date(), status.tinMatchResult);
				if (updatedFiling != null) {
					updatedTINMatchRequests.put(updatedFiling.getId(), updatedFiling);
				}
			}
		} catch (Exception e) {
			logger.error("Error updating the tin match request status: ", e);
		}

		// Now group them by domainId for rejected and accepted filings.
		Map<Long, Set<TINMatchRequest>> verifiedRequestsByDomain = new HashMap<>();
		Map<Long, Set<TINMatchRequest>> unverifiedRequestsByDomain = new HashMap<>();
		Map<Long, Set<TINMatchRequest>> sentToAgencyRequestsByDomain = new HashMap<>();
		for (TINMatchRequest tinMatchRequest : updatedTINMatchRequests.values()) {
			if (TINMatchRequestStatus.Response_Received == tinMatchRequest.getTinMatchStatus()) {
				if (TINMatchResult.INVALID_RESULTS.contains(tinMatchRequest.getTinMatchResult().getResultCode())
						||TINMatchResult.UNKNOWN_RESULTS.contains(tinMatchRequest.getTinMatchResult().getResultCode()) ){
					Set<TINMatchRequest> domainFailedTINMatchRequests = unverifiedRequestsByDomain.get(tinMatchRequest.getPayee().getPayer().getDomainId());
					if (domainFailedTINMatchRequests == null) {
						domainFailedTINMatchRequests = new HashSet<>();
						unverifiedRequestsByDomain.put(tinMatchRequest.getPayee().getPayer().getDomainId(), domainFailedTINMatchRequests);
					}
					domainFailedTINMatchRequests.add(tinMatchRequest);
				} else if (TINMatchResult.VALID_RESULTS.contains(tinMatchRequest.getTinMatchResult().getResultCode())) {
					Set<TINMatchRequest> domainSuccessfulRequests = verifiedRequestsByDomain.get(tinMatchRequest.getPayee().getPayer().getDomainId());
					if (domainSuccessfulRequests == null) {
						domainSuccessfulRequests = new HashSet<>();
						verifiedRequestsByDomain.put(tinMatchRequest.getPayee().getPayer().getDomainId(), domainSuccessfulRequests);
					}
					domainSuccessfulRequests.add(tinMatchRequest);
				}
			} else if(TINMatchRequestStatus.Sent_To_Agency == tinMatchRequest.getTinMatchStatus()) {
				Set<TINMatchRequest> domainSentToAgencyRequests = sentToAgencyRequestsByDomain.get(tinMatchRequest.getPayee().getPayer().getDomainId());
				if (domainSentToAgencyRequests == null) {
					domainSentToAgencyRequests = new HashSet<>();
					sentToAgencyRequestsByDomain.put(tinMatchRequest.getPayee().getPayer().getDomainId(), domainSentToAgencyRequests);
				}
				domainSentToAgencyRequests.add(tinMatchRequest);
			}
		}
		Set<Long> allDomainIds = new HashSet<>();
		allDomainIds.addAll(verifiedRequestsByDomain.keySet());
		allDomainIds.addAll(unverifiedRequestsByDomain.keySet());
		allDomainIds.addAll(sentToAgencyRequestsByDomain.keySet());

		if (allDomainIds.size() > 0) {
			// First get all the users of these domainIds.
			String token = authManager.getTokenForSystemUser();
			Map<Long, List<UserDTO>> usersByDomain = authManager.getDomainUsersRemote(token, allDomainIds);
			Map<Long, List<Long>> accountantsByDomain = authManager.getDomainAccountantsRemote(token, allDomainIds);
			authManager.deleteToken(token);

			// Now send the emails by each domainId.
			for (Long domainId : unverifiedRequestsByDomain.keySet()) {

				List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
				try {
					DomainDTO domainDTO = domainMgr.getDomain(domainId);
					String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
					if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
						UserDTO userDto = new UserDTO();
						userDto.setEmail(domainDTO.emailAddress);
						userDto.setFirstName(displayName);
						userDto.setLastName("");
						domainEmailUsers.add(userDto);
					}
					Set<TINMatchRequest> failedRequests = unverifiedRequestsByDomain.get(domainId);

					//Do not send for GS... this is ok, as this will result in rejecting the filing.
					if(!AppUtil.isClientOfGS(domainId, accountantsByDomain)) {
						mailUtil.sendTINMatchFailedEmail(failedRequests, domainEmailUsers, displayName);
					}
				} catch (NumberFormatException | ApheForbiddenException e2) {
				}

			}

			for (Long domainId : verifiedRequestsByDomain.keySet()) {

				List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
				try {
					DomainDTO domainDTO = domainMgr.getDomain(domainId);
					String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
					//TODO: Make sure this is a valid email address. Here and where domainDTO email is being used.
					if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
						UserDTO userDto = new UserDTO();
						userDto.setEmail(domainDTO.emailAddress);
						userDto.setFirstName(displayName);
						userDto.setLastName("");
						domainEmailUsers.add(userDto);
					}
					Set<TINMatchRequest> verifiedRequests = verifiedRequestsByDomain.get(domainId);

					//Do not send to GS and also not to regular customers.
					// mailUtil.sendTINMatchVerifiedEmail(verifiedRequests, domainEmailUsers, displayName);

				} catch (NumberFormatException | ApheForbiddenException e2) {
				}

			}

			for (Long domainId : sentToAgencyRequestsByDomain.keySet()) {

				List<UserDTO> domainEmailUsers = new ArrayList<>(usersByDomain.get(domainId));
				try {
					DomainDTO domainDTO = domainMgr.getDomain(domainId);
					String displayName = contractorConvertUtil.getDomainDisplayName(domainDTO);
					//TODO: Make sure this is a valid email address. Here and where domainDTO email is being used.
					if(domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
						UserDTO userDto = new UserDTO();
						userDto.setEmail(domainDTO.emailAddress);
						userDto.setFirstName(displayName);
						userDto.setLastName("");
						domainEmailUsers.add(userDto);
					}
					Set<TINMatchRequest> sentToAgencyRequests = sentToAgencyRequestsByDomain.get(domainId);

//					mailUtil.sendTINMatchSentToAgencyEmail(sentToAgencyRequests, domainEmailUsers, displayName);

				} catch (NumberFormatException | ApheForbiddenException e2) {
				}

			}


		}
	}

}
