package com.aphe.contractor.tasks.dev;

import com.aphe.common.mail.CSSInliner;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Profile({"dev"})
public class TemplatePreviewGenerator {

    @Autowired
    private SpringTemplateEngine templateEngine;

    private final Map<String, Date> lastModifiedDates = new HashMap<>();
    private Date lastFagmentsModifiedDate = null;


    private static final String developFolderAbs = "../common/domainservice/src/main/resources/mail/templates/";
    private static final String targetFolderAbs = "../common/domainservice/target/classes/mail/templates/";

    public static final String contractorTemplateFolder = "contractor";
    public static final String fragmentsFolder = "fragments";

    String contractorTemplatesDevFolder = developFolderAbs + contractorTemplateFolder;
    String fragmentsDevFolder = developFolderAbs + fragmentsFolder;

    @Scheduled(initialDelayString = "1000", fixedDelayString = "5000")
    public void generatePreviewFiles() throws IOException {

        //Anything changes in fragments folder, clear up last modified dates map and copy all files from dev to target
        Date fragmentsModifiedDate = getFragmentModifiedDate();
        if (lastFagmentsModifiedDate == null || fragmentsModifiedDate.after(lastFagmentsModifiedDate)) {
            lastModifiedDates.clear();
            lastFagmentsModifiedDate = fragmentsModifiedDate;
            File srcDir = new File(developFolderAbs);
            File destDir = new File(targetFolderAbs);
            if(srcDir.exists() && destDir.exists()) {
                FileUtils.copyDirectory(srcDir, destDir);
            }
        }
        processTemplateFolder();
    }

    private void processTemplateFolder() throws IOException {
        File f = new File(contractorTemplatesDevFolder);
        File[] templateFiles = f.listFiles();
        if (templateFiles != null) {
            for (File file : templateFiles) {
                if (file.isFile()) {
                    String fileName = file.getName();
                    String templateName = contractorTemplateFolder + File.separator + fileName.substring(0, fileName.lastIndexOf('.'));
                    Date lastModifiedDate = new Date(file.lastModified());
                    Date lastProcessedDate = lastModifiedDates.get(templateName);
                    if (lastProcessedDate == null || lastModifiedDate.after(lastProcessedDate)) {
                        lastModifiedDates.put(templateName, lastModifiedDate);
                        FileUtils.copyFile(file, new File(targetFolderAbs + contractorTemplateFolder + File.separator + fileName));
                        //Regenerate the preview for all the templates.
                        generatePreview(file);

                    }
                }
            }
        }
    }

    private void generatePreview(File templateFile) {
        String previewFolder = "mail/preview";
        String fileName = templateFile.getName();
        String templateName = contractorTemplateFolder + File.separator + fileName.substring(0, fileName.lastIndexOf('.'));
        String previewPath = previewFolder + File.separator + fileName;


        //process the template
        Context context = new Context();
        Map<String, Object> mailAttributes = new HashMap<>();
        addDefaultProperties(mailAttributes);
        addContactParams(mailAttributes);
        context.setVariables(mailAttributes);
        String html = templateEngine.process(templateName, context);
        try {
            html = new CSSInliner().addStyles(html);
        } catch (IOException e) {
            e.printStackTrace();
        }
        //write the html to a file in the preview folder
        try {
            FileUtils.writeStringToFile(new File(previewPath), html, Charset.defaultCharset());
        } catch (IOException e) {
            e.printStackTrace();
        }

    }


    private Date getFragmentModifiedDate() {
        Date lastModifiedDate = null;
        File f = new File(fragmentsDevFolder);
        if(f.exists()) {
            File[] fragmentFiles = f.listFiles();
            for (File file : fragmentFiles) {
                if (file.isFile()) {
                    Date fileLastModifiedDate = new Date(file.lastModified());
                    if (lastModifiedDate == null || fileLastModifiedDate.after(lastModifiedDate)) {
                        lastModifiedDate = fileLastModifiedDate;
                    }
                }
            }
        }
        return lastModifiedDate;
    }


    private void addDefaultProperties(Map<String, Object> mailAttributes) {
        String productURL = "https://app.1099smartfile.com";
        String appName = "1099SmartFile";
        String supportName = "1099SmartFile Support Team";
        String addressLine1 = "412 Arlewood Ct";
        String addressCity = "San Ramon";
        String addressState = "CA";
        String addressZip = "94582";
        String supportEmail = "<EMAIL>";
        String supportPhone = "5106931444";
        String supportHours = "5 AM -  7 PM";
        String activeProfile = "dev";


        String theProductURL = productURL;
        mailAttributes.put("PRODUCT_URL_PARAMS", theProductURL);
        mailAttributes.put("PRODUCT_URL", productURL);
        mailAttributes.put("PRODUCT_NAME", appName);
        mailAttributes.put("FROM_NAME", supportName);
        mailAttributes.put("ADDRESS_LINE1", addressLine1);
        mailAttributes.put("CITY", addressCity);
        mailAttributes.put("STATE", addressState);
        mailAttributes.put("ZIP", addressZip);

        mailAttributes.put("SUPPORT_NAME", supportName);
        mailAttributes.put("SUPPORT_EMAIL", supportEmail);
        mailAttributes.put("SUPPORT_PHONE", supportPhone);
        mailAttributes.put("SUPPORT_HOURS", supportHours);

        mailAttributes.put("profile", activeProfile);
    }

    private void addContactParams(Map<String, Object> mailAttributes) {
        String PAYEE_NAME = "Aphelion Technologies";
        String PAYER_NAME = "SSA Technologies LLC";
        String FIRST_NAME = "John";

        mailAttributes.put("PAYEE_NAME", PAYEE_NAME);
        mailAttributes.put("PAYER_NAME", PAYER_NAME);
        mailAttributes.put("FIRST_NAME", FIRST_NAME);
    }


}