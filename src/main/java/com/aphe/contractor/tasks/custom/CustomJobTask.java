package com.aphe.contractor.tasks.custom;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.graphql.PagedResult;
import com.aphe.common.util.ArrayUtil;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.common.util.EncryptionUtil;
import com.aphe.contractor.dto.read.FedSubDTO;
import com.aphe.contractor.dto.read.FilingDTO;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.FilingYear;
import com.aphe.contractor.services.*;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.repo.DomainRepository;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.repo.FilingDAO;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.insights.service.InsightsManager;
import com.aphe.partner.services.AccountingIntegrationMgr;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class CustomJobTask {

	Logger logger = LoggerFactory.getLogger(CustomJobTask.class);

	@Autowired
	FilingsManager efsFilingsMgr;

	@Autowired
	FilingDAO efsFilingRepo;

	@Autowired
	PayerManager payerManager;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	AuthManager authManager;
	
	@Autowired
	DomainMgr domainMgr;

	@Autowired
	PayeeManager payeeManager;

	@Autowired
	DomainRepository domainRepo;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	@Autowired
	AccountingIntegrationMgr accountingIntegrationMgr;

	@Autowired
	InsightsManager insightsManager;

	public Collection<CustomJobResponseDTO> sendDraftFilingsReminder(String domainIds, int reminderNumber, boolean test) {

		//logger.info("Start of executing the taskName=CustomJob method=remindAboutDraftFilings status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		Map<Long, CustomJobResponseDTO> jobResponseMap = new HashMap<>();

		Set<Long> allDomainIds = new HashSet<>(ArrayUtil.stringToLongList(domainIds));

		if(allDomainIds.size() == 0) {
			Set<Long> domainsWithDraftFilings = domainsWithDraftFilings();
			allDomainIds.addAll(domainsWithDraftFilings);
		}

		Map<Long, List<UserDTO>> usersByDomain = getStringListMap(allDomainIds);

		boolean testMailSent = false;

		for (Long domainId : allDomainIds) {
			try {
				List<UserDTO> domainEmailUsers = new ArrayList<>();
				List<UserDTO> userDTOS = usersByDomain.get(domainId);

				if(userDTOS == null) {
					jobResponseMap.put(domainId, new CustomJobResponseDTO(domainId.toString(), false, "Skipping because there are no users for this domain."));
					continue;
				}

				DomainDTO domainDTO = domainMgr.getDomain(domainId);
				if(domainDTO.isTestAccount != null && domainDTO.isTestAccount.booleanValue() == true) {
					jobResponseMap.put(domainId, new CustomJobResponseDTO(domainId.toString(), false, "skipping because test domain"));
					continue;
				}

				if(test && !testMailSent) {
					domainEmailUsers.add(getTestUser());
					testMailSent = true;
					//Send test mail for this first domain.
					sendDraftFilingsReminderForDomain(reminderNumber, domainId, domainEmailUsers, jobResponseMap);
				}

				if(test) {
					jobResponseMap.put(domainId, new CustomJobResponseDTO(domainId.toString(), true, "skipping because of test run."));
					continue;
				}

				domainEmailUsers.addAll(userDTOS);
				sendDraftFilingsReminderForDomain(reminderNumber, domainId, domainEmailUsers, jobResponseMap);

			} catch (NumberFormatException | ApheException e) {
				logger.error("error sending draft filings email", e);
				jobResponseMap.put(domainId, new CustomJobResponseDTO(domainId.toString(), false, e.getMessage()));
			}
		}

		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=UpdateFilingStatusTask status=End");
		return jobResponseMap.values();
	}

	private Map<Long, List<UserDTO>> getStringListMap(Set<Long> allDomainIds) {
		String token = authManager.getTokenForSystemUser();
		Map<Long, List<UserDTO>> usersByDomain = authManager.getDomainUsersRemote(token, allDomainIds);
		authManager.deleteToken(token);
		return usersByDomain;
	}

	private void sendDraftFilingsReminderForDomain(int reminderNumber, Long domainId, List<UserDTO> domainEmailUsers, Map<Long, CustomJobResponseDTO> jobResponseMap) throws ApheException {

		String subjectPrefix = "";

		if(reminderNumber == 1) {
			//Send 6 business days before the deadline.
			subjectPrefix = "Friendly reminder: ";
		} else if(reminderNumber == 2) {
			//Send 3 business days before the deadline.
			subjectPrefix = "Just another nudge: ";
		} else if(reminderNumber == 3) {
			//Send one business day before the deadline.
			subjectPrefix = "FINAL CALL: ";
		}

		HashMap<String, String> campaignParams = new HashMap<>();
		campaignParams.put("utm_source", "sf");
		campaignParams.put("utm_medium", "email");
		campaignParams.put("utm_campaign", "y22d" + reminderNumber);

		String templateName = "contractor/draftFilings";
		List<String> filingsStatuses = new ArrayList<>();
		filingsStatuses.add("Draft");
		List<String> filingYears = new ArrayList<>();
		filingYears.add(FilingYear.getCurrentFilingYear().getYear());

		DomainDTO domainDTO = domainMgr.getDomain(domainId);

		String domainName = contractorConvertUtil.getDomainDisplayName(domainDTO);
		PayerDTO payerDTO = payerManager.getPayerByDomainId(domainDTO.id);
		PagedResult<FilingDTO> draftFilingsForDomain = localFilingMgr.getFilingsByPayer(payerDTO.id, filingsStatuses, filingYears, null, 10, 0);
		List<FilingDTO> draftFilings = (List<FilingDTO>) draftFilingsForDomain.data;
		if(draftFilings.size() > 0) {
			String subject = subjectPrefix + domainName + "'s 1099 Forms";
			mailUtil.sendDraftFilingsEmailDTOs(templateName, subject, domainName, domainEmailUsers, draftFilings, campaignParams);
			jobResponseMap.put(domainId, new CustomJobResponseDTO(domainId.toString(), true, "reminded about draftFilingsSize=" + draftFilings.size()));
		} else {
			jobResponseMap.put(domainId, new CustomJobResponseDTO(domainId.toString(), true, "skipped because no draft filings."));
		}
	}

	@NotNull
	private Set<Long> domainsWithDraftFilings() {
		// Find all the filings that have been in FilingStatus=Draft
		List<FilingStatus> enumFilingsStatuses = new ArrayList<>();
		enumFilingsStatuses.add(FilingStatus.Draft);


		List<FilingYear> enumFilingYears = new ArrayList<>();
		enumFilingYears.add(FilingYear.getCurrentFilingYear());

		List<Filing> draftFilings = localFilingMgr.findByStatusInAndYearsIn(enumFilingsStatuses, enumFilingYears);
		// Now group them by domainId
		Set<Long> draftFilingDomainIds = new HashSet<>();
		for (Filing f : draftFilings) {
			draftFilingDomainIds.add(f.getPayer().getDomainId());
		}
		return draftFilingDomainIds;
	}

	private UserDTO getTestUser() {
		UserDTO testUser = new UserDTO();
		testUser.setEmail("<EMAIL>");
		testUser.setFirstName("Test");
		testUser.setLastName("User");
		return testUser;
	}


	public Collection<CustomJobResponseDTO> sendSeasonReminder(String domainIds, int reminderNumber, boolean test) {
		//logger.info("Start of executing the taskName=CustomJob method=sendSeasonReminder status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		String templateName = "contractor/seasonReminder" + reminderNumber;
		String subjectPrefix = "Upcoming 1099 filing deadline: ";

		subjectPrefix = getSeasonReminderSubject(reminderNumber, subjectPrefix);
		HashMap<String, String> campaingParams = getSeasonReminderCampaignParams(reminderNumber);

		Map<String, CustomJobResponseDTO> jobResponseMap = new HashMap<>();

		Set<String> allDomainIds = new HashSet<>(ArrayUtil.stringToStringList(domainIds));

		Set<String> domainsWithFilings = getDomainsWithFilings();
		Set<String> domainsWithPreviousYearFilings = getDomainsWithPreviousYearFilings();

		List<UserDTO> usersAndDomains = getUserDTOS();

		for (UserDTO userDTO : usersAndDomains) {
			try {
				Set<String> domainIdSet = new HashSet<>();
				domainIdSet.addAll(userDTO.getDomainIds());

				if(domainIdSet.size() == 0) {
					jobResponseMap.put(userDTO.getEmail(), new CustomJobResponseDTO(userDTO.getEmail(), true, "skipped because no active accounts. domainCount=" + domainIdSet.size()));
					continue;
				}

				boolean wasEngaged = domainIdSet.stream().anyMatch(domainsWithPreviousYearFilings::contains);

				if(!wasEngaged) {
					jobResponseMap.put(userDTO.getEmail(), new CustomJobResponseDTO(userDTO.getEmail(), true, "skipped because was not engaged domainCount=" + domainIdSet.size()));
					continue;
				}

				boolean hasFiled = domainIdSet.stream().anyMatch(domainsWithFilings::contains);
				if(hasFiled) {
					jobResponseMap.put(userDTO.getEmail(), new CustomJobResponseDTO(userDTO.getEmail(), true, "skipped because already engaged domainCount=" + domainIdSet.size()));
					continue;
				}

				List<UserDTO> userDTOS = new ArrayList<>();
				String subject = subjectPrefix;

				if(test) {
					jobResponseMap.put(userDTO.getEmail(), new CustomJobResponseDTO(userDTO.getEmail(), true, "skipped because of test run domainCount=" + domainIdSet.size()));
				} else {
					userDTOS.add(userDTO);
					mailUtil.sendSeasonReminder(templateName, subject, "", userDTOS, campaingParams);
					jobResponseMap.put(userDTO.getEmail(), new CustomJobResponseDTO(userDTO.getEmail(), true, "reminded about season to domainCount=" + domainIdSet.size()));
				}
			} catch (NumberFormatException e) {
				logger.error("error sending draft filings email", e);
				jobResponseMap.put(userDTO.getEmail(), new CustomJobResponseDTO(userDTO.getEmail(), false, e.getMessage()));
			}
		}
		if(test) {
			List<UserDTO> userDTOS = new ArrayList<>();
			userDTOS.add(getTestUser());
			mailUtil.sendSeasonReminder(templateName, subjectPrefix, "", userDTOS, campaingParams);
		}
		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=UpdateFilingStatusTask status=End");
		return jobResponseMap.values();
	}

	private List<UserDTO> getUserDTOS() {
		String token = authManager.getTokenForSystemUser();
		List<UserDTO> usersAndDomains = authManager.getAllUserRemote(token);
		authManager.deleteToken(token);
		return usersAndDomains;
	}

	private Set<String> getDomainsWithFilings() {
		List<FilingYear> enumFilingYears = new ArrayList<>();
		enumFilingYears.add(FilingYear.getCurrentFilingYear());
		List<Filing> currentYearFilings = localFilingMgr.findByYearsIn(enumFilingYears);
		Set<String> toBeExcludedDomainIds = currentYearFilings.stream().map(filing -> filing.getPayer().getDomainId().toString()).collect(Collectors.toSet());
		Set<String> allDomainIds = new HashSet<>();
		allDomainIds.addAll(toBeExcludedDomainIds);
		return allDomainIds;
	}

	private Set<String> getDomainsWithPreviousYearFilings() {
		List<FilingYear> enumFilingYears = Arrays.asList(FilingYear.Y2020, FilingYear.Y2021);
		List<Filing> currentYearFilings = localFilingMgr.findByYearsIn(enumFilingYears);
		Set<String> toBeExcludedDomainIds = currentYearFilings.stream().map(filing -> filing.getPayer().getDomainId().toString()).collect(Collectors.toSet());
		Set<String> allDomainIds = new HashSet<>();
		allDomainIds.addAll(toBeExcludedDomainIds);
		return allDomainIds;
	}

	@NotNull
	private HashMap<String, String> getSeasonReminderCampaignParams(int reminderNumber) {
		HashMap<String, String> campaingParams = new HashMap<>();
		campaingParams.put("utm_source", "sf");
		campaingParams.put("utm_medium", "email");
		campaingParams.put("utm_campaign", "y22m" + reminderNumber);
		return campaingParams;
	}

	private String getSeasonReminderSubject(int reminderNumber, String subjectPrefix) {
		if(reminderNumber == 1) {
			//Send some time in early december.
			subjectPrefix = "Get a head start on 1099 season";
		} else if(reminderNumber == 2) {
			//Send in the first week of january, like 3rd of 4th of Jan
			subjectPrefix = "1099 season has begun";
		} else if(reminderNumber == 3) {
			//Send in the middle of january, like 15th of 16th of Jan
			subjectPrefix = "Avoid the last minute rush and get started with your 1099s";
		} else if(reminderNumber == 4) {
			//send on 25th of Jan
			subjectPrefix = "1099 filing deadline is fast approaching";
		} else if(reminderNumber == 5) {
			//send on 30th...
			subjectPrefix = "1099s due in 2 days";
		} else if(reminderNumber == 6) {
			//send on 30th...
			subjectPrefix = "1099s are due tomorrow";
		}
		return subjectPrefix;
	}

	public Collection<CustomJobResponseDTO> deleteDraftFilings(String domainIds, boolean test) {
		//logger.info("Start of executing the taskName=CustomJob method=deleteDraftFilings status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		Map<String, CustomJobResponseDTO> jobResponseMap = new HashMap<>();

		List<Long> allDomainIds = ArrayUtil.stringToLongList(domainIds);
		if(allDomainIds.size() == 0) {
			// Find all the domains with filings that are not from current year, and doesn't have a lot of filing status history.
			List<FilingStatus> enumFilingsStatuses = new ArrayList<>();
			enumFilingsStatuses.add(FilingStatus.Draft);

			List<FilingYear> enumFilingYears = new ArrayList<>();
			enumFilingYears.add(FilingYear.Y2019);
			enumFilingYears.add(FilingYear.Y2020);
			enumFilingYears.add(FilingYear.Y2021);

			List<Filing> oldDraftFilings = localFilingMgr.findByStatusInAndYearsIn(enumFilingsStatuses, enumFilingYears);

			// Now group them by domainId
			Set<Long> domainsWithOldDraftFilings = new HashSet<>();
			for (Filing f : oldDraftFilings) {
				domainsWithOldDraftFilings.add(f.getPayer().getDomainId());
			}
			allDomainIds.addAll(domainsWithOldDraftFilings);
		}

		List<String> filingsStatuses = new ArrayList<>();
		filingsStatuses.add("Draft");

		List<String> filingYears = new ArrayList<>();
		filingYears.add("2020");
		filingYears.add("2019");

		for (Long domainId : allDomainIds) {
			try {
				DomainDTO domainDTO = domainMgr.getDomain(domainId);
				PayerDTO payerDTO = payerManager.getPayerByDomainId(domainDTO.id);
				PagedResult<FilingDTO> draftFilingsForDomain = localFilingMgr.getFilingsByPayer(payerDTO.id, filingsStatuses, filingYears, null, 1000, 0);
				List<FilingDTO> draftFilings = (List<FilingDTO>) draftFilingsForDomain.data;
				if(draftFilings.size() > 0) {
					List<String> draftFilingIds = draftFilings.stream()
							.filter(f-> {
								FedSubDTO fedSubDTO = f.fedSub;
								return fedSubDTO != null && fedSubDTO.filingStatusHistory != null && fedSubDTO.filingStatusHistory.size() == 1;
							})
							.map(f->Long.toString(f.id))
							.collect(Collectors.toList());
					if(!test) {
						localFilingMgr.deleteFilings(draftFilingIds);

						insightsManager.updateFilingCounts(domainId);
					}
					jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), true, "deleted draft filings: totalDraftFilings= " + draftFilings.size()  + "  deletedDraftFilingCount=" + draftFilingIds.size()));
				} else {
					jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), true, "no draft filings."));
				}
			} catch (NumberFormatException | ApheException e) {
				logger.error("error sending draft filings email", e);
				jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), false, e.getMessage()));
			}
		}

		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=UpdateFilingStatusTask status=End");
		return jobResponseMap.values();
	}

	public Collection<CustomJobResponseDTO> updateFilingCounts(String domainIds, boolean test) {
		//logger.info("Start of executing the taskName=CustomJob method=updateFilingCounts status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		Map<String, CustomJobResponseDTO> jobResponseMap = new HashMap<>();

		List<Long> allDomainIds = ArrayUtil.stringToLongList(domainIds);
		if(allDomainIds.size() == 0) {
			//Find all accountant domains...
			allDomainIds = domainRepo.findAll().stream()
//					.filter(d->(d instanceof Accountant))
					.map(d->d.getId()).collect(Collectors.toList());
		}
		for (Long domainId : allDomainIds) {
			try {
				DomainDTO domainDTO = domainMgr.getDomain(domainId);
				insightsManager.updateFilingCounts(domainId);
				jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), true, "updated filing counts for domainId=" + domainId));
			} catch (NumberFormatException | ApheException e) {
				logger.error("error updating filing counts", e);
				jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), false, e.getMessage()));
			}
		}
		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=UpdateFilingStatusTask status=End");
		return jobResponseMap.values();
	}


	public Collection<CustomJobResponseDTO> reEncryptTins(String domainIds, boolean test) {
		//logger.info("Start of executing the taskName=CustomJob method=reEncryptPayeeTins status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		EncryptionUtil encryptionUtil = new EncryptionUtil();

		Map<String, CustomJobResponseDTO> jobResponseMap = new HashMap<>();

		List<Long> allDomainIds = ArrayUtil.stringToLongList(domainIds);
		if(allDomainIds.size() == 0) {
			allDomainIds = domainRepo.findAll().stream().map(d->d.getId()).collect(Collectors.toList());
		}
		for (Long domainId : allDomainIds) {
			try {
				if(!test) {
					logger.debug("Re-encrypting tin for " + domainId);
					domainMgr.reEncryptTin(domainId, encryptionUtil);
					payeeManager.reEncryptTins(domainId, encryptionUtil);
					accountingIntegrationMgr.reEncryptTins(domainId, encryptionUtil);
					jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), true, "encrypted for domainId=" + domainId));
				}else {
					jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), true, "skipped for testing"));
				}
			} catch (Exception e) {
				logger.error("error encrypting TINS", e);
				jobResponseMap.put(domainId.toString(), new CustomJobResponseDTO(domainId.toString(), false, e.getMessage()));
			}
		}
		return jobResponseMap.values();
	}


	public Collection<CustomJobResponseDTO> reEncryptEfsTins(String efsFilingIds, boolean test) {
		//logger.info("Start of executing the taskName=CustomJob method=reEncryptEfsTins status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		EncryptionUtil encryptionUtil = new EncryptionUtil();

		Map<String, CustomJobResponseDTO> jobResponseMap = new HashMap<>();

		List<String> allFilingIds = ArrayUtil.stringToStringList(efsFilingIds);
		if(allFilingIds.size() == 0) {
			for(com.aphe.efs.model.enums.FilingYear filingYear : com.aphe.efs.model.enums.FilingYear.values()) {
				try {
					Iterable<EFSFiling> allFilings = efsFilingRepo.findFilingsToBeEncrypted(filingYear);
					Iterator<EFSFiling> iterator = allFilings.iterator();
					while (iterator.hasNext()) {
						allFilingIds.add(Long.toString(iterator.next().getId()));
					}
					Collections.sort(allFilingIds);
					processEFSFilingIds(allFilingIds, encryptionUtil, jobResponseMap);
				} catch (Exception e) {
					logger.error("Error re-encrypting  taskName=CustomJob  method=reEncryptEfsTins FilingYear=" + filingYear);
					jobResponseMap.put(filingYear.getYear(), new CustomJobResponseDTO(filingYear.getYear(), false, e.getMessage()));
				}
			}
		} else {
			processEFSFilingIds(allFilingIds, encryptionUtil, jobResponseMap);
		}
		return jobResponseMap.values();
	}

	private void processEFSFilingIds(List<String> allFilingIds, EncryptionUtil encryptionUtil, Map<String, CustomJobResponseDTO> jobResponseMap) {
		for (String efsFilingId : allFilingIds) {
			try {
				logger.info("Working on re-encrypting tin for FilingId=" + efsFilingId);
				efsFilingsMgr.reEncryptTins(Long.parseLong(efsFilingId), encryptionUtil);
//				jobResponseMap.put(Long.toString(Long.parseLong(efsFilingId)), new CustomJobResponseDTO(efsFilingId, true, "encrypted for efsFilingId=" + efsFilingId));
			} catch (Exception e) {
				logger.error("Error re-encrypting  taskName=CustomJob  method=reEncryptEfsTins FilingId=" + efsFilingId);
				jobResponseMap.put(efsFilingId, new CustomJobResponseDTO(efsFilingId, false, e.getMessage()));
			}
		}
	}

}
