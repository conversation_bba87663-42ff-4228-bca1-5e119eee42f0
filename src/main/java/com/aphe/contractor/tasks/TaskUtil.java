package com.aphe.contractor.tasks;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class TaskUtil {

    @Autowired
    AuthManager authManager;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    ContractorConvertUtil contractorConvertUtil;

    public void buildCommunicationData(Long domainId,
                                        Map<Long, String> domainNames,
                                        Map<Long, List<UserDTO>> usersByDomain,
                                        Map<Long, List<Long>> accountantsByDomain) {
        String token = authManager.getTokenForSystemUser();

        Set<Long> domainIds = new HashSet<>();
        domainIds.add(domainId);
        usersByDomain.putAll(authManager.getDomainUsersRemote(token, domainIds));
        accountantsByDomain.putAll(authManager.getDomainAccountantsRemote(token, domainIds));
        authManager.deleteToken(token);

        for (Long dId : domainIds) {

            List<UserDTO> domainEmailUsers = usersByDomain.get(dId);

            String domainName = "";

            try {
                DomainDTO domainDTO = domainMgr.getDomain(domainId);
                domainName = contractorConvertUtil.getDomainDisplayName(domainDTO);
                domainNames.put(domainId, domainName);
                //TODO: Make sure this is a valid email address. Here and where domainDTO email is being used.
                if (domainDTO.emailAddress != null && domainDTO.emailAddress.trim().length() > 0) {
                    UserDTO userDto = new UserDTO();
                    userDto.setEmail(domainDTO.emailAddress);
                    userDto.setFirstName(domainName);
                    userDto.setLastName("");
                    domainEmailUsers.add(userDto);
                }
            } catch (NumberFormatException | ApheForbiddenException e2) {
            }
        }
    }

    public String buildFailureReasons(Map<String, String> failureReasons) {
        StringBuilder sb = new StringBuilder();
        for (String id : failureReasons.keySet()) {
            sb.append("emailSubId=").append(id).append(" reason=").append(failureReasons.get(id)).append("<br/>");
        }
        return sb.toString();
    }

}
