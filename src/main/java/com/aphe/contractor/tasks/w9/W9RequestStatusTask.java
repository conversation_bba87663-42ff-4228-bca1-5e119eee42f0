package com.aphe.contractor.tasks.w9;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.enums.W9RequestStatus;
import com.aphe.contractor.model.w9.W9Request;
import com.aphe.contractor.services.ContractorConvertUtil;
import com.aphe.contractor.services.MailManager;
import com.aphe.contractor.services.W9RequestManager;
import com.aphe.domain.service.DomainMgr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Auto task to send emails about W-9 requests.
 */

@Component
@Profile({"test"})
public class W9RequestStatusTask extends GenericBackgroundTask {

    Logger logger = LoggerFactory.getLogger(W9RequestStatusTask.class);

    @Autowired
    W9RequestManager w9RequestManager;

    @Autowired
    AuthManager authManager;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    MailManager mailManager;

    @Autowired
    ContractorConvertUtil contractorConvertUtil;

    //TODO: Does this need to be broken up into smaller tasks too?

//    @Scheduled(initialDelayString = "10000", fixedDelayString = "60000")
    public void sendW9RequestEmails() {

        boolean pauseTasks = isPauseTasks("domainservice-email-w9Requests-job");
        if (pauseTasks) {
            return;
        }

        //logger.info("Start of executing the taskName=sendW9RequestEmails status=Begin");
        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        List<W9Request> submittedW9Requests = w9RequestManager.findByStatusIn(W9RequestStatus.Submitted);
        //Send email to each of these payees, and then update the status of W9Request to requested.
        for (W9Request w9Request : submittedW9Requests) {
            try {
                mailManager.sendW9RequestEmail(w9Request);
                w9RequestManager.updateW9RequestStatus(w9Request, W9RequestStatus.Requested, "Sent email", new Date());
            } catch (Exception e) {
                logger.error("Exception sending w9 request.", e);
            }
        }

        CronJobAuthenticationUtil.cleanAuthentication();
        //logger.info("Done executing the taskName=sendW9RequestEmails status=End");

    }


//    @Scheduled(initialDelayString = "10000", fixedDelayString = "60000")
    public void applyW9Info() {
        boolean pauseTasks = isPauseTasks("domainservice-applyW9Info-job");
        if (pauseTasks) {
            return;
        }
        //logger.info("Start of executing the taskName=applyW9Info status=Begin");
        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        // Find all the w9 requests that are in filled status
        List<W9RequestStatus> w9RequestStatuses = new ArrayList<>();
        w9RequestStatuses.add(W9RequestStatus.Filled);
        List<W9Request> filledW9Requests = w9RequestManager.findByStatusIn(W9RequestStatus.Filled);
        //Apply the information to the system and send email to the payer.
        for (W9Request w9Request : filledW9Requests) {
            try {
                w9RequestManager.applyW9Info(w9Request);
                mailManager.sendW9AppliedEmail(w9Request);
            } catch (Exception e) {
                logger.error("Exception applying w9 information.", e);
            }
        }

        CronJobAuthenticationUtil.cleanAuthentication();
        //logger.info("Done executing the taskName=applyW9Info status=End");

    }

}
