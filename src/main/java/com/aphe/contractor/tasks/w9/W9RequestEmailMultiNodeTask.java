package com.aphe.contractor.tasks.w9;

import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.contractor.model.enums.W9RequestStatus;
import com.aphe.contractor.model.w9.W9Request;
import com.aphe.contractor.services.MailManager;
import com.aphe.contractor.services.W9RequestManager;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Auto task to send emails about W-9 requests.
 */

@Component
@Profile({"dev | qa | sandbox | prod"})
public class W9RequestEmailMultiNodeTask extends ApheBackgroundTask {

    Logger logger = LoggerFactory.getLogger(W9RequestEmailMultiNodeTask.class);
    public static final String DOMAIN_ID = "domainId";
    public static final String DOMAIN_NAME = "domainName";
    public static final String PAYEE_NAME = "payeeName";

    @Autowired
    W9RequestManager w9RequestManager;

    @Autowired
    MailManager mailManager;

    //TODO: Make this part of user interaction in async mode. Have this as back up every 12 hours.
    @Recurring(id = "domainservice-w9Requests-email-job", interval = "PT12H")
    @Override
    public void startTask() {
        jobName = "domainservice-w9Requests-email-job";
        chunkSize = 10;
        executeTask();
    }

    @Override
    public List<String> getUnitsOfWork() {
        CronJobAuthenticationUtil.configureAuthentication("superadmin");
        List<W9Request> submittedW9Requests = w9RequestManager.findByStatusIn(W9RequestStatus.Submitted);
        CronJobAuthenticationUtil.cleanAuthentication();
        return submittedW9Requests.stream().map(w9Request -> w9Request.getId().toString()).collect(Collectors.toList());
    }

    @Override
    public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
        long requestId = Long.parseLong(unitOfWork);
        W9Request w9Request = w9RequestManager.findByById(requestId);
        try {
            mailManager.sendW9RequestEmail(w9Request);
            w9RequestManager.updateW9RequestStatus(w9Request, W9RequestStatus.Requested, "Sent email", new Date());
            Map<String, String> values = new HashMap<>();
            values.put(PAYEE_NAME, w9Request.getDisplayName());
            values.put(DOMAIN_NAME, w9Request.getPayerName());
            values.put(DOMAIN_ID, w9Request.getPayee().getPayer().getDomainId().toString());
            return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
        } catch (Exception e) {
            return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
        }
    }

    @Override
    public List<String> getAdditionalEmailColumns() {
        return List.of(DOMAIN_ID, DOMAIN_NAME, PAYEE_NAME);
    }

    @Override
    public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
        logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
        // Send email if this task ends up processing any requests.
        if (toBeProcessed > 0) {
            long emailStartTime = System.currentTimeMillis();
            mailManager.sendSystemStatusEmail(subject, emailHTML);
            long emailTime = System.currentTimeMillis() - emailStartTime;
            logger.info("Email sent in {}ms", emailTime);
        }
    }
}
