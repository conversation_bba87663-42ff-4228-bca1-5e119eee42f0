package com.aphe.contractor.graphql;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.graphql.PagedResult;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.contractor.dto.read.*;
import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.FilingYear;
import com.aphe.contractor.model.FilingCount;
import com.aphe.contractor.services.*;
import com.aphe.domain.service.ConvertUtil;
import com.aphe.domain.service.DomainMgr;
import com.aphe.print.lob.LobClientFactory;
import com.netflix.graphql.dgs.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
@DgsComponent
public class TNNPRootQuery {

	private static final Logger logger = LoggerFactory.getLogger(TNNPRootQuery.class);

	@Autowired
	DomainMgr domainMgr;

	@Autowired
	LobClientFactory lobClientFactory;

	@Autowired
	PayerManager payerManager;

	@Autowired
	PayeeManager payeeManager;

	@Autowired
	FilingManager filingManager;

	@Autowired
	W9RequestManager w9RequestManager;

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;

	@Autowired
	ConvertUtil convertUtil;

	@DgsQuery
	public String hello() throws Exception {
		return "hello world";
	}

	@DgsQuery
	public boolean flush() throws Exception {
		try {
			domainMgr.flush();
			lobClientFactory.flush();;
			lobClientFactory.isValidSignature("a", "a");
			return true;
		} catch (Exception e) {
			String message = "Error getting current session";
			logger.error(message, e);
			throw new Exception(message);
		}
	}


	@DgsQuery
	public PayerDTO payer() throws Exception {
		try {
			return payerManager.getPayerByDomainId(getCurrentDoaminId());
		} catch (Exception e) {
			logger.error("Error getting payer. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public long payeeCount() throws Exception {
		return payeeManager.getPayeeCount(getCurrentDoaminId());
	}

	@DgsQuery
	public PayeeDTO payee(@InputArgument("id") Long payeeId) throws Exception {
		try {
			// Resiliency call. Ensures the payer is created.
			payerManager.getPayerByDomainId(getCurrentDoaminId());
			return payeeManager.getPayee(payeeId);
		} catch (Exception e) {
			logger.error("Error getting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public PayeeDTO payeeByTin(@InputArgument("tin") String payeeTIN) throws Exception {
		try {
			// Resiliency call. Ensures the payer is created.
			payerManager.getPayerByDomainId(getCurrentDoaminId());
			return payeeManager.getPayeeByTIN(getCurrentDoaminId(), payeeTIN);
		} catch (Exception e) {
			logger.error("Error getting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public PayeeDTO payeeByPartnerId(@InputArgument("partner") String partner, @InputArgument("partnerId") String partnerId) throws Exception {
		try {
			payerManager.getPayerByDomainId(getCurrentDoaminId());
			return payeeManager.getPayeeByPartnerId(getCurrentDoaminId(), partner, partnerId);
		} catch (Exception e) {
			logger.error("Error getting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public List<PayeeDTO> payees(@InputArgument("pageSize") Integer pageSize, @InputArgument("pageNumber") Integer pageNumber) throws ApheException {
		try {
			if (pageSize == null) {
				pageSize = 1000;
			}
			if (pageNumber == null) {
				pageNumber = 0;
			}
			// Resiliency call. Ensures the payer is created.
			PayerDTO payer = payerManager.getPayerByDomainId(getCurrentDoaminId());
			return (List<PayeeDTO>) payeeManager.getPayees(payer.id, pageSize, pageNumber).data;
		} catch (Exception e) {
			logger.error("Error getting payees. " + e.getMessage(), e);
			if(e instanceof ApheException) {
				throw e;
			} else {
				throw new ApheException(500, "ERR_500", e.getMessage());
			}
		}
	}

	@DgsData(parentType = "Query", field = "payeesWithPageMetadata")
	public PagedResult<PayeeDTO> payeesWithPageMetadata(
			@InputArgument("pageSize") Integer pageSize,
			@InputArgument("pageNumber") Integer pageNumber,
			DgsDataFetchingEnvironment dfe) throws ApheException {
		if (pageSize == null) {
			pageSize = 1000;
		}
		if (pageNumber == null) {
			pageNumber = 0;
		}
		PayerDTO payerByDomainId = payerManager.getPayerByDomainId(getCurrentDoaminId());
		PayerDTO payer = payerByDomainId;
		PagedResult<PayeeDTO> payeesPagedData = payeeManager.getPayees(payer.id, pageSize, pageNumber);
		return payeesPagedData;
	}


	@DgsQuery
	public List<FilingCount> filingCounts(@InputArgument("filingYear") String filingYear) throws Exception {
		PayerDTO payerDTO = payerManager.getPayerByDomainId(getCurrentDoaminId());
		return filingManager.getFilingCounts(payerDTO.id, filingYear);
	}

	@DgsQuery
	public List<FilingDTO> filings(@InputArgument("filingStatuses") List<String> statuses,
								   @InputArgument("filingYears") List<String> filingYears,
								   @InputArgument("filingTypes") List<String> formTypes,
								   @InputArgument("pageSize") Integer pageSize,
								   @InputArgument("pageNumber") Integer pageNumber) throws Exception {
		try {
			if (pageSize == null) {
				pageSize = 1000;
			}
			if (pageNumber == null) {
				pageNumber = 0;
			}
			// Resiliency call. Ensures the payer is created.
			PayerDTO payerDTO = payerManager.getPayerByDomainId(getCurrentDoaminId());
			return (List<FilingDTO>) filingManager.getFilingsByPayer(payerDTO.id, statuses, filingYears, formTypes, pageSize, pageNumber).data;
		} catch (Exception e) {
			logger.error("Error geting filings. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsData(parentType = "Query", field = "filingsWithPageMetadata")
	public PagedResult<FilingDTO> filingsWithPageMetadata(
			@InputArgument("filingStatuses") List<String> statuses,
			@InputArgument("filingYears") List<String> filingYears,
			@InputArgument("filingTypes") List<String> formTypes,
			@InputArgument("pageSize") Integer pageSize,
			@InputArgument("pageNumber") Integer pageNumber,
			DgsDataFetchingEnvironment dfe) throws ApheException {
		if (pageSize == null) {
			pageSize = 1000;
		}
		if (pageNumber == null) {
			pageNumber = 0;
		}
		PayerDTO payerDTO = payerManager.getPayerByDomainId(getCurrentDoaminId());
		return filingManager.getFilingsByPayer(payerDTO.id, statuses, filingYears, formTypes, pageSize, pageNumber);
	}

	@DgsQuery
	public List<FilingDTO> filingsByStatus(@InputArgument("status") String status,
										   @InputArgument("year") String year,
										   @InputArgument("pageSize") Integer pageSize,
										   @InputArgument("pageNumber") Integer pageNumber) throws Exception {

		try {
			if (pageSize == null) {
				pageSize = 1000;
			}
			if (pageNumber == null) {
				pageNumber = 0;
			}
			// Resiliency call. Ensures the payer is created.
			PayerDTO payerDTO = payerManager.getPayerByDomainId(getCurrentDoaminId());
			List<FilingStatus> statuses = new ArrayList<FilingStatus>();
            statuses.add(FilingStatus.valueOf(status));

			List<FilingYear> filingYears = new ArrayList<FilingYear>();
			FilingYear yearEnum = FilingYear.getFilingYearForYear(year);
			if(yearEnum == null) {
				filingYears.addAll(Arrays.stream(FilingYear.values()).collect(Collectors.toList()));
			} else {
				filingYears.add(yearEnum);
			}

			return (List<FilingDTO>) filingManager.getFilingsByStatus(payerDTO.id, statuses, filingYears, pageSize, pageNumber).data;
		} catch (Exception e) {
			logger.error("Error getting filings by status. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public List<FilingDTO> filingsByFormType(@InputArgument("formType") String formType,
											 @InputArgument("pageSize") Integer pageSize,
											 @InputArgument("pageNumber") Integer pageNumber) throws Exception {
		try {
			if (pageSize == null) {
				pageSize = 1000;
			}
			if (pageNumber == null) {
				pageNumber = 0;
			}
			// Resiliency call. Ensures the payer is created.
			PayerDTO payerDTO = payerManager.getPayerByDomainId(getCurrentDoaminId());
			List<FilingReturnType> formTypes = new ArrayList<FilingReturnType>();
			formTypes.add(FilingReturnType.valueOf(formType));
			return (List<FilingDTO>) filingManager.getFilingsByFormType(payerDTO.id, formTypes, pageSize, pageNumber).data;
		} catch (Exception e) {
			logger.error("Error getting filings by status. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public FilingDTO filingByPayeeFormTypeFilingYear(@InputArgument("payeeId") Long payeeId,
													 @InputArgument("formType") String formType,
													 @InputArgument("filingYear") String filingYear) throws Exception {
		try {
			FilingReturnType returnType = FilingReturnType.valueOf(formType);
			FilingYear formYear = FilingYear.getFilingYearForYear(filingYear);
			return filingManager.getFilingByPayeeAndFormTypeAndFilingYear(payeeId, returnType, formYear);
		} catch (Exception e) {
			logger.error("Error getting filings by status. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public FilingDTO filing(@InputArgument("id") Long filingId) throws Exception {
		try {
			return filingManager.getFiling(filingId);
		} catch (Exception e) {
			logger.error("Error getting filing by id. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public List<FilingDTO> filingsByIds(@InputArgument("filingIds") List<String> filingIds) throws Exception {
		try {
			if(filingIds.size() == 0) {
				return new ArrayList<>();
			}
			return filingManager.getFilingsByIds(filingIds);
		} catch (Exception e) {
			logger.error("Error getting filings by filingIds. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public List<PrintEmailRequestDTO> printRequests(@InputArgument("printStatus") String printStatus, @InputArgument("filingYear") String filingYear) throws Exception {
		try {
			return filingManager.getPrintEmailRequestsByPrintStatus(printStatus, filingYear);
		} catch (Exception e) {
			logger.error("Error getting filings by print status. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsQuery
	public List<PrintEmailRequestDTO> emailRequests(@InputArgument("emailStatus") String emailStatus, @InputArgument("filingYear") String filingYear) throws Exception {
		try {
			return filingManager.getPrintEmailRequestsByEmailStatus(emailStatus, filingYear);
		} catch (Exception e) {
			logger.error("Error getting filings by email status. " + e.getMessage(), e);
			throw e;
		}
	}

	private Long getCurrentDoaminId() {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth != null && auth.isAuthenticated()) {
			ApheUserDetails principal = (ApheUserDetails) auth.getPrincipal();
			long domainId = principal.getDomainId();
			if (domainId > 0)
				return domainId;
		}
		return null;
	}

	@DgsQuery
	public List<FilingDTO> filingsIssued() throws Exception {
		try {
			return filingManager.getFilingsIssued(filingManager.getCurrentDomainId());
		} catch (Exception e) {
			logger.error("Error getting filings by filingIds. " + e.getMessage(), e);
			throw e;
		}
	}


	@DgsQuery
	public Integer selfEFileCount(@InputArgument("filingYear") String filingYear) throws Exception {
		PayerDTO payerDTO = payerManager.getPayerByDomainId(getCurrentDoaminId());
		return filingManager.selfEFileCount(payerDTO.id, filingYear);
	}

	@DgsData(parentType = "Query", field = "selfEFileFilingsWithPageMetadata")
	public PagedResult<FilingDTO> selfEFileFilingsWithPageMetadata(
			@InputArgument("filingStatuses") List<String> statuses,
			@InputArgument("filingYears") List<String> filingYears,
			@InputArgument("filingTypes") List<String> formTypes,
			@InputArgument("pageSize") Integer pageSize,
			@InputArgument("pageNumber") Integer pageNumber,
			DgsDataFetchingEnvironment dfe) throws ApheException {
		if (pageSize == null) {
			pageSize = 1000;
		}
		if (pageNumber == null) {
			pageNumber = 0;
		}
		PayerDTO payerDTO = payerManager.getPayerByDomainId(getCurrentDoaminId());
		return filingManager.getSelfEFileFilingsByPayer(payerDTO.id, statuses, filingYears, formTypes, pageSize, pageNumber);
	}

	@DgsQuery
	public List<W9RequestDataDTO> w9s(@InputArgument("payeeIds") List<String> payeeIds) throws ApheException {
		try{
			List<W9RequestDataDTO> dtos = w9RequestManager.getW9s(payeeIds);
			return dtos;
		} catch (Exception e) {
			logger.error("Error getting payees. " + e.getMessage(), e);
			if(e instanceof ApheException) {
				throw e;
			} else {
				throw new ApheException(500, "ERR_500", e.getMessage());
			}
		}
	}

	@DgsQuery
	public List<TINMatchRequestDTO> tinMatches(@InputArgument("payeeIds") List<String> payeeIds) throws ApheException {
		try{
			List<TINMatchRequestDTO> dtos = tinMatchRequestManager.getTINMatchRequests(payeeIds);
			return dtos;
		} catch (Exception e) {
			logger.error("Error getting tin matches for payees. " + e.getMessage(), e);
			if(e instanceof ApheException) {
				throw e;
			} else {
				throw new ApheException(500, "ERR_500", e.getMessage());
			}
		}
	}

}