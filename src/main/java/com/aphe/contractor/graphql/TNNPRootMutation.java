package com.aphe.contractor.graphql;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.error.MappedValidationMessages;
import com.aphe.common.error.ValidationMessages;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.contractor.dto.*;
import com.aphe.contractor.dto.read.*;
import com.aphe.contractor.services.*;
import com.aphe.contractor.tasks.custom.CustomJobResponseDTO;
import com.aphe.contractor.tasks.custom.CustomJobTask;
import com.aphe.domain.service.DomainMgr;
import com.aphe.insights.service.InsightsManager;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@DgsComponent
public class TNNPRootMutation {

	private static final Logger logger = LoggerFactory.getLogger(TNNPRootMutation.class);

	@Autowired
	DomainMgr domainMgr;

	@Autowired
	PayerManager payerManager;

	@Autowired
	PayeeManager payeeManager;

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;

	@Autowired
	W9RequestManager w9RequestManager;

	@Autowired
	W9RequestOrchestrator w9RequestOrchestrator;

	@Autowired
	FilingManager filingManager;

	@Autowired
	FilingManager localFilingMgr;

	@Autowired
	MailUtil mailUtil;

	@Autowired
	AuthManager authManager;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	@Autowired
	CustomJobTask customJobTask;

	@Autowired
	InsightsManager insightsManager;


	@DgsMutation
	public PayerDTO updatePayer(@InputArgument("input") AddEditPayerInput payerInput) throws Exception {
		try {
			long payerId = payerManager.updatePayer(payerInput);
			PayerDTO payerDTO = payerManager.getPayer(Long.toString(payerId));

			insightsManager.updateTINInsight(payerDTO.domainId);

			return payerDTO;

		} catch (Exception e) {
			logger.error("Error updating payer. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public ValidationMessages validatePayer(@InputArgument("payerId") String payerId, @InputArgument("filingYear") String filingYear) throws Exception {
		try {
			Long domainId = domainMgr.getCurrentDomainId();
			return payerManager.getValidationMessages(domainId, filingYear);
		} catch (Exception e) {
			logger.error("Error validating domain. " + e.getMessage(), e);
			throw e;
		}

	}

	@DgsMutation
	public PayeeDTO addEditPayeeLight(@InputArgument("input") AddEditPayeeLightInput payeeInput) throws Exception {
		try {
			if(payeeInput.id == null || payeeInput.id == 0 ) {
				long payeeId = payeeManager.createPayeeLight(payeeManager.getCurrentDomainId(),payeeInput);
				return payeeManager.getPayee(payeeId);
			} else {
				long payeeId = payeeManager.updatePayeeLight(payerManager.getCurrentDomainId(), payeeInput);
				return payeeManager.getPayee(payeeId);
			}
		} catch (Exception e) {
			logger.error("Error in adding/editing payer. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public PayeeDTO addEditPayee(@InputArgument("input") AddEditPayeeInput payeeInput) throws Exception {
		try {
			if(payeeInput.id == null || payeeInput.id == 0 ) {
				long payeeId = payeeManager.createPayee(payeeManager.getCurrentDomainId(),payeeInput);
				return payeeManager.getPayee(payeeId);
			} else {
				long payeeId = payeeManager.updatePayee(payerManager.getCurrentDomainId(), payeeInput);
				return payeeManager.getPayee(payeeId);
			}
		} catch (Exception e) {
			logger.error("Error in adding/editing payer. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean deletePayee(@InputArgument("payeeId") String id) throws Exception {
		try {
			payeeManager.deletePayee(id);
			return true;
		} catch (Exception e) {
			logger.error("Error deleting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean deletePayees(@InputArgument("payeeIds") List<String> payeeIds) throws Exception {
		try {
			payeeManager.deletePayees(payeeIds);
			return true;
		} catch (Exception e) {
			logger.error("Error deleting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<TINMatchRequestDTO> createTINMatches(@InputArgument("payeeIds") List<String> payeeIds) throws ApheException {
		try{
			List<TINMatchRequestDTO> dtos = tinMatchRequestManager.createTINMatchRequests(payeeIds);
			return dtos;
		} catch (Exception e) {
			logger.error("Error getting payees. " + e.getMessage(), e);
			if(e instanceof ApheException) {
				throw e;
			} else {
				throw new ApheException(500, "ERR_500", e.getMessage());
			}
		}
	}

	@DgsMutation
	public Boolean requestTINMatches(@InputArgument("payeeIds") List<String> payeeIds) throws Exception {
		try {
			tinMatchRequestManager.submitTINMatchRequests(payeeIds);
			return true;
		} catch (Exception e) {
			logger.error("Error requesting tin matches for the payees. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean cancelTINMatches(@InputArgument("payeeIds") List<String> payeeIds) throws Exception {
		try {
			tinMatchRequestManager.cancelTINMatchRequests(payeeIds);
			return true;
		} catch (Exception e) {
			logger.error("Error cancelling tin match requests for the payees. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean updateTINMatch(@InputArgument("payeeId") String payeeId, boolean selected) throws Exception {
		try {
			tinMatchRequestManager.updateTINMatchRequest(payeeId, selected);
			return true;
		} catch (Exception e) {
			logger.error("Error updating tin match selection for payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean bulkUpdateTINMatches(@InputArgument("payeeIds") List<String> payeeIds, boolean selected) throws Exception {
		try {
			tinMatchRequestManager.bulkUpdateTINMatchRequests(payeeIds, selected);
			return true;
		} catch (Exception e) {
			logger.error("Error updating tin match selection for payees. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean ignoreTINValidation(@InputArgument("payeeId") String id) throws Exception {
		try {
			payeeManager.ignoreTINValidation(id);
			return true;
		} catch (Exception e) {
			logger.error("Error marking tin match failure as ignore for payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<W9RequestDataDTO> createW9s(@InputArgument("payeeIds") List<String> payeeIds) throws ApheException {
		try{
			List<W9RequestDataDTO> dtos = w9RequestManager.createW9Requests(payeeIds);
			return dtos;
		} catch (Exception e) {
			logger.error("Error getting payees. " + e.getMessage(), e);
			if(e instanceof ApheException) {
				throw e;
			} else {
				throw new ApheException(500, "ERR_500", e.getMessage());
			}
		}
	}


	@DgsMutation
	public Boolean requestW9s(@InputArgument("payeeIds") List<String> payeeIds) throws Exception {
		try {
			w9RequestOrchestrator.submitW9Requests(payeeIds);
			return true;
		} catch (Exception e) {
			logger.error("Error requesting w-9s for the payees. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean cancelW9Requests(@InputArgument("payeeIds") List<String> payeeIds) throws Exception {
		try {
			w9RequestManager.cancelW9Requests(payeeIds);
			return true;
		} catch (Exception e) {
			logger.error("Error cancelling w-9 requests for the payees. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean remindW9Requests(@InputArgument("payeeIds") List<String> payeeIds) throws Exception {
		try {
			w9RequestOrchestrator.remindW9Requests(payeeIds);
			return true;
		} catch (Exception e) {
			logger.error("Error reminding w-9 requests for the payees. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean applyW9Info(@InputArgument("payeeIds") List<String> payeeIds) throws Exception {
		try {
			w9RequestManager.applyW9Info(payeeIds);
			return true;
		} catch (Exception e) {
			logger.error("Error reminding w-9 requests for the payees. " + e.getMessage(), e);
			throw e;
		}
	}
	@DgsMutation
	public FilingDTO addEditFiling1099MISC(@InputArgument("input") AddEditFilingInput1099MISC filingInput) throws Exception {
		return addEditFiling(filingInput);
	}

	@DgsMutation
	public FilingDTO addEditFiling1099MISC2020(@InputArgument("input") AddEditFilingInput1099MISC2020 filingInput) throws Exception {
		return addEditFiling(filingInput);
	}

	@DgsMutation
	public FilingDTO addEditFiling1099MISC2021(@InputArgument("input") AddEditFilingInput1099MISC2021 filingInput) throws Exception {
		return addEditFiling(filingInput);
	}

	@DgsMutation
	public FilingDTO addEditFiling1099NEC(@InputArgument("input") AddEditFilingInput1099NEC filingInput) throws Exception {
		return addEditFiling(filingInput);
	}

	@DgsMutation
	public FilingDTO addEditFiling1099NEC2021(@InputArgument("input") AddEditFilingInput1099NEC2021 filingInput) throws Exception {
		return addEditFiling(filingInput);
	}

	@DgsMutation
	public FilingDTO addEditFiling1099INT(@InputArgument("input") AddEditFilingInput1099INT filingInput) throws Exception {
		return addEditFiling(filingInput);
	}

	@DgsMutation
	public FilingDTO addEditFiling1099OID(@InputArgument("input") AddEditFilingInput1099OID filingInput) throws Exception {
		return addEditFiling(filingInput);
	}

	private FilingDTO addEditFiling(AddEditFilingInput filingInput) throws ApheDataValidationException, Exception {
		try {
			if(filingInput.id == null ) {
				filingInput.id = 0L;
			}
			if(filingInput.originalFilingId == null || filingInput.originalFilingId == 0 ) {
				filingInput.originalFilingId = 0L;
			}

			if(filingInput.id == 0 ) {
				long filingId = filingManager.createFiling(filingInput);
				FilingDTO retVal = filingManager.getFiling(filingId);

				//Update filing counts...
				insightsManager.updateFilingCounts(filingManager.getCurrentDomainId());

				return retVal;
			} else {
				long filingId = filingManager.updateFiling(filingInput);
				return filingManager.getFiling(filingId);
			}
		} catch (Exception e) {
			logger.error("Error adding/editing filing. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean deleteFiling(@InputArgument("filingId") String filingId) throws Exception {
		try {
			filingManager.deleteFiling(filingId);

			insightsManager.updateFilingCounts(filingManager.getCurrentDomainId());

			return true;
		} catch (Exception e) {
			logger.error("Error deleting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Boolean deleteFilings(@InputArgument("filingIds") List<String> filingIds) throws Exception {
		try {
			filingManager.deleteFilings(filingIds);

			insightsManager.updateFilingCounts(filingManager.getCurrentDomainId());

			return true;
		} catch (Exception e) {
			logger.error("Error deleting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> updateFedSub(@InputArgument("filingId") String filingId, @InputArgument("selected") boolean selected) throws Exception {
		try {
			return filingManager.updateFedSub(filingId, selected);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> updateStateSub(@InputArgument("filingId") String filingId, @InputArgument("selected") boolean selected, @InputArgument("filingMethod") String filingMethod) throws Exception {
		try {
			return filingManager.updateStateSub(filingId, selected, filingMethod);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> updatePrintSub(@InputArgument("filingId") String filingId, @InputArgument("selected") boolean selected) throws Exception {
		try {
			return filingManager.updatePrintSub(filingId, selected);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> updateEmailSub(@InputArgument("filingId") String filingId, @InputArgument("selected") boolean selected) throws Exception {
		try {
			return filingManager.updateEmailSub(filingId, selected);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> updateTINMatchByFilingIds(@InputArgument("filingIds") List<String> filingIds, @InputArgument("selected") boolean selected) throws Exception {
		try {
			if(filingIds == null || filingIds.size() == 0) {
				return new ArrayList<>();
			}
			return filingManager.updateTINMatch(filingIds, selected);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public boolean send1099Form(@InputArgument("filingId") String filingId) throws Exception {
		try {
			boolean success = filingManager.send1099Form(filingId);
			return success;
		} catch (Exception e) {
			logger.error("Error sending 1099 form." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> bulkUpdateFedSub(@InputArgument("filingIds") List<String> filingIds, @InputArgument("selected") boolean selected) throws Exception {
		try {
			if(filingIds == null || filingIds.size() == 0) {
				return new ArrayList<>();
			}
			return filingManager.bulkUpdateFedSub(filingIds, selected);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> bulkUpdateStateSub(@InputArgument("stateSubs") StateSubInputs stateSubInputs) throws Exception {
		try {
			//Convert state sub inputs to has map.
			Map<Long, StateSubInput> stateSubInputMap =  stateSubInputs.inputs.stream().collect(Collectors.toMap(input->input.filingId, value->value));
			if(stateSubInputMap.keySet().size() == 0) {
				return new ArrayList<>();
			}
			return filingManager.bulkUpdateStateSub(new ArrayList<>(stateSubInputMap.keySet()), stateSubInputMap);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> bulkUpdatePrintSub(@InputArgument("filingIds") List<String> filingIds, @InputArgument("selected") boolean selected) throws Exception {
		try {
			if(filingIds == null || filingIds.size() == 0) {
				return new ArrayList<>();
			}
			return filingManager.bulkUpdatePrintSub(filingIds, selected);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> bulkUpdateEmailSub(@InputArgument("filingIds") List<String> filingIds, @InputArgument("selected") boolean selected) throws Exception {
		try {
			if(filingIds == null || filingIds.size() == 0) {
				return new ArrayList<>();
			}
			return filingManager.bulkUpdateEmailSub(filingIds, selected);
		} catch (Exception e) {
			logger.error("Error updating federal submission." + e.getMessage(), e);
			throw e;
		}
	}


	@DgsMutation
	public Boolean submitFilings(@InputArgument("filingIds") List<String> filingIds) throws Exception {
		try {
			if(filingIds == null || filingIds.size() == 0) {
				return false;
			}
			filingManager.submitFilings(filingIds);
			return true;
		} catch (Exception e) {
			logger.error("Error submitting filings. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public List<MappedValidationMessages> validateFilings(
			@InputArgument("filingIds") List<String> filingIds, @InputArgument("ignoreWarnings") boolean ignoreWarnings) throws Exception {
		try {
			return filingManager.getValidationMessages(filingIds, ignoreWarnings);
		} catch (Exception e) {
			logger.error("Error validating filings. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public boolean updatePrintRequests(@InputArgument("input") UpdatePrintRequestsInput input) throws Exception {
		try {
			filingManager.updatePrintStatus(input);
			return true;
		} catch (Exception e) {
			logger.error("Error updating print status of filings. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public boolean updateEmailRequests(@InputArgument("input") UpdateEmailRequestsInput input) throws Exception {
		try {
			filingManager.updateEmailStatus(input);
			return true;
		} catch (Exception e) {
			logger.error("Error updating email status of filings. " + e.getMessage(), e);
			throw e;
		}
	}

	@DgsMutation
	public Collection<CustomJobResponseDTO> sendDraftFilingsReminder(@InputArgument("domainIds") String domainIds,
																	 @InputArgument("reminderNumber") int reminderNumber,
																	 @InputArgument("test") boolean test) throws Exception {
		if (!domainMgr.isSystemUser()) {
			throw new ApheDataValidationException("userId", "Invalid user. User not authorized");
		}
		return customJobTask.sendDraftFilingsReminder(domainIds, reminderNumber, test);
	}

	@DgsMutation
	public Collection<CustomJobResponseDTO> sendSeasonReminder(@InputArgument("domainIds") String domainIds,
															   @InputArgument("reminderNumber") int reminderNumber,
															   @InputArgument("test") boolean test) throws Exception {
		if (!domainMgr.isSystemUser()) {
			throw new ApheDataValidationException("userId", "Invalid user. User not authorized");
		}
		return customJobTask.sendSeasonReminder(domainIds, reminderNumber, test);
	}

	@DgsMutation
	public Collection<CustomJobResponseDTO> runCustomJob(@InputArgument("jobName") String jobName,
														 @InputArgument("ids") String ids,
														 @InputArgument("test") boolean test) throws Exception {
		if (!domainMgr.isSystemUser()) {
			throw new ApheDataValidationException("userId", "Invalid user. User not authorized");
		}
		if(jobName.equalsIgnoreCase("deleteDraftFilings")) {
			return customJobTask.deleteDraftFilings(ids, test);
		} else if(jobName.equalsIgnoreCase("reEncryptEfsTins")) {
			return customJobTask.reEncryptEfsTins(ids, test);
		} else if(jobName.equalsIgnoreCase("reEncryptTins")) {
			return customJobTask.reEncryptTins(ids, test);
		} else if(jobName.equalsIgnoreCase("updateInsights")) {
			return customJobTask.updateFilingCounts(ids, test);
		} else {
			throw new ApheDataValidationException("jobName", "Invalid jobName");
		}
	}
}