package com.aphe.contractor.rs;

import com.aphe.common.error.ValidationMessages;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.contractor.dto.AddEditPayerInput;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.services.PayerManager;
import com.aphe.insights.service.InsightsManager;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/rs/api/payer/")
@Tag(name = "Payer", description = "API to manage the payer")
public class PayerController {

	private static Logger logger = LoggerFactory.getLogger(PayerController.class);

	@Autowired
	PayerManager payerManager;

	@Autowired
	InsightsManager insightsManager;

	@PutMapping
	public PayerDTO editPayer(@RequestBody AddEditPayerInput dto, HttpServletRequest request) throws Exception {
		try {
			long newPayerId = payerManager.updatePayer(dto);
			PayerDTO payerDTO = payerManager.getPayer(Long.toString(newPayerId));
			insightsManager.updateTINInsight(payerDTO.domainId);
			return payerDTO;
		} catch (Exception e) {
			logger.error("Error editing payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping
	public PayerDTO getPayer() throws Exception {
		try {
			return payerManager.getPayerByDomainId(getCurrentDoaminId());
		} catch (Exception e) {
			logger.error("Error getting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@PutMapping(path = "validate/")
	public ValidationMessages validatePayer() throws Exception {
		try {
			String domainId = getCurrentDoaminId();
			ValidationMessages messages = payerManager.getValidationMessages(domainId, null);
			return messages;
		} catch (Exception e) {
			logger.error("Error validating domain. " + e.getMessage(), e);
			throw e;
		}
	}

	private String getCurrentDoaminId() {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth != null && auth.isAuthenticated()) {
			ApheUserDetails principal = (ApheUserDetails) auth.getPrincipal();
			long domainId = principal.getDomainId();
			if (domainId > 0)
				return Long.toString(domainId);
		}
		return null;
	}

}
