package com.aphe.contractor.rs;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.util.AESEncryptionUtilStatic;
import com.aphe.contractor.dto.read.W9RequestDataDTO;
import com.aphe.contractor.services.W9RequestManager;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.InputStream;
import java.util.Base64;

@RestController
@RequestMapping("/rs/api/vendorportal/")
public class W9RestController {

	private static Logger logger = LoggerFactory.getLogger(W9RestController.class);

	@Autowired
	W9RequestManager w9RequestManager;

	@Value("${aphe.contractor.w9Dir}")
	public String w9Dir;

	@GetMapping(path = "w9/{requestId}/")
	public W9RequestDataDTO getW9RequestDataById(@PathVariable("requestId") String requestId) throws Exception {
		try {
			return w9RequestManager.getW9RequestById(requestId);
		} catch (Exception e) {
			logger.error("Error getting W9 Request Data. " + e.getMessage(), e);
			throw e;
		}
	}


	@PutMapping(path = "w9/")
	public W9RequestDataDTO updateW9Data(@RequestBody W9RequestDataDTO dto) throws Exception {
		try {
			String updatedEntityId = w9RequestManager.updateW9RequestData(dto);
			if(updatedEntityId != null) {
				return w9RequestManager.getW9RequestById(updatedEntityId);
			}
			return null;
		} catch (Exception e) {
			logger.error("Error getting W9 Request Data. " + e.getMessage(), e);
			throw e;
		}
	}

	@PostMapping(path = "w9/{requestId}/file/")
	public W9RequestDataDTO uploadW9Copy(@PathVariable("requestId") String requestId, @RequestParam(value = "file") String fileData) throws ApheException {
		try {
			Base64.Decoder decoder = Base64.getDecoder();
			byte[] decodedByte = decoder.decode(fileData);
			String updatedEntityId = w9RequestManager.saveW9(requestId, decodedByte);
			if(updatedEntityId != null) {
				return w9RequestManager.getW9RequestById(updatedEntityId);
			} else {
				throw new ApheException("Invalid request id");
			}
		} catch (Exception e) {
			logger.error("Error getting W9 Request Data. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping(path = "w9/{requestId}/file/")
	public void downloadW9(@PathVariable("requestId") String requestId, HttpServletResponse response) throws ApheException {
		try {
				String fileName = requestId + ".pdf";
				String filePath = w9Dir + File.separator + fileName;
				response.setStatus(HttpServletResponse.SC_OK);
				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				response.setContentType("application/pdf");

				InputStream is = null;
				try {
					is = AESEncryptionUtilStatic.getDecryptorStream(filePath);
					byte[] buffer = new byte[4096];
					int len;
					while ((len = is.read(buffer)) > 0) {
						response.getOutputStream().write(buffer, 0, len);
					}
				} finally {
					if (is != null) {
						try {
							is.close();
						} catch (Exception e) {
						}
					}
				}
				response.flushBuffer();
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error downloading file of tnn transmission record."));
		}
	}

}
