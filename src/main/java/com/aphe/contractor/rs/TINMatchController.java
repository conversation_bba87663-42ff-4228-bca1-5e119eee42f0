package com.aphe.contractor.rs;

import com.aphe.contractor.services.TINMatchRequestManager;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Validated
@RequestMapping(path = "/rs/api/tinmatch/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "TIN Match", description = "API to manage tin match requests")
public class TINMatchController {

	private static Logger logger = LoggerFactory.getLogger(TINMatchController.class);

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;


	@PutMapping(path = "status/refresh/")
	public void updateTINMatch() throws Exception {
		try {
//			tinMatchRequestManager.refreshTINMatchRequestStatus();
		} catch (Exception e) {
			logger.error("Error validating filings. " + e.getMessage(), e);
			throw e;
		}
	}

}
