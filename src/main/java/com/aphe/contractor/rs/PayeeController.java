package com.aphe.contractor.rs;

import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.graphql.PagedResult;
import com.aphe.common.util.AphePageable;
import com.aphe.contractor.dto.AddEditPayeeInput;
import com.aphe.contractor.dto.read.PayeeDTO;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.services.PayeeManager;
import com.aphe.contractor.services.PayerManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@RestController
@Validated
@RequestMapping("/rs/api/payees/")
@Tag(name = "Payees", description = "API to manage payees")
public class PayeeController {

	private static Logger logger = LoggerFactory.getLogger(PayeeController.class);

	@Autowired
	PayeeManager payeeManager;

	@Autowired
	PayerManager payerManager;

	@Operation(summary = "Create a new payee", description = "Creates a new payee. If a positive id is passed, an invalid request error will be returned.", tags = { "Payees" })
	@ApiResponses(value = { @ApiResponse(responseCode = "400", description = "id has to be 0. To update an existing payee, send a PUT request."),
			@ApiResponse(responseCode = "500", description = "An internal error occurred. We logged it and we are monitoring it.") })
	@PostMapping(produces = "application/json")
	public PayeeDTO addPayee(@RequestBody AddEditPayeeInput dto) throws Exception {
		try {
			// TODO: If Id, year, formType doesn't match throw an error. Ask the users to delete and create a new form
			// TODO: Further payeeId values can be done later...
			if (dto.id != 0) {
				throw new ApheDataValidationException("id", "Use PUT to update an existing payee or set id to 0 to create a new filing");
			}

			long payeeId = payeeManager.createPayee(payerManager.getCurrentDomainId(), dto);
			return payeeManager.getPayee(payeeId);
		} catch (Exception e) {
			logger.error("Error adding payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@PutMapping(path = "{payeeId}/")
	public PayeeDTO editPayee(@PathVariable Long payeeId, @RequestBody AddEditPayeeInput dto) throws Exception {
		try {
			if (payeeId != dto.id) {
				throw new ApheDataValidationException("id", "payeeId in JSON payload doesn't match the path param");
			}
			long newPayeeId = payeeManager.updatePayee(payeeManager.getCurrentDomainId(), dto);
			return payeeManager.getPayee(newPayeeId);
		} catch (Exception e) {
			logger.error("Error editing payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping(path = "{payeeId}/")
	public PayeeDTO getPayee(@PathVariable long payeeId) throws Exception {
		try {
			return payeeManager.getPayee(payeeId);
		} catch (Exception e) {
			logger.error("Error getting payee. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping
	public PagedResult<PayeeDTO> getPayees(@RequestParam(name = "pageSize", defaultValue = "100") @Max(AphePageable.MAX_PAGE_SIZE) @Min(AphePageable.MIN_PAGE_SIZE) int pageSize,
			@RequestParam(name = "pageNumber", defaultValue = "0") @Max(AphePageable.MAX_PAGE_NUMBER) @Min(AphePageable.MIN_PAGE_NUMBER) int pageNumber) throws Exception {
		try {
			PayerDTO payer = payerManager.getPayerByDomainId(payerManager.getCurrentDomainId());
			return payeeManager.getPayees(payer.id, pageSize, pageNumber);
		} catch (Exception e) {
			logger.error("Error getting payees. " + e.getMessage(), e);
			throw e;
		}
	}

	@DeleteMapping(path = "{payeeId}/")
	public void deletePayee(@PathVariable long payeeId) throws Exception {
		try {
			payeeManager.deletePayee(Long.toString(payeeId));
		} catch (Exception e) {
			logger.error("Error deleting payee. " + e.getMessage(), e);
			throw e;
		}
	}

}
