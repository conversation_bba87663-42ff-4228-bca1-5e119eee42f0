package com.aphe.contractor.rs;

import com.aphe.common.error.MappedValidationMessages;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.graphql.PagedResult;
import com.aphe.common.util.AphePageable;
import com.aphe.common.util.ArrayUtil;
import com.aphe.contractor.dto.*;
import com.aphe.contractor.dto.read.FilingDTO;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.FilingYear;
import com.aphe.contractor.model.enums.FormType;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.PayerManager;
import com.aphe.insights.service.InsightsManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RestController
@Validated
@RequestMapping(path = "/rs/api/filings/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Filings", description = "API to manage payees")
public class FilingController {

	private static Logger logger = LoggerFactory.getLogger(FilingController.class);

	private static HashMap<FilingReturnType, HashMap<FilingYear, Class>> filingInputClasses;

	@Autowired
	InsightsManager insightsManager;

	static  {
		filingInputClasses = new HashMap<>();
		HashMap<FilingYear, Class> tnnMiscInputs = new HashMap<>();
		tnnMiscInputs.put(FilingYear.Y2020, AddEditFilingInput1099MISC2020.class);
		tnnMiscInputs.put(FilingYear.Y2021, AddEditFilingInput1099MISC2021.class);
		tnnMiscInputs.put(FilingYear.Y2022, AddEditFilingInput1099MISC2021.class);
		tnnMiscInputs.put(FilingYear.Y2023, AddEditFilingInput1099MISC2021.class);
		tnnMiscInputs.put(FilingYear.Y2024, AddEditFilingInput1099MISC2021.class);
		filingInputClasses.put(FilingReturnType.Type_1099_MISC, tnnMiscInputs);

		HashMap<FilingYear, Class> tnnNECInputs = new HashMap<>();
		tnnNECInputs.put(FilingYear.Y2020, AddEditFilingInput1099NEC.class);
		tnnNECInputs.put(FilingYear.Y2021, AddEditFilingInput1099NEC2021.class);
		tnnNECInputs.put(FilingYear.Y2022, AddEditFilingInput1099NEC2021.class);
		tnnNECInputs.put(FilingYear.Y2023, AddEditFilingInput1099NEC2021.class);
		tnnNECInputs.put(FilingYear.Y2024, AddEditFilingInput1099NEC2021.class);
		filingInputClasses.put(FilingReturnType.Type_1099_NEC, tnnNECInputs);

		HashMap<FilingYear, Class> tnnINTInputs = new HashMap<>();
		tnnINTInputs.put(FilingYear.Y2020, AddEditFilingInput1099INT.class);
		tnnINTInputs.put(FilingYear.Y2021, AddEditFilingInput1099INT.class);
		tnnINTInputs.put(FilingYear.Y2022, AddEditFilingInput1099INT.class);
		tnnINTInputs.put(FilingYear.Y2023, AddEditFilingInput1099INT.class);
		tnnINTInputs.put(FilingYear.Y2024, AddEditFilingInput1099INT.class);
		filingInputClasses.put(FilingReturnType.Type_1099_INT, tnnINTInputs);

		HashMap<FilingYear, Class> tnnOIDInputs = new HashMap<>();
		tnnOIDInputs.put(FilingYear.Y2020, AddEditFilingInput1099OID.class);
		tnnOIDInputs.put(FilingYear.Y2021, AddEditFilingInput1099OID.class);
		tnnOIDInputs.put(FilingYear.Y2022, AddEditFilingInput1099OID.class);
		tnnOIDInputs.put(FilingYear.Y2023, AddEditFilingInput1099OID.class);
		tnnOIDInputs.put(FilingYear.Y2024, AddEditFilingInput1099OID.class);
		filingInputClasses.put(FilingReturnType.Type_1099_OID, tnnOIDInputs);

	}

	@Autowired
	PayerManager payerManager;

	@Autowired
	FilingManager filingMgr;

	/**
	 * Rest Path should be /filings/{formType}/{year} Get the requestBody and parse it to the right type of input, so that internal structure is not exposed.
	 * 
	 * @param
	 * @return
	 * @throws Exception
	 */
	@PostMapping(path = "{formType}/{year}/")
	@Operation(summary = "Create a new filing", description = "Creates a new filing. If a positive id is passed, an invalid request error will be returned.", requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(name = "FilingData", description = "Filing data specific to each form", oneOf = {
			AddEditFilingInput1099MISC.class, AddEditFilingInput1099MISC2020.class, AddEditFilingInput1099NEC.class, AddEditFilingInput1099INT.class,
			AddEditFilingInput1099OID.class })), description = "Description of the request body", required = true))
	public FilingDTO addFiling(@PathVariable String formType, @PathVariable String year, @RequestBody String json) throws Exception {
		try {
			AddEditFilingInput filingInput = buildFilingInput(formType, year, json);

			// TODO: If Id, year, formType doesn't match throw an error. Ask the users to delete and create a new form
			// TODO: Further payeeId values can be done later...
			if (filingInput.id == null || filingInput.id > 0) {
				throw new ApheDataValidationException("id", "Use PUT to update an existing filing or set id to 0 to create a new filing");
			}
			long filingId = filingMgr.createFiling(filingInput);
			FilingDTO retVal = filingMgr.getFiling(filingId);

			//Update filing counts...
			insightsManager.updateFilingCounts(filingMgr.getCurrentDomainId());

			return retVal;

		} catch (Exception e) {
			logger.error("Error adding filing. " + e.getMessage(), e);
			throw e;
		}
	}

	@Operation(summary = "Update an existing filing", description = "Updates an existing filing.", requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(name = "FilingData", description = "Filing data specific to each form", oneOf = {
			AddEditFilingInput1099MISC.class, AddEditFilingInput1099MISC2020.class, AddEditFilingInput1099NEC.class, AddEditFilingInput1099INT.class,
			AddEditFilingInput1099OID.class })), description = "Description of the request body", required = true))
	@PutMapping(path = "{formType}/{year}/{filingId}/")
	public FilingDTO editFiling(@PathVariable String formType, @PathVariable String year, @PathVariable Long filingId, @RequestBody String json) throws Exception {
		try {

			AddEditFilingInput filingInput = buildFilingInput(formType, year, json);

			// TODO: If Id, year, formType doesn't match throw an error. Ask the users to delete and create a new form
			// TODO: Further payeeId values can be done later...
			if (filingInput.id == null || filingInput.id != filingId) {
				throw new ApheDataValidationException("id", "id in JSON payload doesn't match the path param");
			}

			long updatedFilingId = filingMgr.updateFiling(filingInput);
			return filingMgr.getFiling(updatedFilingId);
		} catch (Exception e) {
			logger.error("Error editing filing. " + e.getMessage(), e);
			throw e;
		}
	}



	private AddEditFilingInput buildFilingInput(String formName, String year, String json) throws ApheException, ApheDataValidationException {
		ObjectMapper mapper = new ObjectMapper();
		AddEditFilingInput filingInput = null;

		try {
			HashMap<FilingYear, Class> classesByFilingType = filingInputClasses.get(FormType.getFormTypeForForm(formName));
			if(classesByFilingType == null) {
				throw new ApheDataValidationException("formType", MessageFormat.format("Unsupported filing type ({0})", formName));
			} else {
				Class inputClass = classesByFilingType.get(FilingYear.getFilingYearForYear(year));
				if(inputClass == null) {
					throw new ApheDataValidationException("formType", MessageFormat.format("Unsupported form {0} for Year ({1})", formName, year));
				} else {
					try {
						filingInput = (AddEditFilingInput) mapper.readValue(json, ((Class)inputClass));
					}catch (IOException e) {
					}

				}
			}
		} catch (Exception e) {
			throw new ApheException(e);
		}
		return filingInput;
	}

	@DeleteMapping(path = "{filingId}/")
	public void deleteFiling(@PathVariable long filingId) throws Exception {
		try {
			filingMgr.deleteFiling(Long.toString(filingId));

			insightsManager.updateFilingCounts(filingMgr.getCurrentDomainId());

		} catch (Exception e) {
			logger.error("Error deleting filing. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping(path = "{filingId}/")
	public FilingDTO getFiling(@PathVariable long filingId) throws Exception {
		try {
			FilingDTO f = filingMgr.getFiling(filingId);
			return f;
		} catch (Exception e) {
			logger.error("Error getting filing. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping(path = "byId/")
	public List<FilingDTO> getFilingsByIds(@RequestParam("ids") String filingIds) throws Exception {
		try {
			List<String> filingIdsList = ArrayUtil.stringToStringList(filingIds);
			List<FilingDTO> filings = filingMgr.getFilingsByIds(filingIdsList);
			return filings;
		} catch (Exception e) {
			logger.error("Error editing filings by ids. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping(path = "byFormType/")
	public PagedResult<FilingDTO> getFilingsByFormType(@RequestParam("formTypes") List<String> formTypes,
			@RequestParam(name = "pageSize", defaultValue = "100") @Max(AphePageable.MAX_PAGE_SIZE) @Min(AphePageable.MIN_PAGE_SIZE) int pageSize,
			@RequestParam(name = "pageNumber", defaultValue = "0") @Max(AphePageable.MAX_PAGE_NUMBER) @Min(AphePageable.MIN_PAGE_NUMBER) int pageNumber) throws Exception {
		try {
			List<FilingReturnType> returnTypes = new ArrayList<FilingReturnType>();

			for (String formName : formTypes) {
				if (FormType.TNN_MISC == FormType.getFormTypeForForm(formName)) {
					returnTypes.add(FilingReturnType.Type_1099_MISC);
				} else if (FormType.TNN_NEC == FormType.getFormTypeForForm(formName)) {
					returnTypes.add(FilingReturnType.Type_1099_NEC);
				} else if (FormType.TNN_INT == FormType.getFormTypeForForm(formName)) {
					returnTypes.add(FilingReturnType.Type_1099_INT);
				} else if (FormType.TNN_OID == FormType.getFormTypeForForm(formName)) {
					returnTypes.add(FilingReturnType.Type_1099_OID);
				} else {
					throw new ApheDataValidationException("formTypes", MessageFormat.format("Unsupported form ({0})", formName));
				}
			}

			if (returnTypes.size() > 0) {
				PayerDTO payerDTO = payerManager.getPayerByDomainId(payerManager.getCurrentDomainId());
				PagedResult<FilingDTO> filings = filingMgr.getFilingsByFormType(payerDTO.id, returnTypes, pageSize, pageNumber);
				return filings;
			} else {
				throw new ApheDataValidationException("formTypes", "No formTypes specified.");
			}
		} catch (Exception e) {
			logger.error("Error getting filings by formTypes. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping(path = "byStatusAndYear/")
	public PagedResult<FilingDTO> getFilingsByStatus(@RequestParam("statuses") List<FilingStatus> statuses,
		 	@RequestParam("filingYears") List<FilingYear> filingYears,
			@RequestParam(name = "pageSize", defaultValue = "100") @Max(AphePageable.MAX_PAGE_SIZE) @Min(AphePageable.MIN_PAGE_SIZE) int pageSize,
			@RequestParam(name = "pageNumber", defaultValue = "0") @Max(AphePageable.MAX_PAGE_NUMBER) @Min(AphePageable.MIN_PAGE_NUMBER) int pageNumber) throws Exception {
		try {
			if (statuses.size() > 0) {
				PayerDTO payerDTO = payerManager.getPayerByDomainId(payerManager.getCurrentDomainId());
				PagedResult<FilingDTO> filings = filingMgr.getFilingsByStatus(payerDTO.id, statuses, filingYears, pageSize, pageNumber);
				return filings;
			} else {
				throw new ApheDataValidationException("statuses", "No statuses specified.");
			}
		} catch (Exception e) {
			logger.error("Error getting filings by statuses. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping
	public PagedResult<FilingDTO> getAllFilings(
			@RequestParam(name = "pageSize", defaultValue = "100") @Max(AphePageable.MAX_PAGE_SIZE) @Min(AphePageable.MIN_PAGE_SIZE) int pageSize,
			@RequestParam(name = "pageNumber", defaultValue = "0") @Max(AphePageable.MAX_PAGE_NUMBER) @Min(AphePageable.MIN_PAGE_NUMBER) int pageNumber) throws Exception {
		try {
			PayerDTO payerDTO = payerManager.getPayerByDomainId(filingMgr.getCurrentDomainId());
			PagedResult<FilingDTO> filings = filingMgr.getFilingsByPayer(payerDTO.id, null, null, null, pageSize, pageNumber);
			return filings;
		} catch (Exception e) {
			logger.error("Error getting all filings. " + e.getMessage(), e);
			throw e;
		}
	}

	@PutMapping(path = "validate/")
	public List<MappedValidationMessages> validateFilings(@RequestParam("filingIds") String filingIds, @RequestParam("print") boolean print, @RequestParam("email") boolean email,
											  @RequestParam("ignoreWarnings") boolean ignoreWarnings) throws Exception {
		try {
			List<String> filingIdsList = ArrayUtil.stringToStringList(filingIds);
			List<MappedValidationMessages> messages = filingMgr.getValidationMessages(filingIdsList, ignoreWarnings);
			return messages;
		} catch (Exception e) {
			logger.error("Error validating filings. " + e.getMessage(), e);
			throw e;
		}
	}

	@PutMapping(path = "submit/")
	public void submitFilings(@RequestParam("filingIds") String filingIds, @RequestParam("print") boolean print, @RequestParam("email") boolean email) throws Exception {
		try {
			List<String> filingIdsList = ArrayUtil.stringToStringList(filingIds);
			filingMgr.submitFilings(filingIdsList);
		} catch (Exception e) {
			logger.error("Error submitting filings. " + e.getMessage(), e);
			throw e;
		}
	}
}
