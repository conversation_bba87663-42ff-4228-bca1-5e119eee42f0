package com.aphe.contractor.rs;

import com.aphe.common.util.AESEncryptionUtilStatic;
import com.aphe.common.util.ArrayUtil;
import com.aphe.common.util.ZipUtil;
import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.FilingYear;
import com.aphe.contractor.model.enums.PrintCopyType;
import com.aphe.contractor.services.FilingManager;
import com.aphe.util.pdfgen.Generator;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Stream;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping(path = "/rs/api/filings/")
@Tag(name = "Filings", description = "API to manage payees")
public class FilingFormController {

	private static Logger logger = LoggerFactory.getLogger(FilingFormController.class);

	@Autowired
	FilingManager filingMgr;

	@Value("${aphe.efs.tnnFormsDir}")
	public String tnnFormsDir;

	@Autowired
	ZipUtil zipUtil;

	/**
	 * FilingIds, CopyType,
	 * Call generator.. generally creates a folder and returns that folder name.
	 * Calculate a actual file name to be used as header.
	 * If more than 1 filing, zip up all files in this folder.
	 * If 1 filing Id, just combine the pdf files of that folder and stream that file.
	 */


	@GetMapping(path = "forms/")
	public void getTnnForms(@RequestParam("filingIds") String filingIds, @RequestParam("printCopyType") PrintCopyType printCopyType, HttpServletResponse response) throws Exception {
		try {
			List<String> filingsIdsList = ArrayUtil.stringToStringList(filingIds);
			printCopyType = printCopyType != null ? printCopyType : PrintCopyType.RecipientCopy;

			String fileName = filingMgr.generateForms(filingsIdsList, true, printCopyType);

			String folderPath = tnnFormsDir + File.separator + fileName;

			//Just stream the file with its's name.
			if(filingsIdsList.size() == 1) {
				streamFirstFileOfTheFolder(response, fileName, folderPath);
			} else {
				streamFolderAsZipStream(response, fileName, folderPath);
			}

		} catch (Exception e) {
			logger.error("Error generating forms. " + e.getMessage(), e);
			throw new RuntimeException("Unknown error");
		}
	}


	@GetMapping(path = "formsIssued/")
	public void getTnnForms(@RequestParam("filingIds") String filingIds, HttpServletResponse response) throws Exception {
		try {
			List<String> filingsIdsList = ArrayUtil.stringToStringList(filingIds);
			PrintCopyType printCopyType = PrintCopyType.RecipientCopy;
			String fileName = filingMgr.generateFormsForFilingsIssued(filingMgr.getCurrentDomainId(), filingsIdsList, true, printCopyType);
			String folderPath = tnnFormsDir + File.separator + fileName;

			//Just stream the file with its's name.
			if(filingsIdsList.size() == 1) {
				streamFirstFileOfTheFolder(response, fileName, folderPath);
			} else {
				streamFolderAsZipStream(response, fileName, folderPath);
			}

		} catch (Exception e) {
			logger.error("Error generating forms. " + e.getMessage(), e);
			throw new RuntimeException("Unknown error");
		}
	}

	/**
	 * End point used by Admin's when we are using our print capability.
	 * @param filingIds
	 * @param printCopyType
	 * @param response
	 * @throws Exception
	 */
	@GetMapping(path = "printcopies/")
	public void getPrintCopies(@RequestParam("filingIds") String filingIds, @RequestParam("printCopyType") PrintCopyType printCopyType, HttpServletResponse response)
			throws Exception {
		try {
			List<String> filingsIdsList = ArrayUtil.stringToStringList(filingIds);
			printCopyType = printCopyType != null ? printCopyType : PrintCopyType.RecipientMailCopy;
			
			//TODO: fix this. We are not encrypting the PDF files generated for our own print service.
			// We are not encrypting it because we need to read this files and combine them.. we could probably unencrypt and read combine and encrypt
			// then decrypt while steeaming... so that data at rest is always encrypted.
			String fileName = filingMgr.generateForms(filingsIdsList, false, printCopyType);

			String folderPath = tnnFormsDir + File.separator + fileName;
			String downloadFileName = fileName + "-1099Forms.pdf";

			streamFolderAsFile(response, folderPath, downloadFileName);

		} catch (Exception e) {
			logger.error("Error generating forms. " + e.getMessage(), e);
			throw new RuntimeException("Unknown error");
		}
	}


	@GetMapping(path = "1096form/")
	public void get1096Form(@RequestParam("contactName") String contactName,
							@RequestParam("contactEmail") String contactEmail,
							@RequestParam("formType") FilingReturnType filingReturnType,
							@RequestParam("filingYear") FilingYear filingYear,
							@RequestParam("numberOfForms") int numberOfForms,
							@RequestParam("federalTaxWithheld") String fedTaxWithheld,
							@RequestParam("totalAmountReported") String totalAmountReported,
							HttpServletResponse response) throws Exception {
		try {
			String fileName = filingMgr.generate1096(contactName, contactEmail, filingReturnType, filingYear, numberOfForms, fedTaxWithheld, totalAmountReported);
			String folderPath = tnnFormsDir + File.separator + fileName;
			streamFirstFileOfTheFolder(response, fileName, folderPath);
		} catch (Exception e) {
			logger.error("Error generating forms. " + e.getMessage(), e);
			throw new RuntimeException("Unknown error");
		}
	}

	private void streamFirstFileOfTheFolder(HttpServletResponse response, String fileName, String folderPath) throws Exception {
		String firstFileName = findFirstFile(folderPath, ".pdf");

		if(firstFileName == null) {
			throw new IllegalArgumentException("No file to downlaod");
		}

		response.setStatus(HttpServletResponse.SC_OK);
		response.addHeader("Content-Disposition", "attachment; filename=" + firstFileName);
		response.setContentType("application/pdf");

		InputStream is = null;
		try (OutputStream out = response.getOutputStream()) {
			is = AESEncryptionUtilStatic.getDecryptorStream(folderPath + File.separator + firstFileName);
			byte[] buffer = new byte[4096];
			int len;
			while ((len = is.read(buffer)) > 0) {
				out.write(buffer, 0, len);
			}
			out.flush();
		} catch (IOException e) {
			// handle exception
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (Exception e) {
				}
			}
		}
		response.flushBuffer();
	}

	private void streamFolderAsZipStream(HttpServletResponse response, String fileName, String folderPath) throws Exception {
		response.setStatus(HttpServletResponse.SC_OK);
		response.addHeader("Content-Disposition", "attachment; filename=" + fileName + "-1099Forms.zip");
		response.setContentType("application/zip");

		ZipOutputStream zos = null;
		try {
			zos = new ZipOutputStream(response.getOutputStream());
			zipUtil.addZipEntry(fileName, folderPath, zos);
		} catch (Exception e) {
			throw new Exception("Error creating zip stream.");
		} finally {
			if (zos != null) {
				try {
					zos.close();
				} catch (Exception e) {
				}
			}
		}

		response.flushBuffer();
	}

	private void streamFolderAsFile(HttpServletResponse response, String folderPath, String downloadFileName) throws Exception {
		//We need to combine all the files of this folder path and just return that...

		String finalFilePath = folderPath + File.separator + downloadFileName;

		Generator generator = new Generator();
		generator.mergePDFs(folderPath, finalFilePath);

		response.setStatus(HttpServletResponse.SC_OK);
		response.addHeader("Content-Disposition", "attachment; filename=" + downloadFileName);
		response.setContentType("application/pdf");

		try (OutputStream out = response.getOutputStream()) {
			File finalFile = new File(finalFilePath);
			Path path = finalFile.toPath();
			Files.copy(path, out);
			out.flush();
		} catch (IOException e) {
			// handle exception
		}
		response.flushBuffer();
	}


	private String findFirstFile(String folderPath, String fileExtension) throws IOException {
		Path path = Paths.get(folderPath);
		if (!Files.isDirectory(path)) {
			throw new IllegalArgumentException("Path must be a directory!");
		}
		String result;
		try (Stream<Path> walk = Files.walk(path)) {
			result = walk
					.filter(p -> !Files.isDirectory(p))
					.map(p -> p.getFileName().toString())
					.filter(f -> f.endsWith(fileExtension))
					.findFirst().orElse(null);
		}

		return result;
	}


}
