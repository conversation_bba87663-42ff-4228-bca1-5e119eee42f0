package com.aphe.contractor.util;

import com.aphe.common.beanutils.BeanUtil;
import com.aphe.common.util.TINEncryptionUtil;
import com.aphe.contractor.model.w9.W9Request;
import com.aphe.spring.ApplicationContextProvider;
import org.eclipse.persistence.sessions.UnitOfWork;
import org.eclipse.persistence.sessions.changesets.DirectToFieldChangeRecord;
import org.eclipse.persistence.sessions.changesets.ObjectChangeSet;
import org.eclipse.persistence.sessions.changesets.UnitOfWorkChangeSet;
import org.springframework.stereotype.Component;

import jakarta.persistence.*;


//TODO : Move this to right package.
@Component
public class W9EncryptionListener {
	
	@PostLoad
	@PostUpdate
	public void decryptPassword(Object pc) {
		//Don't do anything special when loading from database.
		return;
	}

	@PrePersist
	@PreUpdate
	public void encryptSecrets(Object pc) {
		if (!(pc instanceof W9Request)) {
			return;
		}
		W9Request w9Request = (W9Request) pc;
		handleTin(w9Request);
	}

	private void handleTin(W9Request w9Request) {
		TINEncryptionUtil tinEncryptionUtil = ApplicationContextProvider.getBean(TINEncryptionUtil.class);
		String existingTin = w9Request.getTin();
		if(w9Request.getId() != null && w9Request.getId() > 0) {
			EntityManager entityManager = BeanUtil.getBean(EntityManager.class);
			UnitOfWorkChangeSet changes = entityManager.unwrap(UnitOfWork.class).getCurrentChanges();
			if(changes != null && changes.getObjectChangeSetForClone(w9Request)  != null) {
				ObjectChangeSet objectChanges = changes.getObjectChangeSetForClone(w9Request);
				DirectToFieldChangeRecord change = (DirectToFieldChangeRecord)objectChanges.getChangesForAttributeNamed("tin");
				existingTin = change != null && change.getOldValue() != null ? change.getOldValue().toString() : existingTin;
			}
			
		}
		
		String tin = w9Request.getTin();
		String encryptedTin = w9Request.getTinEncrypted();
		String hashedTin = w9Request.getTinEncrypted1();
		//TODO: Inject this as a bean. Right now FilingEntityEncryptionListener is not managed by spring, hence this bean is null
		String[] updatedValues = tinEncryptionUtil.getUpdatedValues(existingTin, tin, encryptedTin, hashedTin);
		w9Request.setTin(updatedValues[0]);
		w9Request.setTinEncrypted(updatedValues[1]);
		w9Request.setTinEncrypted1(updatedValues[2]);
	}
}