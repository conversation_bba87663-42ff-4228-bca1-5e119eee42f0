package com.aphe.contractor.dto.read;

import com.aphe.contractor.model.enums.PrintStatus;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

@Schema(name = "PrintSub")
public class PrintSubDTO {

    public long id;
    public long filingId;

    public Boolean selected;

    public Boolean isPaid;

    public PrintStatus printStatus;
    public Date printStatusChangeDate;
    public String printStatusChangeDesc;
    public Date printCopyExpectedDeliveryDate;
    public String printCopyTrackingId;

    public List<PrintStatusHistoryDTO> printStatusHistory;

}
