package com.aphe.contractor.dto.read;

public class W9RequestDataDTO {

    // For W9, we are using GUID as the id, which is going to be a string.
    public String id;

    public long payeeId;

    public boolean isPaid;
    public String w9RequestStatus;

    public String payerName;
    public String payeeEmail;

    public String displayName;

    public String firstName;

    public String lastName;

    public String middleName;

    public String businessName;
    public String dba;

    public String entityType;

    public String fedTaxClassification;
    public String llcTaxClassification;
    public String tinType;
    public String tin;

    public String fileName;

    public String emailAddress;
    public String phoneNumber;
    public String phoneExt;
    public AddressDTO address;
}
