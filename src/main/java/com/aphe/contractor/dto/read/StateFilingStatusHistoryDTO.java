package com.aphe.contractor.dto.read;

import com.aphe.contractor.model.enums.StateFilingStatus;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

@Schema(name = "StateFilingStatus", description = "Filing status changes.")
public class StateFilingStatusHistoryDTO {

	public long id;
	public StateFilingStatus status;
	public Date statusChangeDate;
	public String statusChangeDesc;

}
