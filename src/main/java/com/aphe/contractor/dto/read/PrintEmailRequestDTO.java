package com.aphe.contractor.dto.read;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "PrintEmailRequest", description = "A filing's print and email request")
public class PrintEmailRequestDTO {

	/**
	 * This is TNNP FilingId.
	 */
	public String filingId;
	public String formType;
	public String filingStatus;
	public String emailStatus;
	public String printStatus;
	public String printCopyTrackingId;

	public String domainId;

	public String payerName;
	public AddressDTO payerAddress;

	public String payeeName;
	public AddressDTO payeeAddress;

}
