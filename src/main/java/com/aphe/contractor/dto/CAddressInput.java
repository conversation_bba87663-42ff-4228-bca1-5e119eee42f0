package com.aphe.contractor.dto;

import com.aphe.contractor.model.enums.CountryCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.*;

import jakarta.validation.constraints.NotNull;

@Schema(name = "AddressInput", description = "Address entity")
@PostalCode(countryPropertyName = "country", postalCodePropertyName = "postalCode", value = {
		@PostalCodeRegEx(country = {"US", "AS", "GU", "MP", "PR", "VI"}, regex = "(([0-9]{5})|([0-9]{5}-[0-9]{4}))"),
		@PostalCodeRegEx(country = "CA", regex = "[A-Za-z][0-9][A-Za-z] [0-9][A-Za-z][0-9]")
})
@StateCode(countryPropertyName = "country", statePropertyName = "state", value = {
		@StateCodeEnum(country = "US", enumClass = "com.aphe.contractor.model.enums.StateCode"),
})
public class CAddressInput {

	@NotNull(message = "Address street name is required")
	@TrimLength(min = 1, max = 40)
	public String line1;

	@TrimLength(min = 0, max = 40)
	public String line2;

	@NotNull(message = "Address city is required")
	@TrimLength(min = 1, max = 40)
	public String city;

	@NotNull(message = "Address state is required")
	public String state;

	@NotNull(message = "Address country is required")
	public CountryCode country;

	@NotNull(message = "Address zipcode is required")
	public String postalCode;

	public CAddressInput() {
	}

	public CAddressInput(String line1, String line2, String city, String state, CountryCode country, String zipCode) {
		this.line1 = line1;
		this.line2 = line2;
		this.city = city;
		this.state = state;
		this.country = country;
		this.postalCode = zipCode;
	}
	
	public String getCombinedStreet() {
        String addressLine = "";
        addressLine = line1 != null ? (addressLine + " " + line1).trim() : addressLine;
        addressLine = line2 != null ? (addressLine + " " + line2).trim() : addressLine;
        return addressLine;
	}

	public String getLine1() {
		return line1;
	}

	public void setLine1(String line1) {
		this.line1 = line1;
	}

	public String getLine2() {
		return line2;
	}

	public void setLine2(String line2) {
		this.line2 = line2;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public CountryCode getCountry() {
		return country;
	}

	public void setCountry(CountryCode country) {
		this.country = country;
	}

	public String getPostalCode() {
		return postalCode;
	}

	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}
}
