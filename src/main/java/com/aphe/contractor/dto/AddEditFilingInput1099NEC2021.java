package com.aphe.contractor.dto;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.StateCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.BigDecimalMinMax;
import org.hibernate.validator.constraints.BigDecimalRange;

import jakarta.validation.constraints.NotNull;

@Schema(name = "FilingInput1099NEC2021")
public class AddEditFilingInput1099NEC2021 extends AddEditFilingInput {

	public FilingReturnType getFilingReturnType() {
		return FilingReturnType.Type_1099_NEC;
	}
	
	public String payeeAccountNumber;

	@NotNull
	public Boolean secondTinNotice;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String nonEmployeeComp;

	@NotNull
	public Boolean directSalesIndicator;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String federalTaxWithheld;

	public StateCode state1Code;

	public String state1EIN;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String state1TaxWithheld;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String state1Income;

}
