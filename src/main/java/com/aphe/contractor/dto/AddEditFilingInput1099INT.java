package com.aphe.contractor.dto;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.StateCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.BigDecimalMinMax;
import org.hibernate.validator.constraints.BigDecimalRange;

import jakarta.validation.constraints.NotNull;

@Schema(name = "FilingInput1099INT")
public class AddEditFilingInput1099INT extends AddEditFilingInput {

	public FilingReturnType getFilingReturnType() {
		return FilingReturnType.Type_1099_INT;
	}

	@NotNull
	public Boolean fatcaFilingRequirementIndicator;

	public String payeeAccountNumber;

	@NotNull
	public Boolean secondTinNotice;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String interestIncome;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String earlyWithdrawalPenalty;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String interestOnUSSavingsBonds;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String federalTaxWithheld;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String investmentExpenses;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String foreignTaxPaid;

	public String foreignCountry;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String taxExemptInterest;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String specifiedPrivateActivityBondInterest;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String marketDiscount;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String bondPremium;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String bondPremiumOnTreasuryObligations;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String bondPremiumOnTaxExemptBond;

	public String taxExemptTaxCreditBondCusipNo;

	public StateCode state1Code;

	public String state1EIN;

	@BigDecimalRange(minPrecision = 1, maxPrecision = 11, scale = 2, message = "Can only be 11 digits long")
	@BigDecimalMinMax(min = "0.00", message = "Amount can not be less than 0.00")
	public String state1TaxWithheld;

}
