package com.aphe.contractor.dto;

import com.aphe.contractor.model.enums.CorrectionType;
import com.aphe.contractor.model.enums.FilingReturnType;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;

@Schema(name = "FilingInput")
public abstract class AddEditFilingInput {

	public String id;

	@NotNull
	public String payeeId;

	@NotNull
	public boolean isTestFiling;

	@NotNull
	public String filingYear;

	@NotNull
	public CorrectionType correctionType;

	public String originalFilingId;

	public abstract FilingReturnType getFilingReturnType();

}
