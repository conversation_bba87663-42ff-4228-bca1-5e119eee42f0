package com.aphe.contractor.model;

import com.aphe.common.model.BaseEntity;
import com.aphe.contractor.model.enums.FilingStatus;
import org.eclipse.persistence.annotations.Cache;

import jakarta.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "c_fedsubs")
@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true)
public class FedSub extends BaseEntity {

	@ManyToOne(optional = false, fetch = FetchType.LAZY)
	@JoinColumn(name = "FILING_ID", nullable = false)
	private Filing filing;

	@Column(name = "SELECTED")
	private Boolean selected = false;

	@Column(name = "SUBMISSION_REF_ID")
	private String submissionRefId; //Unique ID representing the submission request. A filing can be submitted to EFS multiple times

	@Column(name = "FILING_REQUEST_ID")
	private long filingRequestId; // ID to track the efile service id.

	@Column(name = "UNIQUE_RECORD_ID")
	private String uniqueRecordId; // ID to track the filing with the agency. uniqueTransmissionIdentifier|submissionGroupId|recordId or TCC|IRS_FILE_NAME|SEQ_NUMBER

	@Enumerated(EnumType.STRING)
	@Column(name = "STATUS")
	private FilingStatus status;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "STATUS_CHANGE_DATE")
	private Date statusChangeDate;

	@Column(name = "STATUS_CHANGE_DESC")
	private String statusChangeDesc;

	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@JoinColumn(name = "SUB_ID")
	private List<FilingStatusEntity> filingStatusHistory;

	@Column(name = "IS_PAID")
	private Boolean isPaid;

	public Filing getFiling() {
		return filing;
	}

	public void setFiling(Filing filing) {
		this.filing = filing;
	}

	public String getSubmissionRefId() {
		return submissionRefId;
	}

	public void setSubmissionRefId(String submissionRefId) {
		this.submissionRefId = submissionRefId;
	}

	public long getFilingRequestId() {
		return filingRequestId;
	}

	public void setFilingRequestId(long filingRequestId) {
		this.filingRequestId = filingRequestId;
	}

	public FilingStatus getStatus() {
		return status;
	}

	public void setStatus(FilingStatus status) {
		this.status = status;
	}

	public Date getStatusChangeDate() {
		return statusChangeDate;
	}

	public void setStatusChangeDate(Date statusChangeDate) {
		this.statusChangeDate = statusChangeDate;
	}

	public String getStatusChangeDesc() {
		return statusChangeDesc;
	}

	public void setStatusChangeDesc(String statusChangeDesc) {
		this.statusChangeDesc = statusChangeDesc;
	}

	public List<FilingStatusEntity> getFilingStatusHistory() {
		return filingStatusHistory;
	}

	public void setFilingStatusHistory(List<FilingStatusEntity> filingStatusHistory) {
		this.filingStatusHistory = filingStatusHistory;
	}

	public Boolean getIsPaid() {
		return isPaid;
	}

	public Boolean getPaid() {
		return isPaid;
	}

	public void setPaid(Boolean paid) {
		isPaid = paid;
	}

	public Boolean getSelected() {
		return selected;
	}

	public void setSelected(Boolean selected) {
		this.selected = selected;
	}

	public String getUniqueRecordId() {
		return uniqueRecordId;
	}

	public void setUniqueRecordId(String uniqueRecordId) {
		this.uniqueRecordId = uniqueRecordId;
	}
}
