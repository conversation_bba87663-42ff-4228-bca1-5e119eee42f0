package com.aphe.contractor.model.w9;

import com.aphe.common.model.BaseEntity;
import com.aphe.contractor.model.CAddress;
import com.aphe.contractor.model.Payee;
import com.aphe.contractor.model.W9RequestStatusEntity;
import com.aphe.contractor.model.enums.EntityType;
import com.aphe.contractor.model.enums.W9RequestStatus;
import com.aphe.contractor.util.W9EncryptionListener;
import org.eclipse.persistence.annotations.Cache;

import jakarta.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "c_w9requests")
@EntityListeners(W9EncryptionListener.class)
@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true)
public class W9Request extends BaseEntity {

	@ManyToOne(optional = false, fetch = FetchType.LAZY)
	@JoinColumn(name = "PAYEE_ID", nullable = false)
	private Payee payee;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="REQUEST_DATE")
	private Date requestDate;

	@Column(name = "IS_PAID")
	private Boolean isPaid;

	@Enumerated(EnumType.STRING)
	@Column(name="STATUS")
	private W9RequestStatus w9RequestStatus;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="STATUS_CHANGE_DATE")
	private Date statusChangeDate;

	@Column(name="STATUS_CHANGE_DESC")
	private String statusChangeDesc;

	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@JoinColumn(name = "REQUEST_ID")
	private List<W9RequestStatusEntity> requestStatusHistory;

	@Column(name="PAYER_NAME")
	private String payerName;

	@Column(name="DISPLAY_NAME")
	private String displayName;

	@Column(name="FIRST_NAME")
	private String firstName;

	@Column(name="MIDDLE_NAME")
	private String middleName;

	@Column(name="LAST_NAME")
	private String lastName;

	@Column(name="BUSINESS_NAME")
	private String businessName;

	@Column(name="DBA")
	private String dba;

	/**
	 * Federal Classification
	 * Individual/Sole Prop/Single member LLC
	 * C Corp
	 * S Corp
	 * Partnership
	 * Trust/Estate
	 * Limited Liability Company (S, C, Partnership)
	 * Other
	 */

	@Enumerated(EnumType.STRING)
	@Column(name="ENTITY_TYPE")
	private EntityType entityType;


	@Enumerated(EnumType.STRING)
	@Column(name="FED_TAX_TYPE")
	private FedTaxClassification fedTaxClassification;

	@Column(name="LLC_TAX_TYPE")
	@Enumerated(EnumType.STRING)
	private LLCTaxClassification llcTaxClassification;

	@Column(name="TIN_TYPE")
	private String tinType;

	@Column(name="TIN")
	private String tin;

	@Column(name="TIN_ENCRYPTED")
	private String tinEncrypted;

	@Column(name="TIN_ENCRYPTED1")
	private String tinEncrypted1;

	@Column(name = "EMAIL_ADDRESS")
	private String emailAddress;

	@Column(name = "PHONE_NUMBER")
	private String phoneNumber;

	@Column(name = "PHONE_EXT")
	private String phoneExt;

	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	private CAddress address;


	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="SIGNED_DATE")
	private Date signedDate;

	@Column(name="SIGNED_IP")
	private String signedIP;

	@Column(name="SIGNED_DEVICE")
	private String signedDevice;

	@Column(name="SIGNED_LOCATION")
	private String signedLocation;

	@Column(name="FILE_NAME")
	private String fileName;


	public Boolean getIsPaid() {
		return isPaid;
	}


	public Payee getPayee() {
		return payee;
	}

	public void setPayee(Payee payee) {
		this.payee = payee;
	}

	public Date getRequestDate() {
		return requestDate;
	}

	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}

	public Boolean getPaid() {
		return isPaid;
	}

	public void setPaid(Boolean paid) {
		isPaid = paid;
	}

	public W9RequestStatus getW9RequestStatus() {
		return w9RequestStatus;
	}

	public void setW9RequestStatus(W9RequestStatus w9RequestStatus) {
		this.w9RequestStatus = w9RequestStatus;
	}

	public Date getStatusChangeDate() {
		return statusChangeDate;
	}

	public void setStatusChangeDate(Date statusChangeDate) {
		this.statusChangeDate = statusChangeDate;
	}

	public String getStatusChangeDesc() {
		return statusChangeDesc;
	}

	public void setStatusChangeDesc(String statusChangeDesc) {
		this.statusChangeDesc = statusChangeDesc;
	}

	public List<W9RequestStatusEntity> getRequestStatusHistory() {
		return requestStatusHistory;
	}

	public void setRequestStatusHistory(List<W9RequestStatusEntity> requestStatusHistory) {
		this.requestStatusHistory = requestStatusHistory;
	}

	public String getBusinessName() {
		return businessName;
	}

	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	public String getTinType() {
		return tinType;
	}

	public void setTinType(String tinType) {
		this.tinType = tinType;
	}

	public String getTin() {
		return tin;
	}

	public void setTin(String tin) {
		this.tin = tin;
	}

	public String getTinEncrypted() {
		return tinEncrypted;
	}

	public void setTinEncrypted(String tinEncrypted) {
		this.tinEncrypted = tinEncrypted;
	}

	public Date getSignedDate() {
		return signedDate;
	}

	public void setSignedDate(Date signedDate) {
		this.signedDate = signedDate;
	}

	public String getSignedIP() {
		return signedIP;
	}

	public void setSignedIP(String signedIP) {
		this.signedIP = signedIP;
	}

	public String getSignedDevice() {
		return signedDevice;
	}

	public void setSignedDevice(String signedDevice) {
		this.signedDevice = signedDevice;
	}

	public String getSignedLocation() {
		return signedLocation;
	}

	public void setSignedLocation(String signedLocation) {
		this.signedLocation = signedLocation;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getDba() {
		return dba;
	}

	public void setDba(String dba) {
		this.dba = dba;
	}

	public CAddress getAddress() {
		return address;
	}

	public void setAddress(CAddress address) {
		this.address = address;
	}

	public FedTaxClassification getFedTaxClassification() {
		return fedTaxClassification;
	}

	public void setFedTaxClassification(FedTaxClassification fedTaxClassification) {
		this.fedTaxClassification = fedTaxClassification;
	}

	public LLCTaxClassification getLlcTaxClassification() {
		return llcTaxClassification;
	}

	public void setLlcTaxClassification(LLCTaxClassification llcTaxClassification) {
		this.llcTaxClassification = llcTaxClassification;
	}

	public EntityType getEntityType() {
		return entityType;
	}

	public void setEntityType(EntityType entityType) {
		this.entityType = entityType;
	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getPhoneExt() {
		return phoneExt;
	}

	public void setPhoneExt(String phoneExt) {
		this.phoneExt = phoneExt;
	}

	public String getTinEncrypted1() {
		return tinEncrypted1;
	}

	public void setTinEncrypted1(String tinEncrypted1) {
		this.tinEncrypted1 = tinEncrypted1;
	}
}
