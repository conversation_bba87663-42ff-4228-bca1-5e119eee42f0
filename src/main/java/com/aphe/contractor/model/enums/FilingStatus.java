package com.aphe.contractor.model.enums;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 
 * Draft - just a working copy. Can be deleted etc.
 * Submitted - No longer a draft. Customer can not change it at all.
 * System_Rejected  - We found an issue with the data. Customer needs to move this filing to draft mode, fix the data and resubmit again. Customer won't be charged for re-submission.  
 * Received - We have been able to validate the data with EFS system and created a record in EFS
 * Queued - EFS system has queued to be filed with next file that is going to be generated.
 * Processing - File generation has begun, file has been created, waiting for an agent to send it to agency.
 * Sent_To_Agency - Our agents have uploaded the file to FIRE system and waiting for the FIRE system to give response.
 * Accepted - Agency accepted the file.
 * Agency_Rejected - Agency rejected for whatever reason.
 * Cancelled is soft delete - not used currently.
 */

public enum FilingStatus {
	Draft, Submitted, WaitingOnTINMatch, Received, Queued, Processing, ReadyForDownload, Sent_To_Agency, Accepted, Cancelled, None;

	public static List<FilingStatus> draftStatuses = Arrays.asList(Draft, Submitted, WaitingOnTINMatch, None);
	public static List<FilingStatus> submittedStatuses = Arrays.asList(Submitted, WaitingOnTINMatch, Received, Queued, Processing, Sent_To_Agency, Accepted);

	public static List<FilingStatus> nonDraftStatuses = Arrays.asList(Submitted, WaitingOnTINMatch, Received, Queued, Processing, Sent_To_Agency, Accepted);

	public static List<FilingStatus> nonTerminalStatuses = Arrays.asList(Submitted, WaitingOnTINMatch, Received, Queued, Processing, Sent_To_Agency);

	public static FilingStatus getFromEFSFilingStatus(String name) {
		for (FilingStatus filingStatus : FilingStatus.values()) {
			if (filingStatus.name().equalsIgnoreCase(name)) {
				return filingStatus;
			}
		}
		return null;
	}
}