package com.aphe.contractor.model.enums;

import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class StateFilingDataSummary {

    public static void main(String[] args) {
        System.out.println("1099-NEC State Filing Report:");
        printFormSummary(FilingReturnType.Type_1099_NEC);

        System.out.println();
        System.out.println();
        System.out.println("1099-MISC State Filing Report:");
        printFormSummary(FilingReturnType.Type_1099_MISC);

    }

    public static List<String> territories = Arrays.asList(new String[]{"AS", "GU", "MP", "PR", "VI"});


    public static void printFormSummary(FilingReturnType filingReturnType) {
        String error = "Error";
        List<StateCode> statesOnly = getStatesOnlyStateCodes();

        //State that don't have income and don't accept the filing.
        Set<String> noFilingRequiredStates = new TreeSet<>();

        //State that require the filing.
        Set<String> filingRequiredStates = new TreeSet<>();

        //States that require eFile even when no state tax is withheld.
        Set<String> eFileOnlyStatesWhenNoTaxWithheld = new TreeSet<>();

        //States with selfEFile only.
        Set<String> selfEFileOnlyStatesWhenNoTaxWithheld = new TreeSet<>();


        //States that require self filing. Not supported by us.
        Set<String> selfFileOnlyStatesWhenNoTaxWithheld = new TreeSet<>();


        //States that we support filing corrections
        Set<String> statesWithNoCorrectionSupport = new TreeSet<>();


        for (StateCode sc : statesOnly) {
            StateFilingData sfd = StateFilingData.getStateFilingData(sc.name());
            try {

                boolean isFilingRequired = sfd.isStateFilingRequired(filingReturnType);

                if(isFilingRequired ) {
                    filingRequiredStates.add(sc.getStateName());
                } else {
                    noFilingRequiredStates.add(sc.getStateName());
                }

                List<StateFilingMethod> supportedOptions = sfd.getSupportedStateFilingMethods(filingReturnType);
                boolean isEFileState = supportedOptions.contains(StateFilingMethod.EFile);

                BigDecimal stateIncome = new BigDecimal("3000.00");
                BigDecimal stateTaxWithheld = new BigDecimal("0.00");
                List<StateFilingMethod> allowedMethods = sfd.getAllowedStateFilingMethods(filingReturnType, stateIncome, stateTaxWithheld, sc, sc, sc);
                boolean isStateFilingRequired = sfd.isStateFilingRequired(filingReturnType, stateIncome, stateTaxWithheld, sc, sc, sc);

                if(isStateFilingRequired && allowedMethods.contains(StateFilingMethod.EFile) && !allowedMethods.contains(StateFilingMethod.CFSF)) {
                    eFileOnlyStatesWhenNoTaxWithheld.add(sc.getStateName());
                }

                if(isStateFilingRequired && allowedMethods.contains(StateFilingMethod.SelfEFile)
                        && !allowedMethods.contains(StateFilingMethod.CFSF)
                        && !allowedMethods.contains(StateFilingMethod.EFile)) {
                    selfEFileOnlyStatesWhenNoTaxWithheld.add(sc.getStateName());
                }


                if(isStateFilingRequired && allowedMethods.contains(StateFilingMethod.SelfFile)
                        && !allowedMethods.contains(StateFilingMethod.CFSF)
                        && !allowedMethods.contains(StateFilingMethod.EFile)
                        && !allowedMethods.contains(StateFilingMethod.SelfEFile)) {
                    selfFileOnlyStatesWhenNoTaxWithheld.add(sc.getStateName());
                }

                if(isStateFilingRequired) {
                    List<StateFilingMethod> supportedCorrectionMethods = sfd.getAllowedFilingMethodsForCorrections(filingReturnType);
                    if(!supportedCorrectionMethods.contains(StateFilingMethod.CFSF)
                            && !supportedCorrectionMethods.contains(StateFilingMethod.EFile)
                            && !supportedCorrectionMethods.contains(StateFilingMethod.SelfEFile)) {
                        statesWithNoCorrectionSupport.add(sc.getStateName());
                    }

                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        noFilingRequiredStates.stream().sorted();
//		System.out.println("NEC Filing Requirement " + filingRequiredStates);
//		System.out.println("MISC Filing Requirement " + miscFilingRequiredStates);
        System.out.println("No Filing Required States: (" + noFilingRequiredStates.size() + ") -- " + noFilingRequiredStates);
        System.out.println("Filing Required States:  (" + filingRequiredStates.size()  + ") -- " + filingRequiredStates);

        System.out.println("EFile Only States: (" + eFileOnlyStatesWhenNoTaxWithheld.size() + ") -- " + eFileOnlyStatesWhenNoTaxWithheld);
        System.out.println("Self EFile Only States: (" + selfEFileOnlyStatesWhenNoTaxWithheld.size() + ") -- " + selfEFileOnlyStatesWhenNoTaxWithheld);
        System.out.println("Self file Only States: (" + selfFileOnlyStatesWhenNoTaxWithheld.size() + ")-- " + selfFileOnlyStatesWhenNoTaxWithheld);

        System.out.println("No Correction Support States: (" + statesWithNoCorrectionSupport.size() + ")-- " + statesWithNoCorrectionSupport);
    }

    @NotNull
    private static List<StateCode> getStatesOnlyStateCodes() {
        StateCode[] allStateCodes = StateCode.values();
        List<StateCode> stateCodeList = Arrays.asList(allStateCodes);
        List<StateCode> statesOnly = stateCodeList.stream().filter(st -> !territories.contains(st.name())).collect(Collectors.toList());
        return statesOnly;
    }


}
