package com.aphe.contractor.model.enums;

/**
 * <AUTHOR>
 * <p>
 * WaitingOnEfile - waiting for efile to be accepted. Is this really needed?
 * Queued - Queued for mail.
 * EMailed - Emailed using third party provider.
 * Delivered - Mail has been delivered to the client system
 * Viewed - User opened the email.
 * Clicked - User clicked a link in the email
 * Bounced - Email bounced.
 */

import java.util.Arrays;
import java.util.List;

/**
 * SendInBlue  -- Webhook events.
 * Sent , Delivered, Opened, Clicked, Soft Bounce, Hard Bounce, Invalid Email, Deferred, Complaint, Unsubscribed, Blocked, Error.
 */

public enum EmailStatus {
    None(false), Submitted(false), WaitingOnEfile(false), Queued(false),
    Emailed(false), Delivered(false), Opened(false), Clicked(false),
    Bounced(true), Errored(false), Cancelled(false);

    public static List<EmailStatus> nonTerminalStatuses = Arrays.asList(Submitted, WaitingOnEfile, Queued, Errored);

    private boolean notify = false;

    private EmailStatus(boolean notify) {
        this.notify = notify;
    }

    public boolean isNotify() {
        return notify;
    }

}