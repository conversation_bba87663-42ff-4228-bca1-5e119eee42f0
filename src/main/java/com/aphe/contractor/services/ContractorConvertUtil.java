package com.aphe.contractor.services;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.dto.AddEditFilingInput;
import com.aphe.contractor.dto.AddEditPayeeInput;
import com.aphe.contractor.dto.AddEditPayeeLightInput;
import com.aphe.contractor.dto.AddEditPayerInput;
import com.aphe.contractor.dto.read.*;
import com.aphe.contractor.model.*;
import com.aphe.contractor.model.enums.*;
import com.aphe.contractor.model.w9.W9Request;
import com.aphe.domain.dto.AddEditDomainInput;
import com.aphe.domain.dto.AddressDTO;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.model.Address;
import com.aphe.domain.model.Domain;
import com.aphe.domain.repo.DomainRepository;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.dto.*;
import com.aphe.efs.model.enums.CorrectionType;
import com.aphe.efs.model.enums.StateCode;
import com.aphe.efs.model.enums.TinType;
import com.aphe.efs.tinm.dto.TINMatchRequestDTO;
import com.aphe.efs.tinm.model.enums.TINMatchTINType;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ContractorConvertUtil {
	Logger logger = LoggerFactory.getLogger(ContractorConvertUtil.class);

	@Autowired
	ContractorMapper mapper;

	@Autowired
	DomainMgr domainMgr;

	@Autowired
	DomainRepository domainRepo;

	@Autowired
	FilingManager filingManager;


	public AddEditDomainInput toAddEditDomainInput(AddEditPayerInput payerInput) {
		return mapper.toAddEditDomainInput(payerInput);
	}

	public DomainDTO toDomainDTO(AddEditPayerInput payerInput) {
		return mapper.toDomainDTO(payerInput);
	}

	public Payer toPayer(AddEditPayerInput payerInput) {
		return mapper.toPayer(payerInput);
	}

	public PayerDTO toAddEditPayerInput(DomainDTO d, Payer p) {
		PayerDTO payerInput = mapper.toAddEditPayerInput(d);
		payerInput.id = p.getId();
		payerInput.isTransferAgent = p.isIsTransferAgent();
		payerInput.transferAgentName = p.getTransferAgentName();
		payerInput.transferAgentAddress = mapper.toAddressDTO(p.getTransferAgentAddress());
		return payerInput;
	}

	public Payee convertPayeeDTOToEntity(AddEditPayeeInput dto) {
		return mapper.toPayee(dto);
	}

	public Payee convertPayeeDTOToEntity(AddEditPayeeLightInput dto) {
		return mapper.toPayee(dto);
	}

	public PayeeDTO convertToPayeeDTO(Payee payee) {
		return mapper.toPayeeDTO(payee);
	}

	public FilingDTO convertToDTO(Filing f) throws ApheException {
		FilingDTO dto = setCommonFilingInputData(f);
		if (f.getFilingType() == FilingReturnType.Type_1099_MISC) {
			if (f.getFilingYear() == FilingYear.Y2019) {
				dto.filingData = getFilingDataDTO(f, new FilingData1099MISC());
			} else if (f.getFilingYear() == FilingYear.Y2020) {
				dto.filingData = getFilingDataDTO(f, new FilingData1099MISC2020());
			} else if (f.getFilingYear() == FilingYear.Y2021
					|| f.getFilingYear() == FilingYear.Y2022
					|| f.getFilingYear() == FilingYear.Y2023
					|| f.getFilingYear() == FilingYear.Y2024) {
				dto.filingData = getFilingDataDTO(f, new FilingData1099MISC2021());
			} else {
				throw new ApheException("Form support not implemented yet :" + f.getFilingYear().getYear() + " " + f.getFilingType());
			}
		} else if (f.getFilingType() == FilingReturnType.Type_1099_NEC) {
			if(f.getFilingYear() == FilingYear.Y2020) {
				dto.filingData = getFilingDataDTO(f, new FilingData1099NEC());
			}else if(f.getFilingYear() == FilingYear.Y2021
					|| f.getFilingYear() == FilingYear.Y2022
					|| f.getFilingYear() == FilingYear.Y2023
					|| f.getFilingYear() == FilingYear.Y2024) {
				dto.filingData = getFilingDataDTO(f, new FilingData1099NEC2021());
			} else {
				throw new ApheException("Form support not implemented yet :" + f.getFilingYear().getYear() + " " + f.getFilingType());
			}
		} else if (f.getFilingType() == FilingReturnType.Type_1099_INT) {
			dto.filingData = getFilingDataDTO(f, new FilingData1099INT());
		} else if (f.getFilingType() == FilingReturnType.Type_1099_OID) {
			dto.filingData = getFilingDataDTO(f, new FilingData1099OID());
		} else {
			throw new ApheException("Form support not implemented yet :" + f.getFilingYear().getYear() + " " + f.getFilingType());
		}
		return dto;
	}

	private FilingDTO setCommonFilingInputData(Filing f) {
		if (f == null) {
			return null;
		}

		FilingDTO filingDTO = new FilingDTO();

		if (f.getId() != null) {
			filingDTO.id = f.getId();
		}

		filingDTO.payerId = f.getPayer().getId();
		filingDTO.payeeId = f.getPayee().getId();

		filingDTO.isTestFiling = f.isTestFiling();

		filingDTO.filingYear = f.getFilingYear().getYear();
		// TODO: change this?
		filingDTO.formType = null;
		filingDTO.filingType = f.getFilingType();

		filingDTO.correctionType = f.getCorrectionType();
		filingDTO.originalFilingId = f.getOriginalFilingId() != null ? f.getOriginalFilingId() : 0;

		FedSub fedSub = f.getFedSub();
		StateSub stateSub = f.getStateSub();
		PrintSub printSub = f.getPrintSub();
		EmailSub emailSub = f.getEmailSub();

		filingDTO.fedSub = mapper.toDTO(fedSub);
		filingDTO.stateSub = mapper.toDTO(stateSub);

		com.aphe.contractor.model.enums.StateCode state1Code = this.getJSONLocalStateCode(f.getFilingData(), "state1Code", null);
		BigDecimal state1TaxWithheld = new BigDecimal(this.getJSONBigDecimalValue(f.getFilingData(), "state1TaxWithheld","0.00"));
		BigDecimal state1Income = new BigDecimal(this.getJSONBigDecimalValue(f.getFilingData(), "state1Income","0.00"));
		if(state1Code != null ) {
			StateFilingData stateFilingData = StateFilingData.getStateFilingData(state1Code.name());
			if(stateFilingData != null) {
				filingDTO.stateSub.isFilingSupported = stateFilingData.isStateFilingRequired(f.getFilingType());
				filingDTO.stateSub.isFilingRequired = stateFilingData.isStateFilingRequired(
						f.getFilingType(), state1Income, state1TaxWithheld, state1Code, state1Code, state1Code);
				filingDTO.stateSub.supportedMethods = stateFilingData.getAllowedStateFilingMethods(f.getFilingType(), state1Income, state1TaxWithheld, state1Code, state1Code, state1Code);
			}
			filingDTO.stateSub.stateCode = state1Code.name();
		}


		filingDTO.printSub = mapper.toDTO(printSub);
		filingDTO.emailSub = mapper.toDTO(emailSub);


		filingDTO.sendHardCopy = printSub.getSelected();
		filingDTO.sendSoftCopy = emailSub.getSelected();

		filingDTO.status = fedSub.getStatus();
		filingDTO.statusChangeDate = fedSub.getStatusChangeDate();
		filingDTO.statusChangeDesc = fedSub.getStatusChangeDesc();

		filingDTO.stateFilingStatus = stateSub.getStatus();
		filingDTO.stateFilingStatusChangeDate = stateSub.getStatusChangeDate();
		filingDTO.stateFilingStatusChangeDesc = stateSub.getStatusChangeDesc();

		filingDTO.printStatus = printSub.getPrintStatus();
		filingDTO.printStatusChangeDate = printSub.getPrintStatusChangeDate();
		filingDTO.printStatusChangeDesc = printSub.getPrintStatusChangeDesc();
		filingDTO.printCopyExpectedDeliveryDate = printSub.getPrintCopyExpectedDeliveryDate();
		filingDTO.printCopyTrackingId = printSub.getPrintCopyTrackingId();

		filingDTO.emailStatus = emailSub.getEmailStatus();
		filingDTO.emailStatusChangeDate = emailSub.getEmailStatusChangeDate();
		filingDTO.emailStatusChangeDesc = emailSub.getEmailStatusChangeDesc();
		filingDTO.emailTrackingId = emailSub.getEmailTrackingId();

		return filingDTO;
	}

	protected List<FilingStatusHistoryDTO> filingStatusEntityListToFilingStatusHistoryDTOList(List<FilingStatusEntity> list) {
		if (list == null) {
			return null;
		}

		List<FilingStatusHistoryDTO> list1 = new ArrayList<FilingStatusHistoryDTO>(list.size());
		for (FilingStatusEntity filingStatusEntity : list) {
			list1.add(filingStatusEntityToFilingStatusHistoryDTO(filingStatusEntity));
		}

		return list1;
	}

	protected List<PrintStatusHistoryDTO> printStatusEntityListToPrintStatusHistoryDTOList(List<PrintStatusEntity> list) {
		if (list == null) {
			return null;
		}

		List<PrintStatusHistoryDTO> list1 = new ArrayList<PrintStatusHistoryDTO>(list.size());
		for (PrintStatusEntity printStatusEntity : list) {
			list1.add(printStatusEntityToPrintStatusHistoryDTO(printStatusEntity));
		}

		return list1;
	}

	protected List<EmailStatusHistoryDTO> emailStatusEntityListToEmailStatusHistoryDTOList(List<EmailStatusEntity> list) {
		if (list == null) {
			return null;
		}

		List<EmailStatusHistoryDTO> list1 = new ArrayList<EmailStatusHistoryDTO>(list.size());
		for (EmailStatusEntity emailStatusEntity : list) {
			list1.add(emailStatusEntityToEmailStatusHistoryDTO(emailStatusEntity));
		}

		return list1;
	}

	protected FilingStatusHistoryDTO filingStatusEntityToFilingStatusHistoryDTO(FilingStatusEntity filingStatusEntity) {
		if (filingStatusEntity == null) {
			return null;
		}

		FilingStatusHistoryDTO filingStatusHistoryDTO = new FilingStatusHistoryDTO();

		if (filingStatusEntity.getId() != null) {
			filingStatusHistoryDTO.id = filingStatusEntity.getId();
		}
		filingStatusHistoryDTO.status = filingStatusEntity.getStatus();
		filingStatusHistoryDTO.statusChangeDate = filingStatusEntity.getStatusChangeDate();
		filingStatusHistoryDTO.statusChangeDesc = filingStatusEntity.getStatusChangeDesc();

		return filingStatusHistoryDTO;
	}

	protected PrintStatusHistoryDTO printStatusEntityToPrintStatusHistoryDTO(PrintStatusEntity printStatusEntity) {
		if (printStatusEntity == null) {
			return null;
		}

		PrintStatusHistoryDTO emailStatusHistoryDTO = new PrintStatusHistoryDTO();

		if (printStatusEntity.getId() != null) {
			emailStatusHistoryDTO.id = printStatusEntity.getId();
		}
		emailStatusHistoryDTO.status = printStatusEntity.getStatus();
		emailStatusHistoryDTO.statusChangeDate = printStatusEntity.getStatusChangeDate();
		emailStatusHistoryDTO.statusChangeDesc = printStatusEntity.getStatusChangeDesc();

		return emailStatusHistoryDTO;
	}

	protected EmailStatusHistoryDTO emailStatusEntityToEmailStatusHistoryDTO(EmailStatusEntity emailStatusEntity) {
		if (emailStatusEntity == null) {
			return null;
		}

		EmailStatusHistoryDTO emailStatusHistoryDTO = new EmailStatusHistoryDTO();

		if (emailStatusEntity.getId() != null) {
			emailStatusHistoryDTO.id = emailStatusEntity.getId();
		}
		emailStatusHistoryDTO.status = emailStatusEntity.getStatus();
		emailStatusHistoryDTO.statusChangeDate = emailStatusEntity.getStatusChangeDate();
		emailStatusHistoryDTO.statusChangeDesc = emailStatusEntity.getStatusChangeDesc();

		return emailStatusHistoryDTO;
	}

	private FilingData getFilingDataDTO(Filing f, FilingData dto) throws ApheException {
		JSONObject filingData = f.getFilingData();

		Map<EFSDTOProp, String> dtoProps = FilingDataMapper.getJsonToDtoMappings(f.getFilingType(), f.getFilingYear());
		if (dtoProps == null) {
			throw new ApheException("Unsupported form");
		}
		for (EFSDTOProp targetProp : dtoProps.keySet()) {
			String sourceProp = dtoProps.get(targetProp);

			if (targetProp.propType == EFSDTOPropType.Boolean) {
				setFieldValue(dto, targetProp, getJSONBooleanValue(filingData, sourceProp, targetProp.defaultValue));
			} else if (targetProp.propType == EFSDTOPropType.BigDecimal) {
				setFieldValue(dto, targetProp, getJSONBigDecimalValue(filingData, sourceProp, targetProp.defaultValue));
			} else if (targetProp.propType == EFSDTOPropType.StateCode) {
				setFieldValue(dto, targetProp, getJSONValue(filingData, sourceProp, targetProp.defaultValue));
			} else if (targetProp.propType == EFSDTOPropType.String) {
				setFieldValue(dto, targetProp, getJSONValue(filingData, sourceProp, targetProp.defaultValue));
			} else {
				throw new ApheException("Unsupported data type");
			}
		}
		return dto;

	}

	private void setFieldValue(FilingData output, EFSDTOProp field, Object value) throws ApheException {
		try {
			Field f = output.getClass().getDeclaredField(field.propName);
			if (f != null) {
				f.set(output, value);
			} else {
				throw new ApheException("Field " + field.propName + " not found in then input object");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new ApheException(e);
		}
	}

	/**
	 *
	 * NEW METHOD TO SUPPORT MULTIPLE FORM TYPES and MULTIPLE FILING YEARS.
	 *
	 * Creates a Filing Entity from one of the form specific input objects. This method is responsible for converting input into some common attributes and form specific
	 * attributes..
	 *
	 *
	 *
	 * @param filingInput
	 * @return
	 */
	public Filing convertToFilingEntity(AddEditFilingInput filingInput) throws ApheException {
		Filing f = new Filing();

		f.setId(filingInput.id);

		f.setTestFiling(filingInput.isTestFiling);

		f.setFilingYear(FilingYear.getFilingYearForYear(filingInput.filingYear));
		f.setFilingType(filingInput.getFilingReturnType());
		f.setCorrectionType(filingInput.correctionType);
		f.setOriginalFilingId(filingInput.originalFilingId);

		JSONObject filingData1 = new JSONObject();
		buildJSONData(filingData1, filingInput);

		f.setFilingData(filingData1);
		return f;

	}

	private Object getFieldValue(AddEditFilingInput input, String fieldName) throws ApheException {
		try {
			Field f = input.getClass().getDeclaredField(fieldName);
			if (f != null) {
				Object value = f.get(input);
				return value;
			} else {
				throw new ApheException("Field " + fieldName + " not found in then input object");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new ApheException(e);
		}
	}

	/**
	 * States that require 1099 filing but do not participate in CFSF
	 *
	 * https://payable.com/taxes/state-1099-requirements * District of Columbia Oregon Pennslyvania - Manual or separate EFile, if you have PA account.
	 *
	 * Montana - if backup withholding Nebraska - if backup withholding Norht Dakota, if withholding. Ohio if backup withholding South Carolina - if income tax is withheld. Utah -
	 * if income tax is withheld. Vermont - if withholding.. Virginia - if withholding. West Virginia - if withholding.
	 *
	 *
	 * New York for some special type of payments. Rhode Island in some cases.
	 *
	 */

	private void buildJSONData(JSONObject filingData, AddEditFilingInput filingInput) throws ApheException {
		Map<String, String> formFields = FilingDataMapper.getDtoToJsonMappings(filingInput.getFilingReturnType(), FilingYear.getFilingYearForYear(filingInput.filingYear));
		if (formFields == null) {
			throw new ApheException("Unsupported form");
		}
		for (String targetProp : formFields.keySet()) {
			String sourceProp = formFields.get(targetProp);
			Object value = getFieldValue(filingInput, sourceProp);
			setJSONProperty(filingData, targetProp, value);
		}
	}

	// TODO : How do you set a property to not null.
	private void setJSONProperty(JSONObject filingData, String keyName, Object keyValue) {
		filingData.put(keyName, keyValue);
	}

	/**
	 * For original filings, originalFiling should be null and second filing should be null.
	 * 	-- Return 1 for FD and 1 for state.
	 * For corrections, this is only called with first correction, never with second correction. orignal filing and second correction are passed as additional parameters.
	 *  -- Returns 1 for FD, 1 state for first correction.
	 *  -- If second correction is not null, return 1 FD for second correcton and 1 state correction.
	 *  -- If state is PA, and second correcion is null, return 1 for FD correction and 2 PA state corrections.
	 *
	 */
	public List<FilingDataDTO> convertToEFSFilingDTOs(Filing f, Long domainId, boolean setTINs, Filing secondCorrection, Filing originalFiling) throws Exception {
		List<FilingDataDTO> efsFilings = new ArrayList<>();

		//Take care of Fed and CFSF submission....
		efsFilings.add(convertToFDDTO(f, domainId, setTINs));
		if(secondCorrection != null) {
			efsFilings.add(convertToFDDTO(secondCorrection, domainId, setTINs));
		}

		//Now take care of EFile and SelfEfile state submissions...
		com.aphe.contractor.model.enums.StateCode stateCode = getStateCode(f);
		if(stateCode != null) {
			StateFilingMethod filingMethod = f.getStateSub().getFilingMethod();
			if(StateFilingMethod.EFile == filingMethod || StateFilingMethod.SelfEFile == filingMethod) {

				//throw an exception if amounts not zero and second correction is present.
				if(secondCorrection != null) {
					boolean allAmountsAreZero = allAmountsAreZero(f);
					if(!allAmountsAreZero) {
						//throw new RuntimeException("First correction has amounts and second correction is not null");
					}
				}

				FilingDataDTO stateDto;
				//If second correction is PA -- and first is not -- how to handle it -- handle it as add (second) which is same as other state correction
				if(f.getCorrectionType() == com.aphe.contractor.model.enums.CorrectionType.First && stateCode == com.aphe.contractor.model.enums.StateCode.PA) {

					//Use original filing to delete.. this is unlike the CFSF stuff
					stateDto = convertToStateDTO(originalFiling, domainId, setTINs, stateCode);
					stateDto.correctionType = CorrectionType.First; //Delete the original.
					//Override the clientREf from the first correction.
					stateDto.clientRefId = f.getStateSub().getSubmissionRefId();

					//State specific filing data on the efs filing.
					stateDto.stateFiling = true;
					stateDto.reportingStateCode = StateCode.valueOf(stateCode.name());
					stateDto.stateFilingMethod = com.aphe.efs.model.enums.StateFilingMethod.getStateFilingMethod(filingMethod.name());

					efsFilings.add(stateDto);

					//if first amounts are not zero and state is PA (which is always true, add first correction as second)
					boolean allAmountsAreZero = allAmountsAreZero(f);
					if(!allAmountsAreZero) {
						//add first correction as second correction for PA purposes.
						FilingDataDTO paSecondCorrection = convertToStateDTO(f, domainId, setTINs, stateCode);
						paSecondCorrection.correctionType = CorrectionType.Second; //Add first
						paSecondCorrection.clientRefId = f.getStateSub().getSubmissionRefId() + "-2";
						efsFilings.add(paSecondCorrection);
					}

					//if second correction is not null
				} else {
					stateDto = convertToStateDTO(f, domainId, setTINs, stateCode);
					efsFilings.add(stateDto);
					//If there is second correction, take care of that too.
				}
			}
		}

		if(secondCorrection != null) {
			FilingDataDTO secondStateCorrection = generateSecondStateCorrection(secondCorrection, domainId, setTINs);
			if(secondStateCorrection != null) {
				efsFilings.add(secondStateCorrection);
			}
		}



		return efsFilings;
	}

	private FilingDataDTO generateSecondStateCorrection(Filing secondCorrection, Long domainId, boolean setTINs) throws Exception {
		com.aphe.contractor.model.enums.StateCode stateCode = getStateCode(secondCorrection);
		if(stateCode != null) {
			com.aphe.contractor.model.enums.StateCode secondCorrectionStateCode = getStateCode(secondCorrection);
			if (secondCorrectionStateCode != null) {
				StateFilingMethod secondStateFilingMethod = secondCorrection.getStateSub().getFilingMethod();
				if (StateFilingMethod.EFile == secondStateFilingMethod || StateFilingMethod.SelfEFile == secondStateFilingMethod) {
					return convertToStateDTO(secondCorrection, domainId, setTINs, secondCorrectionStateCode);
				}
			}
		}
		return null;
	}


	private com.aphe.contractor.model.enums.StateCode getStateCode(Filing f) {
		com.aphe.contractor.model.enums.StateCode stateCode = null;
		StateSub stateSub = f.getStateSub();
		if (stateSub != null) {
			Boolean selected = stateSub.getSelected();
			if (selected != null && selected.booleanValue() == true) {
				stateCode = getJSONLocalStateCode(f.getFilingData(), "state1Code", null);

			}
		}
		return stateCode;
	}

	@NotNull
	private FilingDataDTO convertToStateDTO(Filing f, Long domainId, boolean setTINs, com.aphe.contractor.model.enums.StateCode stateCode) throws Exception {
		FilingDataDTO stateDto = convertToEFSFilingDTO(f, setTINs);

		stateDto.clientRefId = f.getStateSub().getSubmissionRefId();
		stateDto.clientDomainId = domainId.toString();
		stateDto.stateFiling = true;

		//State specific filing data on the efs filing.
		stateDto.reportingStateCode = StateCode.valueOf(stateCode.name());
		StateFilingMethod filingMethod = f.getStateSub().getFilingMethod();
		stateDto.stateFilingMethod = com.aphe.efs.model.enums.StateFilingMethod.getStateFilingMethod(filingMethod.name());
		return stateDto;
	}

	@NotNull
	private FilingDataDTO convertToFDDTO(Filing f, Long domainId, boolean setTINs) throws Exception {
		FilingDataDTO dto = convertToEFSFilingDTO(f, setTINs);

		dto.clientRefId = f.getFedSub().getSubmissionRefId();
		dto.clientDomainId = domainId.toString();
		dto.stateFiling = false;

		//If there is a corresponding state sub with CFSF... set this flag on CFSF side...and keep track of this state sub and update its status accordingly.
		// Since this is CFSF, the resubmission should do the same...
		StateSub stateSub = f.getStateSub();
		if(stateSub != null) {
			Boolean selected = stateSub.getSelected();
			if(selected != null && selected.booleanValue() == true && stateSub.getFilingMethod() != null && stateSub.getFilingMethod() == StateFilingMethod.CFSF) {
				dto.reportUsingCFSF = true;
			}
		}

		return dto;
	}

	public FilingDataDTO convertToEFSFilingDTO(Filing f, boolean setTINs) throws Exception {
		Domain d = domainRepo.findById(f.getPayer().getDomainId()).orElse(null);
		Payee payee = f.getPayee();
		Payer payer = f.getPayer();

		FilingDataDTO dto = null;
		switch (f.getFilingType()) {
			case Type_1099_MISC: {
				switch (f.getFilingYear()){
					case Y2019:{
						dto = new FilingDataDTO1099MISC();
						dto.filingReturnType = com.aphe.efs.model.enums.FilingReturnType.Type_1099_MISC;
						setCommonEFSFilingData(f, d, payee, payer, dto, setTINs);
						set1099MISCFilingData(f, (FilingDataDTO1099MISC) dto);
						break;
					}
					case Y2020:{
						dto = new FilingDataDTO1099MISC2020();
						dto.filingReturnType = com.aphe.efs.model.enums.FilingReturnType.Type_1099_MISC;
						setCommonEFSFilingData(f, d, payee, payer, dto, setTINs);
						set1099MISCFilingData(f, (FilingDataDTO1099MISC2020) dto);
						break;
					}
					case Y2021:
					case Y2022:
					case Y2023:
					case Y2024:
					{
						dto = new FilingDataDTO1099MISC2021();
						dto.filingReturnType = com.aphe.efs.model.enums.FilingReturnType.Type_1099_MISC;
						setCommonEFSFilingData(f, d, payee, payer, dto, setTINs);
						set1099MISCFilingData(f, (FilingDataDTO1099MISC2021) dto);
						break;
					}
					default:{
						throw new Exception("Unknown filing year");
					}
				}
				break;
			}
		case Type_1099_NEC: {
			switch (f.getFilingYear()){
				case Y2020:{
					dto = new FilingDataDTO1099NEC();
					dto.filingReturnType = com.aphe.efs.model.enums.FilingReturnType.Type_1099_NEC;
					setCommonEFSFilingData(f, d, payee, payer, dto, setTINs);
					set1099MISCFilingData(f, (FilingDataDTO1099NEC) dto);
					break;
				}
				case Y2021:
				case Y2022:
				case Y2023:
				case Y2024:
				{
					dto = new FilingDataDTO1099NEC2021();
					dto.filingReturnType = com.aphe.efs.model.enums.FilingReturnType.Type_1099_NEC;
					setCommonEFSFilingData(f, d, payee, payer, dto, setTINs);
					set1099MISCFilingData(f, (FilingDataDTO1099NEC2021) dto);
					break;
				} default:{
					throw new Exception("Unknown filing year");
				}
			}
			break;

		}
		case Type_1099_INT: {
			dto = new FilingDataDTO1099INT();
			dto.filingReturnType = com.aphe.efs.model.enums.FilingReturnType.Type_1099_INT;
			setCommonEFSFilingData(f, d, payee, payer, dto, setTINs);
			set1099MISCFilingData(f, (FilingDataDTO1099INT) dto);
			break;
		}
		case Type_1099_OID: {
			dto = new FilingDataDTO1099OID();
			dto.filingReturnType = com.aphe.efs.model.enums.FilingReturnType.Type_1099_OID;
			setCommonEFSFilingData(f, d, payee, payer, dto, setTINs);
			set1099MISCFilingData(f, (FilingDataDTO1099OID) dto);
			break;
		}
		default:
			throw new Exception("Unknown filing type");
		}
		return dto;
	}

	private void set1099MISCFilingData(Filing f, FilingDataDTO dto) throws ApheException {
		JSONObject filingData = f.getFilingData();

		Map<EFSDTOProp, String> dtoProps = FilingDataMapper.getJsonToDtoMappings(f.getFilingType(), f.getFilingYear());
		if (dtoProps == null) {
			throw new ApheException("Unsupported form");
		}
		for (EFSDTOProp targetProp : dtoProps.keySet()) {
			String sourceProp = dtoProps.get(targetProp);

			if (targetProp.propType == EFSDTOPropType.Boolean) {
				setFieldValue(dto, targetProp, getJSONBooleanValue(filingData, sourceProp, targetProp.defaultValue));
			} else if (targetProp.propType == EFSDTOPropType.BigDecimal) {
				setFieldValue(dto, targetProp, getJSONBigDecimalValue(filingData, sourceProp, targetProp.defaultValue));
			} else if (targetProp.propType == EFSDTOPropType.StateCode) {
				setFieldValue(dto, targetProp, getJSONStateCode(filingData, sourceProp, targetProp.defaultValue));
			} else if (targetProp.propType == EFSDTOPropType.String) {
					setFieldValue(dto, targetProp, StringUtil.cleanTOAscii(getJSONValue(filingData, sourceProp, targetProp.defaultValue)));
			} else {
				throw new ApheException("Unsupported data type");
			}

		}

	}

	private void setFieldValue(FilingDataDTO output, EFSDTOProp field, Object value) throws ApheException {
		try {
			Field f = output.getClass().getField(field.propName);
			if (f != null) {
				f.set(output, value);
			} else {
				throw new ApheException("Field " + field.propName + " not found in then input object");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new ApheException(e);
		}
	}

	private void setCommonEFSFilingData(Filing f, Domain d, Payee payee, Payer payer, FilingDataDTO dto, boolean setTINs) {
		dto.appId = "1099SFApp";
		dto.testFiling = f.isTestFiling();
		dto.filingYear = com.aphe.efs.model.enums.FilingYear.getFilingYearForYear(f.getFilingYear().getYear());
		dto.filingDate = f.getFilingDate();

		// TODO: We need to add support for this UI and TNNPlatform
		dto.lastFilingIndicator = false;

		dto.payerEntityType = d.getDomainType().equalsIgnoreCase("C") ? com.aphe.efs.model.enums.EntityType.Individual.name() : com.aphe.efs.model.enums.EntityType.Business.name();
		String domainType = d.getDomainType();
		if (domainType.equalsIgnoreCase("C")) {
			dto.payerFirstName = clean(d.getFirstName());
			dto.payerLastName = clean(d.getLastName());
			dto.payerMiddleName = clean(d.getMiddleName());
		}
		dto.payerName1 = getDomainDisplayName(d);

		dto.payerTinType = TinType.valueOf(d.getTinType().name());
		if(setTINs) {
			dto.payerTin = d.getTinPlain();
		}

		dto.transferAgent = f.getPayer().isIsTransferAgent();

		Address payerAddress = d.getAddress();
		if (payerAddress != null) {
			String payerState = payerAddress.getState();
			String line1 = StringUtil.cleanTOAscii(payerAddress.getLine1());
			String line2 = StringUtil.cleanTOAscii(payerAddress.getLine2());
			String city = StringUtil.cleanTOAscii(payerAddress.getCity());
			CountryCode payerCountry = payerAddress.getCountry();
			FilingAddressDTO payerAddressDTO = new FilingAddressDTO(line1, line2, city, payerState,
					payerCountry != null ? payerCountry.name() : null, payerAddress.getPostalCode(),
					// TODO: Computer this... defer this to EFS.
					false);
			dto.payerAddress = payerAddressDTO;
		}

		dto.payerPhoneAndExt = d.getPhoneNumber() != null ? d.getPhoneNumber().replaceAll("\\D+", "") : null;

		dto.payerEmailAddress = d.getEmailAddress() != null ? d.getEmailAddress() : null;

		dto.correctionType = CorrectionType.valueOf(f.getCorrectionType().toString());

		if(payee.getEntityType() == EntityType.Individual) {
			dto.payeeEntityType = com.aphe.efs.model.enums.EntityType.Individual.name();
		} else if(payee.getEntityType() == EntityType.Business) {
			dto.payeeEntityType = com.aphe.efs.model.enums.EntityType.Business.name();
		} else {
			dto.payeeEntityType = null;
		}

		dto.payeeTinType = TinType.valueOf(payee.getTinType().name());

		if(setTINs) {
			dto.payeeTin = payee.getTinPlain();
		}


//		dto.payeeName1 = getPayeeDisplayName(payee);
		// If filing year is 2021, use first name and last name format.... just do the old way...
		// Issue will be with 2020 filings submitted between Jan 19th and June 15th. They would have been submitted with LN+FN, and their corresponding corrections will be done with FN+LN format.
		// Issue with all the 2021 filings submitted before Jan 19th. They would have been submitted with FN + LN, but their corresponding corrections will be filed with LN+FN format.
		String[] payeeNames;
		if(f.getFilingYear() == FilingYear.Y2021 ) {
			//TODO: Could we also have added a check to say if the submit date was before Jan 19th, do the other way...
			//Do the special way. LN  + FN format
			payeeNames = getPayeeNames(payee, true);
		} else {
			//do the standard way. FN + LN
			payeeNames = getPayeeNames(payee, false);
		}
		dto.payeeName1 = payeeNames[0];
		dto.payeeName2 = payeeNames[1];

		if(payee.getEntityType() == EntityType.Individual) {
			String firstName = payee.getFirstName();
			firstName = firstName != null ? firstName.replaceAll("[^a-zA-Z0-9-& ]", "") : null;
			dto.payeeFirstName = firstName;
			String lastName = payee.getLastName();
			lastName = lastName != null ? lastName.replaceAll("[^a-zA-Z0-9-& ]", "") : null;
			dto.payeeLastName = lastName;
			String middleName = payee.getMiddleName();
			middleName = StringUtil.isNotEmpty(middleName) ? middleName.replaceAll("[^a-zA-Z0-9-& ]", "") : null;
			dto.payeeMiddleName = middleName;
		}

		CAddress payeeAddress = f.getPayee().getAddress();
		if (payeeAddress != null) {
			String payeeState = payeeAddress.getState();
			String line1 = StringUtil.cleanTOAscii(payeeAddress.getLine1());
			String line2 = StringUtil.cleanTOAscii(payeeAddress.getLine2());
			String city = StringUtil.cleanTOAscii(payeeAddress.getCity());
			CountryCode payeeCountry = payeeAddress.getCountry();
			FilingAddressDTO payeeAddressDTO = new FilingAddressDTO(line1, line2, city, payeeState,
					payeeCountry != null ? payeeCountry.name() : null, payeeAddress.getPostalCode(),
					// TODO: Computer this... defer this to EFS.
					false);
			dto.payeeAddress = payeeAddressDTO;
		}

	}

	public String clean(String s) {
		if( s != null) {
			return s.replaceAll("[^a-zA-Z0-9-& ]", "");
		}
		return s;
	}

	public String getJSONValue(JSONObject jsonObject, String key, String defaultValue) {
		if (jsonObject.has(key)) {
			Object value = jsonObject.get(key);
			if (value != null) {
				return value.toString();
			}
		}
		return defaultValue;
	}

	public StateCode getJSONStateCode(JSONObject jsonObject, String key, String defaultValue) {
		if (jsonObject.has(key)) {
			Object value = jsonObject.get(key);
			if (value != null) {
				return StateCode.valueOf(value.toString());
			}
		}
		return null;
	}

	public com.aphe.contractor.model.enums.StateCode getJSONLocalStateCode(JSONObject jsonObject, String key, String defaultValue) {
		if (jsonObject.has(key)) {
			Object value = jsonObject.get(key);
			if (value != null) {
				return com.aphe.contractor.model.enums.StateCode.valueOf(value.toString());
			}
		}
		return null;
	}


	public Boolean getJSONBooleanValue(JSONObject jsonObject, String key, String defaultValue) {
		if (jsonObject.has(key)) {
			return jsonObject.getBoolean(key);
		}
		return Boolean.parseBoolean(defaultValue);
	}

	public String getJSONBigDecimalValue(JSONObject jsonObject, String key, String defaultValue) {
		if (jsonObject.has(key)) {
			Object value = jsonObject.get(key);
			if (value instanceof String) {
				String stringValue = (String) value;
				if (stringValue.length() > 0) {
					try {
						BigDecimal bigDecimal = new BigDecimal(stringValue);
					} catch (Exception e) {
						logger.error("Error converting string to big decimal for {} with value of {}", key, stringValue);
						throw e;
					}
					return stringValue;
				}
			}
		}
		return defaultValue;
	}

	public BigDecimal getJSONBigDecimal(JSONObject jsonObject, String key, BigDecimal defaultValue) {
		String stringValue = getJSONBigDecimalValue(jsonObject, key, defaultValue.toString());
		return new BigDecimal(stringValue);
	}

	public PrintEmailRequestDTO convertFilingToPrintRequestDTO(Filing f) {
		FedSub fedSub = f.getFedSub();
		PrintSub printSub = f.getPrintSub();
		EmailSub emailSub = f.getEmailSub();

		PrintEmailRequestDTO dto = new PrintEmailRequestDTO();
		dto.filingId = f.getId();
		dto.formType = f.getFilingType().toString();
		dto.filingStatus = fedSub.getStatus().toString();
		dto.emailStatus = emailSub.getEmailStatus() != null ? emailSub.getEmailStatus().toString() : "NULL";
		dto.printStatus = printSub.getPrintStatus() != null ? printSub.getPrintStatus().toString() : "NULL";
		dto.printCopyTrackingId = printSub.getPrintCopyTrackingId();
		dto.domainId = f.getPayer().getDomainId();

		try {
			//TODO: Should we cache this name??
			dto.payerName = getPayerDisplayName(f.getPayer());

			DomainDTO d = domainMgr.getDomain(dto.domainId);
			PayerDTO payerDTO = mapper.toAddEditPayerInput(d);
			dto.payerAddress = payerDTO.address;
		} catch (Exception e) {
			logger.error("Error getting payee name", e);
		}

		dto.payeeName = getPayeeDisplayName(f.getPayee());
		dto.payeeAddress = mapper.toAddressDTO(f.getPayee().getAddress());
		return dto;
	}

	public String getPayeeDisplayName(PayeeDTO p) {

		EntityType entityType = p.entityType;
		String businessName = p.businessName;
		String firstName = p.firstName;
		String lastName = p.lastName;

		return buildPayeeDisplayName(entityType, businessName, firstName, lastName);
	}

	public String getPayeeDisplayName(Payee p) {
		EntityType entityType = p.getEntityType();
		String businessName = p.getBusinessName();
		String firstName = p.getFirstName();
		String lastName = p.getLastName();

		return buildPayeeDisplayName(entityType, businessName, firstName, lastName);
	}

	public String getPayeeNameForTINMatch(Payee p) {
		EntityType entityType = p.getEntityType();
		String businessName = p.getBusinessName();
		String firstName = p.getFirstName();
		String lastName = p.getLastName();
		String payeeName = getCleanedDisplayName(entityType, businessName, firstName, lastName, false);
		if(payeeName.length() > 40) {
			return payeeName.substring(0, 40);
		}
		return payeeName;
	}


	public String[] getPayeeNames(Payee p, boolean lastNameFirst) {
		EntityType entityType = p.getEntityType();
		String businessName = p.getBusinessName();
		String firstName = p.getFirstName();
		String lastName = p.getLastName();
		String displayName = getCleanedDisplayName(entityType, businessName, firstName, lastName, lastNameFirst);
		String dbaName = null;
		if (p.getDba() != null) {
			dbaName = p.getDba().replaceAll("[^a-zA-Z0-9-& ]", "").trim();
		}
		boolean hasDBA = StringUtil.isNotEmpty(dbaName);
		String[] returnValue = splitName(displayName, hasDBA);
		if(hasDBA) {
			returnValue[1] = dbaName.length() > 40 ? dbaName.substring(0, 40) : dbaName;
		}
		return returnValue;
	}

	@NotNull
	private String getCleanedDisplayName(EntityType entityType, String businessName, String firstName, String lastName, boolean lastNameFirst) {
		String displayName = "";
		if (entityType == EntityType.Business) {
			displayName = businessName;
		} else {
			if(lastNameFirst) {
				displayName = (lastName + " " + firstName).trim();
			} else {
				displayName = (firstName + " " + lastName).trim();
			}

		}
		displayName = displayName.replaceAll("[^a-zA-Z0-9-& ]", "");
		return displayName;
	}

	private String[] splitName(String s, boolean hasDBA) {
		if(s==null) {
			s="";
		}
		if(s.length() > 40) {
			if(hasDBA) {
				return new String[] {s.substring(0, 40), null};
			} else {
				int spaceLoc = s.lastIndexOf(" ", 40);
				if (spaceLoc > 20) {
					String firstString = s.substring(0, spaceLoc + 1);
					String secondString = s.substring(spaceLoc + 1);
					if (secondString.length() > 40) {
						secondString = secondString.substring(0, 40);
					}
					return new String[]{firstString, secondString};
				} else {
					String firstString = s.substring(0, 40);
					String secondString = s.substring(40, 80);
					return new String[]{firstString, secondString};
				}
			}
		} else {
			return new String[] {s, null};
		}

	}


	private String buildPayeeDisplayName(EntityType entityType, String businessName, String firstName, String lastName) {
		String displayName = "";
		if (entityType == EntityType.Business) {
			displayName = businessName;
		}else if (entityType == EntityType.Individual) {
			displayName = (firstName + " " + lastName).trim();
		} else {
			if(StringUtil.isNotEmpty(businessName)) {
				displayName = businessName;
			} else {
				if(StringUtil.isNotEmpty(firstName)) {
					displayName = firstName;
				}
				if(StringUtil.isNotEmpty(lastName)) {
					displayName = displayName + " " + lastName;
				}
				displayName = displayName.trim();
			}
		}
		return displayName;
	}

	private String getPayerDisplayName(Payer p) throws Exception {
		DomainDTO d = domainMgr.getDomain(p.getDomainId());
		return getDomainDisplayName(d);
	}

	public String getDomainDisplayName(Domain d) {
		String businessName = d.getName();
		String firstName = d.getFirstName();
		String lastName = d.getLastName();
		String domainType = d.getDomainType();

		return buildDomainDisplayName(businessName, firstName, lastName, domainType);
	}

	public String getDomainDisplayName(DomainDTO d) {
		String businessName = d.name;
		String firstName = d.firstName;
		String lastName = d.lastName;
		String domainType = d.domainType;

		return buildDomainDisplayName(businessName, firstName, lastName, domainType);
	}

	private String buildDomainDisplayName(String businessName, String firstName, String lastName, String domainType) {
		String displayName = "";
		if (domainType.equalsIgnoreCase("C")) {
			displayName = (firstName + " " + lastName).trim();
		} else {
			displayName = businessName;
		}
		displayName = displayName.replaceAll("[^a-zA-Z0-9-& ]", "");
		return displayName;
	}


	public String getCityStateZip(DomainDTO domainDTO) {
		AddressDTO address = domainDTO.address;
		if (address != null) {
			StringBuffer cityStateZipSB = new StringBuffer(address.city != null ? address.city : "");
			String state = address.state != null ? address.state.toString() : "";
			String zip = address.postalCode;
			zip = StringUtil.formatZipCode(zip);

			if (state != null && state.trim().length() != 0) {
				cityStateZipSB.append(" ").append(state.trim());
			}

			if (zip != null && zip.trim().length() != 0) {
				cityStateZipSB.append(" ").append(zip.trim());
			}
			return cityStateZipSB.toString();
		}
		return "";
	}

	public String getAddressLine(DomainDTO domainDTO) {
		AddressDTO address = domainDTO.address;
		if (address != null) {
			StringBuffer addressLineSB = new StringBuffer(address.line1 != null ? address.line1 : "");
			String streetName2 = address.line2;
			if (streetName2 != null && streetName2.trim().length() != 0) {
				addressLineSB.append(" ").append(streetName2.trim());
			}
			return addressLineSB.toString();
		}
		return "";
	}



	public TINMatchRequestDTO convertToEFSTINMatchRequestDTO(TINMatchRequest tinMatchRequest, Long domainId) throws Exception {
		if ( tinMatchRequest == null ) {
			return null;
		}
		TINMatchRequestDTO tinMatchRequestDTO = new TINMatchRequestDTO();
		tinMatchRequestDTO.appId = "1099SFApp";
		tinMatchRequestDTO.clientRefId = Long.toString(tinMatchRequest.getId());
		tinMatchRequestDTO.clientDomainId = domainId.toString();
		tinMatchRequestDTO.entityType = TINMatchTINType.EIN.getByTINType(tinMatchRequest.getTinType().name());
		tinMatchRequestDTO.tin = tinMatchRequest.getTinPlain();
		tinMatchRequestDTO.entityName = StringUtil.cleanTOAscii(tinMatchRequest.getName());
		return tinMatchRequestDTO;
	}

	public boolean allAmountsAreZero(Filing f) throws Exception {
		FilingDTO dto = filingManager.getFiling(f.getId());
		return dto.filingData.allAmountsZero();
	}


	public W9RequestDataDTO toDTO(W9Request w9) {
		return mapper.toW9RequestDTO(w9);
	}

	public W9Request toEntity(W9RequestDataDTO dto) {
		return mapper.toEntity(dto);
	}

	public com.aphe.contractor.dto.read.TINMatchRequestDTO toDTO(TINMatchRequest tinMatchRequest) {
		return mapper.toTINMatchRequestDTO(tinMatchRequest);
	}

	public TINMatchRequest toEntity(com.aphe.contractor.dto.read.TINMatchRequestDTO dto) {
		return mapper.toEntity(dto);
	}

}
