package com.aphe.contractor.services;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.FilingYear;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class FilingDataMapper {

	private static final Map<FilingReturnType, Map<FilingYear, Map<String, String>>> dtoToJson = new HashMap<FilingReturnType, Map<FilingYear, Map<String, String>>>();
	private static final Map<FilingReturnType, Map<FilingYear, Map<EFSDTOProp, String>>> jsonToDTO = new HashMap<FilingReturnType, Map<FilingYear, Map<EFSDTOProp, String>>>();

	static {
		Map<FilingYear, Map<String, String>> miscMappingsByYear = new HashMap<>();
		miscMappingsByYear.put(FilingYear.Y2019, build1099Misc2019Map());
		miscMappingsByYear.put(FilingYear.Y2020, build1099Misc2020Map());
		miscMappingsByYear.put(FilingYear.Y2021, build1099Misc2021Map());
		miscMappingsByYear.put(FilingYear.Y2022, build1099Misc2021Map());
		miscMappingsByYear.put(FilingYear.Y2023, build1099Misc2021Map());
		miscMappingsByYear.put(FilingYear.Y2024, build1099Misc2021Map());
		dtoToJson.put(FilingReturnType.Type_1099_MISC, miscMappingsByYear);

		Map<FilingYear, Map<String, String>> necMappingsByYear = new HashMap<>();
		necMappingsByYear.put(FilingYear.Y2020, build1099NECMap());
		necMappingsByYear.put(FilingYear.Y2021, build1099NEC2021Map());
		necMappingsByYear.put(FilingYear.Y2022, build1099NEC2021Map());
		necMappingsByYear.put(FilingYear.Y2023, build1099NEC2021Map());
		necMappingsByYear.put(FilingYear.Y2024, build1099NEC2021Map());
		dtoToJson.put(FilingReturnType.Type_1099_NEC, necMappingsByYear);


		Map<FilingYear, Map<String, String>> intMappingsByYear = new HashMap<>();
		intMappingsByYear.put(FilingYear.Y2020, build1099IntMap());
		intMappingsByYear.put(FilingYear.Y2021, build1099IntMap());
		intMappingsByYear.put(FilingYear.Y2022, build1099IntMap());
		intMappingsByYear.put(FilingYear.Y2023, build1099IntMap());
		intMappingsByYear.put(FilingYear.Y2024, build1099IntMap());
		dtoToJson.put(FilingReturnType.Type_1099_INT, intMappingsByYear);

		Map<FilingYear, Map<String, String>> oidMappingsByYear = new HashMap<>();
		oidMappingsByYear.put(FilingYear.Y2020, build1099OIDMap());
		oidMappingsByYear.put(FilingYear.Y2021, build1099OIDMap());
		oidMappingsByYear.put(FilingYear.Y2022, build1099OIDMap());
		oidMappingsByYear.put(FilingYear.Y2023, build1099OIDMap());
		oidMappingsByYear.put(FilingYear.Y2024, build1099OIDMap());
		dtoToJson.put(FilingReturnType.Type_1099_OID, oidMappingsByYear);


		Map<FilingYear, Map<EFSDTOProp, String>> miscEFSMappingsByYear = new HashMap<>();
		miscEFSMappingsByYear.put(FilingYear.Y2019, build1099Misc2019EFSMap());
		miscEFSMappingsByYear.put(FilingYear.Y2020, build1099Misc2020EFSMap());
		miscEFSMappingsByYear.put(FilingYear.Y2021, build1099Misc2021EFSMap());
		miscEFSMappingsByYear.put(FilingYear.Y2022, build1099Misc2021EFSMap());
		miscEFSMappingsByYear.put(FilingYear.Y2023, build1099Misc2021EFSMap());
		miscEFSMappingsByYear.put(FilingYear.Y2024, build1099Misc2021EFSMap());
		jsonToDTO.put(FilingReturnType.Type_1099_MISC, miscEFSMappingsByYear);

		Map<FilingYear, Map<EFSDTOProp, String>> necEFSMappingsByYear = new HashMap<>();
		necEFSMappingsByYear.put(FilingYear.Y2020, build1099NECEFSMap());
		necEFSMappingsByYear.put(FilingYear.Y2021, build1099NEC2021EFSMap());
		necEFSMappingsByYear.put(FilingYear.Y2022, build1099NEC2021EFSMap());
		necEFSMappingsByYear.put(FilingYear.Y2023, build1099NEC2021EFSMap());
		necEFSMappingsByYear.put(FilingYear.Y2024, build1099NEC2021EFSMap());
		jsonToDTO.put(FilingReturnType.Type_1099_NEC, necEFSMappingsByYear);

		Map<FilingYear, Map<EFSDTOProp, String>> intEFSMappingsByYear = new HashMap<>();
		intEFSMappingsByYear.put(FilingYear.Y2020, build1099IntEFSMap());
		intEFSMappingsByYear.put(FilingYear.Y2021, build1099IntEFSMap());
		intEFSMappingsByYear.put(FilingYear.Y2022, build1099IntEFSMap());
		intEFSMappingsByYear.put(FilingYear.Y2023, build1099IntEFSMap());
		intEFSMappingsByYear.put(FilingYear.Y2024, build1099IntEFSMap());
		jsonToDTO.put(FilingReturnType.Type_1099_INT, intEFSMappingsByYear);


		Map<FilingYear, Map<EFSDTOProp, String>> oidEFSMappingsByYear = new HashMap<>();
		oidEFSMappingsByYear.put(FilingYear.Y2020, build1099OIDEFSMap());
		oidEFSMappingsByYear.put(FilingYear.Y2021, build1099OIDEFSMap());
		oidEFSMappingsByYear.put(FilingYear.Y2022, build1099OIDEFSMap());
		oidEFSMappingsByYear.put(FilingYear.Y2023, build1099OIDEFSMap());
		oidEFSMappingsByYear.put(FilingYear.Y2024, build1099OIDEFSMap());
		jsonToDTO.put(FilingReturnType.Type_1099_OID, oidEFSMappingsByYear);
	}

	private static Map<String, String> build1099MiscMap() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
		
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "fatcaFilingRequirementIndicator");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "rents");
		addKey(propsFor1099Misc, "royalties");
		addKey(propsFor1099Misc, "otherIncome");
		addKey(propsFor1099Misc, "federalTaxWithheld");
		addKey(propsFor1099Misc, "fishingBoatProceeds");
		addKey(propsFor1099Misc, "medicalPayments");
		addKey(propsFor1099Misc, "nonEmployeeComp");
		addKey(propsFor1099Misc, "substitutePaymentsForDivInt");
		addKey(propsFor1099Misc, "directSalesIndicator");
		addKey(propsFor1099Misc, "cropInsuranceProceeds");
		addKey(propsFor1099Misc, "goldenParachutePayment");
		addKey(propsFor1099Misc, "proceedsPaidToAttorney");
		addKey(propsFor1099Misc, "four09Deferrals");
		addKey(propsFor1099Misc, "four09Income");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");
		addKey(propsFor1099Misc, "state1Income");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");
		// addKey(propsFor1099Misc, "state2Income");

		return propsFor1099Misc;
	}

	private static Map<String, String> build1099Misc2019Map() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "fatcaFilingRequirementIndicator");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "rents");
		addKey(propsFor1099Misc, "royalties");
		addKey(propsFor1099Misc, "otherIncome");
		addKey(propsFor1099Misc, "federalTaxWithheld");
		addKey(propsFor1099Misc, "fishingBoatProceeds");
		addKey(propsFor1099Misc, "medicalPayments");
		addKey(propsFor1099Misc, "nonEmployeeComp");
		addKey(propsFor1099Misc, "substitutePaymentsForDivInt");
		addKey(propsFor1099Misc, "directSalesIndicator");
		addKey(propsFor1099Misc, "cropInsuranceProceeds");
		addKey(propsFor1099Misc, "goldenParachutePayment");
		addKey(propsFor1099Misc, "proceedsPaidToAttorney");
		addKey(propsFor1099Misc, "four09Deferrals");
		addKey(propsFor1099Misc, "four09Income");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");
		addKey(propsFor1099Misc, "state1Income");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");
		// addKey(propsFor1099Misc, "state2Income");

		return propsFor1099Misc;
	}

	private static Map<String, String> build1099Misc2020Map() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "fatcaFilingRequirementIndicator");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "rents");
		addKey(propsFor1099Misc, "royalties");
		addKey(propsFor1099Misc, "otherIncome");
		addKey(propsFor1099Misc, "federalTaxWithheld");
		addKey(propsFor1099Misc, "fishingBoatProceeds");
		addKey(propsFor1099Misc, "medicalPayments");
		addKey(propsFor1099Misc, "substitutePaymentsForDivInt");
		addKey(propsFor1099Misc, "directSalesIndicator");
		addKey(propsFor1099Misc, "cropInsuranceProceeds");
		addKey(propsFor1099Misc, "proceedsPaidToAttorney");
		addKey(propsFor1099Misc, "four09Deferrals");
		addKey(propsFor1099Misc, "goldenParachutePayment");
		addKey(propsFor1099Misc, "nonQualifiedDeferredComp");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");
		addKey(propsFor1099Misc, "state1Income");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");
		// addKey(propsFor1099Misc, "state2Income");

		return propsFor1099Misc;
	}

	private static Map<String, String> build1099Misc2021Map() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "fatcaFilingRequirementIndicator");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "rents");
		addKey(propsFor1099Misc, "royalties");
		addKey(propsFor1099Misc, "otherIncome");
		addKey(propsFor1099Misc, "federalTaxWithheld");
		addKey(propsFor1099Misc, "fishingBoatProceeds");
		addKey(propsFor1099Misc, "medicalPayments");
		addKey(propsFor1099Misc, "substitutePaymentsForDivInt");
		addKey(propsFor1099Misc, "directSalesIndicator");
		addKey(propsFor1099Misc, "cropInsuranceProceeds");
		addKey(propsFor1099Misc, "proceedsPaidToAttorney");
		addKey(propsFor1099Misc, "fishPurchasedForResale");
		addKey(propsFor1099Misc, "four09Deferrals");
		addKey(propsFor1099Misc, "goldenParachutePayment");
		addKey(propsFor1099Misc, "nonQualifiedDeferredComp");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");
		addKey(propsFor1099Misc, "state1Income");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");
		// addKey(propsFor1099Misc, "state2Income");

		return propsFor1099Misc;
	}


	private static Map<String, String> build1099NECMap() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "fatcaFilingRequirementIndicator");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "nonEmployeeComp");
		addKey(propsFor1099Misc, "federalTaxWithheld");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");
		addKey(propsFor1099Misc, "state1Income");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");
		// addKey(propsFor1099Misc, "state2Income");

		return propsFor1099Misc;
	}

	private static Map<String, String> build1099NEC2021Map() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "nonEmployeeComp");
		addKey(propsFor1099Misc, "directSalesIndicator");
		addKey(propsFor1099Misc, "federalTaxWithheld");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");
		addKey(propsFor1099Misc, "state1Income");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");
		// addKey(propsFor1099Misc, "state2Income");

		return propsFor1099Misc;
	}

	private static Map<String, String> build1099IntMap() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "fatcaFilingRequirementIndicator");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "interestIncome");
		addKey(propsFor1099Misc, "earlyWithdrawalPenalty");
		addKey(propsFor1099Misc, "interestOnUSSavingsBonds");
		addKey(propsFor1099Misc, "federalTaxWithheld");
		addKey(propsFor1099Misc, "investmentExpenses");
		addKey(propsFor1099Misc, "foreignTaxPaid");
		addKey(propsFor1099Misc, "foreignCountry");
		addKey(propsFor1099Misc, "taxExemptInterest");
		addKey(propsFor1099Misc, "specifiedPrivateActivityBondInterest");
		addKey(propsFor1099Misc, "marketDiscount");
		addKey(propsFor1099Misc, "bondPremium");
		addKey(propsFor1099Misc, "bondPremiumOnTreasuryObligations");
		addKey(propsFor1099Misc, "bondPremiumOnTaxExemptBond");
		addKey(propsFor1099Misc, "taxExemptTaxCreditBondCusipNo");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");

		return propsFor1099Misc;
	}

	private static Map<String, String> build1099OIDMap() {
		Map<String, String> propsFor1099Misc = new HashMap<>();
//		addKey(propsFor1099Misc, "reportUsingCFSF");
		addKey(propsFor1099Misc, "payeeAccountNumber");
		addKey(propsFor1099Misc, "fatcaFilingRequirementIndicator");
		addKey(propsFor1099Misc, "secondTinNotice");

		addKey(propsFor1099Misc, "oid");
		addKey(propsFor1099Misc, "periodInterest");
		addKey(propsFor1099Misc, "calendarYear");
		addKey(propsFor1099Misc, "earlyWithdrawalPenalty");
		addKey(propsFor1099Misc, "federalTaxWithheld");
		addKey(propsFor1099Misc, "marketDiscount");
		addKey(propsFor1099Misc, "acquisitionPremium");
		addKey(propsFor1099Misc, "description");
		addKey(propsFor1099Misc, "oidOnUSTreasury");
		addKey(propsFor1099Misc, "investmentExpenses");
		addKey(propsFor1099Misc, "bondPremium");
		addKey(propsFor1099Misc, "taxExemptOID");

		addKey(propsFor1099Misc, "state1TaxWithheld");
		addKey(propsFor1099Misc, "state1Code");
		addKey(propsFor1099Misc, "state1EIN");

		// addKey(propsFor1099Misc, "state2TaxWithheld");
		// addKey(propsFor1099Misc, "state2Code");
		// addKey(propsFor1099Misc, "state2EIN");

		return propsFor1099Misc;
	}

	private static void addKey(Map<String, String> propsFor1099Misc, String sourcePropName) {
		propsFor1099Misc.put(sourcePropName, sourcePropName);
	}

	private static void addKey(Map<String, String> propsFor1099Misc, String sourcePropName, String targetPropName) {
		propsFor1099Misc.put(sourcePropName, targetPropName);
	}

	private static Map<EFSDTOProp, String> build1099MiscEFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fatcaFilingRequirementIndicator", EFSDTOPropType.Boolean, "false"), "fatcaFilingRequirementIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("rents", EFSDTOPropType.BigDecimal, "0.00"), "rents");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("royalties", EFSDTOPropType.BigDecimal, "0.00"), "royalties");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("otherIncome", EFSDTOPropType.BigDecimal, "0.00"), "otherIncome");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fishingBoatProceeds", EFSDTOPropType.BigDecimal, "0.00"), "fishingBoatProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("medicalPayments", EFSDTOPropType.BigDecimal, "0.00"), "medicalPayments");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("nonEmployeeComp", EFSDTOPropType.BigDecimal, "0.00"), "nonEmployeeComp");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("substitutePaymentsForDivInt", EFSDTOPropType.BigDecimal, "0.00"), "substitutePaymentsForDivInt");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("directSalesIndicator", EFSDTOPropType.Boolean, "false"), "directSalesIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("cropInsuranceProceeds", EFSDTOPropType.BigDecimal, "0.00"), "cropInsuranceProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("goldenParachutePayment", EFSDTOPropType.BigDecimal, "0.00"), "goldenParachutePayment");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("proceedsPaidToAttorney", EFSDTOPropType.BigDecimal, "0.00"), "proceedsPaidToAttorney");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("four09Deferrals", EFSDTOPropType.BigDecimal, "0.00"), "four09Deferrals");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("four09Income", EFSDTOPropType.BigDecimal, "0.00"), "four09Income");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Income", EFSDTOPropType.BigDecimal, "0.00"), "state1Income");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}

	private static Map<EFSDTOProp, String> build1099Misc2019EFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fatcaFilingRequirementIndicator", EFSDTOPropType.Boolean, "false"), "fatcaFilingRequirementIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("rents", EFSDTOPropType.BigDecimal, "0.00"), "rents");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("royalties", EFSDTOPropType.BigDecimal, "0.00"), "royalties");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("otherIncome", EFSDTOPropType.BigDecimal, "0.00"), "otherIncome");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fishingBoatProceeds", EFSDTOPropType.BigDecimal, "0.00"), "fishingBoatProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("medicalPayments", EFSDTOPropType.BigDecimal, "0.00"), "medicalPayments");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("nonEmployeeComp", EFSDTOPropType.BigDecimal, "0.00"), "nonEmployeeComp");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("substitutePaymentsForDivInt", EFSDTOPropType.BigDecimal, "0.00"), "substitutePaymentsForDivInt");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("directSalesIndicator", EFSDTOPropType.Boolean, "false"), "directSalesIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("cropInsuranceProceeds", EFSDTOPropType.BigDecimal, "0.00"), "cropInsuranceProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("goldenParachutePayment", EFSDTOPropType.BigDecimal, "0.00"), "goldenParachutePayment");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("proceedsPaidToAttorney", EFSDTOPropType.BigDecimal, "0.00"), "proceedsPaidToAttorney");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("four09Deferrals", EFSDTOPropType.BigDecimal, "0.00"), "four09Deferrals");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("four09Income", EFSDTOPropType.BigDecimal, "0.00"), "four09Income");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Income", EFSDTOPropType.BigDecimal, "0.00"), "state1Income");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}

	private static Map<EFSDTOProp, String> build1099Misc2020EFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fatcaFilingRequirementIndicator", EFSDTOPropType.Boolean, "false"), "fatcaFilingRequirementIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("rents", EFSDTOPropType.BigDecimal, "0.00"), "rents");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("royalties", EFSDTOPropType.BigDecimal, "0.00"), "royalties");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("otherIncome", EFSDTOPropType.BigDecimal, "0.00"), "otherIncome");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fishingBoatProceeds", EFSDTOPropType.BigDecimal, "0.00"), "fishingBoatProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("medicalPayments", EFSDTOPropType.BigDecimal, "0.00"), "medicalPayments");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("substitutePaymentsForDivInt", EFSDTOPropType.BigDecimal, "0.00"), "substitutePaymentsForDivInt");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("directSalesIndicator", EFSDTOPropType.Boolean, "false"), "directSalesIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("cropInsuranceProceeds", EFSDTOPropType.BigDecimal, "0.00"), "cropInsuranceProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("proceedsPaidToAttorney", EFSDTOPropType.BigDecimal, "0.00"), "proceedsPaidToAttorney");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("four09Deferrals", EFSDTOPropType.BigDecimal, "0.00"), "four09Deferrals");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("goldenParachutePayment", EFSDTOPropType.BigDecimal, "0.00"), "goldenParachutePayment");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("nonQualifiedDeferredComp", EFSDTOPropType.BigDecimal, "0.00"), "nonQualifiedDeferredComp");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Income", EFSDTOPropType.BigDecimal, "0.00"), "state1Income");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}


	private static Map<EFSDTOProp, String> build1099Misc2021EFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fatcaFilingRequirementIndicator", EFSDTOPropType.Boolean, "false"), "fatcaFilingRequirementIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("rents", EFSDTOPropType.BigDecimal, "0.00"), "rents");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("royalties", EFSDTOPropType.BigDecimal, "0.00"), "royalties");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("otherIncome", EFSDTOPropType.BigDecimal, "0.00"), "otherIncome");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fishingBoatProceeds", EFSDTOPropType.BigDecimal, "0.00"), "fishingBoatProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("medicalPayments", EFSDTOPropType.BigDecimal, "0.00"), "medicalPayments");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("substitutePaymentsForDivInt", EFSDTOPropType.BigDecimal, "0.00"), "substitutePaymentsForDivInt");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("directSalesIndicator", EFSDTOPropType.Boolean, "false"), "directSalesIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("cropInsuranceProceeds", EFSDTOPropType.BigDecimal, "0.00"), "cropInsuranceProceeds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("proceedsPaidToAttorney", EFSDTOPropType.BigDecimal, "0.00"), "proceedsPaidToAttorney");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fishPurchasedForResale", EFSDTOPropType.BigDecimal, "0.00"), "fishPurchasedForResale");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("four09Deferrals", EFSDTOPropType.BigDecimal, "0.00"), "four09Deferrals");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("goldenParachutePayment", EFSDTOPropType.BigDecimal, "0.00"), "goldenParachutePayment");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("nonQualifiedDeferredComp", EFSDTOPropType.BigDecimal, "0.00"), "nonQualifiedDeferredComp");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Income", EFSDTOPropType.BigDecimal, "0.00"), "state1Income");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}
	private static Map<EFSDTOProp, String> build1099NECEFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fatcaFilingRequirementIndicator", EFSDTOPropType.Boolean, "false"), "fatcaFilingRequirementIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("nonEmployeeComp", EFSDTOPropType.BigDecimal, "0.00"), "nonEmployeeComp");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Income", EFSDTOPropType.BigDecimal, "0.00"), "state1Income");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}

	private static Map<EFSDTOProp, String> build1099NEC2021EFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("nonEmployeeComp", EFSDTOPropType.BigDecimal, "0.00"), "nonEmployeeComp");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("directSalesIndicator", EFSDTOPropType.Boolean, "false"), "directSalesIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Income", EFSDTOPropType.BigDecimal, "0.00"), "state1Income");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}

	private static Map<EFSDTOProp, String> build1099IntEFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fatcaFilingRequirementIndicator", EFSDTOPropType.Boolean, "false"), "fatcaFilingRequirementIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("interestIncome", EFSDTOPropType.BigDecimal, "0.00"), "interestIncome");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("earlyWithdrawalPenalty", EFSDTOPropType.BigDecimal, "0.00"), "earlyWithdrawalPenalty");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("interestOnUSSavingsBonds", EFSDTOPropType.BigDecimal, "0.00"), "interestOnUSSavingsBonds");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("investmentExpenses", EFSDTOPropType.BigDecimal, "0.00"), "investmentExpenses");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("foreignTaxPaid", EFSDTOPropType.BigDecimal, "0.00"), "foreignTaxPaid");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("foreignCountry", EFSDTOPropType.String, ""), "foreignCountry");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("taxExemptInterest", EFSDTOPropType.BigDecimal, "0.00"), "taxExemptInterest");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("specifiedPrivateActivityBondInterest", EFSDTOPropType.BigDecimal, "0.00"), "specifiedPrivateActivityBondInterest");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("marketDiscount", EFSDTOPropType.BigDecimal, "0.00"), "marketDiscount");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("bondPremium", EFSDTOPropType.BigDecimal, "0.00"), "bondPremium");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("bondPremiumOnTreasuryObligations", EFSDTOPropType.BigDecimal, "0.00"), "bondPremiumOnTreasuryObligations");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("bondPremiumOnTaxExemptBond", EFSDTOPropType.BigDecimal, "0.00"), "bondPremiumOnTaxExemptBond");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("taxExemptTaxCreditBondCusipNo", EFSDTOPropType.String, ""), "taxExemptTaxCreditBondCusipNo");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}

	private static Map<EFSDTOProp, String> build1099OIDEFSMap() {
		Map<EFSDTOProp, String> propsFor1099MiscEfsDTO = new HashMap<>();

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("reportUsingCFSF", EFSDTOPropType.Boolean, "false"), "reportUsingCFSF");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("secondTinNotice", EFSDTOPropType.Boolean, "false"), "secondTinNotice");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("payeeAccountNumber", EFSDTOPropType.String, ""), "payeeAccountNumber");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("fatcaFilingRequirementIndicator", EFSDTOPropType.Boolean, "false"), "fatcaFilingRequirementIndicator");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("oid", EFSDTOPropType.BigDecimal, "0.00"), "oid");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("periodInterest", EFSDTOPropType.BigDecimal, "0.00"), "periodInterest");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("calendarYear", EFSDTOPropType.String, ""), "calendarYear");
		
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("earlyWithdrawalPenalty", EFSDTOPropType.BigDecimal, "0.00"), "earlyWithdrawalPenalty");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("federalTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "federalTaxWithheld");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("marketDiscount", EFSDTOPropType.BigDecimal, "0.00"), "marketDiscount");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("acquisitionPremium", EFSDTOPropType.BigDecimal, "0.00"), "acquisitionPremium");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("description", EFSDTOPropType.String, ""), "description");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("oidOnUSTreasury", EFSDTOPropType.BigDecimal, "0.00"), "oidOnUSTreasury");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("investmentExpenses", EFSDTOPropType.BigDecimal, "0.00"), "investmentExpenses");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("bondPremium", EFSDTOPropType.BigDecimal, "0.00"), "bondPremium");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("taxExemptOID", EFSDTOPropType.BigDecimal, "0.00"), "taxExemptOID");

		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1Code", EFSDTOPropType.StateCode), "state1Code");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1EIN", EFSDTOPropType.String), "state1EIN");
		propsFor1099MiscEfsDTO.put(new EFSDTOProp("state1TaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "state1TaxWithheld");

//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("localTaxWithheld", EFSDTOPropType.BigDecimal, "0.00"), "localTaxWithheld");
//		propsFor1099MiscEfsDTO.put(new EFSDTOProp("stateSpecialData", EFSDTOPropType.String, ""), "stateSpecialData");

		return propsFor1099MiscEfsDTO;
	}



	public static Map<String, String> getDtoToJsonMappings(FilingReturnType returnType, FilingYear filingYear) {
		Map<FilingYear, Map<String, String>> mappingByYear = dtoToJson.get(returnType);
		if(mappingByYear != null) {
			Map<String, String> fieldMappings = mappingByYear.get(filingYear);
			if(fieldMappings != null) {
				return fieldMappings;
			}
		}
		String message = String.format("Field mappings couldn't be figured out. Possibly unsupported filingType=%s and filingYear=%s", returnType, filingYear);
		throw new RuntimeException(message);
	}

	public static Map<EFSDTOProp, String> getJsonToDtoMappings(FilingReturnType returnType, FilingYear filingYear) {
		Map<FilingYear, Map<EFSDTOProp, String>> mappingByYear = jsonToDTO.get(returnType);
		if(mappingByYear != null) {
			Map<EFSDTOProp, String> fieldMappings = mappingByYear.get(filingYear);
			if(fieldMappings != null) {
				return fieldMappings;
			}
		}
		throw new RuntimeException("Field mappings couldn't be figured out. Possibly unsupported filing type or filing year");
	}

	/**
	 * When moving from filingInput to JSON, figure out the source data type and target data type.
	 * 
	 * For each property that need to go into JSON,
	 * 
	 * know the source (dto's) property name know the target (json's) property name, just save it as that object.
	 * 
	 * @param filingInput
	 * @param filingReturnType
	 * @return
	 */
}
