package com.aphe.contractor.services;


import com.aphe.billing.dto.ChargeItemDTO;
import com.aphe.billing.dto.InvoiceDTO;
import com.aphe.billing.model.enums.TNNService;
import com.aphe.billing.service.BillingMgr;
import com.aphe.common.error.ApheErrorCode;
import com.aphe.common.error.MappedValidationMessages;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.graphql.PagedResult;
import com.aphe.common.util.ArrayUtil;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.dto.*;
import com.aphe.contractor.dto.read.FilingDTO;
import com.aphe.contractor.dto.read.PayeeDTO;
import com.aphe.contractor.dto.read.PrintEmailRequestDTO;
import com.aphe.contractor.model.*;
import com.aphe.contractor.model.enums.*;
import com.aphe.contractor.repo.*;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.model.Domain;
import com.aphe.domain.model.TinType;
import com.aphe.domain.repo.DomainRepository;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.dto.FilingDataDTO;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.efs.services.transmission.TNNTransmissionOrchestrator;
import org.apache.commons.lang.RandomStringUtils;
import org.hibernate.validator.constraints.ValidationErrors;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Component
public class FilingManager extends CommonTNNManager {

    @Autowired
    FilingCreateMagager filingCreateManager;

    @Autowired
    FilingRepository filingRepo;

    @Autowired
    FedSubRepository fedSubRepository;

    @Autowired
    StateSubRepository stateSubRepository;

    @Autowired
    PayerRepository payerRepo;

    @Autowired
    PayeeRepository payeeRepo;

    @Autowired
    DomainRepository domainRepo;

    @Autowired
    ContractorConvertUtil convertUtil;

    @Autowired
    ValidationUtil validationUtil;

    @Autowired
    BillingMgr billingMgr;

    @Autowired
    FilingsManager efsFilingsMgr;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    PayeeManager payeeManager;

    @Autowired
    TINMatchRequestManager tinMatchRequestManager;

    @Autowired
    PayerManager payerManager;

    @Autowired
    FilingsStatusManager filingsStatusManager;

    @Autowired
    EmailSubRepository emailSubRepository;

    @Autowired
    TNNTransmissionOrchestrator tnnTransmissionOrchestrator;

    @Value("${aphe.config.enableTINMatch}")
    private boolean enableTINMatch = true;

    @Autowired
    MailUtil mailUtil;

    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_FILING')")
    public List<FilingCount> getFilingCounts(Long payerId, String filingYear) throws ApheForbiddenException, ApheException {
        Payer payer = payerRepo.findById(payerId).orElse(null);
        FilingYear filingYearEnum = FilingYear.getFilingYearForYear(filingYear);
        if(filingYearEnum != null) {
            return filingRepo.filingCountsByStatusAndFilingYear(payer, filingYearEnum);
        } else {
            return filingRepo.filingCountsByStatus(payer);
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_FILING')")
    public PagedResult<FilingDTO> getFilingsByPayer(Long payerId, List<String> statuses, List<String> years, List<String> types, int pageSize, int pageNumber) throws ApheForbiddenException, ApheException {

        Payer payer = payerRepo.findById(payerId).orElse(null);
        List<FilingStatus> filingStatuses = new ArrayList<>();
        if(statuses != null && statuses.size() > 0) {
            for(String s : statuses) {
                FilingStatus filingStatus = FilingStatus.valueOf(s);
                if(filingStatus != null) {
                    filingStatuses.add(filingStatus);
                }
            }
        }
        if(filingStatuses.size() == 0) {
            filingStatuses.addAll(Stream.of(FilingStatus.values()).collect(Collectors.toList()));
        }

        List<FilingYear> filingYears = new ArrayList<>();
        if(years != null && years.size() > 0) {
            for(String s : years) {
                FilingYear filingYear = FilingYear.getFilingYearForYear(s);
                if(filingYear != null) {
                    filingYears.add(filingYear);
                }
            }
        }
        if(filingYears.size() == 0) {
            filingYears.addAll(Stream.of(FilingYear.values()).collect(Collectors.toList()));
        }

        List<FilingReturnType> filingTypes = new ArrayList<>();
        if(types != null && types.size() > 0) {
            for(String s : types) {
                FilingReturnType filingType = FilingReturnType.getFilingReturnTypeForForm(s);
                if(filingType != null) {
                    filingTypes.add(filingType);
                }
            }
        }
        if(filingTypes.size() == 0) {
            filingTypes.addAll(Stream.of(FilingReturnType.values()).collect(Collectors.toList()));
        }

//        Page<Filing> filings = filingRepo.findByPayer(payer, PageRequest.of(pageNumber, pageSize, Sort.by("id")));

        Page<Filing> filings = filingRepo.findByPayerAndFilingStatusInAndFilingYearInAndFormTypeIn(
                payer, filingStatuses, filingYears, filingTypes, PageRequest.of(pageNumber, pageSize, Sort.by("id"))
        );
        List<FilingDTO> filingDTOs = convertToDTOs(filings.getContent());
        PagedResult<FilingDTO> result = new PagedResult<>();
        result.data = filingDTOs;
        result.pageMetadata = buildPageMetadata(filings);
        return result;
    }

    private List<FilingDTO> convertToDTOs(List<Filing> filings) throws ApheException {
        List<FilingDTO> dtos = new ArrayList<>();
        for (Filing f : filings) {
            dtos.add(convertToDTO(f));
        }
        return dtos;
    }

    @Transactional
    @PreAuthorize("hasPermission(#payeeId, 'PAYEE', 'READ_FILING')")
    public List<FilingDTO> getFilingsByPayee(Long payeeId) throws ApheForbiddenException, ApheException {
        Payee payee = payeeRepo.findById(payeeId).orElse(null);
        List<Filing> filings = filingRepo.findByPayee(payee);
        return convertToDTOs(filings);
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingId, 'FILING', 'READ_FILING')")
    public FilingDTO getFiling(Long filingId) throws ApheForbiddenException, ApheException {
        Filing filing = filingRepo.findById(filingId).orElse(null);
        return convertToDTO(filing);
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingId, 'FILING', 'READ_FILING')")
    public Filing getOriginalFiling(Long filingId)  {
        Filing filing = filingRepo.findById(filingId).orElse(null);
        if(filing != null) {
            Filing originalFiling = filingRepo.findById(filing.getOriginalFilingId()).orElse(null);
            return originalFiling;
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'READ_FILING')")
    public List<FilingDTO> getFilingsByIds(List<String> filingIds) throws ApheForbiddenException, ApheException {
        List<FilingDTO> retList = new ArrayList<>();
        if (filingIds == null || filingIds.size() == 0) {
            return retList;
        }
        Payer payer = payerRepo.findByDomainId(getCurrentDomainId());
        List<Long> longFilingIds = ArrayUtil.stringListToLongList(filingIds);
        List<Filing> filings = filingRepo.findByPayerAndIdIn(payer, longFilingIds);
        for (Filing f : filings) {
            retList.add(convertToDTO(f));
        }
        return retList;
    }

    private boolean isDraftFiling(Filing f) {
        FilingStatus fedFilingStatus = f.getFedSub() == null ?  FilingStatus.Draft : f.getFedSub().getStatus();
        StateFilingStatus stateFilingStatus = f.getStateSub() == null ?  StateFilingStatus.None : f.getStateSub().getStatus();
        return fedFilingStatus == FilingStatus.Draft && stateFilingStatus == StateFilingStatus.None;
    }

    private boolean hasAllSubmissions(Filing f) {
        return f.getFedSub() != null && f.getStateSub() != null && f.getPrintSub() != null && f.getEmailSub() != null;
    }

    /**
     * Returns first draft (no fed submission, no state submission) filing available.
     * @param payeeId
     * @param filingReturnType
     * @param filingYear
     * @return
     * @throws ApheForbiddenException
     * @throws ApheException
     */
    @Transactional
    @PreAuthorize("hasPermission(#payeeId, 'PAYEE', 'READ_FILING')")
    public FilingDTO getFilingByPayeeAndFormTypeAndFilingYear(Long payeeId, FilingReturnType filingReturnType, FilingYear filingYear) throws ApheForbiddenException, ApheException {
        Payer payer = payerRepo.findByDomainId(getCurrentDomainId());
        PayeeDTO payeeDTO = payeeManager.getPayee(payeeId);
        Payee payee = null;
        if (payeeDTO != null) {
            payee = payeeRepo.findById(payeeDTO.id).orElse(null);
        }
        List<Filing> filings = filingRepo.findByPayerAndPayeeAndFilingTypeAndFilingYear(payer, payee, filingReturnType, filingYear);
        FilingDTO returnValue = null;
        for (Filing f : filings) {
//            if (isDraftFiling(f)) {
                returnValue = convertToDTO(f);
                break;
//            }
        }
        return returnValue;
    }

    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_FILING')")
    public PagedResult<FilingDTO> getFilingsByFormType(Long payerId, List<FilingReturnType> filingReturnTypes, int pageSize, int pageNumber) throws ApheForbiddenException, ApheException {
        Payer payer = payerRepo.findById(payerId).orElse(null);
        Page<Filing> filings = filingRepo.findByPayerAndFilingTypes(payer, filingReturnTypes, PageRequest.of(pageNumber, pageSize, Sort.by("id")));

        List<FilingDTO> filingDTOs = new ArrayList<>();
        for (Filing f : filings.getContent()) {
            filingDTOs.add(convertToDTO(f));
        }
        PagedResult<FilingDTO> result = new PagedResult<>();
        result.data = filingDTOs;
        result.pageMetadata = buildPageMetadata(filings);
        return result;
    }

    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_FILING')")
    public PagedResult<FilingDTO> getFilingsByPayerIdStatusAndFilingYear(Long payerId, List<FilingStatus> statuses, List<FilingYear> years, int pageSize, int pageNumber) throws ApheForbiddenException, ApheException {
        Payer payer = payerRepo.findById(payerId).orElse(null);
        Page<Filing> filings = filingRepo.findByPayerAndStatusInAndFilingYearIn(payer, statuses, years, PageRequest.of(pageNumber, pageSize, Sort.by("id")));
        List<Filing> filingsContent = filings.getContent();
        List<FilingDTO> filingDTOs = convertToDTOs(filingsContent);

        PagedResult<FilingDTO> result = new PagedResult<>();
        result.data = filingDTOs;
        result.pageMetadata = buildPageMetadata(filings);
        return result;
    }


    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_FILING')")
    public PagedResult<FilingDTO> getFilingsByStatus(Long payerId, List<FilingStatus> statuses, List<FilingYear> years, int pageSize, int pageNumber) throws ApheForbiddenException, ApheException {
        Payer payer = payerRepo.findById(payerId).orElse(null);

        Page<Filing> filings = filingRepo.findByPayerAndStatusInAndFilingYearIn(payer, statuses, years, PageRequest.of(pageNumber, pageSize, Sort.by("id")));
        List<Filing> filingsContent = filings.getContent();
        List<FilingDTO> filingDTOs = convertToDTOs(filingsContent);

        List<Filing> filingsSelected = filingsContent.stream().filter(f->f.getFedSub() != null && Boolean.TRUE == f.getFedSub().getSelected()).collect(Collectors.toList());
        List<MappedValidationMessages> validationMessages = validationUtil.validateFilings(filingsSelected);
        for(MappedValidationMessages messages : validationMessages) {
            FilingDTO theDTO = filingDTOs.stream().filter(f->Long.toString(f.id).equalsIgnoreCase(messages.getId())).findFirst().orElse(null);
            if(theDTO != null) {
                theDTO.warnings = messages.getWarnings();
                theDTO.errors = messages.getErrors();
            }
        }

        PagedResult<FilingDTO> result = new PagedResult<>();
        result.data = filingDTOs;
        result.pageMetadata = buildPageMetadata(filings);
        return result;
    }

    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_FILING')")
    public boolean hasPendingFilings(Long domainId) throws ApheForbiddenException {
        Payer p = payerRepo.findByDomainId(domainId);

        List<Filing> filings = filingRepo.findByPayerAndStatus(p, FilingStatus.Submitted);
        boolean hasPendingFilings = filings.size() > 0;
        return hasPendingFilings;

    }

    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_FILING')")
    public boolean hasProcessedFilings(Long domainId) throws ApheForbiddenException {
        Payer p = payerRepo.findByDomainId(domainId);

        List<Filing> filings = filingRepo.findByPayerAndStatusIn(p, FilingStatus.nonDraftStatuses);
        boolean hasPendingFilings = filings.size() > 0;
        return hasPendingFilings;

    }

    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_FILING')")
    public boolean hasFilings(Long domainId) throws ApheForbiddenException {
        Payer p = payerRepo.findByDomainId(domainId);

        long count = filingRepo.countByPayer(p);
        boolean hasFilings = count > 0;
        return hasFilings;
    }

    //TODO: is anybody using this? This is to potentially warn when somebody is trying to change EIN, name, address etc, saying you have some submissions for this year...
    // It will return true even if there is one filing that is not in a draft status.
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_FILING')")
    public boolean hasNonDraftFilings(Long domainId) throws ApheForbiddenException {
        payerManager.createPayerIfNotExists(domainId);
        Payer p = payerRepo.findByDomainId(domainId);
        int pageSize = 1000;
        int pageNumber = 0;
        boolean hasMoreFilings = true;
        boolean hasNonDraftFilings = false;

        while (hasMoreFilings && !hasNonDraftFilings) {
            Page<Filing> filingsPage = filingRepo.findByPayer(p, PageRequest.of(pageNumber, pageSize, Sort.by("id")));
            List<Filing> theFilings = filingsPage.getContent();
            for (Filing f : theFilings) {
                boolean isDraftFiling = isDraftFiling(f);
                if (!isDraftFiling) {
                    hasNonDraftFilings = true;
                    break;
                }
            }
            pageNumber++;
            hasMoreFilings = filingsPage.hasNext();
        }
        return hasNonDraftFilings;
    }

    @Transactional
    @PreAuthorize("hasPermission(#addEditFilingInput.payeeId, 'PAYEE', 'CREATE_FILING')")
    public long createFiling(AddEditFilingInput addEditFilingInput) throws ApheForbiddenException, ApheDataValidationException, ApheException {
        return addEditFiling(addEditFilingInput);
    }

    @Transactional
    @PreAuthorize("hasPermission(#addEditFilingInput.id, 'FILING', 'UPDATE_FILING')")
    public long updateFiling(AddEditFilingInput addEditFilingInput) throws ApheForbiddenException, ApheDataValidationException, ApheException {
        return addEditFiling(addEditFilingInput);
    }

    private long addEditFiling(AddEditFilingInput addEditFilingInput) throws ApheForbiddenException, ApheDataValidationException, ApheException {

        // Validate DTO.
        ValidationErrors errors = getValidationErrors(addEditFilingInput);

        //Now compare the filing return type, year and input type to make sure the user is sending the right input
        FilingYear filingYear = FilingYear.getFilingYearForYear(addEditFilingInput.filingYear);

        if (addEditFilingInput.getFilingReturnType() == FilingReturnType.Type_1099_MISC) {
            if (filingYear == FilingYear.Y2020 && !(addEditFilingInput instanceof AddEditFilingInput1099MISC2020)) {
                errors.addMessage("id", "Invalid input. Wrong payload sent");
            }
            if (filingYear == FilingYear.Y2021 && !(addEditFilingInput instanceof AddEditFilingInput1099MISC2021)) {
                errors.addMessage("id", "Invalid input. Wrong payload sent");
            }
        }

        if (addEditFilingInput.getFilingReturnType() == FilingReturnType.Type_1099_NEC) {
            if (filingYear == FilingYear.Y2020 && !(addEditFilingInput instanceof AddEditFilingInput1099NEC)) {
                errors.addMessage("id", "Invalid input. Wrong payload sent");
            }
            if (filingYear == FilingYear.Y2021 && !(addEditFilingInput instanceof AddEditFilingInput1099NEC2021)) {
                errors.addMessage("id", "Invalid input. Wrong payload sent");
            }
        }
        if (addEditFilingInput.getFilingReturnType() == FilingReturnType.Type_1099_INT) {
            if ((filingYear == FilingYear.Y2020 || filingYear == FilingYear.Y2021)
                    && !(addEditFilingInput instanceof AddEditFilingInput1099INT)) {
                errors.addMessage("id", "Invalid input. Wrong payload sent");
            }
        }
        if (addEditFilingInput.getFilingReturnType() == FilingReturnType.Type_1099_OID) {
            if ((filingYear == FilingYear.Y2020 || filingYear == FilingYear.Y2021)
                    && !(addEditFilingInput instanceof AddEditFilingInput1099OID)) {
                errors.addMessage("id", "Invalid input. Wrong payload sent");
            }
        }

        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors, "Invalid data");
        }

        long existingFilingId = -1;
        long payeeId = -1;
        if (addEditFilingInput.id != null && addEditFilingInput.id > 0) {
            existingFilingId = addEditFilingInput.id;
        }
        if (addEditFilingInput.payeeId != null && addEditFilingInput.payeeId > 0) {
            payeeId = addEditFilingInput.payeeId;
        }

        if (existingFilingId > 0) {
            // If not new filing... check if the filing is accessible.
            Filing existingEntity = filingRepo.findById(addEditFilingInput.id).orElse(null);

            Payee payee = payeeRepo.findById(payeeId).orElse(null);
            if (!isAccessible(payee)) {
                throw new ApheDataValidationException("payeeId", "Invalid payee");
            }

            FedSub fedSub = existingEntity.getFedSub();
            FilingStatus fedSubStatus = fedSub.getStatus();
            boolean isFedNonDraft = fedSub != null && fedSubStatus != null && fedSubStatus != FilingStatus.Draft;
            StateSub stateSub = existingEntity.getStateSub();
            StateFilingStatus stateSubStatus = stateSub.getStatus();
            boolean isStateNonDraft = stateSub != null && stateSubStatus != null && stateSubStatus != StateFilingStatus.None;

            if (!isDraftFiling(existingEntity)) {
                throw new ApheDataValidationException("id", "You can not modify a filing that has been submitted for processing. If you need to modify a filing that been accepted by the IRS, you need to file corrections. Please check out our correction guide for more information.", ApheErrorCode.APP_401);
            }
        } else {
            Payer payer = payerRepo.findByDomainId(getCurrentDomainId());
            Payee payee = payeeRepo.findById(payeeId).orElse(null);
            if (!isAccessible(payer)) {
                throw new ApheDataValidationException("id", "Invalid payer");
            }
            if (!isAccessible(payee)) {
                throw new ApheDataValidationException("payeeId", "Invalid payee");
            }
        }

        // Convert to Entity.
        Filing filing = convertUtil.convertToFilingEntity(addEditFilingInput);

        if (existingFilingId > 0) {
            // If not new filing... check if the filing is accessible.
            Filing existingEntity = filingRepo.findById(existingFilingId).orElse(null);
            //TODO: ensure this payee belongs to this domain.
            Payee payee = payeeRepo.findById(payeeId).orElse(null);
            existingEntity.setPayee(payee);
            Filing savedFiling = filingRepo.mergeAndSave(filing, existingEntity);
            return savedFiling.getId();

        } else {
            // If new filing, check if payer and Payee are accessible.. if so, set it.
            Payer payer = payerRepo.findByDomainId(getCurrentDomainId());

            //TODO: ensure this payee belongs to this domain.
            Payee payee = payeeRepo.findById(payeeId).orElse(null);
            filing.setPayee(payee);
            filing.setPayer(payer);

            FedSub fedSub = new FedSub();
            fedSub.setFiling(filing);
            fedSub.setSelected(false);
            fedSub.setStatus(FilingStatus.Draft);
            fedSub.setStatusChangeDate(new Date());
            filing.setFedSub(fedSub);
            filingsStatusManager.updateFedFilingStatusHistory(fedSub);

            StateSub stateSub = new StateSub();
            stateSub.setFiling(filing);
            stateSub.setSelected(false);
            stateSub.setStatus(StateFilingStatus.None);
            stateSub.setStatusChangeDate(new Date());
            filing.setStateSub(stateSub);
            filingsStatusManager.updateStateFilingStatusHistory(stateSub);

            PrintSub printSub = new PrintSub();
            printSub.setFiling(filing);
            printSub.setSelected(false);
            printSub.setPrintStatus(PrintStatus.None);
            printSub.setPrintStatusChangeDate(new Date());
            filing.setPrintSub(printSub);
            filingsStatusManager.updatePrintStatusHistory(printSub);


            EmailSub emailSub = new EmailSub();
            emailSub.setFiling(filing);
            emailSub.setSelected(false);
            emailSub.setEmailStatus(EmailStatus.None);
            emailSub.setEmailStatusChangeDate(new Date());
            filing.setEmailSub(emailSub);
            filingsStatusManager.updateEmailStatusHistory(emailSub);

            Filing savedFiling = filingCreateManager.createFiling(filing);
            return savedFiling.getId();
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'READ_FILING')")
    public List<MappedValidationMessages> getValidationMessages(List<String> filingIds, boolean ignoreWarnings) throws ApheForbiddenException, ApheDataValidationException {
        if (filingIds.size() == 0) {
            throw new ApheDataValidationException("filingIds", "No filings were selected");
        }

        List<Long> filingIdsList = ArrayUtil.stringListToLongList(filingIds);
        List<Filing> filings = filingRepo.findByIdIn(filingIdsList);
        if (filings.size() != filingIds.size()) {
            logger.info("All the filingIds={} are not found in account.  domainId={}", ArrayUtil.listToString(filingIdsList, ","), getCurrentDomainId());
            throw new ApheForbiddenException();
        }

        List<MappedValidationMessages> validationMessages = validationUtil.validateFilings(filings, ignoreWarnings);

        return validationMessages;
    }




    @Transactional
    @PreAuthorize("hasPermission(#filingId, 'FILING', 'DELETE_FILING')")
    public void deleteFiling(String filingId) throws ApheForbiddenException, ApheDataValidationException {
        Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);

        if (!isDraftFiling(filing)) {
            throw new ApheDataValidationException("filing", "Can not delete a filing that has been submitted and is in processing.");
        }
        filing.setEmailSub(null);
        // TODO : Should this be soft delete???
        filingRepo.delete(filing);
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'DELETE_FILING')")
    public void deleteFilings(List<String> filingIds) throws ApheForbiddenException, ApheDataValidationException {
        for (String filingId : filingIds) {
            deleteFiling(filingId);
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingId, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> updateFedSub(String filingId, boolean selected) throws ApheForbiddenException, ApheDataValidationException {
        Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
        if (!isDraftFiling(filing)) {
            throw new ApheDataValidationException("filing", "Can not submit a non draft filing.");
        }
        updateSubSelections(filing, selected);
        filingRepo.save(filing);
        //Validate and return the results..
        List<Filing> filings = new ArrayList<>();
        filings.add(filing);
        List<MappedValidationMessages> mappedValidationMessages = validationUtil.validateFilings(filings);
        return mappedValidationMessages;
    }

    private void updateSubSelections(Filing filing, boolean selected) throws ApheDataValidationException, ApheForbiddenException {
        filing.getFedSub().setSelected(selected);

        boolean isFilingSupported = false;
        boolean isFilingRequired = false;
        boolean supportedByUs = false;
        StateCode state1Code = convertUtil.getJSONLocalStateCode(filing.getFilingData(), "state1Code", null);
        BigDecimal state1TaxWithheld = new BigDecimal(convertUtil.getJSONBigDecimalValue(filing.getFilingData(), "state1TaxWithheld","0.00"));
        BigDecimal state1Income = new BigDecimal(convertUtil.getJSONBigDecimalValue(filing.getFilingData(), "state1Income","0.00"));

        if(state1Code != null ) {
            StateFilingData stateFilingData = StateFilingData.getStateFilingData(state1Code.name());
            if(stateFilingData != null) {
                isFilingSupported = stateFilingData.isStateFilingRequired(filing.getFilingType());
                isFilingRequired = stateFilingData.isStateFilingRequired(filing.getFilingType(), state1Income, state1TaxWithheld, state1Code, state1Code, state1Code);
                List<StateFilingMethod> allowedStateFilingMethods = stateFilingData.getAllowedStateFilingMethods(filing.getFilingType(), state1Income, state1TaxWithheld, state1Code, state1Code, state1Code);
                supportedByUs = allowedStateFilingMethods.contains(StateFilingMethod.EFile) || allowedStateFilingMethods.contains(StateFilingMethod.CFSF);
            }
        }
        if(selected) {
            if(isFilingSupported && (isFilingRequired || supportedByUs)) {
                setStateSubValues(filing, selected, null);
            } else {
                setStateSubValues(filing, false, null);
            }
        } else {
            setStateSubValues(filing, selected, null);
        }

        filing.getPrintSub().setSelected(selected);

        if(selected) {
            String emailAddress = filing.getPayee().getEmailAddress();
            if(emailAddress != null && StringUtil.isNotEmpty(emailAddress)) {
                filing.getEmailSub().setSelected(true);
            }
        } else {
            filing.getEmailSub().setSelected(false);
        }

        tinMatchRequestManager.updateTINMatchRequest(Long.toString(filing.getPayee().getId()), enableTINMatch ? selected : false);
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingId, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> updateStateSub(String filingId, boolean selected, String filingMethod) throws ApheForbiddenException, ApheDataValidationException {
        Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
        if (!isDraftFiling(filing)) {
            throw new ApheDataValidationException("filing", "Can not submit a non draft filing.");
        }
        setStateSubValues(filing, selected, filingMethod);
        filingRepo.save(filing);
        //Validate and return the results..
        List<Filing> filings = new ArrayList<>();
        filings.add(filing);
        List<MappedValidationMessages> mappedValidationMessages = validationUtil.validateFilings(filings);
        return mappedValidationMessages;
    }

    private void setStateSubValues(Filing filing, boolean selected, String filingMethod) {
        StateFilingMethod currentFilingMethod = filing.getStateSub().getFilingMethod();

        boolean isFilingSupported = false;
        List<StateFilingMethod> supportedMethods = new ArrayList<>();

        StateCode state1Code = convertUtil.getJSONLocalStateCode(filing.getFilingData(), "state1Code", null);
        BigDecimal state1TaxWithheld = new BigDecimal(convertUtil.getJSONBigDecimalValue(filing.getFilingData(), "state1TaxWithheld","0.00"));
        BigDecimal state1Income = new BigDecimal(convertUtil.getJSONBigDecimalValue(filing.getFilingData(), "state1Income","0.00"));

        if(state1Code != null ) {
            StateFilingData stateFilingData = StateFilingData.getStateFilingData(state1Code.name());
            if(stateFilingData != null) {
                isFilingSupported = stateFilingData.isStateFilingRequired(filing.getFilingType());
//                supportedMethods = stateFilingData.getSupportedStateFilingMethods(filing.getFilingType());
                supportedMethods = stateFilingData.getAllowedStateFilingMethods(filing.getFilingType(), state1Income, state1TaxWithheld, state1Code, state1Code, state1Code);
            }
        }

        if(selected == true) {
            if(isFilingSupported) {
                filing.getStateSub().setSelected(true);
                if(filingMethod == null || filingMethod.trim().equalsIgnoreCase("")) {
                    //Check if there is current filing method. If so, leave it alone.
                    if(currentFilingMethod == null){
                        //pick the best option from supportedMethods.
                        if(supportedMethods.contains(StateFilingMethod.CFSF)) {
                            filing.getStateSub().setFilingMethod(StateFilingMethod.CFSF);
                        } else if(supportedMethods.contains(StateFilingMethod.EFile)) {
                            filing.getStateSub().setFilingMethod(StateFilingMethod.EFile);
                        }
                    }
                } else {
                    StateFilingMethod theMethod = StateFilingMethod.getStateFilingMethod(filingMethod);
                    filing.getStateSub().setFilingMethod(theMethod);
                }
            }
        } else {
            filing.getStateSub().setSelected(false);
            filing.getStateSub().setFilingMethod(null);
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingId, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> updatePrintSub(String filingId, boolean selected) throws ApheForbiddenException, ApheDataValidationException {
        Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
        if (!isDraftFiling(filing)) {
            throw new ApheDataValidationException("filing", "Can not submit a non draft filing.");
        }
        filing.getPrintSub().setSelected(selected);
        filingRepo.save(filing);
        //Validate and return the results..
        List<Filing> filings = new ArrayList<>();
        filings.add(filing);
        List<MappedValidationMessages> mappedValidationMessages = validationUtil.validateFilings(filings);
        return mappedValidationMessages;
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingId, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> updateEmailSub(String filingId, boolean selected) throws ApheForbiddenException, ApheDataValidationException {
        Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
        if (!isDraftFiling(filing)) {
            throw new ApheDataValidationException("filing", "Can not submit a non draft filing.");
        }
        filing.getEmailSub().setSelected(selected);
        filingRepo.save(filing);
        //Validate and return the results..
        List<Filing> filings = new ArrayList<>();
        filings.add(filing);
        List<MappedValidationMessages> mappedValidationMessages = validationUtil.validateFilings(filings);
        return mappedValidationMessages;
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> updateTINMatch(List<String> filingIds, boolean selected) throws ApheForbiddenException, ApheDataValidationException {
        List<Filing> filings = new ArrayList<>();
        for(String filingId : filingIds) {
            Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
            if(filing != null) {
                tinMatchRequestManager.updateTINMatchRequest(Long.toString(filing.getPayee().getId()), selected);
                filings.add(filing);
            }
        }
        List<MappedValidationMessages> mappedValidationMessages = validationUtil.validateFilings(filings);
        return mappedValidationMessages;
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> bulkUpdateFedSub(List<String> filingIds, boolean selected) throws ApheForbiddenException, ApheDataValidationException {
        List<Filing> filings = new ArrayList<>();
        for (String filingId : filingIds) {
            Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
            if (!isDraftFiling(filing)) {
                throw new ApheDataValidationException("filing", "Can not select eFile submission on a non draft filing.");
            }
            updateSubSelections(filing, selected);
            filingRepo.save(filing);
            filings.add(filing);
        }
        return new ArrayList<>();
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> bulkUpdateStateSub(List<Long> filingIds, Map<Long, StateSubInput> stateSubInputs) throws ApheForbiddenException, ApheDataValidationException {
        List<Filing> filings = new ArrayList<>();
        for (Long filingId: filingIds) {
            StateSubInput stateSub = stateSubInputs.get(filingId);
            boolean selected = stateSub.selected;
            String filingMethod = stateSub.filingMethod;
            Filing filing = filingRepo.findById(filingId).orElse(null);
            if (!isDraftFiling(filing)) {
                throw new ApheDataValidationException("filing", "Can not select state submission on a non draft filing.");
            }
            StateCode filingStateCode = convertUtil.getJSONLocalStateCode(filing.getFilingData(), "state1Code", null);
            if(selected) {
                if(filingStateCode != null) {
                    StateFilingData filingStateFilingData = StateFilingData.getStateFilingData(filingStateCode.name());
                    if(filingStateFilingData != null && filingStateFilingData.isStateFilingRequired(filing.getFilingType())) {
                        filing.getStateSub().setSelected(true);
                        filing.getStateSub().setFilingMethod(StateFilingMethod.getStateFilingMethod(filingMethod));
                    } else {
                        filing.getStateSub().setSelected(false);
                    }
                }
            } else {
                filing.getStateSub().setSelected(false);
                filing.getStateSub().setFilingMethod(null);

            }
            filingRepo.save(filing);
            filings.add(filing);
        }
        return new ArrayList<>();
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> bulkUpdatePrintSub(List<String> filingIds, boolean selected) throws ApheForbiddenException, ApheDataValidationException {
        List<Filing> filings = new ArrayList<>();
        for (String filingId : filingIds) {
            Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
            if (!isDraftFiling(filing)) {
                throw new ApheDataValidationException("filing", "Can not select print submission on a non draft filing.");
            }
            filing.getPrintSub().setSelected(selected);
            filingRepo.save(filing);
            filings.add(filing);
        }
        return new ArrayList<>();

    }

    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'UPDATE_FILING')")
    public List<MappedValidationMessages> bulkUpdateEmailSub(List<String> filingIds, boolean selected) throws ApheForbiddenException, ApheDataValidationException {
        List<Filing> filings = new ArrayList<>();
        for (String filingId : filingIds) {
            Filing filing = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
            if (!isDraftFiling(filing)) {
                throw new ApheDataValidationException("filing", "Can not select email submission on a non draft filing.");
            }
            filing.getEmailSub().setSelected(selected);
            filingRepo.save(filing);
            filings.add(filing);
        }
        return new ArrayList<>();
    }


    @Transactional
    @PreAuthorize("hasPermission(#filingIds, 'FILING', 'SUBMIT_FILING')")
    // This will be just filingIds, what kind of submissions we want are all stored in the corresponding submission tables.
    public void submitFilings(List<String> filingIds) throws ApheDataValidationException, ApheForbiddenException {

        // Override testFiling status based on test account flag.
        boolean isTestAccount = Boolean.TRUE.equals(domainMgr.getDomain().isTestAccount);

        List<Long> longFilingIds = ArrayUtil.stringListToLongList(filingIds);

        Set<String> userErrors = new HashSet<>();

        if (filingIds == null || filingIds.size() == 0) {
            throw new ApheDataValidationException("filingIds", "Invalid filing ids");
        }

        // Make sure user's email address is verified.
        if (!isEmailAddressConfirmed()) {
            throw new ApheDataValidationException("filingIds", "Email address is not verified.");
        }

        List<Filing> filings = filingRepo.findByIdIn(longFilingIds);
        if (filings.size() != filingIds.size()) {
            logger.info("All the filingIds={} are not found domainId={}", ArrayUtil.listToString(longFilingIds, ","), getCurrentDomainId());
            throw new ApheForbiddenException();
        }

        for (Filing f : filings) {
            //For now block submitting a filing for another service, if an existing service is in progress..
            //This could be enhanced to check, if the service requested is in progress or finished..
            boolean isDraftFiling = isDraftFiling(f);
            if (!isDraftFiling) {
                String s = MessageFormat.format("Filing {0} is already being processed.", f.getId());
                throw new ApheDataValidationException("filingIds", "Invalid filing ids. Some filings are already being processed.");
            }

            //Check there are 4 submissions...
            if(!hasAllSubmissions(f)){
                String s = MessageFormat.format("Not all services are selected for filing {0} .", f.getId());
                throw new ApheDataValidationException("filingIds", "Invalid filing ids. Not all services were selected for some filings.");
            }

            //If Fed sub is not selected, it is a concern...
            FedSub fedSub = f.getFedSub();
            if(Boolean.TRUE != fedSub.getSelected()) {
                throw new ApheDataValidationException("filingIds", "Invalid filing ids. Selected filingIds do not have Federal submission selected.");
            }

            StateSub stateSub = f.getStateSub();
            if(Boolean.TRUE == stateSub.getSelected() && stateSub.getFilingMethod() == null) {
                throw new ApheDataValidationException("filingIds", "State filing selected, but state filing method not selected.");
            }

        }

        if (userErrors.size() > 0) {
            throw new ApheDataValidationException("filingIds", userErrors);
        }

        // Figure out the charge based on things being requested.
        HashMap<TNNService, Long> serviceCounts = new HashMap<>();
        serviceCounts.put(TNNService.FED_EFILE, 0L);
        serviceCounts.put(TNNService.STATE_CFSF, 0L);
        serviceCounts.put(TNNService.STATE_EFILE, 0L);
        serviceCounts.put(TNNService.RECIPIENT_PRINT_DELIVERY, 0L);
        serviceCounts.put(TNNService.RECIPIENT_EMAIL_DELIVERY, 0L);
        serviceCounts.put(TNNService.TIN_MATCH, 0L);

        Set<Long> tinMatchPayeeIds = new HashSet<>();
        for (Filing f : filings) {

            FedSub fedSub = f.getFedSub();
            if(Boolean.TRUE != fedSub.getIsPaid()) {
                serviceCounts.put(TNNService.FED_EFILE, serviceCounts.get(TNNService.FED_EFILE) + 1);
            }

            StateSub stateSub = f.getStateSub();
            if(Boolean.TRUE == stateSub.getSelected() && Boolean.TRUE != stateSub.getIsPaid()) {
                if(stateSub.getFilingMethod() == StateFilingMethod.CFSF) {
                    serviceCounts.put(TNNService.STATE_CFSF, serviceCounts.get(TNNService.STATE_CFSF) + 1);
                } else if(stateSub.getFilingMethod() == StateFilingMethod.EFile) {
                    serviceCounts.put(TNNService.STATE_EFILE, serviceCounts.get(TNNService.STATE_EFILE) + 1);
                }
            }

            PrintSub printSub = f.getPrintSub();
            if(Boolean.TRUE == printSub.getSelected() && Boolean.TRUE != printSub.getIsPaid()) {
                serviceCounts.put(TNNService.RECIPIENT_PRINT_DELIVERY, serviceCounts.get(TNNService.RECIPIENT_PRINT_DELIVERY) + 1);
            }

            EmailSub emailSub = f.getEmailSub();
            if(Boolean.TRUE == emailSub.getSelected() && Boolean.TRUE != emailSub.getIsPaid()) {
                serviceCounts.put(TNNService.RECIPIENT_EMAIL_DELIVERY, serviceCounts.get(TNNService.RECIPIENT_EMAIL_DELIVERY) + 1);
            }

            TINMatchRequest tinMatchRequest = f.getPayee().getTinMatchRequest();
            if(tinMatchRequest != null && Boolean.TRUE == tinMatchRequest.getSelected() && Boolean.TRUE != tinMatchRequest.getPaid()) {
                tinMatchPayeeIds.add(f.getPayee().getId());
            }
        }
        serviceCounts.put(TNNService.TIN_MATCH, Long.valueOf(tinMatchPayeeIds.size()));

        List<ChargeItemDTO> chargeItemDTOS = new ArrayList<>();
        for(TNNService tnnService : serviceCounts.keySet()) {
            ChargeItemDTO chargeItemDTO = new ChargeItemDTO(tnnService,serviceCounts.get(tnnService));
            chargeItemDTOS.add(chargeItemDTO);
        }
        InvoiceDTO invoice = billingMgr.generateInvoice(chargeItemDTOS);

        // Apply the charge and record it.
        SimpleDateFormat dateFormat = new SimpleDateFormat();
        Date filingSubmissionDate = new Date();
        String billingDescription = MessageFormat.format("eFile of {0} filings for Entity {1} on {2}", filings.size(), getCurrentDomainId(),
                dateFormat.format(filingSubmissionDate));
        boolean chargeSuccessful = billingMgr.useCredits(invoice.totalAmount, billingDescription);

        // Mark the filings as submitted.
        // TODO: Mark the filings as paid. Do we need to store the billing transaction Id as reference? -- later.
        if (chargeSuccessful) {
            List<String> payeeIds = tinMatchPayeeIds.stream().map(id->Long.toString(id)).collect(Collectors.toList());
            if(payeeIds.size() > 0) {
                tinMatchRequestManager.submitTINMatchRequestsWithFilings(payeeIds);
            }

            for (Filing f : filings) {
                /**
                 * TODO: To prevent users from making changes to the Payers and Payees.. Generate the JSON String needed for the EFS Filing... right here Or Block changes to the
                 * payees and payers when there are some filings with PROCESSING_STATUS as null. processing mode and have an aggressively running auto task.
                 */

                f.setTestFiling(isTestAccount);

                //TODO: set this from the UI. For now, set it to the current date.
                f.setFilingDate(new Date());

                FedSub fedSub = f.getFedSub();
                String submissionRefId = getCurrentDomainId() + "-" + f.getId() + "-" + RandomStringUtils.randomAlphanumeric(4);
                if(Boolean.TRUE ==  fedSub.getSelected()) {
                    fedSub.setPaid(true);
                    if(fedSub.getStatus() == FilingStatus.Draft) {
                        fedSub.setSubmissionRefId(submissionRefId);
                    }
                }

                StateSub stateSub = f.getStateSub();
                if(Boolean.TRUE == stateSub.getSelected()) {
                    stateSub.setPaid(true);
                    if(stateSub.getFilingMethod() == StateFilingMethod.CFSF) {
                        stateSub.setSubmissionRefId(submissionRefId);
                    } else if(stateSub.getFilingMethod() == StateFilingMethod.EFile || stateSub.getFilingMethod() == StateFilingMethod.SelfEFile) {
                        String stateRefId = getCurrentDomainId() + "-" + f.getId() + "-" + RandomStringUtils.randomAlphanumeric(4);
                        stateSub.setSubmissionRefId(stateRefId);
                    }
                }

                PrintSub printSub = f.getPrintSub();
                if(Boolean.TRUE == printSub.getSelected()) {
                    printSub.setPaid(true);
                }

                EmailSub emailSub = f.getEmailSub();
                if(Boolean.TRUE == emailSub.getSelected()) {
                    emailSub.setPaid(true);
                }

                filingsStatusManager.submitFiling(f, "Submitted by User", filingSubmissionDate);
                filingRepo.save(f);
            }
        } else {
            throw new ApheDataValidationException("_", "Not enough funds to submit the filings. Please add funds to your account before submitting.");
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#filingsIds, 'FILING', 'READ_FILING')")
    public String generateForms(List<String> filingsIds, boolean encrypt, PrintCopyType copyType) throws ApheDataValidationException, ApheException {

        return getDownloadURL(filingsIds, encrypt, copyType);

    }

    public String generate1096(String contactName,
                               String contactEmail,
                               FilingReturnType filingReturnType,
                               FilingYear filingYear,
                               int numberOfForms,
                               String fedTaxWithheld,
                               String totalAmountReported) throws ApheException {
        try {
            HashMap<String, Object> formData = new HashMap<>();

            DomainDTO domain = domainMgr.getDomain();
            String domainDisplayName = convertUtil.getDomainDisplayName(domain);
            formData.put("payerName", domainDisplayName);
            formData.put("payerAddressLine1Line2", convertUtil.getAddressLine(domain));
            formData.put("payerAddressCityStateZip", convertUtil.getCityStateZip(domain));

            formData.put("contactName", contactName);
            formData.put("contactEmail", contactEmail);
            formData.put("contactPhone", "");

            Domain theDomain = domainRepo.findById(domain.id).orElse(null);
            if(theDomain != null) {
                String tin = theDomain.getTinPlain();
                if(theDomain.getTinType() == TinType.EIN) {
                    formData.put("payerEIN", tin);
                } else {
                    formData.put("payerSSN", tin);
                }
            }

            formData.put("numberOfForms", numberOfForms);
            formData.put("federalTaxWithheld", fedTaxWithheld);
            formData.put("totalAmountReported", totalAmountReported);

            com.aphe.efs.model.enums.FilingYear efsFilingYear = com.aphe.efs.model.enums.FilingYear.getFilingYearForYear(filingYear.getYear());
            com.aphe.efs.model.enums.FilingReturnType efsFilingReturnTpype = null;
            switch (filingReturnType) {
                case Type_1099_INT:{
                    efsFilingReturnTpype = com.aphe.efs.model.enums.FilingReturnType.Type_1099_INT;
                    break;
                }
                case Type_1099_OID:{
                    efsFilingReturnTpype = com.aphe.efs.model.enums.FilingReturnType.Type_1099_OID;
                    break;
                }
                case Type_1099_MISC:{
                    efsFilingReturnTpype = com.aphe.efs.model.enums.FilingReturnType.Type_1099_MISC;
                    break;
                }
                case Type_1099_NEC:{
                    efsFilingReturnTpype = com.aphe.efs.model.enums.FilingReturnType.Type_1099_NEC;
                    break;
                }
                default:{
                    //TODO: clean up.
                    throw new ApheException("Invalid form type");
                }
            }
            return efsFilingsMgr.generate1096Form(domainDisplayName, efsFilingReturnTpype, efsFilingYear, formData);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApheException(e);
        }
    }

    private String getDownloadURL(List<String> filingsIds, boolean encrypt, PrintCopyType copyType) throws ApheException {
        if (filingsIds.size() == 0) {
            throw new ApheDataValidationException("filingIds", "Invalid filing ids");
        }
        List<Long> longFilingIds = ArrayUtil.stringListToLongList(filingsIds);
        List<Filing> filings = filingRepo.findByIdIn(longFilingIds);

        List<Filing> draftFilings = filings.stream().filter(filing -> (filing.getFedSub() == null || FilingStatus.draftStatuses.contains(filing.getFedSub().getStatus()))).collect(Collectors.toList());
        List<Filing> accessibleFilings = filings.stream().filter(filing -> (filing.getFedSub() != null && !FilingStatus.draftStatuses.contains(filing.getFedSub().getStatus()))).collect(Collectors.toList());

        List<Long> efsFilingIds = accessibleFilings.stream().map(filing -> filing.getFedSub().getFilingRequestId()).collect(Collectors.toList());
        List<Long> draftFilingIds = draftFilings.stream().map(filing -> filing.getId()).collect(Collectors.toList());

        boolean addAddressPage = false;
        if(PrintCopyType.RecipientMailCopy == copyType) {
            addAddressPage = true;
        }

        try {
            if (efsFilingIds.size() > 0) {
                return efsFilingsMgr.generateForms(efsFilingIds, encrypt, copyType.toString(), addAddressPage);
            } else if (draftFilingIds.size() > 0) {

                Map<Long, String> conversionErrors = new HashMap<>();
                Map<String, String> submissionErrors = new HashMap<>();
                Map<FilingDataDTO, Filing> convertedFilings = new HashMap<>();
                List<Filing> successfulFilings = new ArrayList<>();
                List<Filing> failedFilings = new ArrayList<>();
                for (Filing f : filings) {
                    try {
                        FilingDataDTO dto = convertUtil.convertToEFSFilingDTO(f, true);
                        convertedFilings.put(dto, f);
                    } catch (Exception e) {
                        logger.error("Error converting a filings to EFS DTO ", e);
                        conversionErrors.put(f.getId(), "Error processing your data. Please contact us.");
                        failedFilings.add(f);
                    }
                }
                List<FilingDataDTO> efsFilings = new ArrayList<>();
                efsFilings.addAll(convertedFilings.keySet());

                return efsFilingsMgr.generateDraftForms(efsFilings, encrypt, copyType.toString());
            } else {
                throw new ApheDataValidationException("filingIds", "Invalid filings ids. No filings that have been accepted.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApheException(e);
        }
    }

    public FilingDTO convertToDTO(Filing f) throws ApheException {
        return convertUtil.convertToDTO(f);
    }

    private List<FilingYear> buildFilingYearList(boolean currentYearOnly) {
        List<FilingYear> filingYears = new ArrayList<>();
        if(currentYearOnly) {
            filingYears.add(FilingYear.getCurrentFilingYear());
        } else {
            filingYears.addAll(Arrays.stream(FilingYear.values()).toList());
        }
        return filingYears;
    }

    //@PreAuthorize("hasPermission(#parentDomainId, 'ACCOUNTANT', 'READ_FILING')")
    public Long getFedSubsForAllClients(List<Long> payerIds, Date submittedAfter, Date submittedBefore, boolean currentYearOnly) {
        List<FilingYear> filingYears = buildFilingYearList(currentYearOnly);
        if (payerIds.size() > 0) {
            //Long submittedFilings = filingRepo.countFedSubByPayersAndFilingYear(payerIds, FilingYear.getCurrentFilingYear());
            Long submittedFilings = filingRepo.countFedSubByPayersAndDate(payerIds, filingYears, FilingStatus.Submitted, submittedAfter, submittedBefore);
            return submittedFilings;
        }
        return 0L;
    }

    //@PreAuthorize("hasPermission(#parentDomainId, 'ACCOUNTANT', 'READ_FILING')")
    public Long getStateSubsForAllClients(List<Long> payerIds, Date submittedAfter, Date submittedBefore, boolean currentYearOnly) {
        List<FilingYear> filingYears = buildFilingYearList(currentYearOnly);
        if (payerIds.size() > 0) {
            //Long submittedFilings = filingRepo.countStateSubByPayersAndFilingYear(payerIds, FilingYear.getCurrentFilingYear(), StateFilingMethod.EFile);
            Long submittedFilings = filingRepo.countStateSubByPayersAndDate(payerIds, filingYears, StateFilingStatus.Submitted, submittedAfter, submittedBefore);
            return submittedFilings;
        }
        return 0L;
    }

    //@PreAuthorize("hasPermission(#parentDomainId, 'ACCOUNTANT', 'READ_FILING')")
    public Long getPrintSubsForAllClients(List<Long> payerIds, Date submittedAfter, Date submittedBefore, boolean currentYearOnly) {
        List<FilingYear> filingYears = buildFilingYearList(currentYearOnly);
            if (payerIds.size() > 0) {
            //Long submittedFilings = filingRepo.countPrintSubByPayersAndFilingYear(payerIds, FilingYear.getCurrentFilingYear());
            Long submittedFilings = filingRepo.countPrintSubByPayersAndDate(payerIds, filingYears, PrintStatus.Submitted, submittedAfter, submittedBefore);
            return submittedFilings;
        }
        return 0L;
    }


    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_FILING')")
    public Long getFedSubs(Long domainId) {
        Payer payer = payerRepo.findByDomainId(domainId);
        if (payer != null) {
            Long submittedFilings = filingRepo.countFedSubByPayerAndFilingYear(payer, FilingYear.getCurrentFilingYear());
            return submittedFilings;
        }
        return 0L;
    }

    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_FILING')")
    public Long getStateSubs(Long domainId) {
        Payer payer = payerRepo.findByDomainId(domainId);
        if (payer != null) {
            Long submittedFilings = filingRepo.countStateSubByPayerAndFilingYear(payer, FilingYear.getCurrentFilingYear(), StateFilingMethod.EFile);
            return submittedFilings;
        }
        return 0L;
    }



    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<Filing> waitOnTINMatchByIds(List<Long> filingIds) {
        List<Filing> filings = new ArrayList<>();
        Date statusChangeDate = new Date();
        for (Long filingId : filingIds) {
            Filing filing = filingRepo.findById(filingId).orElse(null);
            if (filing != null) {
                filings.add(filing);
                filingsStatusManager.waitOnTINMatchFedFiling(filing, "TIN match for payee in progress", statusChangeDate);
                filingsStatusManager.waitOnTINMatchStateFiling(filing, "TIN match for payee in progress.", statusChangeDate);
            }
        }
        return filings;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public Filing receiveFilingBySubRefId(String submissionRefId, long efsFilingId) {
        Filing filing = null;
        FedSub fedSub = fedSubRepository.findBySubmissionRefId(submissionRefId);
        if (fedSub != null) {
            filing = fedSub.getFiling();
            receiveFedSubByRefId(efsFilingId, fedSub);
        }
        // Because of CFSF.. there could be state sub with same refId. Update that one too.
        // In case of direct state efile, fed sub for this subRefId will be null and only state sub will be there.
        StateSub stateSub = stateSubRepository.findBySubmissionRefId(submissionRefId);
        if(stateSub != null) {
            filing = stateSub.getFiling();
            receiveStateSubByRefId(efsFilingId, stateSub);
        }
        return filing;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void receiveStateFilingBySubRefId(String submissionRefId, long efsFilingId) {
        StateSub stateSub = stateSubRepository.findBySubmissionRefId(submissionRefId);
        if(stateSub != null) {
            receiveStateSubByRefId(efsFilingId, stateSub);
        }

    }

    private void receiveStateSubByRefId(long efsFilingId, StateSub stateSub) {
        stateSub.setFilingRequestId(efsFilingId);
        Date statusChangeDate = new Date();
        filingsStatusManager.receiveStateFiling(stateSub.getFiling(), "Filing received by backend system", statusChangeDate);
        stateSubRepository.save(stateSub);
    }

    private void receiveFedSubByRefId(long efsFilingId, FedSub fedSub) {
        fedSub.setFilingRequestId(efsFilingId);
        Date statusChangeDate = new Date();
        filingsStatusManager.receiveFedFiling(fedSub.getFiling(), "Filing received by backend system", statusChangeDate);
        fedSubRepository.save(fedSub);
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<Filing> systemRejectFilingsByIds(Map<Long, String> filingIds) throws ApheDataValidationException {
        List<Filing> filings = new ArrayList<>();
        Date statusChangeDate = new Date();
        for (Long filingId : filingIds.keySet()) {
            String description = filingIds.get(filingId);
            Filing filing = filingRepo.findById(filingId).orElse(null);
            if (filing != null) {
                filings.add(filing);
                filingsStatusManager.draftFedFiling(filing, description, statusChangeDate);
                filingsStatusManager.draftStateFiling(filing, description, statusChangeDate);
            }
        }
        return filings;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<Filing> systemRejectFilingsBySubmissionRefIds(Map<String, String> submissionRefIds) throws ApheDataValidationException {
        List<Filing> filings = new ArrayList<>();
        Date statusChangeDate = new Date();
        for (String subRefId : submissionRefIds.keySet()) {
            String description = submissionRefIds.get(subRefId);
            FedSub fedSub = fedSubRepository.findBySubmissionRefId(subRefId);
            if (fedSub != null) {
                filings.add(fedSub.getFiling());
                filingsStatusManager.draftFedFiling(fedSub.getFiling(), description, statusChangeDate);
            } else {
                StateSub stateSub = stateSubRepository.findBySubmissionRefId(subRefId);
                if(stateSub != null) {
                    filings.add(stateSub.getFiling());
                    filingsStatusManager.draftStateFiling(stateSub.getFiling(), description, statusChangeDate);
                }

            }
        }
        return filings;
    }

    /**
     * Called by the update filing task to receive updates from EFS.
     *
     * @param submissionRefId
     * @param newStatus
     * @param description
     * @param statusChangeDate
     * @return
     */
    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public Filing updateFedFilingStatusByRefId(String submissionRefId, FilingStatus newStatus, String description, Date statusChangeDate, String uniqueRecordId) {
        FedSub fedSub = fedSubRepository.findBySubmissionRefId(submissionRefId);
        if (fedSub != null) {
            Filing f1 = updateFedSubByRefId(newStatus, description, statusChangeDate, uniqueRecordId, fedSub);
            if (f1 != null) return f1;
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public Filing updateStateFilingStatusByRefId(String submissionRefId, FilingStatus newStatus, String description, Date statusChangeDate, String uniqueRecordId) {
        StateSub stateSub = stateSubRepository.findBySubmissionRefId(submissionRefId);
        if(stateSub != null) {
            StateFilingStatus newStateFilingStatus = StateFilingStatus.getStateFilingStatusByName(newStatus.name());
            if(newStateFilingStatus == null) {
                if(newStatus == FilingStatus.Draft) {
                    newStateFilingStatus = StateFilingStatus.None;
                } else {
                    logger.error("Attention Required: Invalid state filing status: " + newStatus.name());
                    return null;
                }
            }
            Filing f1 = updateStateSubByRefId(newStateFilingStatus, description, statusChangeDate, uniqueRecordId, stateSub);
            if (f1 != null) return f1;
        }
        return null;
    }

    @Nullable
    private Filing updateFedSubByRefId(FilingStatus newStatus, String description, Date statusChangeDate, String uniqueRecordId, FedSub f) {
        if(newStatus != f.getStatus()) {
            if (newStatus == FilingStatus.Accepted) {
                filingsStatusManager.acceptFedSub(f.getFiling(), description, statusChangeDate, uniqueRecordId);
            } else if (newStatus == FilingStatus.Draft) {
                filingsStatusManager.draftFedFiling(f.getFiling(), description, statusChangeDate);
            } else if (newStatus == FilingStatus.Sent_To_Agency) {
                filingsStatusManager.sentToAgency(f.getFiling(), description, statusChangeDate);
            } else {
                //just update the status and record history.
                //this change should not affect print and email status.
                f.setStatus(newStatus);
                f.setStatusChangeDate(statusChangeDate);
                f.setStatusChangeDesc(description);
                filingsStatusManager.updateFedFilingStatusHistory(f);
            }
            fedSubRepository.save(f);
            return f.getFiling();
        }
        return null;
    }

    @Nullable
    private Filing updateStateSubByRefId(StateFilingStatus newStatus, String description, Date statusChangeDate, String uniqueRecordId, StateSub stateSub) {
        if(newStatus != stateSub.getStatus()) {
            if (newStatus == StateFilingStatus.Accepted) {
                filingsStatusManager.acceptStateSub(stateSub.getFiling(), description, statusChangeDate, uniqueRecordId);
            } else if (newStatus == StateFilingStatus.None) {
                filingsStatusManager.draftStateFiling(stateSub.getFiling(), description, statusChangeDate);
            } else {
                //just update the status and record history.
                //this change should not affect print and email status.
                stateSub.setStatus(newStatus);
                stateSub.setStatusChangeDate(statusChangeDate);
                stateSub.setStatusChangeDesc(description);
                filingsStatusManager.updateStateFilingStatusHistory(stateSub);
            }
            stateSubRepository.save(stateSub);
            return stateSub.getFiling();
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updatePrintCopyTrackingId(String subRefId, Date expectedDeliveryDate, String trackingId) throws ApheDataValidationException {
        FedSub fedSub = fedSubRepository.findBySubmissionRefId(subRefId);
        Filing f = fedSub.getFiling();
        PrintSub printSub = f.getPrintSub();
        if (printSub != null) {
            printSub.setPrintCopyTrackingId(trackingId);
            printSub.setPrintCopyExpectedDeliveryDate(expectedDeliveryDate);
            filingsStatusManager.updatePrintStatus(printSub, PrintStatus.Mailing, "Submitted to print system", new Date());
        }
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updatePrintStatusToFail(String subRefId, String message) throws ApheDataValidationException {
        FedSub fedSub = fedSubRepository.findBySubmissionRefId(subRefId);
        Filing f = fedSub.getFiling();
        PrintSub printSub = f.getPrintSub();
        if (printSub != null) {
            filingsStatusManager.updatePrintStatus(printSub, PrintStatus.Errored, "Submitting to print system failed: " + message, new Date());
        }
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updateEmailTrackingId(long emailSubId, String trackingId) throws ApheDataValidationException {
        EmailSub emailSub = emailSubRepository.findById(emailSubId).orElse(null);
        if (emailSub != null) {
            emailSub.setEmailTrackingId(trackingId);
            filingsStatusManager.updateEmailStatus(emailSub, EmailStatus.Emailed, "Submitted to email system", new Date());
        }
    }


    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updateEmailStatusToBounced(long emailSubId, String message) throws ApheDataValidationException {
        EmailSub emailSub = emailSubRepository.findById(emailSubId).orElse(null);
        if (emailSub != null) {
            filingsStatusManager.updateEmailStatus(emailSub, EmailStatus.Bounced, "Sending email failed with message : " + message, new Date());
        }
    }


    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updateEmailStatusToFail(long emailSubId, String message) throws ApheDataValidationException {
        EmailSub emailSub = emailSubRepository.findById(emailSubId).orElse(null);
        if (emailSub != null) {
            filingsStatusManager.updateEmailStatus(emailSub, EmailStatus.Errored, "Sending email failed with message : " + message, new Date());
        }
    }

//    @Transactional
//    @PreAuthorize("hasAuthority('superadmin')")
//    public List<Filing> findByStatus(FilingStatus filingStatus) {
//        return filingRepo.findByStatus(filingStatus);
//    }

//    @Transactional
//    @PreAuthorize("hasAuthority('superadmin')")
//    public List<Filing> findByStatusIn(List<FilingStatus> filingStatuses) {
//        return filingRepo.findByStatusIn(filingStatuses);
//    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<Filing> findByYearsIn(List<FilingYear> filingYears) {
        return filingRepo.findByYearsIn(filingYears);
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<Filing> findByStatusInAndYearsIn(List<FilingStatus> filingStatuses, List<FilingYear> filingYears) {
        return filingRepo.findByStatusInAndYearsIn(filingStatuses, filingYears);
    }



//    @Transactional
//    @PreAuthorize("hasAuthority('superadmin')")
//    public List<Filing> findByPrintStatus(PrintStatus printStatus) {
//        return filingRepo.findByPrintStatus(printStatus);
//    }
//
//    @Transactional
//    @PreAuthorize("hasAuthority('superadmin')")
//    public List<Filing> findByEmailStatus(EmailStatus emailStatus) {
//        return filingRepo.findByEmailStatus(emailStatus);
//    }

//    @Transactional
//    @PreAuthorize("hasAuthority('superadmin')")
//    public List<Filing> getFilingsEmailNotifiable() {
//        return filingRepo.findByEmailNotifyTrue();
//    }

//    @Transactional
//    @PreAuthorize("hasAuthority('superadmin')")
//    public List<Filing> getFilingsPrintNotifiable() {
//        return filingRepo.findByPrintNotifyTrue();
//    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updateEmailNotify(List<Long> filingIds, boolean notify) throws ApheDataValidationException, ApheForbiddenException {
        List<Filing> filings = filingRepo.findByIdIn(filingIds);
        for (Filing f : filings) {
            EmailSub emailSub = f.getEmailSub();
            emailSub.setEmailNotify(notify);
        }
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updatePrintNotify(List<Long> filingIds, boolean notify) throws ApheDataValidationException, ApheForbiddenException {
        List<Filing> filings = filingRepo.findByIdIn(filingIds);
        for (Filing f : filings) {
            PrintSub printSub = f.getPrintSub();
            printSub.setPrintNotify(notify);
        }
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<PrintEmailRequestDTO> getPrintEmailRequestsByPrintStatus(String printStatus, String filingYear) {
        PrintStatus printStatusEnum = PrintStatus.valueOf(printStatus);
        FilingYear filingYearEnum = FilingYear.getFilingYearForYear(filingYear);
        List<FilingStatus> filingStatuses = Arrays.asList(new FilingStatus[]{FilingStatus.Accepted, FilingStatus.Sent_To_Agency});
        List<Filing> filings = filingRepo.findByPrintStatusAndFilingYearAndStatusIn(printStatusEnum, filingYearEnum, filingStatuses);
        return convertToPrintEmailRequests(filings);
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<PrintEmailRequestDTO> getPrintEmailRequestsByEmailStatus(String emailStatus, String filingYear) {
        EmailStatus emailStatusForEnum = EmailStatus.valueOf(emailStatus);
        FilingYear filingYearEnum = FilingYear.getFilingYearForYear(filingYear);
        List<FilingStatus> filingStatuses = Arrays.asList(new FilingStatus[]{FilingStatus.Accepted, FilingStatus.Sent_To_Agency});
        List<Filing> filings = filingRepo.findByEmailStatusAndFilingYearAndStatusIn(emailStatusForEnum, filingYearEnum, filingStatuses);
        return convertToPrintEmailRequests(filings);
    }

    private List<PrintEmailRequestDTO> convertToPrintEmailRequests(List<Filing> filings) {
        List<PrintEmailRequestDTO> printEmailRequests = new ArrayList<>();
        for (Filing f : filings) {
            PrintEmailRequestDTO printRequestDTO = convertUtil.convertFilingToPrintRequestDTO(f);
            printEmailRequests.add(printRequestDTO);
        }
        return printEmailRequests;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updatePrintStatus(UpdatePrintRequestsInput input) {
        PrintStatus printStatusEnum = PrintStatus.valueOf(input.printStatus);
        Date statusChangeDate = new Date();
        for (Long filingId : input.filingIds) {
            filingsStatusManager.updatePrintStatusByFilingId(filingId, printStatusEnum, "Updating print status from admin console", statusChangeDate);
        }
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void updateEmailStatus(UpdateEmailRequestsInput input) {
        EmailStatus emailStatusEnum = EmailStatus.valueOf(input.emailStatus);
        Date statusChangeDate = new Date();
        for (Long filingId : input.filingIds) {
            filingsStatusManager.updateEmailStatusByFilingId(filingId, emailStatusEnum, "Updating email status from admin console", statusChangeDate);
        }
    }

    /**
     * Access filings from customer side... or filings issued to this domain.
     */
    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_CUSTOMER')")
    public List<FilingDTO> getFilingsIssued(Long domainId) throws ApheForbiddenException, ApheException {
        List<Filing> filings = getAllowedFilings();
        List<FilingDTO> filingDTOs = convertToDTOs(filings);
        return filingDTOs;
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_CUSTOMER')")
    public String generateFormsForFilingsIssued(Long domainId, List<String> filingsIds, boolean encrypt, PrintCopyType copyType) throws ApheDataValidationException, ApheException {
        List<Filing> filings = getAllowedFilings();
        List<String> allowedFilingsIds = filings.stream().map(f -> Long.toString(f.getId())).collect(Collectors.toList());
        if (allowedFilingsIds.containsAll(filingsIds)) {
            return getDownloadURL(filingsIds, encrypt, copyType);
        } else {
            throw new ApheDataValidationException("filingIds", "Invalid filings ids.");
        }
    }

    private List<Filing> getAllowedFilings() {
        List<Long> payeeIds = getDomainSourceEntityIds("Vendor");
        if(payeeIds != null && payeeIds.size() > 0) {
            List<Filing> filings = filingRepo.findByPayeeIdIn(payeeIds);
            return filings.stream().filter(f -> f.getFedSub().getStatus() == FilingStatus.Sent_To_Agency || f.getFedSub().getStatus() == FilingStatus.Accepted).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }



    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_FILING')")
    public int selfEFileCount(Long payerId, String filingYear) throws ApheForbiddenException, ApheException {
        Payer payer = payerRepo.findById(payerId).orElse(null);
        FilingYear filingYearEnum = FilingYear.getFilingYearForYear(filingYear);
        if(filingYearEnum != null) {
            return filingRepo.selfEFileCount(payer, filingYearEnum, StateFilingMethod.SelfEFile);
        }
        return 0;
    }

    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_FILING')")
    public PagedResult<FilingDTO> getSelfEFileFilingsByPayer(Long payerId, List<String> statuses, List<String> years, List<String> types, int pageSize, int pageNumber) throws ApheForbiddenException, ApheException {

        Payer payer = payerRepo.findById(payerId).orElse(null);
        List<StateFilingStatus> stateFilingStatuses = getStateFilingStatuses(statuses);
        stateFilingStatuses.remove(StateFilingStatus.None);
        List<FilingYear> filingYears = getFilingYears(years);
        List<FilingReturnType> filingTypes = getFilingReturnTypes(types);

        Page<Filing> filings = filingRepo.findByPayerAndStateFilingStatusInAndFilingYearInAndFormTypeIn(
                payer, stateFilingStatuses, filingYears, filingTypes, StateFilingMethod.SelfEFile, PageRequest.of(pageNumber, pageSize, Sort.by("id"))
        );
        List<FilingDTO> filingDTOs = convertToDTOs(filings.getContent());
        PagedResult<FilingDTO> result = new PagedResult<>();
        result.data = filingDTOs;
        result.pageMetadata = buildPageMetadata(filings);
        return result;
    }

    @NotNull
    private List<FilingReturnType> getFilingReturnTypes(List<String> types) {
        List<FilingReturnType> filingTypes = new ArrayList<>();
        if(types != null && types.size() > 0) {
            for(String s : types) {
                FilingReturnType filingType = FilingReturnType.getFilingReturnTypeForForm(s);
                if(filingType != null) {
                    filingTypes.add(filingType);
                }
            }
        }
        if(filingTypes.size() == 0) {
            filingTypes.addAll(Stream.of(FilingReturnType.values()).collect(Collectors.toList()));
        }
        return filingTypes;
    }

    @NotNull
    private List<FilingYear> getFilingYears(List<String> years) {
        List<FilingYear> filingYears = new ArrayList<>();
        if(years != null && years.size() > 0) {
            for(String s : years) {
                FilingYear filingYear = FilingYear.getFilingYearForYear(s);
                if(filingYear != null) {
                    filingYears.add(filingYear);
                }
            }
        }
        if(filingYears.size() == 0) {
            filingYears.addAll(Stream.of(FilingYear.values()).collect(Collectors.toList()));
        }
        return filingYears;
    }

    @NotNull
    private List<StateFilingStatus> getStateFilingStatuses(List<String> statuses) {
        List<StateFilingStatus> filingStatuses = new ArrayList<>();
        if(statuses != null && statuses.size() > 0) {
            for(String s : statuses) {
                StateFilingStatus filingStatus = StateFilingStatus.getStateFilingStatusByName(s);
                if(filingStatus != null) {
                    filingStatuses.add(filingStatus);
                }
            }
        }
        if(filingStatuses.size() == 0) {
            filingStatuses.addAll(Stream.of(StateFilingStatus.values()).collect(Collectors.toList()));
        }
        return filingStatuses;
    }

    @PreAuthorize("hasPermission(#filingsIds, 'FILING', 'READ_FILING')")
    public List<String> getTransmissionFiles(List<String> filingsIds) throws Exception {
        List<Long> longFilingIds = filingsIds.stream().map(Long::parseLong).collect(Collectors.toList());
        List<Filing> filings = filingRepo.findByIdIn(longFilingIds);

        //Each filing should have state sub with a filing method of state efile, and status as received.
        List<Filing> stateEFileFilings = filings.stream().filter(f ->
                f.getStateSub().getFilingMethod() == StateFilingMethod.SelfEFile &&
                        f.getStateSub().getStatus() == StateFilingStatus.ReadyForDownload).collect(Collectors.toList());
        if (stateEFileFilings.size() != filings.size()) {
            throw new ApheDataValidationException("filingsIds", "Invalid filings ids.");
        }

        //Now collect efs filing of these filings.
        List<Long> efsFilingIds = stateEFileFilings.stream().map(f -> f.getStateSub().getFilingRequestId()).collect(Collectors.toList());
        if (stateEFileFilings.size() != efsFilingIds.size()) {
            logger.error("Attention Required: efsFilingIds count is not equal to filings count.");
            throw new ApheDataValidationException("_", "Error generating transmission files.");
        }

        return tnnTransmissionOrchestrator.generateStateSelfEFileTransmissions(efsFilingIds);
    }

    public boolean send1099Form(String filingId) throws Exception {
        Filing f = filingRepo.findById(Long.parseLong(filingId)).orElse(null);
        if (f != null) {
            if (f.getFedSub().getStatus() == FilingStatus.Sent_To_Agency || f.getFedSub().getStatus() == FilingStatus.Accepted) {
                String messageId = mailUtil.send1099Form(f);
                return true;
            } else {
                throw new ApheDataValidationException("filingId", "Invalid filing id.");
            }
        }
        return false;
    }
}
