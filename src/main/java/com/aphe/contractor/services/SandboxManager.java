package com.aphe.contractor.services;

import com.aphe.billing.service.BillingMgr;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.contractor.dto.AddEditPayeeInput;
import com.aphe.contractor.dto.AddEditPayerInput;
import com.aphe.contractor.dto.CAddressInput;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.model.enums.CountryCode;
import com.aphe.contractor.model.enums.EntityType;
import com.aphe.contractor.model.enums.TinType;
import com.aphe.insights.service.InsightsManager;
import com.github.javafaker.Faker;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
public class SandboxManager extends CommonTNNManager {

	@Autowired
	PayerManager payerManager;

	@Autowired
	PayeeManager payeeManger;
	
	@Autowired
	BillingMgr billingMgr;

	@Autowired
	InsightsManager insightsManager;

	public void seedAccount(long domainId) throws ApheDataValidationException, ApheForbiddenException {

		PayerDTO payerDTO = payerManager.getPayerByDomainId(domainId);
		Faker faker = new Faker(new Locale(Locale.US.getCountry()));
		
		AddEditPayerInput payerInput = createFakePayer(faker);
		payerInput.id = payerDTO.id;
		long payerId = payerManager.updatePayer(payerInput);

		PayerDTO updatePayerDTO = payerManager.getPayer(Long.toString(payerId));

		insightsManager.updateTINInsight(updatePayerDTO.domainId);

		for (int i = 0; i < 4; i++) {
			AddEditPayeeInput addEditPayeeInput = createFakePayee(faker);
			payeeManger.createPayee(payeeManger.getCurrentDomainId(), addEditPayeeInput);
		}
		
		billingMgr.addPromoCreditsPrivate(10000, "Promo funds for sandbox companies");

	}

	private AddEditPayeeInput createFakePayee(Faker faker) {
		AddEditPayeeInput payeeInput = new AddEditPayeeInput();

		payeeInput.entityType = EntityType.Individual;
		payeeInput.businessName = faker.company().name().replaceAll("[^A-Za-z0-9\\-& ]", "");
		payeeInput.firstName = faker.name().firstName().replaceAll("[^A-Za-z0-9\\-& ]", "");
		payeeInput.lastName = faker.name().lastName().replaceAll("[^A-Za-z0-9\\-& ]", "");
		payeeInput.tinType = TinType.SSN;
		payeeInput.tin = faker.idNumber().ssnValid();
		
		payeeInput.phoneNumber = faker.phoneNumber().cellPhone();
		payeeInput.phoneExt = faker.phoneNumber().extension();
		payeeInput.emailAddress = payeeInput.firstName.substring(0, 1) + "." + payeeInput.lastName + "@mailinator.com";

		payeeInput.address = new CAddressInput();
		payeeInput.address.line1 = faker.address().streetAddress();
		payeeInput.address.city = faker.address().city();
		payeeInput.address.state = faker.address().stateAbbr();
		payeeInput.address.country = CountryCode.US;
		payeeInput.address.postalCode = faker.address().zipCode();
		
		
		return payeeInput;
	}
	
	private AddEditPayerInput createFakePayer(Faker faker) {
		AddEditPayerInput payerInput = new AddEditPayerInput();

//		payerInput.businessName = faker.company().name();  
//		payerInput.firstName = faker.name().firstName();
//		payerInput.lastName = faker.name().lastName();

		payerInput.tinType = TinType.SSN;
		payerInput.tin = faker.idNumber().ssnValid();

		payerInput.phoneNumber = faker.phoneNumber().cellPhone();
		payerInput.phoneExt = faker.phoneNumber().extension();
		payerInput.emailAddress = RandomStringUtils.random(10, true, true) + "@mailinator.com";

		payerInput.address = new CAddressInput();
		payerInput.address.line1 = faker.address().streetAddress();
		payerInput.address.city = faker.address().city();
		payerInput.address.state = faker.address().stateAbbr();
		payerInput.address.country = CountryCode.US;
		payerInput.address.postalCode = faker.address().zipCode();
		return payerInput;
	}

}
