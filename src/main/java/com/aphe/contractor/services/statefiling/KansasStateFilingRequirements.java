package com.aphe.contractor.services.statefiling;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.StateCode;
import com.aphe.contractor.model.enums.StateFilingMethod;

import java.math.BigDecimal;
import java.util.List;

public class KansasStateFilingRequirements extends StateFilingRequirements {

    String deptName = "Department of Revenue";

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType) {
        return true;
    }

    @Override
    public List<StateFilingMethod> getSupportedStateFilingMethods(FilingReturnType filingReturnType) {
        return cfsfAndSelfFile;
    }

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        boolean meetsFederalThreshold = meetsFederalThreshold(stateIncome);
        boolean hasStateTaxWithheld = hasStateTaxWithheld(stateTaxWithheld);
        switch (filingReturnType) {
            case Type_1099_NEC:
            case Type_1099_MISC: {
                return meetsFederalThreshold || hasStateTaxWithheld;
            }
            case Type_1099_INT:
            case Type_1099_OID:
                return true;
            default:
                throw new RuntimeException("unknown form type");
        }
    }

    @Override
    public List<StateFilingMethod> getAllowedStateFilingMethods(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        return cfsfAndSelfFile;
    }

    @Override
    public String getDeptName() {
        return deptName;
    }
}
