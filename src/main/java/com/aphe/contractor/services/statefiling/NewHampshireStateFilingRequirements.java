package com.aphe.contractor.services.statefiling;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.StateCode;
import com.aphe.contractor.model.enums.StateFilingMethod;

import java.math.BigDecimal;
import java.util.List;

public class NewHampshireStateFilingRequirements extends StateFilingRequirements {

    String deptName = "The State of New Hampshire";

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType) {
        return false;
    }

    @Override
    public List<StateFilingMethod> getSupportedStateFilingMethods(FilingReturnType filingReturnType) {
        return noOptions;
    }

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        return false;
    }

    @Override
    public List<StateFilingMethod> getAllowedStateFilingMethods(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        return noOptions;
    }

    @Override
    public String getDeptName() {
        return deptName;
    }


}
