package com.aphe.contractor.services.statefiling;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.StateCode;
import com.aphe.contractor.model.enums.StateFilingMethod;

import java.math.BigDecimal;
import java.util.List;

public class WisconsinStateFilingRequirements extends StateFilingRequirements {

    private String deptName = "Department of Revenue";

    private String defaultWithholdingNumber = "036888888888801";


    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType) {
        return true;
    }

    @Override
    public List<StateFilingMethod> getSupportedStateFilingMethods(FilingReturnType filingReturnType) {
        return cfsfAndSelfFile;
    }


    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        switch (filingReturnType) {
            case Type_1099_NEC:
            case Type_1099_MISC:
                return true;
            case Type_1099_INT:
            case Type_1099_OID:
                return hasStateTaxWithheld(stateTaxWithheld);
            default:
                throw new RuntimeException("unknown form type");
        }
    }

    @Override
    public List<StateFilingMethod> getAllowedStateFilingMethods(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        switch (filingReturnType) {
            case Type_1099_NEC:
            case Type_1099_MISC:
            case Type_1099_INT:
            case Type_1099_OID:
                if(hasStateTaxWithheld(stateTaxWithheld)) {
                    return selfFileOnly;
                } else {
                    return cfsfAndSelfFile;
                }
            default:
                throw new RuntimeException("unknown form type");
        }
    }

    @Override
    public String getDeptName() {
        return deptName;
    }




}
