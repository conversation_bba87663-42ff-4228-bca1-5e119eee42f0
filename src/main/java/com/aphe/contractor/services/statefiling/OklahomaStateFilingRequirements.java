package com.aphe.contractor.services.statefiling;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.StateCode;
import com.aphe.contractor.model.enums.StateFilingMethod;

import java.math.BigDecimal;
import java.util.List;

public class OklahomaStateFilingRequirements extends StateFilingRequirements {

    String deptName = "Tax Commission";

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType) {
        return true;
    }

    @Override
    public List<StateFilingMethod> getSupportedStateFilingMethods(FilingReturnType filingReturnType) {
        switch (filingReturnType) {
            case Type_1099_INT:
            case Type_1099_MISC:
            case Type_1099_OID:
                return cfsfAndEFileAndSelfFile;
            case Type_1099_NEC:
                return eFileAndSelf;
            default:
                throw new RuntimeException("Unsupported form");
        }
    }

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        switch (filingReturnType) {
            case Type_1099_NEC:
            case Type_1099_MISC:
            case Type_1099_INT:
            case Type_1099_OID:
                return true;
            default:
                throw new RuntimeException("unknown form type");
        }
    }

    @Override
    public List<StateFilingMethod> getAllowedStateFilingMethods(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        boolean isStateTaxWithheld = hasStateTaxWithheld(stateTaxWithheld);
        switch (filingReturnType) {
            case Type_1099_NEC:
                return eFileAndSelf;
            case Type_1099_MISC:
            case Type_1099_INT:
            case Type_1099_OID:
                return cfsfAndEFileAndSelfFile;
            default:
                throw new RuntimeException("unknown form type");
        }
    }

    @Override
    public String getDeptName() {
        return deptName;
    }


}
