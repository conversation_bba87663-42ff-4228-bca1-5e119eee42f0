package com.aphe.contractor.services.statefiling;

import com.aphe.contractor.model.enums.FilingReturnType;
import com.aphe.contractor.model.enums.StateCode;
import com.aphe.contractor.model.enums.StateFilingMethod;

import java.math.BigDecimal;
import java.util.List;

public class OregonStateFilingRequirements extends StateFilingRequirements {

    String deptName = "Department of Revenue";

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType) {
        return true;
    }

    @Override
    public List<StateFilingMethod> getSupportedStateFilingMethods(FilingReturnType filingReturnType) {
        return eFileAndSelf;
    }

    @Override
    public boolean isStateFilingRequired(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        switch (filingReturnType) {
            case Type_1099_NEC:
            case Type_1099_MISC:
            case Type_1099_INT:
            case Type_1099_OID:
                return true;
            default:
                throw new RuntimeException("unknown form type");
        }
    }

    @Override
    public List<StateFilingMethod> getAllowedStateFilingMethods(FilingReturnType filingReturnType, BigDecimal stateIncome, BigDecimal stateTaxWithheld, StateCode payerState, StateCode payeeState, StateCode workState) {
        switch (filingReturnType) {
            case Type_1099_NEC:
            case Type_1099_MISC:
            case Type_1099_INT:
            case Type_1099_OID:
                return eFileAndSelf;
            default:
                throw new RuntimeException("unknown form type");
        }
    }

    @Override
    public String getDeptName() {
        return deptName;
    }

    @Override
    public List<StateFilingMethod> getAllowedFilingMethodsForCorrections(FilingReturnType filingReturnType) {
        return eFileAndSelf;
    }

}
