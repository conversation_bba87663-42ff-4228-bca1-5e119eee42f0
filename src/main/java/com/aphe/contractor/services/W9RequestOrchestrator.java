package com.aphe.contractor.services;

import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

@Component
@Service
public class W9RequestOrchestrator extends CommonTNNManager {

    @Autowired
    W9RequestManager w9RequestManager;


    /**
     * Submit W9 requests for payees. Used from the payees screen. Charges for the submissions.
     *
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    public void submitW9Requests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        w9RequestManager.submitW9Requests(payeeIds);

        //Fire async tosk send emails to the payees immediately. This transaction should be independent of the main transaction.
        w9RequestManager.sendW9RequestEmails(payeeIds);

    }

    /**
     * Remind about W-9 Requests in progress.
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    public void remindW9Requests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        //First reset the status of the requests to be reminded to 'Submitted'.
        w9RequestManager.remindW9Requests(payeeIds);

        //Fire async tosk send emails to the payees immediately. This transaction should be independent of the main transaction.
        w9RequestManager.sendW9RequestEmails(payeeIds);
    }

    public String saveW9(String id, byte[] bytes) throws ApheForbiddenException {
        String globaId = w9RequestManager.saveW9(id, bytes);

        //Fire async method to apply this data to the payee.
        w9RequestManager.applyW9InfoAndSendEmail(globaId);

        return globaId;
    }


}
