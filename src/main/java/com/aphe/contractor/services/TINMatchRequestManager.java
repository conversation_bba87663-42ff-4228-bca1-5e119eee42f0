package com.aphe.contractor.services;

import com.aphe.billing.dto.ChargeItemDTO;
import com.aphe.billing.dto.InvoiceDTO;
import com.aphe.billing.model.enums.TNNService;
import com.aphe.billing.service.BillingMgr;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.util.ArrayUtil;
import com.aphe.common.util.DateUtil;
import com.aphe.contractor.dto.read.TINMatchRequestDTO;
import com.aphe.contractor.model.Payee;
import com.aphe.contractor.model.Payer;
import com.aphe.contractor.model.TINMatchRequest;
import com.aphe.contractor.model.TINMatchRequestStatusEntity;
import com.aphe.contractor.model.enums.FilingYear;
import com.aphe.contractor.model.enums.TINMatchRequestStatus;
import com.aphe.contractor.model.enums.TINMatchResult;
import com.aphe.contractor.model.enums.TINMatchStatus;
import com.aphe.contractor.repo.PayeeRepository;
import com.aphe.contractor.repo.PayerRepository;
import com.aphe.contractor.repo.TINMatchRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Service
public class TINMatchRequestManager extends CommonTNNManager {


    @Autowired
    PayeeRepository payeeRepository;

    @Autowired
    PayerRepository payerRepo;

    @Autowired
    TINMatchRequestRepository tinMatchRequestRepository;

    @Autowired
    ContractorConvertUtil convertUtil;

    @Autowired
    BillingMgr billingMgr;

//    @Autowired
//    UpdateTINMatchRequestStatusMultiNodeTask updateTINMatchRequestStatusMultiNodeTask;

    /**
     * Get TINMatch requests for payees.
     * @param payeeIds
     * @return
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public List<TINMatchRequestDTO> getTINMatchRequests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        List<TINMatchRequest> tinMatchRequests = new ArrayList<>();
        for (Payee p : payees) {
            TINMatchRequest tinMatchRequest = createOrGetTINMatchRequest(p);
            tinMatchRequests.add(tinMatchRequest);
        }
        List<TINMatchRequestDTO> dtos = tinMatchRequests.stream().map(e -> {
            TINMatchRequestDTO dto = convertUtil.toDTO(e);
            dto.payeeId = e.getPayee().getId();
            return dto;
        }).toList();
        return dtos;
    }

    /**
     * Create TINMatch requests for payees. Used from the payees screen. Throws an exception if the payee doesn't have TIN and Name.
     * @param payeeIds
     * @return
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public List<TINMatchRequestDTO> createTINMatchRequests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, true, true);
        List<TINMatchRequest> tinMatchRequests = new ArrayList<>();
        for (Payee p : payees) {
            TINMatchRequest tinMatchRequest = createOrGetTINMatchRequest(p);
            tinMatchRequests.add(tinMatchRequest);
        }
        List<TINMatchRequestDTO> dtos = tinMatchRequests.stream().map(e -> {
            TINMatchRequestDTO dto = convertUtil.toDTO(e);
            dto.payeeId = e.getPayee().getId();
            return dto;
        }).toList();
        return dtos;
    }

    /**
     * Update single TIN Match request selection. Used in submit filings flow.
     * @param payeeId
     * @param selected
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void updateTINMatchRequest(String payeeId, boolean selected) throws ApheDataValidationException, ApheForbiddenException {
        List<String> payeeIds = Arrays.asList(payeeId);
        bulkUpdateTINMatchRequests(payeeIds, selected);
    }

    /**
     * Bulk update the TIN Match selection for the payees. Used in submit filings flow.
     * @param payeeIds
     * @param selected
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void bulkUpdateTINMatchRequests(List<String> payeeIds, boolean selected) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        for(Payee p : payees) {
            TINMatchRequest tinMatchRequest = createOrGetTINMatchRequest(p);
            if(tinMatchRequest.getTinMatchStatus() != TINMatchRequestStatus.Draft) {
                continue;
            }
            TINMatchStatus tinMatchStatus = p.getTinMatchStatus();
            if(selected == true) {
                if(TINMatchStatus.Valid != tinMatchStatus && TINMatchStatus.InvalidTINType != tinMatchStatus) {
                    tinMatchRequest.setSelected(selected);
                }
            } else {
                tinMatchRequest.setSelected(selected);
            }
            payeeRepository.save(p);
        }
    }

    /**
     * To be used with submit filings flow. Won't try to charge for these tin match requests.
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void submitTINMatchRequestsWithFilings(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, true, true);
        for (Payee p : payees) {
            TINMatchRequest tinMatchRequest = createOrGetTINMatchRequest(p);
            submitTinMatch(p, "Submitted by User", new Date());
            payeeRepository.save(p);
        }
    }


    /**
     * Submit TIN Match requests on payees. Used from the payees screen. Charges for the submissions. Creates the TIN Matches if they don't exist.
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void submitTINMatchRequests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, true, true);

        // Figure out the charge based on things being requested.
        HashMap<Long, Payee> tinMatchPayees = new HashMap<>();
        for (Payee p : payees) {
            TINMatchRequest tinMatchRequest = createOrGetTINMatchRequest(p);
            if(Boolean.TRUE != tinMatchRequest.getPaid()) {
                tinMatchPayees.put(p.getId(), p);
            }
        }
        HashMap<TNNService, Long> serviceCounts = new HashMap<>();
        serviceCounts.put(TNNService.TIN_MATCH, Long.valueOf(tinMatchPayees.size()));

        List<ChargeItemDTO> chargeItemDTOS = new ArrayList<>();
        for(TNNService tnnService : serviceCounts.keySet()) {
            ChargeItemDTO chargeItemDTO = new ChargeItemDTO(tnnService,serviceCounts.get(tnnService));
            chargeItemDTOS.add(chargeItemDTO);
        }
        InvoiceDTO invoice = billingMgr.generateInvoice(chargeItemDTOS);

        // Apply the charge and record it.
        SimpleDateFormat dateFormat = new SimpleDateFormat();
        Date filingSubmissionDate = new Date();
        String billingDescription = MessageFormat.format("TIN Match requests for {0} payees for Entity {1} on {2}", payees.size(), getCurrentDomainId(),
                dateFormat.format(filingSubmissionDate));
        boolean chargeSuccessful = billingMgr.useCredits(invoice.totalAmount, billingDescription);

        // Mark the payees as submitted.
        // TODO: Mark the tin match requests as paid. Do we need to store the billing transaction Id as reference? -- later.
        if (chargeSuccessful) {
            for (Long payeeId : tinMatchPayees.keySet()) {
                Payee p = tinMatchPayees.get(payeeId);
                submitTinMatch(p, "Submitted by User", filingSubmissionDate);
                payeeRepository.save(p);
            }
        } else {
            throw new ApheDataValidationException("_", "Not enough funds to submit TIN match requests for the payees. Please add funds to your account before requesting TIN matches.");
        }
    }

    /**
     * Cancel the TIN Match requests in bulk.
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void cancelTINMatchRequests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException{
        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        for (Payee p : payees) {
            TINMatchRequest tinMatchRequest = p.getTinMatchRequest();
            if(tinMatchRequest != null && tinMatchRequest.getTinMatchStatus() != TINMatchRequestStatus.Draft) {
                p.setTinMatchStatus(TINMatchStatus.NotVerified);
                updateTINMatchRequestStatus(tinMatchRequest, TINMatchRequestStatus.Cancelled, "Cancelled by user", new Date());
                p.setTinMatchRequest(null);
            }
            payeeRepository.save(p);
        }
    }

    /**
     * Method that validates the ownership of payees and optionally verifies if the TIN and Name or not null.
     * @param payeeIds
     * @param validateTINAndName
     * @return
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    private List<Payee> validateAndGetPayees(List<String> payeeIds, boolean validateTINAndName, boolean checkExistingRequests) throws ApheDataValidationException, ApheForbiddenException {
        List<Long> longPayeeIds = ArrayUtil.stringListToLongList(payeeIds);
        if (payeeIds == null || payeeIds.size() == 0) {
            return new ArrayList<>();
        }

        // Make sure user's email address is verified.
        if (!isEmailAddressConfirmed()) {
            throw new ApheDataValidationException("payeeIds", "Email address is not verified.");
        }

        List<Payee> payees = payeeRepository.findByIdIn(longPayeeIds);
        if (payees.size() != payeeIds.size()) {
            logger.info("All the payeeIds={} are not found on domainId={}", ArrayUtil.listToString(longPayeeIds, ","), getCurrentDomainId());
            throw new ApheForbiddenException();
        }

        if(validateTINAndName) {
            // Ensure all the selected payees have TIN and Name
            for (Payee p : payees) {
                //Check there is a TIN and Name entered for Payee
                if(!hasTINAndName(p)) {
                    String s = MessageFormat.format("Either payee TIN or Name is not entered for payeeId {0} .", p.getId());
                    throw new ApheDataValidationException("_", "Some of the payees do not have either TIN or Name.");
                }
            }
        }

        List<String> inProgressTinChecks = new ArrayList<>();
        if (checkExistingRequests) {
            for (Payee p : payees) {
                TINMatchRequest tinMatchRequest = p.getTinMatchRequest();
                if (tinMatchRequest == null || !TINMatchRequestStatus.nonTerminalStatuses.contains(tinMatchRequest.getTinMatchStatus())) {
                    continue;
                } else {
                    inProgressTinChecks.add(convertUtil.getPayeeDisplayName(p));
                }
            }
        }

        if (inProgressTinChecks.size() > 0) {
            if (inProgressTinChecks.size() == 1) {
                String s = MessageFormat.format("A TIN Verification request is already in progress for payee {0}.", ArrayUtil.stringListToString(inProgressTinChecks, ", "));
                throw new ApheDataValidationException("_", s);
            } else {
                String s = MessageFormat.format("TIN Verification requests are in progress for payees {0}.", ArrayUtil.stringListToString(inProgressTinChecks, ", "));
                throw new ApheDataValidationException("_", s);
            }
        }

        return payees;
    }

    private boolean hasTINAndName(Payee p) throws ApheDataValidationException {
        String tinEncrypted = p.getTinEncrypted();
        String payeeDisplayName = convertUtil.getPayeeDisplayName(p);
        if(tinEncrypted == null || tinEncrypted.length() == 0 && payeeDisplayName == null && payeeDisplayName.trim().length() == 0){
            return false;
        }
        return true;
    }

    @Transactional
    public TINMatchRequest createOrGetTINMatchRequest(Payee p) {
        TINMatchRequest tinMatchRequest = p.getTinMatchRequest();
        if(tinMatchRequest != null) {
            //check if this is done
            if(tinMatchRequest.getTinMatchStatus() == TINMatchRequestStatus.Cancelled || tinMatchRequest.getTinMatchStatus() == TINMatchRequestStatus.Response_Received) {
                tinMatchRequest = null;
            }
        }
        if(tinMatchRequest == null) {
            tinMatchRequest = new TINMatchRequest();
            tinMatchRequest.setPayee(p);
            tinMatchRequest.setSelected(false);
            tinMatchRequest.setPaid(false);
            updateTINMatchRequestStatus(tinMatchRequest, TINMatchRequestStatus.Draft, "Created", new Date());
            p.setTinMatchRequest(tinMatchRequest);
            if(p.getTinMatchRequests() == null) {
                p.setTinMatchRequests(new ArrayList<>());
            }
            p.getTinMatchRequests().add(tinMatchRequest);
        }
        return tinMatchRequest;
    }


    @Transactional
    public void submitTinMatch(Payee p, String desc, Date submissionDate){
        TINMatchRequest tinMatchRequest = p.getTinMatchRequest();
        if(tinMatchRequest != null && tinMatchRequest.getTinMatchStatus() == TINMatchRequestStatus.Draft) {
            p.setTinMatchStatus(TINMatchStatus.InProgress);
            tinMatchRequest.setSelected(true);
            tinMatchRequest.setPaid(true);
            tinMatchRequest.setTin(p.getTin());
            tinMatchRequest.setTinEncrypted(p.getTinEncrypted());
            tinMatchRequest.setName(convertUtil.getPayeeNameForTINMatch(p));
            tinMatchRequest.setTinType(p.getTinType());
            tinMatchRequest.setRequestDate(submissionDate);
            updateTINMatchRequestStatus(tinMatchRequest, TINMatchRequestStatus.Submitted, desc, submissionDate);
        }
    }

    @Transactional
    public void updateTINMatchRequestStatus(TINMatchRequest tinMatchRequest, TINMatchRequestStatus newStatus, String description, Date statusChangeDate) {
        if (newStatus != tinMatchRequest.getTinMatchStatus()) {
            tinMatchRequest.setTinMatchStatus(newStatus);
            tinMatchRequest.setStatusChangeDate(statusChangeDate);
            tinMatchRequest.setStatusChangeDesc(description);
            updateTINMatchStatusHistory(tinMatchRequest);
        }
    }

    @Transactional
    public void updateTINMatchStatusHistory(TINMatchRequest tinMatchRequest) {
        TINMatchRequestStatusEntity requestStatusEntity = new TINMatchRequestStatusEntity(tinMatchRequest.getTinMatchStatus(), tinMatchRequest.getStatusChangeDate(), tinMatchRequest.getStatusChangeDesc());
        List<TINMatchRequestStatusEntity> history = tinMatchRequest.getRequestStatusHistory();
        if (history == null) {
            history = new ArrayList<>();
            tinMatchRequest.setRequestStatusHistory(history);
        }
        history.add(requestStatusEntity);
    }

    public List<TINMatchRequest> findByDomainId(Long domainId) {
        return tinMatchRequestRepository.findByDomainId(domainId);
    }

    public List<TINMatchRequest> findByDomainIdAndStatusIn(Long domainId, List<TINMatchRequestStatus> statuses) {
        return tinMatchRequestRepository.findByDomainIdAndStatusIn(domainId, statuses);
    }


    public List<TINMatchRequest> findByStatusIn(TINMatchRequestStatus status) {
        return tinMatchRequestRepository.findByStatusIn(Arrays.asList(status));
    }

    public List<TINMatchRequest> findByStatusIn(List<TINMatchRequestStatus> statuses) {
        return tinMatchRequestRepository.findByStatusIn(statuses);
    }

    @Transactional
    public void receiveTINMatchRequest(String tinMatchRequestId, Long efsTinMatchRequestId) {
        TINMatchRequest tinMatchRequest = tinMatchRequestRepository.findById(Long.parseLong(tinMatchRequestId)).orElse(null);
        if(tinMatchRequest != null) {
            tinMatchRequest.setTinMatchRequestId(Long.toString(efsTinMatchRequestId));
            updateTINMatchRequestStatus(tinMatchRequest, TINMatchRequestStatus.Received, "received by backend system", new Date());
            tinMatchRequestRepository.save(tinMatchRequest);
        }
    }

    public void cancelTINMatchRequestsByIds(Map<Long, String> conversionErrorsById) {
        if (conversionErrorsById == null || conversionErrorsById.size() == 0) {
            return;
        }
        Date statusChangeDate = new Date();
        for (Long requestId : conversionErrorsById.keySet()) {
            String description = conversionErrorsById.get(requestId);
            TINMatchRequest tinMatchRequest = tinMatchRequestRepository.findById(requestId).orElse(null);
            if (tinMatchRequest != null) {
                updateTINMatchRequestStatus(tinMatchRequest, TINMatchRequestStatus.Cancelled, description, statusChangeDate);
                Payee payee = tinMatchRequest.getPayee();
                payee.setTinMatchStatus(TINMatchStatus.NotVerified);
                payeeRepository.save(payee);
                tinMatchRequestRepository.save(tinMatchRequest);
            }
        }
    }

    public void cancelTINMatchRequestsBySubmissionRefIds(Map<String, String> submissionErrorsByEFSId) {
        if (submissionErrorsByEFSId == null || submissionErrorsByEFSId.size() == 0) {
            return;
        }
        Date statusChangeDate = new Date();
        for (String efsRequestId : submissionErrorsByEFSId.keySet()) {
            String description = submissionErrorsByEFSId.get(efsRequestId);
            TINMatchRequest tinMatchRequest = tinMatchRequestRepository.findByTinMatchRequestId(efsRequestId);
            if (tinMatchRequest != null) {
                updateTINMatchRequestStatus(tinMatchRequest, TINMatchRequestStatus.Cancelled, description, statusChangeDate);
                Payee payee = tinMatchRequest.getPayee();
                payee.setTinMatchStatus(TINMatchStatus.NotVerified);
                payeeRepository.save(payee);
                tinMatchRequestRepository.save(tinMatchRequest);
            }
        }
    }


    public TINMatchRequest updateStatusByRefId(String efsRequestId, TINMatchRequestStatus newStatus, String desc, Date date, int result) {
        TINMatchRequest tinMatchRequest = tinMatchRequestRepository.findById(Long.parseLong(efsRequestId)).orElse(null);
        if (tinMatchRequest != null) {
            if (newStatus != tinMatchRequest.getTinMatchStatus()) {
                if (newStatus == TINMatchRequestStatus.Response_Received) {
                    //Update tin match status on payee based on the result...
                    if(result >= 0) {
                        Payee payee = tinMatchRequest.getPayee();
                        if(TINMatchResult.INVALID_RESULTS.contains(result)) {
                            payee.setTinMatchStatus(TINMatchStatus.InValid);
                        } else if(TINMatchResult.UNKNOWN_RESULTS.contains(result)) {
                            payee.setTinMatchStatus(TINMatchStatus.NotVerified);
                        } else if(TINMatchResult.VALID_RESULTS.contains(result)) {
                            payee.setTinMatchStatus(TINMatchStatus.Valid);
                        }
                        payeeRepository.save(payee);
                        tinMatchRequest.setTinMatchResult(TINMatchResult.getTINMatchResult(result));
                    }
                }
                updateTINMatchRequestStatus(tinMatchRequest, newStatus, desc, date);
                tinMatchRequestRepository.save(tinMatchRequest);
                return tinMatchRequest;
            }
        }
        return null;
    }

    @Transactional
    public long getTINMatchRequests(Long currentDomainId) {
        Payer payer = payerRepo.findByDomainId(currentDomainId);
        if (payer != null) {
            FilingYear currentFilingYear = FilingYear.getCurrentFilingYear();
            String filingYear = currentFilingYear.getYear();
            int filingYearInt = Integer.parseInt(filingYear);
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, filingYearInt+1);
            cal.set(Calendar.DAY_OF_YEAR, 1);
            Date firstDatOfTheYear = cal.getTime();
            Date requestDate = DateUtil.addDays(firstDatOfTheYear, -47);
            Long submittedFilings = tinMatchRequestRepository.countByPayerAndYear(payer.getId(), requestDate);
            return submittedFilings;
        }
        return 0L;
    }

    @Transactional
    public long getTINMatchRequestsForAllClients(List<Long> payerIds, Date submissionDate) {
        if (payerIds.size() > 0) {
            Long submittedFilings = tinMatchRequestRepository.countByPayersAndYear(payerIds, submissionDate);
            return submittedFilings;
        }
        return 0L;
    }

    @PreAuthorize("hasAuthority('superadmin')")
    public void refreshTINMatchRequestStatus() {
//        updateTINMatchRequestStatusMultiNodeTask.startTask();
    }
}
