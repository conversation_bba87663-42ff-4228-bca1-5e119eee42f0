package com.aphe.contractor.services;

import com.aphe.billing.dto.ChargeItemDTO;
import com.aphe.billing.dto.InvoiceDTO;
import com.aphe.billing.model.enums.TNNService;
import com.aphe.billing.service.BillingMgr;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.util.AESEncryptionUtilStatic;
import com.aphe.common.util.ArrayUtil;
import com.aphe.common.util.EncryptionUtil;
import com.aphe.contractor.dto.read.W9RequestDataDTO;
import com.aphe.contractor.model.CAddress;
import com.aphe.contractor.model.Payee;
import com.aphe.contractor.model.Payer;
import com.aphe.contractor.model.W9RequestStatusEntity;
import com.aphe.contractor.model.enums.*;
import com.aphe.contractor.model.w9.W9Request;
import com.aphe.contractor.repo.PayeeRepository;
import com.aphe.contractor.repo.PayerRepository;
import com.aphe.contractor.repo.W9RequestRepository;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.security.GeneralSecurityException;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Service
public class W9RequestManager extends CommonTNNManager {

    @Value("${aphe.contractor.w9Dir}")
    public String w9Dir;

    @Autowired
    PayeeRepository payeeRepository;

    @Autowired
    PayerRepository payerRepo;

    @Autowired
    W9RequestRepository w9RequestRepository;

    @Autowired
    ContractorConvertUtil convertUtil;

    @Autowired
    BillingMgr billingMgr;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    MailManager mailManager;

    /**
     * Submit W9 requests for payees. Used from the payees screen. Charges for the submissions.
     *
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void submitW9Requests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, true, true);

        // Figure out the charge based on things being requested.
        HashMap<Long, Payee> w9Payees = new HashMap<>();
        for (Payee p : payees) {
            W9Request w9Request = createOrGetW9Request(p);
            if (Boolean.TRUE != w9Request.getPaid()) {
                w9Payees.put(p.getId(), p);
            }
        }

        HashMap<TNNService, Long> serviceCounts = new HashMap<>();
        serviceCounts.put(TNNService.W9, (long) w9Payees.size());

        List<ChargeItemDTO> chargeItemDTOS = new ArrayList<>();
        for (TNNService tnnService : serviceCounts.keySet()) {
            ChargeItemDTO chargeItemDTO = new ChargeItemDTO(tnnService, serviceCounts.get(tnnService));
            chargeItemDTOS.add(chargeItemDTO);
        }
        InvoiceDTO invoice = billingMgr.generateInvoice(chargeItemDTOS);

        // Apply the charge and record it.
        SimpleDateFormat dateFormat = new SimpleDateFormat();
        Date filingSubmissionDate = new Date();
        String billingDescription = MessageFormat.format("W-9 requests for {0} payees for Entity {1} on {2}", payees.size(), getCurrentDomainId(), dateFormat.format(filingSubmissionDate));
        boolean chargeSuccessful = billingMgr.useCredits(invoice.totalAmount, billingDescription);

        if (chargeSuccessful) {
            for (Long payeeId : w9Payees.keySet()) {
                Payee p = w9Payees.get(payeeId);
                submitW9Request(p, "Submitted by User", filingSubmissionDate);
                payeeRepository.save(p);
            }

            //TODO: Fire async tosk send emails to the payees immediately. This transsaction should be independent of the main transaction.


        } else {
            throw new ApheDataValidationException("_", "You do not have enough funds to submit W-9 requests for the payees. Please add funds to your account from Reports->Billing before requesting W-9 info.");
        }
    }


    /**
     * Create W9 requests for payees. Used from the payees screen. Throws an exception if the payee doesn't have an email address or if a request is already in progress.
     * @param payeeIds
     * @return
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public List<W9RequestDataDTO> getW9s(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        List<W9Request> w9Requests = new ArrayList<>();
        for (Payee p : payees) {
            W9Request w9Request = createOrGetW9Request(p);
            w9Requests.add(w9Request);
        }
        List<W9RequestDataDTO> dtos = w9Requests.stream().map(e -> {
            W9RequestDataDTO dto = convertUtil.toDTO(e);
            dto.payeeId = e.getPayee().getId();
            return dto;
        }).toList();
        return dtos;
    }

    /**
     * Create W9 requests for payees. Used from the payees screen. Throws an exception if the payee doesn't have an email address or if a request is already in progress.
     * @param payeeIds
     * @return
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public List<W9RequestDataDTO> createW9Requests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, true, true);
        List<W9Request> w9Requests = new ArrayList<>();
        for (Payee p : payees) {
            W9Request w9Request = createOrGetW9Request(p);
            w9Requests.add(w9Request);
        }
        List<W9RequestDataDTO> dtos = w9Requests.stream().map(e -> {
            W9RequestDataDTO dto = convertUtil.toDTO(e);
            dto.payeeId = e.getPayee().getId();
            return dto;
        }).toList();
        return dtos;
    }

    /**
     * Cancel the W9 requests in bulk.
     *
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void cancelW9Requests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        for (Payee p : payees) {
            W9Request w9Request = p.getW9Request();
            if (w9Request != null && (w9Request.getW9RequestStatus() != W9RequestStatus.Draft || w9Request.getW9RequestStatus() != W9RequestStatus.Cancelled)) {
                updateW9RequestStatus(w9Request, W9RequestStatus.Cancelled, "Cancelled by user", new Date());
            }
            payeeRepository.save(p);
        }
    }

    /**
     * Remind about W-9 Requests in progress. we do this by just setting the status back to Submitted, if they have already paid.
     *
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void remindW9Requests(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        for (Payee p : payees) {
            W9Request w9Request = p.getW9Request();
            if (w9Request != null && w9Request.getPaid() != null && w9Request.getPaid().booleanValue() == true) {
                W9RequestStatus w9RequestStatus = w9Request.getW9RequestStatus();
                if (W9RequestStatus.processingStatuses.contains(w9RequestStatus)) {
                    updateW9RequestStatus(w9Request, W9RequestStatus.Submitted, "Resubmitted by user", new Date());
                }
            }
            payeeRepository.save(p);
        }
    }

    @Transactional
    @Async
    public void sendW9RequestEmails(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        //First create an email and then set the status of the request to requested.
        try {
            Thread.sleep(1000*60*2);
        }catch (Exception e) {
        }

        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        for (Payee p : payees) {
            W9Request w9Request = p.getW9Request();
            if (w9Request != null && w9Request.getPaid() != null && w9Request.getPaid().booleanValue() == true) {
                //Check if the request is in a status where we can send an email.
                W9RequestStatus w9RequestStatus = w9Request.getW9RequestStatus();
                if(w9RequestStatus == W9RequestStatus.Submitted) {
                    mailManager.sendW9RequestEmail(w9Request);
                    updateW9RequestStatus(w9Request, W9RequestStatus.Requested, "Sent email", new Date());
                }
            }
        }
    }

    /**
     * Applies the information obtained from the payees to the system.
     *
     * @param payeeIds
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    @Transactional
    public void applyW9Info(List<String> payeeIds) throws ApheDataValidationException, ApheForbiddenException {
        List<Payee> payees = validateAndGetPayees(payeeIds, false, false);
        for (Payee p : payees) {
            W9Request w9Request = p.getW9Request();
            applyW9Info(w9Request);
        }
    }



    @Transactional
    @Async
    public void applyW9InfoAndSendEmail(String id) throws ApheForbiddenException {

        try {
            Thread.sleep(1000*60*2);
        }catch (Exception e) {
        }

        W9Request w9Request = w9RequestRepository.findByGlobalId(UUID.fromString(id));
        applyW9Info(w9Request);

        //Send email to the payer.
        mailManager.sendW9AppliedEmail(w9Request);

    }

    @Transactional
    public void applyW9Info(W9Request w9Request) {
        W9RequestStatus w9RequestStatus = w9Request.getW9RequestStatus();
        if (w9RequestStatus == W9RequestStatus.Filled) {
            Payee payee = w9Request.getPayee();

            EntityType entityType = w9Request.getEntityType();
            if (entityType == EntityType.Individual) {
                payee.setEntityType(EntityType.Individual);
                payee.setFirstName(w9Request.getFirstName());
                payee.setLastName(w9Request.getLastName());
                payee.setMiddleName(w9Request.getMiddleName());
            } else {
                payee.setEntityType(EntityType.Business);
                payee.setBusinessName(w9Request.getBusinessName());
            }
            payee.setDba(w9Request.getDba());
            String tinType = w9Request.getTinType();
            if (tinType.equalsIgnoreCase("SSN")) {
                payee.setTinType(TinType.SSN);
            } else {
                payee.setTinType(TinType.EIN);
            }
            String tinEncrypted = w9Request.getTinEncrypted();
            String plainTin = new EncryptionUtil().decrypt(tinEncrypted);
            payee.setTin(plainTin);

            //What if the payer aleady entered the TIN and requested a TIN match verification?
            payee.setTinMatchStatus(TINMatchStatus.NotVerified);

            CAddress payeeAddress = payee.getAddress();
            if (payeeAddress == null) {
                payeeAddress = new CAddress();
                payee.setAddress(payeeAddress);
            }
            payeeAddress.setLine1(w9Request.getAddress().getLine1());
            payeeAddress.setLine2(w9Request.getAddress().getLine2());
            payeeAddress.setCity(w9Request.getAddress().getCity());
            payeeAddress.setState(w9Request.getAddress().getState());
            payeeAddress.setPostalCode(w9Request.getAddress().getPostalCode());
            payeeAddress.setCountry(w9Request.getAddress().getCountry());
            payeeAddress.setAddressVerificateionState(CAddressVerificatiionState.UKNOWN);
            payeeRepository.save(payee);
            updateW9RequestStatus(w9Request, W9RequestStatus.Applied, "Accepted by the user.", new Date());
        }
    }

    public List<W9Request> findByStatusIn(W9RequestStatus status) {
        return w9RequestRepository.findByStatusIn(Arrays.asList(status));
    }

    public W9Request findByById(Long id) {
        return w9RequestRepository.findById(id).orElse(null);
    }


    private void submitW9Request(Payee p, String desc, Date submissionDate) {
        W9Request w9Request = p.getW9Request();
        if (w9Request != null && w9Request.getW9RequestStatus() == W9RequestStatus.Draft) {
            w9Request.setPaid(true);
            w9Request.setRequestDate(submissionDate);
            updateW9RequestStatus(w9Request, W9RequestStatus.Submitted, desc, submissionDate);
        }
    }


    /**
     * Method that validates the ownership of payees and optionally verifies if the email address is present, and check if an existing request is in progress.
     *
     * @param payeeIds
     * @param validateEmail
     * @return
     * @throws ApheDataValidationException
     * @throws ApheForbiddenException
     */
    private List<Payee> validateAndGetPayees(List<String> payeeIds, boolean validateEmail, boolean checkExistingRequests) throws ApheDataValidationException, ApheForbiddenException {
        List<Long> longPayeeIds = ArrayUtil.stringListToLongList(payeeIds);
        if (payeeIds == null || payeeIds.size() == 0) {
            return new ArrayList<>();
        }

        // Make sure user's email address is verified.
        if (!isEmailAddressConfirmed()) {
            throw new ApheDataValidationException("payeeIds", "Your email address is not verified. Please verify your email address from user profile icon on top right corner.");
        }

        List<Payee> payees = payeeRepository.findByIdIn(longPayeeIds);
        if (payees.size() != payeeIds.size()) {
            logger.info("All the payeeIds={} are not found on domainId={}", ArrayUtil.listToString(longPayeeIds, ", "), getCurrentDomainId());
            throw new ApheForbiddenException();
        }

        List<String> invalidEmailPayees = new ArrayList<>();
        if (validateEmail) {
            // Ensure all the selected payees have an email address.
            for (Payee p : payees) {
                //Check if the email address is valid for the payee.
                if (!hasValidEmailAddress(p)) {
                    invalidEmailPayees.add(convertUtil.getPayeeDisplayName(p));
                }
            }
        }

        if (invalidEmailPayees.size() > 0) {
            if (invalidEmailPayees.size() == 1) {
                String s = MessageFormat.format("Payee {0} doesn't not have a valid email address", ArrayUtil.stringListToString(invalidEmailPayees, ", "));
                throw new ApheDataValidationException("_", s);
            } else {
                String s = MessageFormat.format("Payees {0}, do not have valid email addresses", ArrayUtil.stringListToString(invalidEmailPayees, ", "));
                throw new ApheDataValidationException("_", s);
            }
        }

        List<String> alreadySubmittedPayees = new ArrayList<>();
        if (checkExistingRequests) {
            for (Payee p : payees) {
                W9Request w9Request = p.getW9Request();
                if (w9Request == null || !W9RequestStatus.nonTerminalStatuses.contains(w9Request.getW9RequestStatus())) {
                    continue;
                } else {
                    alreadySubmittedPayees.add(convertUtil.getPayeeDisplayName(p));
                }
            }
        }

        if (alreadySubmittedPayees.size() > 0) {
            if (alreadySubmittedPayees.size() == 1) {
                String s = MessageFormat.format("A W-9 request is already in progress for payee {0}.", ArrayUtil.stringListToString(alreadySubmittedPayees, ", "));
                throw new ApheDataValidationException("_", s);
            } else {
                String s = MessageFormat.format("W-9 requests are in progress for payees {0}.", ArrayUtil.stringListToString(alreadySubmittedPayees, ", "));
                throw new ApheDataValidationException("_", s);
            }
        }

        return payees;
    }


    private W9Request createOrGetW9Request(Payee p) throws ApheForbiddenException {
        W9Request w9Request = p.getW9Request();
        if (w9Request != null && (w9Request.getW9RequestStatus() == W9RequestStatus.Applied ||
                w9Request.getW9RequestStatus() == W9RequestStatus.Cancelled ||
                w9Request.getW9RequestStatus() == W9RequestStatus.Expired)
        ) {
            w9Request = null;
        }
        if (w9Request == null) {
            w9Request = new W9Request();
            w9Request.setPayee(p);
            w9Request.setPaid(false);
            w9Request.setPayerName(getPayerName(p));
            w9Request.setDisplayName(convertUtil.getPayeeDisplayName(p));
            updateW9RequestStatus(w9Request, W9RequestStatus.Draft, "Created", new Date());
            p.setW9Request(w9Request);
            if (p.getW9Requests() == null) {
                p.setW9Requests(new ArrayList<>());
            }
            p.getW9Requests().add(w9Request);
        }
        return w9Request;
    }

    private String getPayerName(Payee p) throws ApheForbiddenException {
        Payer payer = p.getPayer();
        Long domainId = payer.getDomainId();
        DomainDTO domainDTO = domainMgr.getDomain(domainId);
        String payerName = convertUtil.getDomainDisplayName(domainDTO);
        return payerName;
    }


    private boolean hasValidEmailAddress(Payee p) throws ApheDataValidationException {
        String emailAddress = p.getEmailAddress();
        if (emailAddress == null || emailAddress.length() == 0) {
            return false;
        }
        return true;
    }

    @Transactional
    public void updateW9RequestStatus(W9Request w9Request, W9RequestStatus newStatus, String description, Date statusChangeDate) {
        if (newStatus != w9Request.getW9RequestStatus()) {
            w9Request.setW9RequestStatus(newStatus);
            w9Request.setStatusChangeDate(statusChangeDate);
            w9Request.setStatusChangeDesc(description);
            updateW9RequestStatusHistory(w9Request);
            w9RequestRepository.save(w9Request);
        }
    }

    private void updateW9RequestStatusHistory(W9Request w9Request) {
        W9RequestStatusEntity requestStatusEntity = new W9RequestStatusEntity(w9Request.getW9RequestStatus(), w9Request.getStatusChangeDate(), w9Request.getStatusChangeDesc());
        List<W9RequestStatusEntity> history = w9Request.getRequestStatusHistory();
        if (history == null) {
            history = new ArrayList<>();
            w9Request.setRequestStatusHistory(history);
        }
        history.add(requestStatusEntity);
    }

    @Transactional
    public long getW9RequestsForAllClients(List<Long> payerIds, Date submissionDate) {
        if (payerIds.size() > 0) {
            Long submittedFilings = w9RequestRepository.countByPayersAndYear(payerIds, submissionDate);
            return submittedFilings;
        }
        return 0L;
    }

    @Transactional
    public W9RequestDataDTO getW9RequestById(String requestId) throws ApheForbiddenException {
        W9Request w9Request = w9RequestRepository.findByGlobalId(UUID.fromString(requestId));
        if (w9Request != null) {
            W9RequestStatus w9RequestStatus = w9Request.getW9RequestStatus();
            if (!W9RequestStatus.notVendorVisibleStates.contains(w9RequestStatus)) {
                W9RequestDataDTO dto = convertUtil.toDTO(w9Request);
                return dto;
            }
        }
        return null;
    }

    @Transactional
    public String updateW9RequestData(W9RequestDataDTO dto) throws ApheForbiddenException {
        W9Request existingEntity = w9RequestRepository.findByGlobalId(UUID.fromString(dto.id));
        if (existingEntity != null) {
            W9RequestStatus w9RequestStatus = existingEntity.getW9RequestStatus();
            if (W9RequestStatus.editingAllowedStatuses.contains(w9RequestStatus)) {
                W9Request newEntity = convertUtil.toEntity(dto);
                if (newEntity.getAddress() != null && existingEntity.getAddress() != null) {
                    newEntity.getAddress().setId(existingEntity.getAddress().getId());
                    newEntity.getAddress().setVersion(existingEntity.getAddress().getVersion());
                }
                W9Request savedEntity = w9RequestRepository.mergeAndSave(newEntity, existingEntity);
                return savedEntity.getGlobalId();
            }
        }
        return null;
    }

    @Transactional
    public String saveW9(String id, byte[] bytes) throws ApheForbiddenException {
        W9Request w9Request = w9RequestRepository.findByGlobalId(UUID.fromString(id));
        if (w9Request != null) {
            W9RequestStatus w9RequestStatus = w9Request.getW9RequestStatus();
            if (W9RequestStatus.editingAllowedStatuses.contains(w9RequestStatus)) {
                File dir = new File(w9Dir);
                dir.mkdirs();
                String fileName = id + ".pdf";
                String w9FilePath = dir.getAbsolutePath() + File.separator + fileName;
                OutputStream os = null;
                ByteArrayOutputStream byteOutputStream = null;
                try {
                    os = AESEncryptionUtilStatic.getEncryptorStream(w9FilePath);
                    os.write(bytes);
                } catch (IOException | GeneralSecurityException e) {
                    throw new RuntimeException(e);
                } finally {
                    try {
                        if (os != null) {
                            os.close();
                        }
                    } catch (IOException e) {
                    }
                }
                w9Request.setFileName(fileName);
                updateW9RequestStatus(w9Request, W9RequestStatus.Filled, "Signed by the payee", new Date());
                w9RequestRepository.save(w9Request);
                return w9Request.getGlobalId();
            }
        }
        return null;
    }

}
