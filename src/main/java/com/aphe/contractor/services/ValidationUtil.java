package com.aphe.contractor.services;

import com.aphe.AppUtil;
import com.aphe.common.auth.AuthManager;
import com.aphe.common.error.MappedValidationMessages;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.model.*;
import com.aphe.contractor.model.enums.*;
import com.aphe.contractor.repo.FedSubRepository;
import com.aphe.contractor.repo.FilingRepository;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.model.Domain;
import com.aphe.domain.repo.DomainRepository;
import com.aphe.domain.service.DomainMgr;
import com.aphe.efs.dto.*;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.services.filings.EFSFilingValidationUtil;
import com.aphe.efs.services.filings.FilingsManager;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.hibernate.validator.constraints.ValidationErrors;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ValidationUtil {

    @Autowired
    ContractorConvertUtil convertUtil;

    @Autowired
    FilingManager localFilingMgr;

    @Autowired
    FilingsManager efsFilingsMgr;

    @Autowired
    EFSFilingValidationUtil efsFilingValidationUtil;

    @Autowired
    PayeeManager payeeManager;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    protected Validator validator;

    @Value("${aphe.config.enableTINMatch}")
    private boolean enableTINMatch = true;

    @Autowired
    AuthManager authManager;

    @Autowired
    DomainRepository domainRepository;


    /**
     * TODO: direct access to repo, is this a security hole??
     * OK as long as methods are not returning usable data. If this ever changes the methods that are leaking data
     * need to add hasPermission annotation on those methods.
     */
    @Autowired
    FilingRepository filingRepo;

    @Autowired
    FedSubRepository fedSubRepository;


    private Logger logger = LoggerFactory.getLogger(getClass());

    public List<MappedValidationMessages> validateFilings(List<Filing> filings) {
        return validateFilings(filings, false);
    }

    public List<MappedValidationMessages> validateFilings(List<Filing> filings, boolean ignoreWarnings) {

        List<MappedValidationMessages> validationMessages = new ArrayList<>();

        List<Filing> filingsToBeValidated = filings.stream().filter(f->isFedSubSelected(f.getFedSub())).collect(Collectors.toList());
        List<Filing> filingsToBeIgnored = filings.stream().filter(f->!isFedSubSelected(f.getFedSub())).collect(Collectors.toList());
        for(Filing f : filingsToBeIgnored) {
            validationMessages.add(new MappedValidationMessages(f.getId().toString(), new ArrayList<>(), new ArrayList<>()));
        }

        DomainDTO domainDTO = null;
        Map<Long, List<Long>> accountantsByDomain = new HashMap<>();
        if(filingsToBeValidated.size() > 0) {
            Filing f = filingsToBeValidated.get(0);
            try {
                domainDTO = domainMgr.getDomain(f.getPayer().getDomainId());
                if(domainDTO != null) {
                    accountantsByDomain = authManager.getDomainAccountantsRemote(domainDTO.id);
                }
            } catch (ApheForbiddenException e) {
            }
        }

        for (Filing f : filingsToBeValidated) {

            long filingValidationStart = System.currentTimeMillis();

            List<String> userErrors = new ArrayList<>();;
            List<String> userWarnings = new ArrayList<>();;
            try {

                addPayerValidations(f, userErrors, userWarnings, domainDTO);
                addPayeeValidations(f, userErrors, userWarnings);
                addStateValidations(f, userErrors, userWarnings, domainDTO, accountantsByDomain);
                addPrintValidations(f, userErrors, userWarnings);
                addEmailValidations(f, userErrors, userWarnings);
                addTINMatchValidations(f, userErrors, userWarnings);

                //For all other validations, where we convert to EFS DTO, skip validation second separately, but pass it as part of first validation.
                if(f.getCorrectionType() == CorrectionType.Second) {
                    continue;
                }
                Filing secondFiling = null;
                Filing originalFiling = null;
                if(f.getCorrectionType() == CorrectionType.First) {
                    //Find second correction type.
                    secondFiling = filingsToBeValidated.stream()
                            .filter(f2-> f2.getCorrectionType() == CorrectionType.Second && f2.getOriginalFilingId().longValue() == f.getOriginalFilingId().longValue())
                            .findFirst().orElse(null);
                        originalFiling = localFilingMgr.getOriginalFiling(f.getId());
                        if(originalFiling == null) {
                            logger.error("Error processing a correction filing. Original filing is null");
                        }
                }
                addOtherFilingValidations(f, userErrors, userWarnings, domainDTO, secondFiling, originalFiling);
            } catch (Exception e) {
                logger.error("Error converting a filings to EFS DTO during validation. ", e);
                userErrors.add("This filing has encountered errors while processing. Please unselect this filing to proceed, or contact us.");
            } finally {
                validationMessages.add(new MappedValidationMessages(f.getId().toString(), userErrors, ignoreWarnings ? new ArrayList<>() : userWarnings));
                long filingValidationEnd = System.currentTimeMillis();
                logger.info("Filing validation took for validationTime={} for filingId={} ", (filingValidationEnd - filingValidationStart), f.getId());
            }
        }

        if(filingsToBeValidated.size() > 0) {
            addDuplicatePayerWarnings(filings, validationMessages, ignoreWarnings);
            addDuplicateWarnings(filings, validationMessages, ignoreWarnings);
            addFIRECorrectionErrors(filings, validationMessages, domainDTO, ignoreWarnings);
//            addIRISCorrectionErrors(filingsToBeValidated, validationMessages, domainDTO, ignoreWarnings);
        }
        return validationMessages;
    }

//    private void addEFSValidations(Filing f, List<String> userErrors) throws Exception {
//        // Convert them to EFSFilingsDTO and validate the dto.
//        List<FilingDataDTO> dtos = convertUtil.convertToEFSFilingDTOs(f, "1", false);
//        Set<String> errorMessages = new HashSet<>();
//        for(FilingDataDTO dto : dtos) {
//            dto.clientRefId = f.getId() + "";
//            ValidationErrors errors = efsFilingValidationUtil.getEFSValidationErrors(dto);
//            if (errors.getMessages().size() > 0) {
//                for (String fieldName : errors.getMessages().keySet()) {
//                    for (String message : errors.getMessages().get(fieldName)) {
//                        errorMessages.add(fieldName + " " + message);
//                    }
//                }
//            }
//        }
//        userErrors.addAll(errorMessages);
//    }

    private void addTINMatchValidations(Filing f, List<String> userErrors, List<String> userWarnings) throws ApheForbiddenException, ApheDataValidationException {
        Payee p = f.getPayee();
        TINMatchStatus tinMatchStatus = p.getTinMatchStatus();
        TINMatchRequest tinMatchRequest = p.getTinMatchRequest();
        boolean isTINInvalid = tinMatchStatus != null && TINMatchStatus.InValid == tinMatchStatus;
        if(isTINInvalid) {
            userErrors.add("The payee's name doesn't match with TIN accoriding to IRS records. Please correct payee info before submitting.");
        }
        boolean notVerified = tinMatchStatus == null || tinMatchStatus == TINMatchStatus.NotVerified;
        boolean tinMatchNotSelected = tinMatchRequest == null || Boolean.TRUE != tinMatchRequest.getSelected();
        if(enableTINMatch && notVerified && tinMatchNotSelected) {
            userWarnings.add("The payee's name and TIN hasn't been verified. You can request TIN match so that we can check against the IRS database.");
        }

        if(!enableTINMatch && !tinMatchNotSelected) {
            userErrors.add("TIN match is not available at this time. Please unselect TIN Match request.");
        }
    }


    private void addPrintValidations(Filing f, List<String> userErrors, List<String> userWarnings) throws ApheForbiddenException, ApheDataValidationException {
        PrintSub printSub = f.getPrintSub();
        if (!isPrintSubSelected(printSub)) {
            userWarnings.add("You are required to send a print copy of 1099 form to the payees. Save some time for yourself by selecting print option.");
        } else {
            // if print is selected and country is not present... it is an error.
            CountryCode payeeCountry = f.getPayee().getAddress().getCountry();
            Long domainId = f.getPayer().getDomainId();
            DomainDTO domainDTO = domainMgr.getDomain(domainId);
            CountryCode payerCountry = domainDTO.address.country;
            if (payerCountry == null) {
                userErrors.add("You have selected print delivery option, but the payer address doesn't have country specified. Please update the payer's address.");
            } else {
                if (CountryCode.isForeignEntity(payerCountry.name())) {
                    userErrors.add("You have selected print delivery option, but the payer's address is a foreign address. We do not support mailing from an international address. Please unselect print copy checkbox.");
                }
            }


            if (payeeCountry == null) {
                userErrors.add("You have selected print delivery option, but the payee address doesn't have country specified. Please update the payee's address.");
            } else {
                // if print is selected and country is not US, it is a warning.
                if (CountryCode.isForeignEntity(payeeCountry.name())) {
                    userErrors.add("You have selected print delivery option, but the payee's address is a foreign address. We do not support mailing to an international address. Please unselect print copy checkbox.");
                } else {
                    //TODO: Figure out if we need to validate domain/payer address.
                    if (PropertiesManager.isProd()) {
                        CAddressVerificatiionState currentState = null;
                        CAddress payeeAddress = f.getPayee().getAddress();
                        currentState = payeeAddress != null ? payeeAddress.getAddressVerificateionState() : null;
                        if (payeeAddress != null && currentState == null || currentState == CAddressVerificatiionState.UKNOWN) {
                            payeeAddress = payeeManager.validatePayeeAddress(Long.toString(f.getPayee().getId()));
                            currentState = payeeAddress.getAddressVerificateionState();
                        }
                        if (CAddressVerificatiionState.VALID != currentState) {

                            String addressErrorDetail = "The address was not found in USPS database.";
                            if (CAddressVerificatiionState.VALID_INCORRECT_UNIT == currentState) {
                                addressErrorDetail = "The address is valid, but has incorrect apt/suite information.";
                            } else if (CAddressVerificatiionState.VALID_MISSING_UNIT == currentState) {
                                addressErrorDetail = "The address is valid, but has missing apt/suite information.";
                            }

                            //TODO: refer them here.... https://www.ups.com/address_validator/search?loc=en_US
                            userWarnings.add("You have selected a print delivery option, but the payee has non deliverable address. " + addressErrorDetail +
                                    " Please fix the payee address or select the checkbox 'Ignore warnings' when submitting your filings.");
                        }
                    }
                }
            }
        }
    }

    private void addEmailValidations(Filing f, List<String> userErrors, List<String> userWarnings) {
        EmailSub emailSub = f.getEmailSub();
//        if (!isEmailSubSelected(emailSub) && !isPrintSubSelected(printSub)) {
//            String emailAddress = f.getPayee().getEmailAddress();
//            userWarnings.add("Payee '" + convertUtil.getPayeeDisplayName(f.getPayee()) + "' - '" + " Neither email delivery nor print delivery is selected. You are required to issue a 1099 form to the recipient. Please select a delivery option or select the checkbox 'Ignore warnings'.");
//        }

        if (isEmailSubSelected(emailSub)) {
            String emailAddress = f.getPayee().getEmailAddress();
            if (!StringUtil.isValidEmailAddress(emailAddress)) {
                if(StringUtil.isEmpty(emailAddress)) {
                    userWarnings.add("You have selected email delivery option, but the you haven't entered an email address for the payee. Please enter a valid email address for the payee or unselect email delivery option.");
                } else {
                    userWarnings.add("You have selected email delivery option, but the payee's emailAddress (" + emailAddress + ") is not a valid email address. Please enter a valid email address for the payee or unselect email delivery option.");
                }
            }
        }
    }

    private void addPayerValidations(Filing f, List<String> userErrors, List<String> userWarnings, DomainDTO domainDTO) {
        if (domainDTO.tinEncrypted == null) {
            userErrors.add("Invalid payer TIN. Please edit the payer and enter a valid TIN.");
        }
    }

    private void addPayeeValidations(Filing f, List<String> userErrors, List<String> userWarnings) {

        Payee payee = f.getPayee();
        if (payee == null) {
            userErrors.add("Payee is required for this filing. Please enter the payee information.");
            return;
        }
        if (payee.getTinType() == null) {
            userErrors.add("Payee's TIN type is required for this filing. Edit the payee and select the TIN type.");
        }
        if (StringUtil.isEmpty(f.getPayee().getTinEncrypted())) {
            userErrors.add("Payee's TIN is required for this filing.  Edit the payee and enter a valid TIN.");
        }
        if (payee.getEntityType() == null) {
            userErrors.add("Payee type is not specified. Edit the payee and select the payee type.");
        }
    }

    private void addStateValidations(Filing f,  List<String> userErrors, List<String> userWarnings, DomainDTO domainDTO, Map<Long, List<Long>> accountantsByDomain) throws ApheForbiddenException {
        StateCode payerStateCode = StateCode.getStateCode(domainDTO.address.state);
        StateCode payeeStateCode = StateCode.getStateCode(f.getPayee().getAddress() != null ? f.getPayee().getAddress().getState() : null);
        StateCode filingStateCode = convertUtil.getJSONLocalStateCode(f.getFilingData(), "state1Code", null);

        StateFilingData payeeStateFilingData = payeeStateCode !=null ? StateFilingData.getStateFilingData(payeeStateCode.name()) : null;
        StateFilingData payerStateFilingData = payerStateCode != null ? StateFilingData.getStateFilingData(payerStateCode.name()) : null;

        BigDecimal stateTaxWithheld = convertUtil.getJSONBigDecimal(f.getFilingData(), "state1TaxWithheld", BigDecimal.ZERO);
        BigDecimal stateIncome = convertUtil.getJSONBigDecimal(f.getFilingData(), "state1Income", BigDecimal.ZERO);

        if (stateTaxWithheld.compareTo(BigDecimal.ZERO) > 0 || stateIncome.compareTo(BigDecimal.ZERO) > 0) {
            if (filingStateCode == null) {
                userErrors.add("You have entered state income or state tax withheld, but the you haven't entered a state for this filing. Please enter the state where you want this income to be reported.");
            }
        }

        //No state reported on the filing, but the payer address and payee address suggest a filing is required.
        if(filingStateCode == null && ((payeeStateFilingData != null && payeeStateFilingData.isStateFilingRequired(f.getFilingType()))
                || (payerStateFilingData != null && payerStateFilingData.isStateFilingRequired(f.getFilingType())))) {
            if(!AppUtil.isClientOfGS(domainDTO.id, accountantsByDomain)) {
                userWarnings.add("You haven't entered a state for this filing. Based on the payer address and payee address, you may be required to report to one of the states. Edit this filing and enter the state where this income needs to be reported.");
            }
            return;
        }

        //State was entered, that state requires filing, but state submission is not selected.
        StateSub stateSub = f.getStateSub();

        BigDecimal state1TaxWithheld = new BigDecimal(convertUtil.getJSONBigDecimalValue(f.getFilingData(), "state1TaxWithheld","0.00"));
        BigDecimal state1Income = new BigDecimal(convertUtil.getJSONBigDecimalValue(f.getFilingData(), "state1Income","0.00"));
        String state1EIN = convertUtil.getJSONValue(f.getFilingData(), "state1EIN","");

        if(filingStateCode != null) {
            StateFilingData filingStateFilingData = StateFilingData.getStateFilingData(filingStateCode.name());

            if(filingStateFilingData == null) {
                return;
            }

            boolean stateHasNoFilingOptions  = filingStateFilingData.getSupportedStateFilingMethods(f.getFilingType()).size() == 0;
            boolean stateDoesNotAccept = !filingStateFilingData.isStateFilingRequired(f.getFilingType());

            if(stateHasNoFilingOptions) {
                stateSub.setSelected(false);
            }

            boolean stateSubSelected = isStateSubSelected(stateSub);

            if(stateDoesNotAccept && stateSubSelected){
                userErrors.add("The state doesn't accept " + f.getFilingType().getFormName() + " form, but you have a selected state filing option. Please unselect state filing option.");
                return;
            }

            boolean stateFilingRequired = filingStateFilingData.isStateFilingRequired(f.getFilingType(), state1Income, state1TaxWithheld, payerStateCode, payeeStateCode, filingStateCode);

            if(stateFilingRequired && !stateSubSelected) {
                userWarnings.add("You are required to file this form with the state, but you haven't selected state filing option. Please select a state filing option.");
                return;
            }

            //State income is low.
            if(f.getFilingType() != FilingReturnType.Type_1099_INT && (f.getCorrectionType() == CorrectionType.Original || f.getCorrectionType() == CorrectionType.Second)) {
                boolean isDirectSales = convertUtil.getJSONBooleanValue(f.getFilingData(), "directSalesIndicator", "false");
                if(!isDirectSales && state1Income.compareTo(BigDecimal.ZERO) <= 0) {
                    userWarnings.add("State income is too low.");
                }
            }

            if(isStateTaxWithheldHigh(state1Income, state1TaxWithheld)) {
                userErrors.add("State tax withheld is too high. Please double check your data. Your filing will be rejected for incorrect data.");
            }

            //State submission is required and selected, but the filing method is null.
            if(stateSubSelected) {
                validateDEStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN);
                validateORStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN);
                validateMDStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN);
                validateMEStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN);
                validateNMStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN);
                validateVTStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN);
                validatePAStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN, domainDTO);
                validateNCStateRequirements(f, userErrors, filingStateCode, state1Income, state1TaxWithheld, state1EIN);

                if(state1TaxWithheld.compareTo(BigDecimal.ZERO) > 0 && state1EIN.isEmpty()) {
                    userErrors.add("Payer's tax id is required if you have withheld state taxes. Please edit the filing and enter a payer's state tax id.");
                }

                StateFilingMethod sfm = stateSub.getFilingMethod();
                if(sfm == null) {
                    userErrors.add("You have selected a state filing, but haven't selected a state filing method. Please select a sate filing method. You may need to scroll to the right in the table to see the state filing method option.");
                } else {
                    //State submission is required and selected, but the filing method selected is not supported for the given criteria.
                    List<StateFilingMethod> allowedStateFilingMethods = filingStateFilingData.getAllowedStateFilingMethods(f.getFilingType(),
                            state1Income, state1TaxWithheld, payerStateCode, payeeStateCode, filingStateCode);
                    if(!allowedStateFilingMethods.contains(sfm)) {
                        userErrors.add("The selected state filing method is not allowed based on the data entered for this filing. Please select a state filing method that is allowed for this filing.");
                    }
                }
            }
        }
    }

    private void validateMEStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN) {
        //For MAINE enforce proper tin type.
        if(StateCode.ME != filingStateCode)
            return ;

        TinType payeeTinType = f.getPayee().getTinType();
        if(TinType.NA == payeeTinType) {
            userErrors.add("The payee tin type is marked as 'don't know'. The state of Maine requires to properly identify the tin type. Please find out the tin type of payee and update the payee in our system.");
        }
    }

    private void validateORStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN) {
        //For OR cleaned EIN should be 8 digit EIN number or 9 digit SSN number.
        if(StateCode.OR != filingStateCode)
            return ;
        boolean isStateTaxWithheld = state1TaxWithheld.compareTo(BigDecimal.ZERO) > 0;
        if(isStateTaxWithheld) {
            String cleanedEIN = state1EIN.replaceAll("[^0-9]", "");
            if(cleanedEIN.length() != 8 || cleanedEIN.length() != 9) {
                userErrors.add("The payer's OR tax id is not valid. Please enter a 8 digit OR BIN or 9 digit SSN of the payer.");
            }
        }

        Domain d = domainRepository.findById(f.getPayer().getDomainId()).orElse(null);
        String payeePlainTIN = f.getPayee().getTinPlain();
        String payerPlainTIN = d.getTinPlain();
        //Remove all non-numeric characters from the tin.
        if(payeePlainTIN != null) {
            payeePlainTIN = payeePlainTIN.replaceAll("[^0-9]", "");
        }
        if(payerPlainTIN != null) {
            payerPlainTIN = payerPlainTIN.replaceAll("[^0-9]", "");
        }
        if(payeePlainTIN != null && payerPlainTIN != null && payeePlainTIN.equals(payerPlainTIN)) {
            userErrors.add("The payer's TIN is same as payee's TIN. Please enter a valid TIN for the payee or delete this filing.");
        }

    }

    private void validateDEStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN) {
        //For DE cleaned EIN should not be more than 20 chars long.
        if(StateCode.DE != filingStateCode)
            return ;
        if(state1EIN != null) {
            String stateEINString = state1EIN.trim();
            if(stateEINString.length() > 20) {
                userErrors.add("The payer's DE state tax id is too long. Please edit the filing and enter a payer's DE state tax id that is 20 characters or less.");
            }
        }
    }

    private void validateNMStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN) {
        if(StateCode.NM != filingStateCode)
            return ;
        boolean isStateTaxWithheld = state1TaxWithheld.compareTo(BigDecimal.ZERO) > 0;
        if(isStateTaxWithheld) {
            String cleanedEIN = state1EIN.replaceAll("[^0-9]", "");
            if( cleanedEIN.length() != 11) {
                userErrors.add("The payer's NM tax id is not valid. Please enter a 11 digit NMBTIN (CRS Identification Number).");
            }
        }
    }

    private void validateMDStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN) {
        if(StateCode.MD != filingStateCode)
            return ;
        boolean isStateTaxWithheld = state1TaxWithheld.compareTo(BigDecimal.ZERO) > 0;
        String cleanedEIN = state1EIN.replaceAll("[^0-9]", "");
        if(isStateTaxWithheld) {
            if( cleanedEIN.length() != 8) {
                userErrors.add("The payer's MD tax id is not valid. Please enter a 8 digit Central Registration Number (CRN).");
            }
        } else {
            if(cleanedEIN.length() > 0 && cleanedEIN.length() != 8) {
                userErrors.add("The payer's MD tax id is not valid. Please enter a 8 digit Central Registration Number (CRN).");
            }
        }
    }

    private void validateVTStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN) {
        String VT_WTH_REGEX = "^WHT[0-9]{8}$";
        if(StateCode.VT != filingStateCode)
            return ;
        boolean isStateTaxWithheld = state1TaxWithheld.compareTo(BigDecimal.ZERO) > 0;
        if(isStateTaxWithheld) {
            if(state1EIN != null && state1EIN.matches(VT_WTH_REGEX)) {
                userErrors.add("The payer's VT tax id is not valid. Please enter VT tax in the format of WHT followed by a 8 digit number.");
            }
        }
    }

    private void validateNCStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN) {
        boolean isStateTaxWithheld = state1TaxWithheld.compareTo(BigDecimal.ZERO) > 0;
        if(StateCode.NC != filingStateCode)
            return ;
        if(isStateTaxWithheld) {
            String cleanedEIN = state1EIN.replaceAll("[^0-9]", "");
            if(cleanedEIN.length() != 9) {
                userErrors.add("The payer's NC tax id is not valid. Please enter the 9 digit NC business registration number.");
            }
        }
    }

    private void validatePAStateRequirements(Filing f, List<String> userErrors, StateCode filingStateCode, BigDecimal state1Income, BigDecimal state1TaxWithheld, String state1EIN, DomainDTO domainDTO) {
        if(StateCode.PA != filingStateCode)
            return ;
        //Check payer tin type.
        if(com.aphe.domain.model.TinType.EIN != domainDTO.tinType && com.aphe.domain.model.TinType.SSN != domainDTO.tinType) {
            userErrors.add("For PA state submissions, payer TIN type must be either EIN or SSN. Click 'Back' button and then 'Edit Payer Info' to update TIN type of payer.");
        }
        String cleanedEIN = state1EIN.replaceAll("[^0-9]", "").trim();
        boolean isStateTaxWithheld = state1TaxWithheld.compareTo(BigDecimal.ZERO) > 0;
        if(isStateTaxWithheld) {
            if(cleanedEIN.length() != 8) {
                userErrors.add("A valid 8 digit PA tax id is required, if state tax is withheld.");
            }
        } else {
            if(cleanedEIN.length()> 0  && cleanedEIN.length() != 8) {
                userErrors.add("The payer's PA tax id is not valid. Please enter a 8 digit PA tax id.");
            }
        }

        //Check payee tin type.
        if(TinType.EIN != f.getPayee().getTinType() && TinType.SSN != f.getPayee().getTinType() && TinType.ITIN != f.getPayee().getTinType()) {
            userErrors.add("For PA state submissions, payee TIN type must be either EIN or SSN or ITIN. Click on payee name to update the payee's TIN type.");
        }
    }

    protected boolean isStateTaxWithheldHigh(BigDecimal stateIncome, BigDecimal stateTaxWithheld) {
        if(stateIncome == null) {
            stateIncome = BigDecimal.ZERO.ZERO;
        }
        if(stateTaxWithheld == null) {
            stateTaxWithheld = BigDecimal.ZERO.ZERO;
        }
        BigDecimal oneThirdAmount = stateIncome.divide(new BigDecimal("3.00"), 2, RoundingMode.HALF_UP);
        return oneThirdAmount.compareTo(stateTaxWithheld) < 0;
    }


    private boolean isFedSubSelected(FedSub fedSub) {
        return fedSub != null && fedSub.getSelected() != null && fedSub.getSelected() == Boolean.TRUE;
    }

    private boolean isStateSubSelected(StateSub stateSub) {
        return stateSub != null && stateSub.getSelected() != null && stateSub.getSelected() == Boolean.TRUE;
    }

    private boolean isEmailSubSelected(EmailSub emailSub) {
        return emailSub != null && emailSub.getSelected() != null && emailSub.getSelected() == Boolean.TRUE;
    }

    private boolean isPrintSubSelected(PrintSub printSub) {
        return printSub != null && printSub.getSelected() != null && printSub.getSelected() == Boolean.TRUE;
    }

    public void addDuplicatePayerWarnings(List<Filing> filings, List<MappedValidationMessages> validationMessages, boolean ignoreWarnings) {
        Payer thePayer = filings.get(0).getPayer();
        String filingYear = filings.get(0).getFilingYear().toString();
        boolean hasDuplicateDomains = domainMgr.getDuplicateDomains(thePayer.getDomainId(), filingYear);
        if(hasDuplicateDomains && !ignoreWarnings) {
            for(Filing f : filings) {
                MappedValidationMessages theMappedValidation = validationMessages.stream().filter(v->v.getId().equalsIgnoreCase(Long.toString(f.getId()))).findFirst().orElse(null);
                if(theMappedValidation == null) {
                    theMappedValidation = new MappedValidationMessages(Long.toString(f.getId()));
                    validationMessages.add(theMappedValidation);
                }
                List<String> warnings = theMappedValidation.getWarnings();
                warnings.add("We have found other accounts with same Payer TIN and submitted filings. Please ensure that you have entered the right Payer TIN to avoid incorrect or duplicate filings. If you are sure that you have the right payer TIN, check 'Ignore warnings' checkbox when submitting your filings.");
            }
        }
    }

    @NotNull
    public void addDuplicateWarnings(List<Filing> filings, List<MappedValidationMessages> validationMessages, boolean ignoreWarnings) {
        if(ignoreWarnings || filings.size() == 0) {
            return;
        }

        Payer thePayer = filings.get(0).getPayer();

        Map<String, List<Filing>> duplicateDrafts = new HashMap<>();
        for (Filing f : filings) {
            String s = f.getPayee().getId() + "-" + f.getFilingType().toString() + "-" + f.getFilingYear().toString() + "-" + f.getCorrectionType().toString();
            List<Filing> dupList = duplicateDrafts.get(s);
            if (dupList == null) {
                dupList = new ArrayList<>();
                duplicateDrafts.put(s, dupList);
            }
            dupList.add(f);
        }
        for (String s : duplicateDrafts.keySet()) {
            if (duplicateDrafts.get(s).size() > 1) {
                for(Filing dupFiling : duplicateDrafts.get(s)) {
                    MappedValidationMessages theMappedValidation = validationMessages.stream().filter(v->v.getId().equalsIgnoreCase(Long.toString(dupFiling.getId()))).findFirst().orElse(null);
                    if(theMappedValidation == null) {
                        theMappedValidation = new MappedValidationMessages(Long.toString(dupFiling.getId()));
                        validationMessages.add(theMappedValidation);
                    }
                    List<String> warnings = theMappedValidation.getWarnings();
                    warnings.add("This filing looks like a " +
                            " duplicate of another filing you are submitting. Please double check the filings you are submitting. If you are sure this is not a duplicate, check 'Ignore warnings' checkbox when submitting your filings.");
                }
            }
        }

        Map<String, List<Filing>> duplicatePrevious = new HashMap<>();
        Map<String, Filing> identifierToFiling = new HashMap<>();

        for (Filing f : filings) {
            String s = f.getPayee().getId() + "-" + f.getFilingType().toString() + "-" + f.getFilingYear().toString() + "-" + f.getCorrectionType().toString();
            duplicatePrevious.put(s, new ArrayList<>());
            identifierToFiling.put(s, f);
        }

        List<Filing> submittedFilings = filingRepo.findByPayerAndStatusIn(thePayer, FilingStatus.submittedStatuses);
        for (Filing f : submittedFilings) {
            String s = f.getPayee().getId() + "-" + f.getFilingType().toString() + "-" + f.getFilingYear().toString() + "-" + f.getCorrectionType().toString();
            if (duplicatePrevious.get(s) == null) {
                continue;
            } else {
                duplicatePrevious.get(s).add(f);
            }
        }
        for (String filingIdentifier : duplicatePrevious.keySet()) {
            if (duplicatePrevious.get(filingIdentifier).size() > 0) {
                Filing erroredFiling = identifierToFiling.get(filingIdentifier);
                MappedValidationMessages theMappedValidation = validationMessages.stream().filter(v-> {
                    return v.getId().equalsIgnoreCase(Long.toString(erroredFiling.getId()));
                }).findFirst().orElse(null);
                if(theMappedValidation == null) {
                    theMappedValidation = new MappedValidationMessages(Long.toString(erroredFiling.getId()));
                    validationMessages.add(theMappedValidation);
                }
                List<String> warnings = theMappedValidation.getWarnings();

                warnings.add("This filing looks like a duplicate of an already submitted filing. Please double check your previously submitted filings. If you are sure this is not a duplicate, check 'Ignore warnings' checkbox when submitting your filings.");
            }
        }

    }

    public void addFIRECorrectionErrors(List<Filing> filings, List<MappedValidationMessages> validationMessages, DomainDTO domainDTO, boolean ignoreWarnings) {
        List<Filing> correctionFilings = filings.stream().filter(filing -> isCorrection(filing)).collect(Collectors.toList());
        if (correctionFilings.size() == 0) {
            return;
        }
        //Initialize the error messages.
        HashMap<Filing, List<String>> correctionErrors = new HashMap<>();
        for(Filing f : filings) {
            correctionErrors.put(f, new ArrayList<>());
        }
        HashMap<Filing, List<String>> correctionWarnings = new HashMap<>();
        for(Filing f : filings) {
            correctionWarnings.put(f, new ArrayList<>());
        }


        //form correction pairs.
        HashMap<Long, List<Filing>> correctionPairs = new HashMap<>();
        for (Filing f : filings) {
            if (isCorrection(f)) {
                long originalFilingId = f.getOriginalFilingId() != null ? f.getOriginalFilingId() : 0;

                StateSub stateSub = f.getStateSub();
                if(isStateSubSelected(stateSub)) {
                    StateCode filingStateCode = convertUtil.getJSONLocalStateCode(f.getFilingData(), "state1Code", null);
                    if(filingStateCode != null) {
                        StateFilingData stateFilingData = filingStateCode != null ? StateFilingData.getStateFilingData(filingStateCode.name()) : null;
                        if (stateFilingData != null) {
                            List<StateFilingMethod> correctionMethods = stateFilingData.getAllowedFilingMethodsForCorrections(f.getFilingType());
                            if (!correctionMethods.contains(stateSub.getFilingMethod())) {
                                correctionErrors.get(f).add("Selected filing method is not supported for corrections. Either unselect the state filing check box or select Manual method.");
                            }
                        }
                    }
                } else {
                    if(!ignoreWarnings) {
                        correctionWarnings.get(f).add("States require you to report corrections too. Please make sure you file them on your own.");
                    }
                }

                //Make sure original filing is there.
                if (originalFilingId <= 0) {
                    correctionErrors.get(f).add("This filing is a correction filing and doesn't have an associated original filing. Please edit the correction filing and select the original filing.");
                }
                //Make sure it is accepted.
                //TODO: operation on DTO. What is the txn implication?
                Filing originalFiling = filingRepo.findById(originalFilingId).orElse(null);
                if (originalFiling == null) {
                    correctionErrors.get(f).add("This filing is a correction filing and we couldn't find the associated original filing.");
                } else if (originalFiling.getFedSub().getStatus() != FilingStatus.Accepted) {
                    correctionErrors.get(f).add("This filing is a correction filing and the associated filing is not accepted yet. Please wait until the original filing is accepted.");
                } else {
                    boolean fireFormat = isFIREFormat(originalFiling);
                    if(!fireFormat) {
                        continue;
                    }
                    List<Filing> corrections = correctionPairs.get(originalFilingId);
                    if (corrections == null) {
                        corrections = new ArrayList<>();
                        correctionPairs.put(originalFilingId, corrections);
                    }
                    corrections.add(f);
                }
            }
        }
        //By this time, filings are paired up and warnings are added if state filing is not selected or if filing method is not supported for correction.

        //Validate correction pairs counts..
        for (Long originalFilingId : correctionPairs.keySet()) {
            Filing originalFiling = filingRepo.findById(originalFilingId).orElse(null);
            List<Filing> corrections = correctionPairs.get(originalFilingId);


            List<String> pairValidations = new ArrayList<>();

            boolean twoPart = false;


            int firstCount = 0;
            int secondCount = 0;
            Filing firstFiling = null;
            Filing secondFiling = null;
            for (Filing f : corrections) {
                if (f.getCorrectionType() == com.aphe.contractor.model.enums.CorrectionType.First) {
                    firstCount++;
                    firstFiling = f;
                }
                if (f.getCorrectionType() == com.aphe.contractor.model.enums.CorrectionType.Second) {
                    secondCount++;
                    secondFiling = f;
                }
            }

            if (corrections.size() > 2) {
                pairValidations.add("This filing is a correction filing and has more than 2 corrections associated. Please follow instructions from our corrections guide. Please contact us if you need help.");
            } else if (corrections.size() == 2) {
                twoPart = true;
                if (firstCount != 1 || secondCount != 1) {
                    pairValidations.add("This filing is a correction filing and has incorrect combination of 'First' and 'Second' corrections. Please follow instructions from our corrections guide. Please contact us if you need help.");
                }
            } else if (corrections.size() == 1) {
                twoPart = false;
                if (firstCount != 1) {
                    pairValidations.add("This filing is a correction filing and doesn't have a corresponding 'First' correction. Make sure you have selected the 'First' correction. Please contact us if you need help.");
                }
            }
            // Count validations are done by here.

            //Continue data validations for these pairs.
            try {
                long federalEFSFilingId = originalFiling.getFedSub().getFilingRequestId();
                long stateEFSFilingId = -1;
                StateSub stateSub = originalFiling.getStateSub();
                if(stateSub != null && stateSub.getSelected() != null && stateSub.getSelected() == Boolean.TRUE) {
                    stateEFSFilingId = stateSub.getFilingRequestId();
                }

                EFSFiling originalEFSFiling = efsFilingsMgr.getFiling(originalFiling.getFedSub().getFilingRequestId());
                FilingDataDTO originalFederalDTO = efsFilingsMgr.convertToDTO(originalEFSFiling);
                FilingDataDTO originalStateDTO = null;
                if(stateEFSFilingId > 0 && federalEFSFilingId != stateEFSFilingId) {
                    EFSFiling stateEFSFiling = efsFilingsMgr.getFiling(stateEFSFilingId);
                    originalStateDTO = efsFilingsMgr.convertToDTO(stateEFSFiling);
                }

//                List<FilingDataDTO> originalEFSFilingDTOs = convertUtil.convertToEFSFilingDTOs(originalFiling, Long.toString(domainDTO.id), true, null, null);
//                FilingDataDTO originalFederalDTO = originalEFSFilingDTOs.stream().filter(f -> f.stateFiling == false).findFirst().orElse(null);
//                FilingDataDTO originalStateDTO = originalEFSFilingDTOs.stream().filter(f -> f.stateFiling == true).findFirst().orElse(null);

                if (originalFederalDTO == null) {
                    pairValidations.add("This filing is a correction filing and we couldn't find original filing. Please contact us.");
                } else {

                    // TODO: what do we do direct state corrections. Block them and say you need to file manually.
                    //Validate First
                    if (firstFiling != null) {
                        List<FilingDataDTO> correctionDTOs = convertUtil.convertToEFSFilingDTOs(firstFiling, domainDTO.id, true, secondFiling, originalFiling);
                        FilingDataDTO firstFederalDTO = correctionDTOs.stream().filter(f -> f.stateFiling == false && f.correctionType == com.aphe.efs.model.enums.CorrectionType.First).findFirst().orElse(null);
                        FilingDataDTO firstStateDTO = correctionDTOs.stream().filter(f -> f.stateFiling == true && f.correctionType == com.aphe.efs.model.enums.CorrectionType.First).findFirst().orElse(null);
                        FilingDataDTO secondFederalDTO = null;
                        FilingDataDTO secondStateDTO = null;
                        if (secondFiling != null) {
                            //Validate second filing DTO
                            secondFederalDTO = correctionDTOs.stream().filter(f -> f.stateFiling == false && f.correctionType == com.aphe.efs.model.enums.CorrectionType.Second).findFirst().orElse(null);
                            secondStateDTO = correctionDTOs.stream().filter(f -> f.stateFiling == true && f.correctionType == com.aphe.efs.model.enums.CorrectionType.Second).findFirst().orElse(null);
                        }

                        /**
                         * By now we have all possible 6 DTOs.
                         * First correction only for CFSF state -- only 2 -- originalFederalDTO, firstFederalDTO.
                         * First correction only for direct state  -- 4 filings -- originalFederalDTO, firstFederalDTO, originalFedDTO, firstStateDTO.
                         * First correction for PA - 4 filings (possibly 5, but ignoring the last),
                         *
                         * Second correction for CFSF State -- 3 filings -- originalFederalDTO, firstFederalDTO, secondFederalDTO
                         * Second correction for direct State -- 6 filings -- originalFederalDTO, firstFederalDTO, secondFederalDTO, originalStateDTO, firstStateDTO, secondStateDTO.
                         * First correction for PA -- 6 filings -- originalFederalDTO, firstFederalDTO, secondFederalDTO, originalStateDTO, firstStateDTO, secondStateDTO
                         */

                        //Make sure state is submission is selected, if original was filed with state. If we don't support corrections for the original state, manual is ok.
                        validateStateSelectionAndStateFilingMethod(correctionErrors, originalFiling, firstFiling, originalFederalDTO, firstFederalDTO);

                        validateCorrectionPair(correctionErrors, twoPart, firstFiling, secondFiling, firstFederalDTO, secondFederalDTO, originalEFSFiling, originalFederalDTO);
                        if (firstStateDTO != null) {
                            //Validate state DTOs.. do we even need this... now that we have moved up the state filing method check.
                            validateCorrectionPair(correctionErrors, twoPart, firstFiling, secondFiling, firstStateDTO, secondStateDTO, originalEFSFiling, originalStateDTO);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("Error submitting a correction form", e);
                pairValidations.add("This filing had encountered an unknown error. Please check the filing data or contact us.");
            }
            //Add these pair validations on each of the correction filing pair.
            if (pairValidations.size() > 0) {
                for(Filing f : corrections) {
                    correctionErrors.get(f).addAll(pairValidations);
                }
            }
        }

        Set<Filing> filingsWithMessages = new HashSet<>(correctionErrors.keySet());
        filingsWithMessages.addAll(correctionWarnings.keySet());

        for(Filing f : filingsWithMessages) {
            MappedValidationMessages theMappedValidation = validationMessages.stream().filter(v->v.getId().equalsIgnoreCase(Long.toString(f.getId()))).findFirst().orElse(null);
            if(theMappedValidation == null) {
                theMappedValidation = new MappedValidationMessages(Long.toString(f.getId()));
                validationMessages.add(theMappedValidation);
            }
            List<String> errors = theMappedValidation.getErrors();
            List<String> warnings = theMappedValidation.getWarnings();
            errors.addAll(correctionErrors.get(f));
            warnings.addAll(correctionWarnings.get(f));
        }
    }

    private boolean isFIREFormat(Filing originalFiling) {
        //TODO: This will not work all the time as for some filings we may enter random ACK number that doesn't match the check.
        String uniqueRecordId = originalFiling.getFedSub().getUniqueRecordId();
        if(uniqueRecordId == null || uniqueRecordId.contains("52F70")) {
            return true;
        }
        return false;
    }

    public void addIRISCorrectionErrors(List<Filing> filings, List<MappedValidationMessages> validationMessages, DomainDTO domainDTO, boolean ignoreWarnings) {

        List<Filing> correctionFilings = filings.stream().filter(filing -> isCorrection(filing)).collect(Collectors.toList());
        if (correctionFilings.size() == 0) {
            return;
        }

        //Initialize the error and warning messages.
        HashMap<Filing, List<String>> correctionErrors = new HashMap<>();
        for(Filing f : filings) {
            correctionErrors.put(f, new ArrayList<>());
        }
        HashMap<Filing, List<String>> correctionWarnings = new HashMap<>();
        for(Filing f : filings) {
            correctionWarnings.put(f, new ArrayList<>());
        }

        //form correction pairs.j
        HashMap<String, Filing> correctionPairs = new HashMap<>();
        for (Filing f : correctionFilings) {
            String uniqueRecordId = null;
            Long originalFilingId = f.getOriginalFilingId() != null ? f.getOriginalFilingId() : 0;
            if(originalFilingId <= 0) {
                //Check if original record identifier is stored...
//                uniqueRecordId = f.getOriginalRecordId();
            } else {
                Filing originalFiling = filingRepo.findById(originalFilingId).orElse(null);
                if(originalFiling != null) {
                    boolean isFIREFormat = isFIREFormat(originalFiling);
                    if(isFIREFormat) {
                        continue;
                    }
                    uniqueRecordId = f.getFedSub().getUniqueRecordId();
                } else {
                    correctionErrors.get(f).add("Original filing id is missing. Please make sure you enter a valid original filing.");
                    continue;
                }
            }

            if(uniqueRecordId == null) {
                //This is a filing with no unique record id.
                correctionErrors.get(f).add("Original record id is missing for this correction.");
                continue;
            }

            //TODO: check for the original filing id format.
            //TODO: make sure no other correction was filed from our system.
            if(correctionPairs.get(uniqueRecordId) != null) {
                correctionErrors.get(f).add("You are trying to submit more than one correction filing against a filing. You can only file one correction against a filing.");
            }

            //Make sure the original filing is accepted.
            FedSub originalFedSub = fedSubRepository.findByUniqueRecordId(uniqueRecordId);
            if (originalFedSub == null) {
                correctionWarnings.get(f).add("The original filing is filed outside of our system. Please make sure you have entered the correct unique record id from your previous service provider. Also ensure that payer information is exactly same as your previous filing.");
            } else {
                if (originalFedSub.getStatus() != FilingStatus.Accepted) {
                    correctionErrors.get(f).add("This filing is a correction filing and the associated original filing is not accepted yet. Please wait until the original filing is accepted.");
                }
            }

            //TODO: Revisit this.. what do we do?
            StateSub stateSub = f.getStateSub();
            if(isStateSubSelected(stateSub)) {
                StateCode filingStateCode = convertUtil.getJSONLocalStateCode(f.getFilingData(), "state1Code", null);
                if(filingStateCode != null) {
                    StateFilingData stateFilingData = filingStateCode != null ? StateFilingData.getStateFilingData(filingStateCode.name()) : null;
                    if (stateFilingData != null) {
                        List<StateFilingMethod> correctionMethods = stateFilingData.getAllowedFilingMethodsForCorrections(f.getFilingType());
                        if (!correctionMethods.contains(stateSub.getFilingMethod())) {
                            correctionErrors.get(f).add("Selected filing method is not supported for corrections. Either unselect the state filing check box or select Manual method.");
                        }
                    }
                }
            } else {
                if(!ignoreWarnings) {
                    correctionWarnings.get(f).add("States require you to report corrections too. Please make sure you file them on your own.");
                }
            }

            if(correctionErrors.get(f).size() > 0) {
                continue;
            }
            correctionPairs.put(uniqueRecordId, f);
        }
        //By this time, filings are paired up and warnings are added if state filing is not selected or if filing method is not supported for correction.

        //Validate correction pairs counts..
        for (String uniqueRecordId : correctionPairs.keySet()) {
            FedSub originalFedSub = fedSubRepository.findByUniqueRecordId(uniqueRecordId);
            Filing correction = correctionPairs.get(uniqueRecordId);

            if(originalFedSub != null) {
                Filing originalFiling = originalFedSub.getFiling();

                //Validate the filing year.
                FilingYear originalFilingYear = originalFiling.getFilingYear();
                FilingYear correctionFilingYear = correction.getFilingYear();
                if(originalFilingYear != correctionFilingYear) {
                    correctionErrors.get(correction).add("The correction filing year is not same as the original filing.");
                }

                //Filing return type is same too.
                FilingReturnType originalFormType = originalFiling.getFilingType();
                FilingReturnType correctionFormType = correction.getFilingType();
                if(correctionFormType != originalFormType) {
                    //Check if original filing itself is a correction.
                    if(!isCorrection(originalFiling)) {
                        correctionErrors.get(correction).add("The correction filing form type is not same as the original filing. To change form type, you first need to file a correction with the same form type and zero out the amounts, then file another correction to change form type.");
                    }
                }

                //Make sure payer info is correct.
                try {
                    EFSFiling originalFederalEFSFiling = efsFilingsMgr.getFiling(originalFiling.getFedSub().getFilingRequestId());
                    FilingDataDTO originalFederalDTO = efsFilingsMgr.convertToDTO(originalFederalEFSFiling);
                    EFSFiling originalStateEFSFiling = efsFilingsMgr.getFiling(originalFiling.getStateSub().getFilingRequestId());
                    FilingDataDTO originalStateDTO = null;
                    if(originalStateEFSFiling != null ) {
                        originalStateDTO = efsFilingsMgr.convertToDTO(originalStateEFSFiling);
                    }

                    if (originalFederalDTO == null) {
                        correctionErrors.get(correction).add("This filing is a correction filing and we couldn't find original filing. Please contact us.");
                    }

                    List<FilingDataDTO> correctionDTOs = convertUtil.convertToEFSFilingDTOs(correction, domainDTO.id, true, null, originalFiling);
                    FilingDataDTO firstFederalDTO = correctionDTOs.stream().filter(f -> f.stateFiling == false && f.correctionType == com.aphe.efs.model.enums.CorrectionType.First).findFirst().orElse(null);
                    FilingDataDTO firstStateDTO = correctionDTOs.stream().filter(f -> f.stateFiling == true && f.correctionType == com.aphe.efs.model.enums.CorrectionType.First).findFirst().orElse(null);


                    //Check if the state filing check box is same as the original and filing method (enforces cfsf to cfsf, other to other.) is same as the original.
                    validateStateSelectionAndStateFilingMethod(correctionErrors, originalFiling, correction, originalFederalDTO, firstFederalDTO);

                    validateIRISCorrection(correctionErrors, correction, firstFederalDTO, originalFederalEFSFiling, originalFederalDTO);
                    if (firstStateDTO != null) {
                        validateIRISCorrection(correctionErrors, correction, firstStateDTO, originalStateEFSFiling, originalStateDTO);
                    }
                } catch (Exception e) {
                    logger.error("Error submitting a correction form", e);
                    correctionErrors.get(correction).add("This filing had encountered an unknown error. Please check the filing data or contact us.");
                }
            }
        }

        Set<Filing> filingsWithMessages = new HashSet<>(correctionErrors.keySet());
        filingsWithMessages.addAll(correctionWarnings.keySet());

        for(Filing f : filingsWithMessages) {
            MappedValidationMessages theMappedValidation = validationMessages.stream().filter(v->v.getId().equalsIgnoreCase(Long.toString(f.getId()))).findFirst().orElse(null);
            if(theMappedValidation == null) {
                theMappedValidation = new MappedValidationMessages(Long.toString(f.getId()));
                validationMessages.add(theMappedValidation);
            }
            List<String> errors = theMappedValidation.getErrors();
            List<String> warnings = theMappedValidation.getWarnings();
            errors.addAll(correctionErrors.get(f));
            warnings.addAll(correctionWarnings.get(f));
        }
    }

    private void validateIRISCorrection(HashMap<Filing, List<String>> correctionErrors, Filing correctionFiling, FilingDataDTO correctionDTO, EFSFiling originalEFSFiling, FilingDataDTO originalEFSFilingDTO) {
        String originalPayerTIN = originalEFSFiling.getPayerTinPlain();
        List<List<String>> differingPayerAttributes = comparePayerPart(originalEFSFilingDTO, correctionDTO, originalPayerTIN);
        if (differingPayerAttributes.size() > 0) {
            for (List<String> nonMatchingAttribute : differingPayerAttributes) {
                String errorMessage = "This filing is a correction filing and payer's " + nonMatchingAttribute.get(0) + " doesn't match the original filing. The old value was '" + nonMatchingAttribute.get(1) + "' and the new value is '" + nonMatchingAttribute.get(2) + "'. The payer details of a correction filing need to match the payer details of the original filing. Please update the payer details to the original filing values.";
                correctionErrors.get(correctionFiling).add(errorMessage);
            }
        }
    }



    private void validateStateSelectionAndStateFilingMethod(HashMap<Filing, List<String>> correctionErrors, Filing originalFiling, Filing firstFiling, FilingDataDTO originalFederalDTO, FilingDataDTO firstFederalDTO) {
        String originalStateFilingMethod = "None";
        String firstStateFilingMethod = "None";
        boolean originalStateFiling = false;
        boolean firstStateFiling = false;
        if(originalFiling.getStateSub().getSelected() !=null && originalFiling.getStateSub().getSelected().booleanValue() == true) {
            originalStateFiling = true;
            if (originalFederalDTO.reportUsingCFSF != null && originalFederalDTO.reportUsingCFSF.booleanValue() == true) {
                originalStateFilingMethod = "Combined Fed/State Filing";
            } else {
                originalStateFilingMethod = "EFile or manual";
            }
        }

        if(firstFiling.getStateSub().getSelected() !=null && firstFiling.getStateSub().getSelected().booleanValue() == true) {
            firstStateFiling = true;
            if (firstFederalDTO.reportUsingCFSF != null && firstFederalDTO.reportUsingCFSF.booleanValue() == true) {
                firstStateFilingMethod = "Combined Fed/State Filing";
            } else {
                firstStateFilingMethod = "EFile or manual";
            }
        }
        if(originalStateFiling != firstStateFiling) {
            correctionErrors.get(firstFiling).add("This filing is a correction filing and the state filing selection is different from the original filing. The old value was '" + originalStateFiling + "' and the new value is '" + firstStateFiling + "'. Please update state filing selection.");
        } else {
            if(!originalStateFilingMethod.equalsIgnoreCase(firstStateFilingMethod)) {
                correctionErrors.get(firstFiling).add("This filing is a correction filing and the state filing method is different from the original filing. The old value was '" + originalStateFilingMethod + "' and the new value is '" + firstStateFilingMethod + "'. Please update state filing method.");
            }
        }
    }

    private void validateCorrectionPair(HashMap<Filing, List<String>> correctionErrors, boolean twoPart, Filing firstFiling, Filing secondFiling, FilingDataDTO firstFilingDTO, FilingDataDTO secondFilingDTO, EFSFiling originalEFSFiling, FilingDataDTO originalEFSFilingDTO) {
        String originalPayerTIN = originalEFSFiling.getPayerTinPlain();
        String originalPayeeTIN = originalEFSFiling.getPayeeTinPlain();

        List<List<String>> differingPayerAttributes = comparePayerPart(originalEFSFilingDTO, firstFilingDTO, originalPayerTIN);
        if (differingPayerAttributes.size() > 0) {
            for (List<String> nonMatchingAttribute : differingPayerAttributes) {
                String errorMessage = "This filing is a correction filing and payer's " + nonMatchingAttribute.get(0) + " doesn't match the original filing. The old value was '" + nonMatchingAttribute.get(1) + "' and the new value is '" + nonMatchingAttribute.get(2) + "'. The payer details of a correction filing need to match the payer details of the original filing. Please update the payer details to the original filing values.";
                correctionErrors.get(firstFiling).add(errorMessage);
            }
        }

        List<List<String>> differingFilingAttributes = compareFilingAttributes(originalEFSFilingDTO, firstFilingDTO, twoPart, false);
        if (differingFilingAttributes.size() > 0) {
            for (List<String> nonMatchingAttribute : differingFilingAttributes) {
                String errorMessage = "This filing is a correction filing and filing's " + nonMatchingAttribute.get(0) + " doesn't match the original filing. The old value was '" + nonMatchingAttribute.get(1) + "' and the new value is '" + nonMatchingAttribute.get(2) + "'. The first correction values need to match the original filing. Please update the filing details to the original filing values.";
                correctionErrors.get(firstFiling).add(errorMessage);
            }
        }

        if (twoPart) {

            boolean areAmountsZero = compareBoxAmounts(firstFilingDTO);
            if (!areAmountsZero) {
                //do not validation if the reportingStateCode is PA
                if (com.aphe.efs.model.enums.StateCode.PA != firstFilingDTO.reportingStateCode) {
                    correctionErrors.get(firstFiling).add("This filing is the first part of two part correction filing and the first correction filing has non-zero values in the box amounts. The first correction filing should have zero values in the box amounts. Please update the box amounts to zero.");
                }
            }

            List<List<String>> differingPayeeAttributes = comparePayeePartAll(originalEFSFilingDTO, firstFilingDTO, originalPayeeTIN);
            if (differingPayeeAttributes.size() > 0) {
                for (List<String> nonMatchingAttribute : differingPayeeAttributes) {
                    String e = "This filing is the first part of two part correction filing and payee's " + nonMatchingAttribute.get(0) + " doesn't match the original filing. The old value was '" + nonMatchingAttribute.get(1) + "' and the new value is '" + nonMatchingAttribute.get(2) + "'. The first correction payee details need to match the payee details of the original filing. Please update the payee details to the original filing values.";
                    correctionErrors.get(firstFiling).add(e);
                }
            }
        } else {
            List<List<String>> differingPayeeAttributes = comparePayeePart(originalEFSFilingDTO, firstFilingDTO, originalPayeeTIN);
            if (differingPayeeAttributes.size() > 0) {
                for (List<String> nonMatchingAttribute : differingPayeeAttributes) {
                    String e = "The payee's " + nonMatchingAttribute.get(0) + " doesn't match the original filing. The old value was '" + nonMatchingAttribute.get(1) + "' and the new value is '" + nonMatchingAttribute.get(2) + "'. The first correction payee details need to match the payee details of the original filing. Please update the payee details to the original filing values.";
                    correctionErrors.get(firstFiling).add(e);
                }
            }
            // This is one part correction. Something has to be different from previous filing except for basic payee info
            //TODO: make sure at something is different about b record data. ignore this validation for now.
        }

        if(secondFilingDTO != null) {
            //Validate second filing DTO
            List<List<String>> secondFilingDifferingPayerAttributes = comparePayerPart(originalEFSFilingDTO, secondFilingDTO, originalPayerTIN);
            if (secondFilingDifferingPayerAttributes.size() > 0) {
                for (List<String> nonMatchingAttribute : secondFilingDifferingPayerAttributes) {
                    String e = "This filing is a correction filing and payer's " + nonMatchingAttribute.get(0) + " doesn't match the original filing. The old value was '" + nonMatchingAttribute.get(1) + "' and the new value is '" + nonMatchingAttribute.get(2) + "'. The payer details of a correction filing need to match the payer details of the original filing. Please update the payer details to the original filing values.";
                    correctionErrors.get(secondFiling).add(e);
                }
            }

            //this call is not really validating anything on the second filing, because all the valdiation in compareFilingAttributes is done on the first filing.
            List<List<String>> differingFilingAttributesSecondCorrection = compareFilingAttributes(originalEFSFilingDTO, secondFilingDTO, twoPart, true);
            if (differingFilingAttributesSecondCorrection.size() > 0) {
                for (List<String> nonMatchingAttribute : differingFilingAttributesSecondCorrection) {
                    String e = "This filing is a correction filing and filing's " + nonMatchingAttribute.get(0) + " doesn't match the original filing. The old value was '" + nonMatchingAttribute.get(1) + "' and the new value is '" + nonMatchingAttribute.get(2) + "'. This value need to match the original filing value. Please update the filing to match the original filing value.";
                    correctionErrors.get(secondFiling).add(e);
                }
            }
        }
    }

    private boolean compareBoxAmounts(FilingDataDTO firstFilingDTO) {

        boolean allZero = false;
        if (firstFilingDTO instanceof FilingDataDTO1099MISC2020) {
            FilingDataDTO1099MISC2020 originalMisc = (FilingDataDTO1099MISC2020) firstFilingDTO;
            allZero = isZero(originalMisc.rents)
                    && isZero(originalMisc.royalties)
                    && isZero(originalMisc.otherIncome)
                    && isZero(originalMisc.federalTaxWithheld)
                    && isZero(originalMisc.fishingBoatProceeds)
                    && isZero(originalMisc.medicalPayments)
                    && isZero(originalMisc.substitutePaymentsForDivInt)
                    && isZero(originalMisc.cropInsuranceProceeds)
                    && isZero(originalMisc.proceedsPaidToAttorney)
                    && isZero(originalMisc.four09Deferrals)
                    && isZero(originalMisc.goldenParachutePayment)
                    && isZero(originalMisc.nonQualifiedDeferredComp)
                    && isZero(originalMisc.state1Income)
                    && isZero(originalMisc.state1TaxWithheld);
        } else if (firstFilingDTO instanceof FilingDataDTO1099MISC2021) {
            FilingDataDTO1099MISC2021 originalMisc = (FilingDataDTO1099MISC2021) firstFilingDTO;
            allZero = isZero(originalMisc.rents)
                    && isZero(originalMisc.royalties)
                    && isZero(originalMisc.otherIncome)
                    && isZero(originalMisc.federalTaxWithheld)
                    && isZero(originalMisc.fishingBoatProceeds)
                    && isZero(originalMisc.medicalPayments)
                    && isZero(originalMisc.substitutePaymentsForDivInt)
                    && isZero(originalMisc.cropInsuranceProceeds)
                    && isZero(originalMisc.proceedsPaidToAttorney)
                    && isZero(originalMisc.fishPurchasedForResale)
                    && isZero(originalMisc.four09Deferrals)
                    && isZero(originalMisc.goldenParachutePayment)
                    && isZero(originalMisc.nonQualifiedDeferredComp)
                    && isZero(originalMisc.state1Income)
                    && isZero(originalMisc.state1TaxWithheld);
        } else if (firstFilingDTO instanceof FilingDataDTO1099NEC) {
            FilingDataDTO1099NEC correctedNEC = (FilingDataDTO1099NEC) firstFilingDTO;
            allZero = isZero(correctedNEC.nonEmployeeComp)
                    && isZero(correctedNEC.federalTaxWithheld)
                    && isZero(correctedNEC.state1Income)
                    && isZero(correctedNEC.state1TaxWithheld);
        } else if (firstFilingDTO instanceof FilingDataDTO1099NEC2021) {
            FilingDataDTO1099NEC2021 correctedNEC = (FilingDataDTO1099NEC2021) firstFilingDTO;
            allZero = isZero(correctedNEC.nonEmployeeComp)
                    && isZero(correctedNEC.federalTaxWithheld)
                    && isZero(correctedNEC.state1Income)
                    && isZero(correctedNEC.state1TaxWithheld);
        } else if (firstFilingDTO instanceof FilingDataDTO1099INT) {
            FilingDataDTO1099INT correctedNEC = (FilingDataDTO1099INT) firstFilingDTO;
            allZero = isZero(correctedNEC.interestIncome)
                    && isZero(correctedNEC.earlyWithdrawalPenalty)
                    && isZero(correctedNEC.interestOnUSSavingsBonds)
                    && isZero(correctedNEC.federalTaxWithheld)
                    && isZero(correctedNEC.investmentExpenses)
                    && isZero(correctedNEC.foreignTaxPaid)
                    && isZero(correctedNEC.taxExemptInterest)
                    && isZero(correctedNEC.specifiedPrivateActivityBondInterest)
                    && isZero(correctedNEC.marketDiscount)
                    && isZero(correctedNEC.bondPremium)
                    && isZero(correctedNEC.bondPremiumOnTreasuryObligations)
                    && isZero(correctedNEC.bondPremiumOnTaxExemptBond)
                    && isZero(correctedNEC.state1TaxWithheld);
        } else {
            throw new RuntimeException("Unknown filing type");
        }

        return allZero;
    }

    private boolean isZero(String s) {
        if (s == null) {
            return true;
        }
        return new BigDecimal(s).compareTo(BigDecimal.ZERO) <= 0;
    }


    private List<List<String>> compareFilingAttributes(FilingDataDTO originalEFSFilingDTO, FilingDataDTO correctionFilingDTO, boolean isTwoPart, boolean isSecondCorrection) {
        List<List<String>> nonMatchingAttributes = new ArrayList<>();
        compareAndAdd(originalEFSFilingDTO.filingYear.getYear(), correctionFilingDTO.filingYear.getYear(), nonMatchingAttributes, "filing year");
        boolean isFirstCorrection = !isSecondCorrection;

        if (isFirstCorrection) {
            compareAndAdd(originalEFSFilingDTO.filingReturnType.name(), correctionFilingDTO.filingReturnType.name(), nonMatchingAttributes, "filing type");

            if (originalEFSFilingDTO instanceof FilingDataDTO1099MISC2020 && correctionFilingDTO instanceof FilingDataDTO1099MISC2020) {
                FilingDataDTO1099MISC2020 originalMisc = (FilingDataDTO1099MISC2020) originalEFSFilingDTO;
                FilingDataDTO1099MISC2020 correctedMisc = (FilingDataDTO1099MISC2020) correctionFilingDTO;
                compareAndAddStateCode(originalMisc.state1Code, correctedMisc.state1Code, nonMatchingAttributes, "State 1 Code");
                compareAndAdd(originalMisc.state1EIN, correctedMisc.state1EIN, nonMatchingAttributes, "State 1 EIN");
            } else if (originalEFSFilingDTO instanceof FilingDataDTO1099MISC2021 && correctionFilingDTO instanceof FilingDataDTO1099MISC2021) {
                FilingDataDTO1099MISC2021 originalMisc = (FilingDataDTO1099MISC2021) originalEFSFilingDTO;
                FilingDataDTO1099MISC2021 correctedMisc = (FilingDataDTO1099MISC2021) correctionFilingDTO;
                compareAndAddStateCode(originalMisc.state1Code, correctedMisc.state1Code, nonMatchingAttributes, "State 1 Code");
                compareAndAdd(originalMisc.state1EIN, correctedMisc.state1EIN, nonMatchingAttributes, "State 1 EIN");
            } else if (originalEFSFilingDTO instanceof FilingDataDTO1099NEC && correctionFilingDTO instanceof FilingDataDTO1099NEC) {
                FilingDataDTO1099NEC originalNEC = (FilingDataDTO1099NEC) originalEFSFilingDTO;
                FilingDataDTO1099NEC correctedNEC = (FilingDataDTO1099NEC) correctionFilingDTO;
                compareAndAddStateCode(originalNEC.state1Code, correctedNEC.state1Code, nonMatchingAttributes, "State 1 Code");
                compareAndAdd(originalNEC.state1EIN, correctedNEC.state1EIN, nonMatchingAttributes, "State 1 EIN");
            } else if (originalEFSFilingDTO instanceof FilingDataDTO1099NEC2021 && correctionFilingDTO instanceof FilingDataDTO1099NEC2021) {
                FilingDataDTO1099NEC2021 originalNEC = (FilingDataDTO1099NEC2021) originalEFSFilingDTO;
                FilingDataDTO1099NEC2021 correctedNEC = (FilingDataDTO1099NEC2021) correctionFilingDTO;
                compareAndAddStateCode(originalNEC.state1Code, correctedNEC.state1Code, nonMatchingAttributes, "State 1 Code");
                compareAndAdd(originalNEC.state1EIN, correctedNEC.state1EIN, nonMatchingAttributes, "State 1 EIN");
            } else if (originalEFSFilingDTO instanceof FilingDataDTO1099INT && correctionFilingDTO instanceof FilingDataDTO1099INT) {
                FilingDataDTO1099INT originalINT = (FilingDataDTO1099INT) originalEFSFilingDTO;
                FilingDataDTO1099INT correctedINT = (FilingDataDTO1099INT) correctionFilingDTO;
                compareAndAddStateCode(originalINT.state1Code, correctedINT.state1Code, nonMatchingAttributes, "State 1 Code");
                compareAndAdd(originalINT.state1EIN, correctedINT.state1EIN, nonMatchingAttributes, "State 1 EIN");
            } else {
                throw new RuntimeException("Unknown filing type");
            }
        }

        if (isTwoPart) {
            if (isFirstCorrection) {
                compareAndAddBoolean(originalEFSFilingDTO.lastFilingIndicator, correctionFilingDTO.lastFilingIndicator, nonMatchingAttributes, "last filing indicator flag");

                if (originalEFSFilingDTO instanceof FilingDataDTO1099MISC2020 && correctionFilingDTO instanceof FilingDataDTO1099MISC2020) {
                    FilingDataDTO1099MISC2020 originalMisc = (FilingDataDTO1099MISC2020) originalEFSFilingDTO;
                    FilingDataDTO1099MISC2020 correctedMisc = (FilingDataDTO1099MISC2020) correctionFilingDTO;
                    compareAndAddBoolean(originalMisc.secondTinNotice, correctedMisc.secondTinNotice, nonMatchingAttributes, "2nd TIN notice flag");
                    compareAndAddBoolean(originalMisc.fatcaFilingRequirementIndicator, correctedMisc.fatcaFilingRequirementIndicator, nonMatchingAttributes, "FATCA indicator flag");
//                    compareAndAddBoolean(originalMisc.directSalesIndicator, correctedMisc.directSalesIndicator, nonMatchingAttributes, "direct sales indicator flag");
                } else if (originalEFSFilingDTO instanceof FilingDataDTO1099MISC2021 && correctionFilingDTO instanceof FilingDataDTO1099MISC2021) {
                    FilingDataDTO1099MISC2021 originalMisc = (FilingDataDTO1099MISC2021) originalEFSFilingDTO;
                    FilingDataDTO1099MISC2021 correctedMisc = (FilingDataDTO1099MISC2021) correctionFilingDTO;
                    compareAndAddBoolean(originalMisc.secondTinNotice, correctedMisc.secondTinNotice, nonMatchingAttributes, "2nd TIN notice flag");
                    compareAndAddBoolean(originalMisc.fatcaFilingRequirementIndicator, correctedMisc.fatcaFilingRequirementIndicator, nonMatchingAttributes, "FATCA indicator flag");
//                    compareAndAddBoolean(originalMisc.directSalesIndicator, correctedMisc.directSalesIndicator, nonMatchingAttributes, "direct sales indicator flag");
                } else if (originalEFSFilingDTO instanceof FilingDataDTO1099NEC && correctionFilingDTO instanceof FilingDataDTO1099NEC) {
                    FilingDataDTO1099NEC originalNEC = (FilingDataDTO1099NEC) originalEFSFilingDTO;
                    FilingDataDTO1099NEC correctedNEC = (FilingDataDTO1099NEC) correctionFilingDTO;
                    compareAndAddBoolean(originalNEC.secondTinNotice, correctedNEC.secondTinNotice, nonMatchingAttributes, "2nd TIN notice flag");
                    compareAndAddBoolean(originalNEC.fatcaFilingRequirementIndicator, correctedNEC.fatcaFilingRequirementIndicator, nonMatchingAttributes, "FATCA indicator flag");
                } else if (originalEFSFilingDTO instanceof FilingDataDTO1099NEC2021 && correctionFilingDTO instanceof FilingDataDTO1099NEC2021) {
                    FilingDataDTO1099NEC2021 originalNEC = (FilingDataDTO1099NEC2021) originalEFSFilingDTO;
                    FilingDataDTO1099NEC2021 correctedNEC = (FilingDataDTO1099NEC2021) correctionFilingDTO;
                    compareAndAddBoolean(originalNEC.secondTinNotice, correctedNEC.secondTinNotice, nonMatchingAttributes, "2nd TIN notice flag");
//                    compareAndAddBoolean(originalNEC.directSalesIndicator, correctedNEC.directSalesIndicator, nonMatchingAttributes, "direct sales indicator flag");
                } else if (originalEFSFilingDTO instanceof FilingDataDTO1099INT && correctionFilingDTO instanceof FilingDataDTO1099INT) {
                    FilingDataDTO1099INT originalNEC = (FilingDataDTO1099INT) originalEFSFilingDTO;
                    FilingDataDTO1099INT correctedNEC = (FilingDataDTO1099INT) correctionFilingDTO;
                    compareAndAddBoolean(originalNEC.secondTinNotice, correctedNEC.secondTinNotice, nonMatchingAttributes, "2nd TIN notice flag");
//                    compareAndAddBoolean(originalNEC.directSalesIndicator, correctedNEC.directSalesIndicator, nonMatchingAttributes, "direct sales indicator flag");
                } else {
					throw new RuntimeException("Unknown filing type");
                }
            }
        }
        return nonMatchingAttributes;
    }

    private List<List<String>> comparePayerPart(FilingDataDTO originalEFSFilingDTO, FilingDataDTO firstFilingDTO, String originalPayerTIN) {
        List<List<String>> nonMatchingAttributes = new ArrayList<>();

        compareAndAdd(originalEFSFilingDTO.payerName1, firstFilingDTO.payerName1, nonMatchingAttributes, "name");
        compareAndAdd(originalEFSFilingDTO.payerName2, firstFilingDTO.payerName2, nonMatchingAttributes, "name2");
        compareAndAdd(originalPayerTIN, firstFilingDTO.payerTin, nonMatchingAttributes, "Payer TIN");
        compareAndAdd(originalEFSFilingDTO.payerPhoneAndExt, firstFilingDTO.payerPhoneAndExt, nonMatchingAttributes, "phone");

        compareAndAdd(originalEFSFilingDTO.payerAddress.streetName1, firstFilingDTO.payerAddress.streetName1, nonMatchingAttributes, "address line1");
        compareAndAdd(originalEFSFilingDTO.payerAddress.streetName2, firstFilingDTO.payerAddress.streetName2, nonMatchingAttributes, "address line2");
        compareAndAdd(originalEFSFilingDTO.payerAddress.city, firstFilingDTO.payerAddress.city, nonMatchingAttributes, "city");
        compareAndAdd(originalEFSFilingDTO.payerAddress.state.toString(), firstFilingDTO.payerAddress.state.toString(), nonMatchingAttributes, "state");
        compareAndAdd(originalEFSFilingDTO.payerAddress.country, firstFilingDTO.payerAddress.country, nonMatchingAttributes, "country");
        compareAndAdd(originalEFSFilingDTO.payerAddress.zipCode, firstFilingDTO.payerAddress.zipCode, nonMatchingAttributes, "zip");

        return nonMatchingAttributes;
    }

    private void compareAndAdd(String s1, String s2, List<List<String>> nonMatchingAttributes, String attributeName) {
        boolean isStringMatch = compareStrings(s1, s2);
        if (!isStringMatch) {
            List<String> nonMatchingAttribute = new ArrayList<>();
            nonMatchingAttribute.add(attributeName);
            nonMatchingAttribute.add(s1 == null ? "no value" : s1);
            nonMatchingAttribute.add(s2 == null ? "no value" : s2);
            nonMatchingAttributes.add(nonMatchingAttribute);
        }
    }

    private void compareAndAddStateCode(com.aphe.efs.model.enums.StateCode s1, com.aphe.efs.model.enums.StateCode s2, List<List<String>> nonMatchingAttributes, String attributeName) {
        boolean isStringMatch = compareStateCodes(s1, s2);
        if (!isStringMatch) {
            List<String> nonMatchingAttribute = new ArrayList<>();
            nonMatchingAttribute.add(attributeName);
            nonMatchingAttribute.add(s1 == null ? "no value" : s1.name());
            nonMatchingAttribute.add(s2 == null ? "no value" : s2.name());
            nonMatchingAttributes.add(nonMatchingAttribute);
        }
    }

    private boolean compareStrings(String s1, String s2) {
        boolean equal = false;
        if (StringUtil.isEmpty(s1) && StringUtil.isEmpty(s2)) {
            equal = true;
        } else if (StringUtil.isNotEmpty(s1) && StringUtil.isEmpty(s2)) {
            equal = false;
        } else if (StringUtil.isEmpty(s1) && StringUtil.isNotEmpty(s2)) {
            equal = false;
        } else {
            equal = s1.trim().equalsIgnoreCase(s2.trim());
        }
        return equal;
    }

    private void compareAndAddBoolean(Boolean b1, Boolean b2, List<List<String>> nonMatchingAttributes, String attributeName) {
        boolean isBooleanMatch = compareBoolean(b1, b2);
        if (!isBooleanMatch) {
            List<String> nonMatchingAttribute = new ArrayList<>();
            nonMatchingAttribute.add(attributeName);
            nonMatchingAttribute.add(b1 == null ? "false" : b1.toString());
            nonMatchingAttribute.add(b2 == null ? "false" : b2.toString());
            nonMatchingAttributes.add(nonMatchingAttribute);
        }
    }


    private boolean compareBoolean(Boolean b1, Boolean b2) {
        boolean boolean1 = b1 != null ? b1.booleanValue() : false;
        boolean boolean2 = b2 != null ? b2.booleanValue() : false;
        return boolean1 == boolean2;
    }

    private boolean compareStateCodes(com.aphe.efs.model.enums.StateCode s1, com.aphe.efs.model.enums.StateCode s2) {
        String s1String = s1 != null ? s1.name() : "";
        String s2String = s2 != null ? s2.name() : "";
        return s1String.equalsIgnoreCase(s2String);
    }


    private List<List<String>> comparePayeePart(FilingDataDTO originalEFSFilingDTO, FilingDataDTO firstFilingDTO, String originalPayeeTIN) {

        List<List<String>> nonMatchingAttributes = new ArrayList<>();

        compareAndAdd(originalEFSFilingDTO.payeeTinType.toString(), firstFilingDTO.payeeTinType.toString(), nonMatchingAttributes, "TIN type");
        compareAndAdd(originalPayeeTIN, firstFilingDTO.payeeTin, nonMatchingAttributes, "TIN");

        compareAndAdd(originalEFSFilingDTO.payeeName1, firstFilingDTO.payeeName1, nonMatchingAttributes, "name");
        compareAndAdd(originalEFSFilingDTO.payeeName2, firstFilingDTO.payeeName2, nonMatchingAttributes, "name2");
        compareAndAdd(originalEFSFilingDTO.payeeAccountNumber, firstFilingDTO.payeeAccountNumber, nonMatchingAttributes, "account number");


        return nonMatchingAttributes;
    }

    private List<List<String>> comparePayeePartAll(FilingDataDTO originalEFSFilingDTO, FilingDataDTO firstFilingDTO, String originalPayeeTIN) {

        List<List<String>> nonMatchingAttributes = comparePayeePart(originalEFSFilingDTO, firstFilingDTO, originalPayeeTIN);

        compareAndAdd(originalEFSFilingDTO.payeeAddress.streetName1, firstFilingDTO.payeeAddress.streetName1, nonMatchingAttributes, "address line1");
        compareAndAdd(originalEFSFilingDTO.payeeAddress.streetName2, firstFilingDTO.payeeAddress.streetName2, nonMatchingAttributes, "address line2");
        compareAndAdd(originalEFSFilingDTO.payeeAddress.city, firstFilingDTO.payeeAddress.city, nonMatchingAttributes, "city");
        compareAndAdd(originalEFSFilingDTO.payeeAddress.state.toString(), firstFilingDTO.payeeAddress.state.toString(), nonMatchingAttributes, "state");
        compareAndAdd(originalEFSFilingDTO.payeeAddress.country, firstFilingDTO.payeeAddress.country, nonMatchingAttributes, "country");
        compareAndAdd(originalEFSFilingDTO.payeeAddress.zipCode, firstFilingDTO.payeeAddress.zipCode, nonMatchingAttributes, "zip");

        return nonMatchingAttributes;
    }

    private boolean isCorrection(Filing f) {
        return f.getCorrectionType() == com.aphe.contractor.model.enums.CorrectionType.First || f.getCorrectionType() == com.aphe.contractor.model.enums.CorrectionType.Second;
    }


    private void addOtherFilingValidations(Filing f,  List<String> userErrors, List<String> userWarnings, DomainDTO domainDTO, Filing secondCorrection, Filing originalFiling) throws Exception {
        Set<String> errorMessages = new HashSet<>();

        if(f.getFilingYear().isGreaterThanCurrentYear()) {
            userErrors.add("You can't submit next year's filings yet. The next year's filing season will open in November of this year.");
        } else {
            if(f.getFilingYear().isLessThanYear(FilingYear.getCurrentFilingYear())) {
                userWarnings.add("You are submitting a filing for one of the previous years. Please make sure you are submitting the filings for the correct year.");
            }
        }

        List<FilingDataDTO> dtos = convertUtil.convertToEFSFilingDTOs(f, 1L, false, secondCorrection, originalFiling);
        for(FilingDataDTO dto : dtos) {
            dto.clientRefId = f.getId() + "";
            ValidationErrors errors = getEFSValidationErrors(dto);
            if (errors.getMessages().size() > 0) {
                for (String fieldName : errors.getMessages().keySet()) {
                    for (String message : errors.getMessages().get(fieldName)) {
                        errorMessages.add(message);
                    }
                }
            }
        }
        userErrors.addAll(errorMessages);
    }


    public ValidationErrors getEFSValidationErrors(FilingDataDTO filingDataDTO) {
        ValidationErrors errors = getValidationErrors(filingDataDTO);
        errors.removeProperty("payerTin");
        errors.removeProperty("payeeTin");

        //Make sure reportUsingCFSF is not set for state filings.
        if(Boolean.TRUE == filingDataDTO.stateFiling && Boolean.TRUE == filingDataDTO.reportUsingCFSF ) {
            errors.addMessage("State Filing Options: ", "State filings should not have CFSF flag.");
        }

        if(Boolean.TRUE == filingDataDTO.stateFiling && filingDataDTO.reportingStateCode == null ) {
            errors.addMessage("State Filing Options: ", "reportingStateCode can not be null for a state filing.");
        }

        List<com.aphe.efs.model.enums.StateCode> noCorrectionStateCodes = Arrays.asList(
                com.aphe.efs.model.enums.StateCode.DE,
                com.aphe.efs.model.enums.StateCode.ME,
                com.aphe.efs.model.enums.StateCode.ND);

        boolean stateDoesNotSupportCorrections = noCorrectionStateCodes.contains(filingDataDTO.reportingStateCode);
        if(Boolean.TRUE == filingDataDTO.stateFiling && stateDoesNotSupportCorrections && filingDataDTO.correctionType != com.aphe.efs.model.enums.CorrectionType.Original) {
            errors.addMessage("Correction Type: ", "Corrections can not be filed with state of " + filingDataDTO.reportingStateCode.name());
        }

        //add custom validations..
        String previousYearMessage = "We are currently not accepting filings for previous years. Please exclude this filing from your submission or contact <NAME_EMAIL>, if you need help.";
        if (filingDataDTO.filingReturnType == com.aphe.efs.model.enums.FilingReturnType.Type_1099_MISC) {
            if(filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2020) {
                FilingDataDTO1099MISC2020 miscDto = (FilingDataDTO1099MISC2020) filingDataDTO;
                if (miscDto.reportUsingCFSF != null && miscDto.reportUsingCFSF.booleanValue() == true) {
                    if (miscDto.state1Code == null) {
                        errors.addMessage("State Code: ", "State is required if reporting using Combined Federal State Filing program.");
                    }
                }
                if(miscDto.allAmountsAreZero()) {
                    boolean hasNoDirectSalesIndicator = miscDto.directSalesIndicator == null || miscDto.directSalesIndicator.booleanValue() == false;
                    boolean isNotFirstCorrection = filingDataDTO.correctionType != com.aphe.efs.model.enums.CorrectionType.First;
                    if(hasNoDirectSalesIndicator && isNotFirstCorrection) {
                        errors.addMessage("Box Amounts: ", "At least one box amount is required or direct sales indicator must be checked.");
                    }
                }
                if(miscDto.isFedTaxWithheldHigh()) {
                    errors.addMessage("Federal Tax Withheld: ", "Federal tax withheld is too high.");
                }
            } else if(filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2021
                    || filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2022
                    || filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2023
                    || filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2024) {
                FilingDataDTO1099MISC2021 miscDto = (FilingDataDTO1099MISC2021) filingDataDTO;
                if (miscDto.reportUsingCFSF != null && miscDto.reportUsingCFSF.booleanValue() == true) {
                    if (miscDto.state1Code == null) {
                        errors.addMessage("State Code:", "State is required if reporting using Combined Federal State Filing program.");
                    }

                }
                if(miscDto.allAmountsAreZero()) {
                    boolean hasNoDirectSalesIndicator = miscDto.directSalesIndicator == null || miscDto.directSalesIndicator.booleanValue() == false;
                    boolean isNotFirstCorrection = filingDataDTO.correctionType != com.aphe.efs.model.enums.CorrectionType.First;
                    if(hasNoDirectSalesIndicator && isNotFirstCorrection) {
                        errors.addMessage("Box Amounts", "At least one box amount is required or direct sales indicator must be checked.");
                    }
                }
                if(miscDto.isFedTaxWithheldHigh()) {
                    errors.addMessage("Federal Tax Withheld: ", "Federal tax withheld is too high.");
                }
            }
        } else if (filingDataDTO.filingReturnType == com.aphe.efs.model.enums.FilingReturnType.Type_1099_NEC) {
            if(filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2020) {
                FilingDataDTO1099NEC miscDto = (FilingDataDTO1099NEC) filingDataDTO;
                if (miscDto.reportUsingCFSF != null && miscDto.reportUsingCFSF.booleanValue() == true) {
                    if (miscDto.state1Code == null) {
                        errors.addMessage("State Code: ", "State is required if reporting using Combined Federal State Filing program.");
                    }
                }
                if(miscDto.allAmountsAreZero()) {
                    boolean isNotFirstCorrection = filingDataDTO.correctionType != com.aphe.efs.model.enums.CorrectionType.First;
                    if(isNotFirstCorrection) {
                        errors.addMessage("Box Amounts: ", "At least one box amount is required.");
                    }
                }
                if(miscDto.isFedTaxWithheldHigh()) {
                    errors.addMessage("Federal Tax Withheld: ", "Federal tax withheld is too high.");
                }
            } else if(filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2021
                    || filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2022
                    || filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2023
                    || filingDataDTO.filingYear == com.aphe.efs.model.enums.FilingYear.Y2024) {
                FilingDataDTO1099NEC2021 miscDto = (FilingDataDTO1099NEC2021) filingDataDTO;
                if (miscDto.reportUsingCFSF != null && miscDto.reportUsingCFSF.booleanValue() == true) {
                    if (miscDto.state1Code == null) {
                        errors.addMessage("State Code: ", "State is required if reporting using Combined Federal State Filing program.");
                    }
                }
                if(miscDto.allAmountsAreZero()) {
                    boolean hasNoDirectSalesIndicator = miscDto.directSalesIndicator == null || miscDto.directSalesIndicator.booleanValue() == false;
                    boolean isNotFirstCorrection = filingDataDTO.correctionType != com.aphe.efs.model.enums.CorrectionType.First;
                    if(hasNoDirectSalesIndicator && isNotFirstCorrection) {
                        errors.addMessage("Box Amounts: ", "At least one box amount is required or direct sales indicator must be checked.");
                    }
                }
                if(miscDto.isFedTaxWithheldHigh()) {
                    errors.addMessage("Federal Tax Withheld: ", "Federal tax withheld is too high.");
                }
            }
        }

//        if (filingDataDTO.filingYear != com.aphe.efs.model.enums.FilingYear.getCurrentFilingYear()) {
//            errors.addMessage("Filing Year: ", previousYearMessage);
//        }

        if (filingDataDTO != null && filingDataDTO.payerAddress != null && filingDataDTO.payerAddress.getCombinedStreet().length() > 40) {
            errors.addMessage("Payer Address: ", "Combined length of Payer's address Street Name and Suite/Apt can not be more than 40 characters");
        }

        if (filingDataDTO != null && filingDataDTO.payeeAddress != null && filingDataDTO.payeeAddress.getCombinedStreet().length() > 40) {
            errors.addMessage("Payee Address: ", "Combined length of Payee's address Street Name and Suite/Apt can not be more than 40 characters");
        }

        return errors;
    }

    public ValidationErrors getValidationErrors(Object dto) {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(dto);
        return convertConstraintViolations(constraintViolations);
    }

    public ValidationErrors convertConstraintViolations(Set<? extends ConstraintViolation<?>> violations) {
        Map<String, Set<String>> responseObj = new HashMap<String, Set<String>>();
        for (ConstraintViolation<?> violation : violations) {
            String propertyPath = violation.getPropertyPath().toString();
            Set<String> messages = responseObj.get(propertyPath);
            if (messages == null) {
                messages = new HashSet<String>();
                responseObj.put(propertyPath, messages);
            }
            messages.add(violation.getMessage());
        }
        // MapWrapper<String, Set<String>> mapWrapper = new MapWrapper<String, Set<String>>(responseObj);
        ValidationErrors errors = new ValidationErrors();
        errors.setMessages(responseObj);
        return errors;
    }


}
