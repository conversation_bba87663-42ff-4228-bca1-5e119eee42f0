package com.aphe.contractor.services;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.service.CommonBaseManager;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.Payee;
import com.aphe.contractor.model.Payer;
import com.aphe.contractor.repo.PayerRepository;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

public class CommonTNNManager extends CommonBaseManager {

	@Autowired
	AuthManager authManager;

	@Autowired
	PayerRepository payerRepo;

	/**
	 * @deprecated Use this sparingly only for business rules validation but not for access checking..
	 */
	protected boolean isAccessible(Payer p) {
		if (p != null && p.getId() > 0) {
			Long domainId = getCurrentDomainId();
			return (domainId != null && (domainId == p.getDomainId())) || isSystemUser();
		}
		return false;
	}

	/**
	 * @deprecated Use this sparingly only for business rules validation but not for access checking..
	 */
	protected boolean isAccessible(Payee payee) {
		if (payee != null && payee.getId() > 0) {
			Payer p = payee.getPayer();
			return isAccessible(p);
		}
		return false;
	}

	/**
	 * @deprecated Use this sparingly only for business rules validation but not for access checking..
	 */
	protected boolean isAccessible(Filing filing) {
		if (filing != null && filing.getId() > 0) {
			Payer p = filing.getPayer();
			return isAccessible(p);
		}
		return false;
	}

	public List<Long> getPayerIdsForAllClients(Long parentDomainId) {
		List<Long> domainIds = authManager.getClientDomainIds();
		domainIds.add(parentDomainId);
		List<Payer> payers = payerRepo.findByDomainIdIn(domainIds);
		List<Long> payerIds = payers.stream().map(Payer::getId).collect(Collectors.toList());
		return payerIds;
	}

}
