package com.aphe.contractor.services;

import com.aphe.common.error.ValidationMessages;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.contractor.dto.AddEditPayerInput;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.model.Payer;
import com.aphe.contractor.repo.PayerRepository;
import com.aphe.domain.dto.AddEditDomainInput;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import org.hibernate.validator.constraints.ValidationErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Component
public class PayerManager extends CommonTNNManager {

    @Autowired
    PayerRepository payerDAO;

    @Autowired
    ContractorMapper contractorMapper;

    @Autowired
    DomainMgr domainManager;

    @Autowired
    FilingManager filingManager;

    @Autowired
    ContractorConvertUtil convertUtil;

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_PAYER')")
    public PayerDTO getPayerByDomainId(Long domainId) throws ApheForbiddenException {
        createPayerIfNotExists(domainId);
        Payer p = payerDAO.findByDomainId(domainId);
        DomainDTO d = domainManager.getDomain(p.getDomainId());
        return convertUtil.toAddEditPayerInput(d, p);
    }

    @Transactional
    @PreAuthorize("hasPermission(#payerId, 'PAYER', 'READ_PAYER')")
    public PayerDTO getPayer(String payerId) throws ApheForbiddenException {
        createPayerIfNotExists(getCurrentDomainId());
        Payer p = payerDAO.findById(Long.parseLong(payerId)).orElse(null);
        DomainDTO d = domainManager.getDomain(p.getDomainId());
        return convertUtil.toAddEditPayerInput(d, p);
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'CREATE_PAYER')")
    public void createPayerIfNotExists(Long domainId) {
        Payer p = payerDAO.findByDomainId(domainId);
        if (p == null) {
            p = new Payer();
            p.setDomainId(domainId);
            payerDAO.save(p);
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#updatePayerInput.id, 'PAYER', 'UPDATE_PAYER')")
    public long updatePayer(AddEditPayerInput updatePayerInput) throws ApheDataValidationException, ApheForbiddenException {

        ValidationErrors errors = getValidationErrors(updatePayerInput);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors, "Invalid input");
        }

        Payer existingEntity = payerDAO.findById(updatePayerInput.id).orElse(null);

        boolean hasPendingFilings = filingManager.hasPendingFilings(existingEntity.getDomainId());
        if (hasPendingFilings) {
            throw new ApheDataValidationException("id", "There are some pending filings. You can not change Payer info, when your filings are being processed.");
        }

        // Extract domain portion of it and update the domain using domain manager.
        AddEditDomainInput d = convertUtil.toAddEditDomainInput(updatePayerInput);
        d.id = existingEntity.getDomainId();
        domainManager.updateDomain(d);

        // Convert to Entity.
        Payer p = convertUtil.toPayer(updatePayerInput);
        if (p.getTransferAgentAddress() != null && existingEntity.getTransferAgentAddress() != null) {
            p.getTransferAgentAddress().setId(existingEntity.getTransferAgentAddress().getId());
            p.getTransferAgentAddress().setVersion(existingEntity.getTransferAgentAddress().getVersion());
        }

        Payer savedPayer = payerDAO.mergeAndSave(p, existingEntity);

        try {
            domainManager.updateDomainOnAuth(domainManager.getDomain(d.id));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return savedPayer.getId();
    }

    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAIN')")
    public ValidationMessages getValidationMessages(Long domainId, String filingYear) throws ApheForbiddenException {
        PayerDTO dto = getPayerByDomainId(domainId);
        AddEditPayerInput addEditPayerDTO = contractorMapper.toAddEditPayerInput(dto);
        ValidationErrors errors = getValidationErrors(addEditPayerDTO);

        if (addEditPayerDTO.address != null && addEditPayerDTO.address.getCombinedStreet().length() > 40) {
            errors.addMessage("Address", "Combined length of Street Name and Suite/Apt can not be more than 40 characters.");
        }

        List<String> errors1 = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        boolean hasDuplicateDomains = domainManager.getDuplicateDomains(domainId, filingYear);
        if(hasDuplicateDomains) {
            warnings.add("We have found other accounts with same Payer TIN and submitted filings. Please ensure that you have entered the right Payer TIN to avoid incorrect or duplicate filings.");
        }

        if (errors.getMessages().size() > 0) {
            for (String fieldName : errors.getMessages().keySet()) {
                for (String message : errors.getMessages().get(fieldName)) {
                    errors1.add(fieldName + " : " + message);
                }
            }
        }
        return new ValidationMessages(errors1, warnings);
    }

}
