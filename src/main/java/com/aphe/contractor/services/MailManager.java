package com.aphe.contractor.services;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.UserDTO;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.mail.Email;
import com.aphe.common.mail.MailService;
import com.aphe.common.util.JSONUtils;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.dto.read.PayeeDTO;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.model.w9.W9Request;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Component
public class MailManager {

	private static Logger logger = LoggerFactory.getLogger(MailService.class);

	@Autowired
	DomainMgr domainMgr;

	@Autowired
	PayerManager payerManager;

	@Autowired
	PayeeManager payeeManager;

	@Autowired
	ContractorConvertUtil contractorConvertUtil;

	@Autowired
	AuthManager authManager;

	@Autowired
	MailService mailService;

	@Autowired
	JSONUtils jsonUtils;

	@Value("${aphe.product.url}")
	private String productURL;

	@Value("${aphe.product.appName}")
	private String productName;

	@Value("${aphe.support.email}")
	public String supportEmail;

	@Value("${aphe.support.name}")
	public String supportName;


	@Value("${aphe.email.submittedFilings.templateName}")
	private String submittedFilingsTemplateName;

	@Value("${aphe.email.systemRejectedFilings.templateName}")
	private String systemRejectedFilingsTemplateName;

	@Value("${aphe.email.acceptedFilings.templateName}")
	private String acceptedFilingsTemplateName;

	@Value("${aphe.email.sentToAgencyFilings.templateName}")
	private String sentToAgencyFilingsTemplateName;

	@Value("${aphe.email.rejectedFilings.templateName}")
	private String rejectedFilingsTemplateName;

	@Value("${aphe.email.your1099Form.templateName}")
	private String your1099FormTemplateName;

	@Value("${aphe.email.emailBounced.templateName}")
	private String emailBouncedTemplateName;

	@Value("${aphe.email.printReturned.templateName}")
	private String printReturnedTemplateName;

	@Value("${aphe.email.submittedTINMatches.templateName}")
	private String submittedTINMatchesTemplateName;

	@Value("${aphe.email.systemRejectedTINMatches.templateName}")
	private String systemRejectedTINMatchesTemplateName;

	@Value("${aphe.email.sentToAgencyTINMatches.templateName}")
	private String sentToAgencyTINMatchesTemplateName;

	@Value("${aphe.email.verifiedTINMatches.templateName}")
	private String verifiedTINMatchesTemplateName;

	@Value("${aphe.email.failedTINMatches.templateName}")
	private String failedTINMatchesTemplateName;

	@Value("${aphe.email.review.templateName}")
	private String reviewTemplateName;

	@Value("${aphe.email.w9Request.templateName}")
	private String w9RequestTemplateName;

	@Value("${aphe.email.w9Applied.templateName}")
	private String w9AppliedTemplateName;

	@Value("${aphe.product.w9RequestPath}")
	private String w9RequestPath;


	private String sysStatusTemplateName = "sysStatus";

	@Value("${aphe.product.filingsPath}")
	private String filingsPath;

	public void sendFilingsSubmitted(List<Map<String, Object>> filings, List<UserDTO> users, String domainName) {
		String templateIdToUse = submittedFilingsTemplateName;
		sendFilingStatusNotification(filings, templateIdToUse, "Success!!! Your 1099 forms for " + domainName  + " have been received by us.", users, domainName);
	}

	public void sendFilingsSystemRejected(List<Map<String, Object>> filings, List<UserDTO> users, String domainName) {
		String templateIdToUse = systemRejectedFilingsTemplateName;
		sendFilingStatusNotification(filings, templateIdToUse, "ACTION REQUIRED: Some of your 1099 forms for " + domainName + " couldn't be processed.", users, domainName);
	}

	public void sendFilingsSentToAgency(List<Map<String, Object>> filings, List<UserDTO> users, String domainName) {
		String templateIdToUse = sentToAgencyFilingsTemplateName;
		sendFilingStatusNotification(filings, templateIdToUse, "Success!!! Some of your 1099 forms for " + domainName + " have been submitted to the IRS and/or state.", users, domainName);
	}

	public void sendFilingsAccepted(List<Map<String, Object>> filings, List<UserDTO> users, String domainName) {
		String templateIdToUse = acceptedFilingsTemplateName;
		sendFilingStatusNotification(filings, templateIdToUse, "Congratulations!!! Some of your 1099 forms for " + domainName + " have been accepted by the IRS and/or state.", users, domainName);
	}

	public void sendFilingsRejected(List<Map<String, Object>> filings, List<UserDTO> users, String domainName) {
		String templateIdToUse = rejectedFilingsTemplateName;
		sendFilingStatusNotification(filings, templateIdToUse, "ACTION REQUIRED: Some of your 1099 forms for " + domainName + " have been rejected by the IRS and/or state.", users, domainName);
	}

	public void sendFilingsEmailBounced(List<Map<String, Object>> filings, List<UserDTO> users, String domainName) {
		String templateIdToUse = emailBouncedTemplateName;
		sendFilingStatusNotification(filings, templateIdToUse, "ACTION REQUIRED: Some of your 1099 forms for " + domainName + " couldn't be e-delivered.", users, domainName);
	}

	public void sendFilingsPrintReturned(List<Map<String, Object>> filings, List<UserDTO> users, String domainName) {
		String templateIdToUse = printReturnedTemplateName;
		sendFilingStatusNotification(filings, templateIdToUse, "ACTION REQUIRED: Some of your 1099 forms for " + domainName + " couldn't be delivered", users, domainName);
	}

	public void sendTINMatchRequestsSubmitted(List<Map<String, Object>> payees, List<UserDTO> users, String domainName) {
		String templateIdToUse = submittedTINMatchesTemplateName;
		sendFilingStatusNotification(payees, templateIdToUse, "Success!!! Your TIN Match requests for " + domainName  + " have been received by us.", users, domainName);
	}

	public void sendTINMatchRequestsSystemRejected(List<Map<String, Object>> payees, List<UserDTO> users, String domainName) {
		String templateIdToUse = systemRejectedTINMatchesTemplateName;
		sendFilingStatusNotification(payees, templateIdToUse, "ACTION REQUIRED: Some of your TIN Match requests for " + domainName  + " couldn't be processed.", users, domainName);
	}

	public void sendTINMatchRequestsSentToAgency(List<Map<String, Object>> payees, List<UserDTO> users, String domainName) {
		String templateIdToUse = sentToAgencyTINMatchesTemplateName;
		sendFilingStatusNotification(payees, templateIdToUse, "Success!!! Some of your TIN Match requests for " + domainName  + " have been submitted to the IRS.", users, domainName);
	}

	public void sendTINMatchRequestsVerified(List<Map<String, Object>> payees, List<UserDTO> users, String domainName) {
		String templateIdToUse = verifiedTINMatchesTemplateName;
		sendFilingStatusNotification(payees, templateIdToUse, "Success!!! Some of your TIN Match requests for " + domainName  + " have been successfully verified.", users, domainName);
	}

	public void sendTINMatchRequestsFailed(List<Map<String, Object>> payees, List<UserDTO> users, String domainName) {
		String templateIdToUse = failedTINMatchesTemplateName;
		sendFilingStatusNotification(payees, templateIdToUse, "ACTION REQUIRED: Some of Your TIN Match requests for " + domainName  + " have failed.", users, domainName);
	}

	public void sendFilingsNotSubmitted(String templateName, String subject, String domainName, List<UserDTO> users, List<Map<String, Object>> filings, HashMap<String, String> campaignParams) {
		sendFilingStatusNotification(filings, templateName, subject, users, domainName, campaignParams);
	}

	public String send1099Form(String payerEmail, String payerName, String payeeEmail, String payeeName, Map<String, Object> params, File file) {

		Email m = new Email(your1099FormTemplateName, "Important tax document from " + payerName, payeeName, "", payeeEmail, jsonUtils.mapToJSONObject(params));
		if(StringUtil.isNotEmpty(payerEmail, true)) {
			m.addTo(payerName, "", payerEmail);
			m.setFrom(payerName, "(via " + productName + ")", supportEmail);
			m.setReplyTo(payerName, "", payerEmail);
		}

		m.addAddarchment(file.getName(), file);
		Email email = mailService.createEmail(m);
		return Long.toString(email.getId());
	}

	@Transactional
	public void sendW9RequestEmail(W9Request w9Request) throws ApheForbiddenException {
		String templateIdToUse = w9RequestTemplateName;

		PayeeDTO payeeDTO = payeeManager.getPayee(w9Request.getPayee().getId());
		PayerDTO payerDTO = payerManager.getPayer(Long.toString(payeeDTO.payerId));
		DomainDTO domainDTO = domainMgr.getDomain(payerDTO.domainId);
		String domainName = contractorConvertUtil.getDomainDisplayName(domainDTO);

		Map<String, Object> mailAttributes = new HashMap<String, Object>();

		StringBuilder w9URL = new StringBuilder();
		String requestGUID = w9Request.getGlobalId().toString();
		w9URL.append(productURL).append(w9RequestPath).append("?").append("id=").append(requestGUID);

		mailAttributes.put("PAYEE_NAME", payeeDTO.displayName);
		mailAttributes.put("PAYER_NAME", domainName);
		mailAttributes.put("W9_REQUEST_URL", w9URL.toString());

		String payeeEmail = payeeDTO.emailAddress;


		String subject = "ACTION REQUIRED: Fill W-9 tax info for " + domainName;

		Email m = new Email(templateIdToUse, subject, "", payeeDTO.displayName, payeeEmail, jsonUtils.mapToJSONObject(mailAttributes));

		String payerEmail = domainDTO.emailAddress;
		if(StringUtil.isNotEmpty(payerEmail, true)) {
			m.addTo(domainName, "", payerEmail);
			m.setFrom(domainName, "(via " + productName + ")", supportEmail);
			m.setReplyTo(domainName, "", payerEmail);
		}
		mailService.createEmail(m);
	}

	public void sendW9AppliedEmail(W9Request w9Request) throws ApheForbiddenException {

 		PayeeDTO payeeDTO = payeeManager.getPayee(w9Request.getPayee().getId());
		PayerDTO payerDTO = payerManager.getPayer(Long.toString(payeeDTO.payerId));
		DomainDTO domainDTO = domainMgr.getDomain(payerDTO.domainId);
		String domainName = contractorConvertUtil.getDomainDisplayName(domainDTO);
		Map<String, Object> mailAttributes = new HashMap<String, Object>();

		mailAttributes.put("PAYEE_NAME", payeeDTO.displayName);
		mailAttributes.put("PAYER_NAME", domainName);
		String payerEmail = domainDTO.emailAddress;

		String subject = "Successfully received W-9 information from " + payeeDTO.displayName;
		Email m = new Email(w9AppliedTemplateName, subject, "", domainName, payerEmail, jsonUtils.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);
	}


	public void sendSystemStatusEmail(String subject, String message) {

		Map<String, Object> mailAttributes = new HashMap<>();

		Email m = new Email(sysStatusTemplateName);
		m.setSubject(subject);
		m.addTo(supportName, "", supportEmail);
		mailAttributes.put("STATUS_MESSAGE", message);
		m.setParams(jsonUtils.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);
	}

	public void sendEmailsToDomainUsers(String templateIdToUse, String subject, String domainName, List<UserDTO> users, HashMap<String, String> campaignParams) {
		Map<String, Object> mailAttributes = new HashMap<String, Object>();
		mailAttributes.put("FORM_NAME", "1099-MISC");
		mailAttributes.put("DOMAIN_NAME", domainName);
		mailAttributes.put("CAMPAIGN_PARAMS", campaignParams);

		Email m = new Email(templateIdToUse);
		m.setParams(jsonUtils.mapToJSONObject(mailAttributes));
		m.setSubject(subject);
		for (UserDTO u : users) {
			m.addTo(u.getFirstName(), u.getLastName(), u.getEmail());
		}
		mailService.createEmail(m);
	}

	private void sendFilingStatusNotification(List<Map<String, Object>> filings, String templateIdToUse, String subject, List<UserDTO> users, String domainName) {
		HashMap<String, String> campaignParams = new HashMap<>();
		sendFilingStatusNotification(filings, templateIdToUse, subject, users, domainName, campaignParams);
	}


	private void sendFilingStatusNotification(List<Map<String, Object>> filings, String templateIdToUse, String subject, List<UserDTO> users, String domainName, HashMap<String, String> campaignParams) {
		String filingsURL = productURL + filingsPath;
		if(campaignParams.size() > 0) {
			StringBuffer queryString = mailService.concatParams(campaignParams);
			filingsURL = filingsURL + "?" + queryString.toString();
		}

		Map<String, Object> mailAttributes = new HashMap<String, Object>();
		mailAttributes.put("FORM_NAME", "1099-MISC");
		mailAttributes.put("filings", filings);
		mailAttributes.put("payees", filings);
		mailAttributes.put("DOMAIN_NAME", domainName);
		mailAttributes.put("CAMPAIGN_PARAMS", campaignParams);
		mailAttributes.put("FILINGS_URL", filingsURL);

		Email m = new Email(templateIdToUse);
		m.setParams(jsonUtils.mapToJSONObject(mailAttributes));
		m.setSubject(subject);
		for (UserDTO u : users) {
			m.addTo(u.getFirstName(), u.getLastName(), u.getEmail());
		}
		mailService.createEmail(m);
	}


	public void sendReviewEmail(String partenr, List<UserDTO> users) {
		String templateIdToUse = reviewTemplateName;

//		String partnerProductName = "Xero";
//		String reviewPlace = "Xero App Store";
//		String partnerLogoURL = "https://1099smartfile.com/images/logos/Xero-Logo.png";
//		String reviewURL = "https://apps.xero.com/us/search/app/1099smartfile/reviews";


		String partnerProductName = "Zoho Books";
		String reviewPlace = "Zoho Marketplace";
		String partnerLogoURL = "https://1099smartfile.com/images/logos/zoho-books-logo.png";
		String reviewURL = "https://marketplace.zoho.com/app/books/1099-smartfile-for-zoho-books#ratingsReview";



		String subject = "Please review 1099SmartFile on " + reviewPlace;

		Map<String, Object> mailAttributes = new HashMap<String, Object>();

		mailAttributes.put("REVIEW_URL", reviewURL);
		mailAttributes.put("REVIEW_PLACE", reviewPlace);
		mailAttributes.put("PARTNER_PRODUCT_NAME", partnerProductName);
		mailAttributes.put("PARTNER_LOGO_URL", partnerLogoURL);


		Email m = new Email(templateIdToUse);
		m.setParams(jsonUtils.mapToJSONObject(mailAttributes));
		m.setSubject(subject);
		for (UserDTO u : users) {
			m.addTo(u.getFirstName(), u.getLastName(), u.getEmail());
		}
		mailService.createEmail(m);
	}

}
