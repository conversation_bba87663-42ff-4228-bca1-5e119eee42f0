package com.aphe.contractor.repo;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;

import com.aphe.common.data.repository.ApheCustomRepository;
import com.aphe.contractor.model.Payer;

public interface PayerRepository extends JpaRepository<Payer, Long>, ApheCustomRepository<Payer> {

	public Payer findByDomainId(String domainId);

	@Query("SELECT p FROM Payer p WHERE p.domainId IN :domainIds")
	public List<Payer> findByDomainIdIn(@Param("domainIds") List<String> domainIds);

}