package com.aphe.contractor.repo;

import com.aphe.common.data.repository.ApheCustomRepository;
import com.aphe.contractor.model.FedSub;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.enums.FilingStatus;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface FedSubRepository extends JpaRepository<FedSub, Long>, ApheCustomRepository<FedSub> {

	public List<FedSub> findByFiling(Filing filing);

	public FedSub findBySubmissionRefId(String submissionRefId);

	public List<FedSub> findByStatus(FilingStatus filingStatus);

	@Query("SELECT f FROM FedSub f WHERE f.status IN :filingStatuses")
	public List<FedSub> findByStatusIn(List<FilingStatus> filingStatuses);

	@Query("SELECT fs FROM FedSub fs join fs.filing f on fs.filing.id = f.id join f.payer p on f.payer.id = p.id join Domain d on p.domainId = :domainId WHERE fs.status IN :filingStatuses")
	public List<FedSub> findByDomainIdAndStatusIn(Long domainId, List<FilingStatus> filingStatuses);

	public FedSub findByUniqueRecordId(String uniqueRecordId);

}