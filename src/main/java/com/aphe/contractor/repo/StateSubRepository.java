package com.aphe.contractor.repo;

import com.aphe.common.data.repository.ApheCustomRepository;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.StateSub;
import com.aphe.contractor.model.enums.StateFilingStatus;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface StateSubRepository extends JpaRepository<StateSub, Long>, ApheCustomRepository<StateSub> {

	public List<StateSub> findByFiling(Filing filing);

	public StateSub findBySubmissionRefId(String submissionRefId);

	public List<StateSub> findByStatus(StateFilingStatus filingStatus);

	@Query("SELECT f FROM StateSub f WHERE f.status IN :filingStatuses")
	public List<StateSub> findByStatusIn(List<StateFilingStatus> filingStatuses);

	@Query("SELECT ss FROM StateSub ss join ss.filing f on ss.filing.id = f.id join f.payer p on f.payer.id = p.id join Domain d on p.domainId = :domainId WHERE ss.status IN :filingStatuses")
	public List<StateSub> findByDomainIdAndStatusIn(Long domainId, List<StateFilingStatus> filingStatuses);

}