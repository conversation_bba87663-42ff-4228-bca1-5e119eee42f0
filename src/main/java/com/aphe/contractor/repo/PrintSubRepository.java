package com.aphe.contractor.repo;

import com.aphe.common.data.repository.ApheCustomRepository;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.PrintSub;
import com.aphe.contractor.model.enums.PrintStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PrintSubRepository extends JpaRepository<PrintSub, Long>, ApheCustomRepository<PrintSub> {

	public List<PrintSub> findByFiling(Filing filing);

	public List<PrintSub> findByPrintCopyTrackingId(String printTrackingId);

	public List<PrintSub> findByPrintNotifyTrue();

	@Query("SELECT ps FROM PrintSub ps join ps.filing f on ps.filing.id = f.id join f.payer p on f.payer.id = p.id join Domain d on p.domainId = d.id WHERE p.domainId = :domainId and ps.printNotify = true")
	public List<PrintSub> findByDomainIdAndPrintNotifyTrue(Long domainId);

	public List<PrintSub> findByPrintStatus(PrintStatus printStatus);

	@Query("SELECT ps FROM PrintSub ps join ps.filing f on ps.filing.id = f.id join f.payer p on f.payer.id = p.id join Domain d on p.domainId = d.id WHERE p.domainId = :domainId and ps.printStatus = :printStatus")
	public List<PrintSub> findByDomainIdAndPrintStatus(Long domainId, PrintStatus printStatus);

	public List<PrintSub> findByPrintStatusIn(List<PrintStatus> printStatuses);


}