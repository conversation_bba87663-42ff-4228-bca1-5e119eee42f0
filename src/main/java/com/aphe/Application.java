package com.aphe;

import com.aphe.common.util.PropertiesManager;
import com.aphe.contractor.services.MailManager;
import com.aphe.email.services.EmailPrefManager;
import com.aphe.print.lob.LobClientFactory;
import io.sentry.Sentry;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.HashMap;

@SpringBootApplication
@EnableJpaRepositories("com.aphe.*")
@ComponentScan("com.aphe.*")
@EntityScan("com.aphe.*")
@ServletComponentScan("com.aphe")
@EnableScheduling
//@EnableZuulProxy
//@SpringCloudApplication
@PropertySource("classpath:git.properties")
public class Application extends SpringBootServletInitializer {

	private static final Logger logger = LoggerFactory.getLogger(Application.class);

	@Autowired
	Environment env;

	@Value("${app}")
	private String app;

	@Value("${appName}")
	private String appName;

	@Value("${spring.profiles.active:}")
	private String activeProfiles;

	@Value("${aphe.sentry.dsn}")
	private String sentryDSN;

	@Value("${aphe.sentry.stacktrace.app.packages}")
	private String sentryPackages;

	@Value("${git.commit.id.describe}")
	private String gitCommitId;

	@Autowired
	LobClientFactory lobClientFactory;

	@Autowired
	MailManager mailManager;

	@Autowired
	EmailPrefManager emailPrefManager;

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(Application.class);
	}

	@Bean
	public CommandLineRunner demo() {
		return (args) -> {

			if(!PropertiesManager.isDev()) {
				String sentryURL = getSentryURL();
				Sentry.init(sentryURL);
			}

			logger.info("************* APP NAME           *** " + appName);
			logger.info("************* APP MODE           *** " + PropertiesManager.getAppMode());
			logger.info("************* ACTIVE PROFILES    *** " + activeProfiles);

			logger.info("************* DB Config      *** " + env.getProperty("aphe.dbProperty"));

			logger.error("This is a sentry test for domain service... please ignore");
			lobClientFactory.isValidSignature("a", "b");


//			emailPrefManager.seedData();

//			List<UserPrefEmailGroupDTO> user1Prefs = emailPrefManager.getEmailPrefe	rences("1");
//
//			List<UserPrefEmailGroupDTO> user2Prefs = emailPrefManager.getEmailPreferences("3");
//
//			System.out.println(user2Prefs);



//			UserDTO user = new UserDTO();
//			user.setEmail("<EMAIL>");
//			user.setFirstName("Sandeep");
//			user.setLastName("Saini");
//			mailManager.sendReviewEmail("Xero", Arrays.asList(user));
		};
	}

	public String getSentryURL() {
		HashMap<String, String> sentryParams = new HashMap<>();
		sentryParams.put("stacktrace.app.packages", sentryPackages);
		sentryParams.put("environment", activeProfiles);
		sentryParams.put("release", gitCommitId);

		StringBuffer buffer = new StringBuffer();
		buffer.append(sentryDSN).append("?");
		for (String s : sentryParams.keySet()) {
			buffer.append(s).append("=").append(sentryParams.get(s)).append("&");
		}
		buffer.setLength(buffer.length() - 1);
		return buffer.toString();
	}

//	@Bean
//	public SchemaParserDictionary getSchemaParser() {
//		SchemaParserDictionary dictionary = new SchemaParserDictionary();
//		dictionary.add(new HashMap() {
//			{
//				put("Consumer", Consumer.class);
//				put("Business", Business.class);
//				put("Accountant", Accountant.class);
//				put("FilingData1099MISC", FilingData1099MISC.class);
//				put("FilingData1099MISC2020", FilingData1099MISC2020.class);
//				put("FilingData1099MISC2021", FilingData1099MISC2021.class);
//				put("FilingData1099NEC", FilingData1099NEC.class);
//				put("FilingData1099NEC2021", FilingData1099NEC2021.class);
//				put("FilingData1099INT", FilingData1099INT.class);
//				put("FilingData1099OID", FilingData1099OID.class);
//			}
//		});
//		return dictionary;
//	}

//	@Bean
//	public GraphQLErrorHandler errorHandler() {
//		return new GraphQLErrorHandler() {
//			@Override
//			public List<GraphQLError> processErrors(List<GraphQLError> errors) {
//				List<GraphQLError> clientErrors = errors.stream().filter(this::isClientError).collect(Collectors.toList());
//				List<GraphQLError> serverErrors = errors.stream().filter(e -> !isClientError(e)).map(GraphQLErrorAdapter::new).collect(Collectors.toList());
//
//				List<GraphQLError> e = new ArrayList<>();
//				e.addAll(clientErrors);
//				e.addAll(serverErrors);
//				return e;
//			}
//
//			protected boolean isClientError(GraphQLError error) {
//				return !(error instanceof ExceptionWhileDataFetching || error instanceof Throwable);
//			}
//		};
//	}

	@Bean
	public OpenAPI customOpenAPI() {
		return new OpenAPI().components(new Components().addSecuritySchemes("basicScheme", new SecurityScheme().type(SecurityScheme.Type.HTTP).scheme("basic"))).info(new Info()
				.title("1099SmartFile API Documentation").version("1.0")
				//TODO: Read this from README.md file. Easier to edit in a text file.
				.description(
						"This is a sample server Petstore server.  You can find out more about     Swagger at [http://swagger.io](http://swagger.io) or on [irc.freenode.net, #swagger](http://swagger.io/irc/).      "
								+ "For this sample, you can use the api key `special-key` to test the authorization  filters. Schema at [User](#/components/schemas/User). ")
				//TODO: What is the right term URL
				//TODO: Add contact and other bean properties.
				.termsOfService("https://app.1099smartfile.com/terms/")
				//TODO: What is the right license to use.
				.license(new License().name("Apache 2.0").url("http://www.1099smartfile.com")));
	}

//	@Bean
//	public RateLimiterErrorHandler rateLimitErrorHandler() {
//		return new DefaultRateLimiterErrorHandler() {
//			@Override
//			public void handleSaveError(String key, Exception e) {
//				// implementation
//			}
//
//			@Override
//			public void handleFetchError(String key, Exception e) {
//				// implementation
//			}
//
//			@Override
//			public void handleError(String msg, Exception e) {
//				// implementation
//			}
//		};
//	}
}