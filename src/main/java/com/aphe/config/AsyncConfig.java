package com.aphe.config;

import org.slf4j.MDC;
import org.springframework.boot.task.ThreadPoolTaskExecutorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;
import org.springframework.web.context.request.RequestContextHolder;

@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * The core pool used by @Async methods.
     * Uses the modern ThreadPoolTaskExecutorBuilder under the covers via Boot auto-config,
     * then we explicitly customize and wrap with SecurityContext propagation.
     */
    @Bean(name = "applicationTaskExecutor") // modern canonical name
    public AsyncTaskExecutor applicationTaskExecutor(ThreadPoolTaskExecutorBuilder builder) {
        ThreadPoolTaskExecutor delegate = builder
                .corePoolSize(8)
                .maxPoolSize(32)
                .queueCapacity(500)
                .threadNamePrefix("app-async-")
                .taskDecorator(contextPropagatingTaskDecorator()).build();
        delegate.initialize();

        // Propagate SecurityContext to @Async tasks
        return new DelegatingSecurityContextAsyncTaskExecutor(delegate);
    }

    @Bean
    public TaskDecorator contextPropagatingTaskDecorator() {
        return runnable -> {
            var securityContext = SecurityContextHolder.getContext();
            var mdc = MDC.getCopyOfContextMap();
            var reqAttrs = RequestContextHolder.getRequestAttributes();

            return () -> {
                try {
                    SecurityContextHolder.setContext(securityContext);
                    if (mdc != null) MDC.setContextMap(mdc);
                    else MDC.clear();
                    if (reqAttrs != null) RequestContextHolder.setRequestAttributes(reqAttrs);

                    runnable.run();
                } finally {
                    // avoid context bleed between reused pool threads
                    SecurityContextHolder.clearContext();
                    MDC.clear();
                    RequestContextHolder.resetRequestAttributes();
                }
            };
        };
    }

}

