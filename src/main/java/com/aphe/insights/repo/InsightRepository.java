package com.aphe.insights.repo;

import com.aphe.common.data.repository.ApheCustomRepository;
import com.aphe.insights.model.Insight;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;


public interface InsightRepository extends JpaRepository<Insight, Long>, ApheCustomRepository<Insight> {

    public Insight findByDomainIdAndName(@Param("domainId") String domainId, @Param("name") String name);

    public List<Insight> findByDomainIdInAndNameIn(@Param("domainIds") List<String> domainIds, @Param("names") List<String> names);


}