package com.aphe.insights.service;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.service.CommonBaseManager;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.model.FilingCount;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.FilingYear;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.PayerManager;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.service.DomainMgr;
import com.aphe.insights.dto.InsightDTO;
import com.aphe.insights.dto.InsightsDTO;
import com.aphe.insights.model.Insight;
import com.aphe.insights.repo.InsightRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class InsightsManager extends CommonBaseManager {

    @Autowired
    private InsightRepository insightRepository;

    @Autowired
    DomainMgr domainMgr;

    @Autowired
    PayerManager payerManager;

    @Autowired
    FilingManager filingManager;

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<InsightsDTO> getInsights(List<Long> domainIds, List<String> insights) throws ApheForbiddenException {

        if(domainIds == null || domainIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<Insight> insightList = insightRepository.findByDomainIdInAndNameIn(domainIds, insights);

        //Group by domainId
        Map<Long, List<Insight>> domainIdInsightMap = insightList.stream().collect(Collectors.groupingBy(Insight::getDomainId));



        //Create InsightsDTO
        List<InsightsDTO> insightsDTOList = domainIdInsightMap.entrySet().stream().map(entry -> {
            InsightsDTO insightsDTO = new InsightsDTO();
            insightsDTO.domainId = entry.getKey();

            DomainDTO domainDTO = null;
            try {
                domainDTO  = domainMgr.getDomain(entry.getKey());
            }catch (ApheForbiddenException e) {
                e.printStackTrace();
            }

            insightsDTO.insights = entry.getValue().stream().map(insight -> {
                InsightDTO insightDTO = new InsightDTO();
                insightDTO.name = insight.getName();
                insightDTO.value = insight.getValue();
                insightDTO.lastUpdated = insight.getLastUpdated();
                return insightDTO;
            }).collect(Collectors.toList());

            List<InsightDTO> customAttributes = new ArrayList<>();
            if(domainDTO != null) {
                if(domainDTO.customAttributes != null) {
                    customAttributes = domainDTO.customAttributes.stream().map(domainCustomAttribute -> {
                        InsightDTO insightDTO = new InsightDTO();
                        insightDTO.name = domainCustomAttribute.key;
                        insightDTO.value = domainCustomAttribute.value;
                        return insightDTO;
                    }).collect(Collectors.toList());
                }
            }
            insightsDTO.insights.addAll(customAttributes);

            return insightsDTO;
        }).collect(Collectors.toList());

        return insightsDTOList;
    }


    @Transactional
    public void updateInsight(Long domainId, String insightName, String insightValue) {
        Insight insight = insightRepository.findByDomainIdAndName(domainId, insightName);
        if(insight == null) {
            insight = new Insight();
            insight.setDomainId(domainId);
            insight.setName(insightName);
            insight.setValue(insightValue);
            insight.setLastUpdated(new Date());
        } else {
            insight.setValue(insightValue);
            insight.setLastUpdated(new Date());
        }
        insightRepository.save(insight);
    }


    public void updateTINInsight(Long domainId) {
        try {
            DomainDTO domainDTO = domainMgr.getDomain(domainId);
            String tin = domainDTO.tin;
            if(StringUtil.isNotEmpty(tin)) {
                updateInsight(domainId, "tin", tin);
            }
        } catch (ApheForbiddenException e) {
            e.printStackTrace();
        }
    }

    @Transactional
    public void updateFilingCounts(Long domainId) {
        try {
            PayerDTO payerDTO = payerManager.getPayerByDomainId(domainId);
            List<FilingCount> filingCounts = filingManager.getFilingCounts(payerDTO.id, FilingYear.getCurrentFilingYear().getYear());
            //TODO: Figure out to get the errored counts...
            int draftCount = 0;
            int processingCount = 0;
            int acceptedCount = 0;
            int totalFilingCount = 0;
            //Loop over the filing counts.
            for(FilingCount filingCount : filingCounts) {
                FilingStatus filingStatus = filingCount.getStatus();
                if(filingStatus == FilingStatus.Draft) {
                    draftCount += filingCount.getFilingCount();
                } else if(filingStatus == FilingStatus.Accepted) {
                    acceptedCount += filingCount.getFilingCount();
                } else {
                    processingCount += filingCount.getFilingCount();
                }
                totalFilingCount += filingCount.getFilingCount();
            }
            updateInsight(domainId, "draftCount", Integer.toString(draftCount));
            updateInsight(domainId, "processingCount", Integer.toString(processingCount));
            updateInsight(domainId, "acceptedCount", Integer.toString(acceptedCount));
            updateInsight(domainId, "totalCount", Integer.toString(totalFilingCount));
        } catch (ApheForbiddenException e) {
            e.printStackTrace();
        } catch (ApheException e) {
            e.printStackTrace();
        }
    }




}
