package com.aphe.insights.rs;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.insights.dto.InsightsDTO;
import com.aphe.insights.service.InsightsManager;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(path = "/rs/api/insights/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Insights", description = "API to manage insights about a domain")
public class InsightsController {

	private static Logger logger = LoggerFactory.getLogger(InsightsController.class);

	@Autowired
	InsightsManager insightsManager;

	@GetMapping
//	@ApiOperation(value = "Get the requested insights for the requested domainIds", notes = "Gets the insights requested", response = InsightsDTO.class, responseContainer = "List")
	public List<InsightsDTO> getInsights(@RequestParam (name="domainIds",required = false ) List<String> domainIds, @RequestParam (name="insights", required = false) List<String> insights) throws Exception {
		try {
			return insightsManager.getInsights(domainIds, insights);
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting Insights"));
		}
	}

}
