
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;
import java.math.BigInteger;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Form 1099 OID Total by State Group Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2021-10-21&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;The content model for the form 1099 OID totals by state&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for Form1099OIDTotalByStateGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Form1099OIDTotalByStateGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="StateAbbreviationCd" type="{urn:us:gov:treasury:irs:ir}StateType"/&gt;
 *         &lt;element name="TotalReportedRcpntFormCnt" type="{urn:us:gov:treasury:irs:ir}TotalNumberNonNegativeType" minOccurs="0"/&gt;
 *         &lt;element name="FederalIncomeTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="StateTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="LocalTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="OriginalIssueDiscountAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="OtherPeriodicInterestAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="EarlyWithdrawalPenaltyAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="MarketDiscountAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="AcquisitionPremiumAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="TreasuryObligationOIDAmt" type="{urn:us:gov:treasury:irs:ir}USAmountType" minOccurs="0"/&gt;
 *         &lt;element name="InvestmentExpenseAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="BondPremiumAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="TaxExemptOIDAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Form1099OIDTotalByStateGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "stateAbbreviationCd",
    "totalReportedRcpntFormCnt",
    "federalIncomeTaxWithheldAmt",
    "stateTaxWithheldAmt",
    "localTaxWithheldAmt",
    "originalIssueDiscountAmt",
    "otherPeriodicInterestAmt",
    "earlyWithdrawalPenaltyAmt",
    "marketDiscountAmt",
    "acquisitionPremiumAmt",
    "treasuryObligationOIDAmt",
    "investmentExpenseAmt",
    "bondPremiumAmt",
    "taxExemptOIDAmt"
})
public class Form1099OIDTotalByStateGrpType {

    @XmlElement(name = "StateAbbreviationCd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    @XmlSchemaType(name = "string")
    protected StateType stateAbbreviationCd;
    @XmlElement(name = "TotalReportedRcpntFormCnt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger totalReportedRcpntFormCnt;
    @XmlElement(name = "FederalIncomeTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger federalIncomeTaxWithheldAmt;
    @XmlElement(name = "StateTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger stateTaxWithheldAmt;
    @XmlElement(name = "LocalTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger localTaxWithheldAmt;
    @XmlElement(name = "OriginalIssueDiscountAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger originalIssueDiscountAmt;
    @XmlElement(name = "OtherPeriodicInterestAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger otherPeriodicInterestAmt;
    @XmlElement(name = "EarlyWithdrawalPenaltyAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger earlyWithdrawalPenaltyAmt;
    @XmlElement(name = "MarketDiscountAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger marketDiscountAmt;
    @XmlElement(name = "AcquisitionPremiumAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger acquisitionPremiumAmt;
    @XmlElement(name = "TreasuryObligationOIDAmt", namespace = "urn:us:gov:treasury:irs:ir")
    protected BigInteger treasuryObligationOIDAmt;
    @XmlElement(name = "InvestmentExpenseAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger investmentExpenseAmt;
    @XmlElement(name = "BondPremiumAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger bondPremiumAmt;
    @XmlElement(name = "TaxExemptOIDAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger taxExemptOIDAmt;

    /**
     * Gets the value of the stateAbbreviationCd property.
     * 
     * @return
     *     possible object is
     *     {@link StateType }
     *     
     */
    public StateType getStateAbbreviationCd() {
        return stateAbbreviationCd;
    }

    /**
     * Sets the value of the stateAbbreviationCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link StateType }
     *     
     */
    public void setStateAbbreviationCd(StateType value) {
        this.stateAbbreviationCd = value;
    }

    /**
     * Gets the value of the totalReportedRcpntFormCnt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTotalReportedRcpntFormCnt() {
        return totalReportedRcpntFormCnt;
    }

    /**
     * Sets the value of the totalReportedRcpntFormCnt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTotalReportedRcpntFormCnt(BigInteger value) {
        this.totalReportedRcpntFormCnt = value;
    }

    /**
     * Gets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getFederalIncomeTaxWithheldAmt() {
        return federalIncomeTaxWithheldAmt;
    }

    /**
     * Sets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setFederalIncomeTaxWithheldAmt(BigInteger value) {
        this.federalIncomeTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the stateTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getStateTaxWithheldAmt() {
        return stateTaxWithheldAmt;
    }

    /**
     * Sets the value of the stateTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setStateTaxWithheldAmt(BigInteger value) {
        this.stateTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the localTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getLocalTaxWithheldAmt() {
        return localTaxWithheldAmt;
    }

    /**
     * Sets the value of the localTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setLocalTaxWithheldAmt(BigInteger value) {
        this.localTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the originalIssueDiscountAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getOriginalIssueDiscountAmt() {
        return originalIssueDiscountAmt;
    }

    /**
     * Sets the value of the originalIssueDiscountAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setOriginalIssueDiscountAmt(BigInteger value) {
        this.originalIssueDiscountAmt = value;
    }

    /**
     * Gets the value of the otherPeriodicInterestAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getOtherPeriodicInterestAmt() {
        return otherPeriodicInterestAmt;
    }

    /**
     * Sets the value of the otherPeriodicInterestAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setOtherPeriodicInterestAmt(BigInteger value) {
        this.otherPeriodicInterestAmt = value;
    }

    /**
     * Gets the value of the earlyWithdrawalPenaltyAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getEarlyWithdrawalPenaltyAmt() {
        return earlyWithdrawalPenaltyAmt;
    }

    /**
     * Sets the value of the earlyWithdrawalPenaltyAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setEarlyWithdrawalPenaltyAmt(BigInteger value) {
        this.earlyWithdrawalPenaltyAmt = value;
    }

    /**
     * Gets the value of the marketDiscountAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getMarketDiscountAmt() {
        return marketDiscountAmt;
    }

    /**
     * Sets the value of the marketDiscountAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setMarketDiscountAmt(BigInteger value) {
        this.marketDiscountAmt = value;
    }

    /**
     * Gets the value of the acquisitionPremiumAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getAcquisitionPremiumAmt() {
        return acquisitionPremiumAmt;
    }

    /**
     * Sets the value of the acquisitionPremiumAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setAcquisitionPremiumAmt(BigInteger value) {
        this.acquisitionPremiumAmt = value;
    }

    /**
     * Gets the value of the treasuryObligationOIDAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTreasuryObligationOIDAmt() {
        return treasuryObligationOIDAmt;
    }

    /**
     * Sets the value of the treasuryObligationOIDAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTreasuryObligationOIDAmt(BigInteger value) {
        this.treasuryObligationOIDAmt = value;
    }

    /**
     * Gets the value of the investmentExpenseAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getInvestmentExpenseAmt() {
        return investmentExpenseAmt;
    }

    /**
     * Sets the value of the investmentExpenseAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setInvestmentExpenseAmt(BigInteger value) {
        this.investmentExpenseAmt = value;
    }

    /**
     * Gets the value of the bondPremiumAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getBondPremiumAmt() {
        return bondPremiumAmt;
    }

    /**
     * Sets the value of the bondPremiumAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setBondPremiumAmt(BigInteger value) {
        this.bondPremiumAmt = value;
    }

    /**
     * Gets the value of the taxExemptOIDAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTaxExemptOIDAmt() {
        return taxExemptOIDAmt;
    }

    /**
     * Sets the value of the taxExemptOIDAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTaxExemptOIDAmt(BigInteger value) {
        this.taxExemptOIDAmt = value;
    }

}
