
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Information Return (IR) Submission Group Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2015-10-27&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;A group that wraps the data related to a (IR) information return submission&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for IRSubmission1GrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="IRSubmission1GrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="IRSubmission1Header" type="{urn:us:gov:treasury:irs:ir}IRSubmission1HeaderType"/&gt;
 *         &lt;element name="IRSubmission1Detail" type="{urn:us:gov:treasury:irs:ir}IRSubmission1DetailType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IRSubmission1GrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "irSubmission1Header",
    "irSubmission1Detail"
})
public class IRSubmission1GrpType {

    @XmlElement(name = "IRSubmission1Header", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected IRSubmission1HeaderType irSubmission1Header;
    @XmlElement(name = "IRSubmission1Detail", namespace = "urn:us:gov:treasury:irs:ir")
    protected IRSubmission1DetailType irSubmission1Detail;

    /**
     * Gets the value of the irSubmission1Header property.
     * 
     * @return
     *     possible object is
     *     {@link IRSubmission1HeaderType }
     *     
     */
    public IRSubmission1HeaderType getIRSubmission1Header() {
        return irSubmission1Header;
    }

    /**
     * Sets the value of the irSubmission1Header property.
     * 
     * @param value
     *     allowed object is
     *     {@link IRSubmission1HeaderType }
     *     
     */
    public void setIRSubmission1Header(IRSubmission1HeaderType value) {
        this.irSubmission1Header = value;
    }

    /**
     * Gets the value of the irSubmission1Detail property.
     * 
     * @return
     *     possible object is
     *     {@link IRSubmission1DetailType }
     *     
     */
    public IRSubmission1DetailType getIRSubmission1Detail() {
        return irSubmission1Detail;
    }

    /**
     * Sets the value of the irSubmission1Detail property.
     * 
     * @param value
     *     allowed object is
     *     {@link IRSubmission1DetailType }
     *     
     */
    public void setIRSubmission1Detail(IRSubmission1DetailType value) {
        this.irSubmission1Detail = value;
    }

}
