
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Contact Person Information Group Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2015-09-01&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;A group that wraps information related to the contact person.&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for ContactPersonInformationGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ContactPersonInformationGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ContactPersonNm" type="{urn:us:gov:treasury:irs:ir}PersonNameType"/&gt;
 *         &lt;element name="ContactPhoneNum" type="{urn:us:gov:treasury:irs:ir}ContactPhoneNumberType" minOccurs="0"/&gt;
 *         &lt;element name="ContactEmailAddressTxt" type="{urn:us:gov:treasury:irs:ir}EmailAddressType" minOccurs="0"/&gt;
 *         &lt;element name="ContactFaxNum" type="{urn:us:gov:treasury:irs:ir}ContactPhoneNumberType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ContactPersonInformationGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "contactPersonNm",
    "contactPhoneNum",
    "contactEmailAddressTxt",
    "contactFaxNum"
})
public class ContactPersonInformationGrpType {

    @XmlElement(name = "ContactPersonNm", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String contactPersonNm;
    @XmlElement(name = "ContactPhoneNum", namespace = "urn:us:gov:treasury:irs:ir")
    protected String contactPhoneNum;
    @XmlElement(name = "ContactEmailAddressTxt", namespace = "urn:us:gov:treasury:irs:ir")
    protected String contactEmailAddressTxt;
    @XmlElement(name = "ContactFaxNum", namespace = "urn:us:gov:treasury:irs:ir")
    protected String contactFaxNum;

    /**
     * Gets the value of the contactPersonNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactPersonNm() {
        return contactPersonNm;
    }

    /**
     * Sets the value of the contactPersonNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactPersonNm(String value) {
        this.contactPersonNm = value;
    }

    /**
     * Gets the value of the contactPhoneNum property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactPhoneNum() {
        return contactPhoneNum;
    }

    /**
     * Sets the value of the contactPhoneNum property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactPhoneNum(String value) {
        this.contactPhoneNum = value;
    }

    /**
     * Gets the value of the contactEmailAddressTxt property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactEmailAddressTxt() {
        return contactEmailAddressTxt;
    }

    /**
     * Sets the value of the contactEmailAddressTxt property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactEmailAddressTxt(String value) {
        this.contactEmailAddressTxt = value;
    }

    /**
     * Gets the value of the contactFaxNum property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactFaxNum() {
        return contactFaxNum;
    }

    /**
     * Sets the value of the contactFaxNum property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactFaxNum(String value) {
        this.contactFaxNum = value;
    }

}
