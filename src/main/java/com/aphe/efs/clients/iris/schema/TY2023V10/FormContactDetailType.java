
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Contact Detail Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2022-01-12&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;The content model for the form Contact Detail.&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for FormContactDetailType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="FormContactDetailType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ContactNm" type="{urn:us:gov:treasury:irs:ir}PersonNameType" minOccurs="0"/&gt;
 *         &lt;element name="ContactAddressGrp" type="{urn:us:gov:treasury:irs:ir}AddressGrpType" minOccurs="0"/&gt;
 *         &lt;element name="ContactPhoneNum" type="{urn:us:gov:treasury:irs:ir}ContactPhoneNumberType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FormContactDetailType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "contactNm",
    "contactAddressGrp",
    "contactPhoneNum"
})
public class FormContactDetailType {

    @XmlElement(name = "ContactNm", namespace = "urn:us:gov:treasury:irs:ir")
    protected String contactNm;
    @XmlElement(name = "ContactAddressGrp", namespace = "urn:us:gov:treasury:irs:ir")
    protected AddressGrpType contactAddressGrp;
    @XmlElement(name = "ContactPhoneNum", namespace = "urn:us:gov:treasury:irs:ir")
    protected String contactPhoneNum;

    /**
     * Gets the value of the contactNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactNm() {
        return contactNm;
    }

    /**
     * Sets the value of the contactNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactNm(String value) {
        this.contactNm = value;
    }

    /**
     * Gets the value of the contactAddressGrp property.
     * 
     * @return
     *     possible object is
     *     {@link AddressGrpType }
     *     
     */
    public AddressGrpType getContactAddressGrp() {
        return contactAddressGrp;
    }

    /**
     * Sets the value of the contactAddressGrp property.
     * 
     * @param value
     *     allowed object is
     *     {@link AddressGrpType }
     *     
     */
    public void setContactAddressGrp(AddressGrpType value) {
        this.contactAddressGrp = value;
    }

    /**
     * Gets the value of the contactPhoneNum property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactPhoneNum() {
        return contactPhoneNum;
    }

    /**
     * Sets the value of the contactPhoneNum property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactPhoneNum(String value) {
        this.contactPhoneNum = value;
    }

}
