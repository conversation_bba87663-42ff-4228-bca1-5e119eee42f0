
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Description xmlns="urn:us:gov:treasury:irs:ir" xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;Group that wraps contact person name details.&lt;/Description&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for ContactNameGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ContactNameGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;choice&gt;
 *         &lt;element name="PersonNm" type="{urn:us:gov:treasury:irs:ir}PersonNameType"/&gt;
 *         &lt;sequence&gt;
 *           &lt;element name="PersonFirstNm" type="{urn:us:gov:treasury:irs:ir}PersonFirstNameType"/&gt;
 *           &lt;element name="PersonMiddleNm" type="{urn:us:gov:treasury:irs:ir}PersonMiddleNameType" minOccurs="0"/&gt;
 *           &lt;element name="PersonLastNm" type="{urn:us:gov:treasury:irs:ir}PersonLastNameType"/&gt;
 *           &lt;element name="SuffixNm" type="{urn:us:gov:treasury:irs:ir}SuffixNameType" minOccurs="0"/&gt;
 *         &lt;/sequence&gt;
 *       &lt;/choice&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ContactNameGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "personNm",
    "personFirstNm",
    "personMiddleNm",
    "personLastNm",
    "suffixNm"
})
public class ContactNameGrpType {

    @XmlElement(name = "PersonNm", namespace = "urn:us:gov:treasury:irs:ir")
    protected String personNm;
    @XmlElement(name = "PersonFirstNm", namespace = "urn:us:gov:treasury:irs:ir")
    protected String personFirstNm;
    @XmlElement(name = "PersonMiddleNm", namespace = "urn:us:gov:treasury:irs:ir")
    protected String personMiddleNm;
    @XmlElement(name = "PersonLastNm", namespace = "urn:us:gov:treasury:irs:ir")
    protected String personLastNm;
    @XmlElement(name = "SuffixNm", namespace = "urn:us:gov:treasury:irs:ir")
    protected String suffixNm;

    /**
     * Gets the value of the personNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonNm() {
        return personNm;
    }

    /**
     * Sets the value of the personNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonNm(String value) {
        this.personNm = value;
    }

    /**
     * Gets the value of the personFirstNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonFirstNm() {
        return personFirstNm;
    }

    /**
     * Sets the value of the personFirstNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonFirstNm(String value) {
        this.personFirstNm = value;
    }

    /**
     * Gets the value of the personMiddleNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonMiddleNm() {
        return personMiddleNm;
    }

    /**
     * Sets the value of the personMiddleNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonMiddleNm(String value) {
        this.personMiddleNm = value;
    }

    /**
     * Gets the value of the personLastNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonLastNm() {
        return personLastNm;
    }

    /**
     * Sets the value of the personLastNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonLastNm(String value) {
        this.personLastNm = value;
    }

    /**
     * Gets the value of the suffixNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSuffixNm() {
        return suffixNm;
    }

    /**
     * Sets the value of the suffixNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSuffixNm(String value) {
        this.suffixNm = value;
    }

}
