
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;
import java.math.BigInteger;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Description xmlns="urn:us:gov:treasury:irs:ir" xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;A group that wraps the data regarding local tax information&lt;/Description&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for LocalTaxGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="LocalTaxGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="LocalTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="LocalIncomeAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="LocalityNm" type="{urn:us:gov:treasury:irs:ir}ShortDescriptionType" minOccurs="0"/&gt;
 *         &lt;element name="LocalAbbreviationCdTxt" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{urn:us:gov:treasury:irs:ir}TextType"&gt;
 *               &lt;maxLength value="10"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LocalDistributionAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "LocalTaxGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "localTaxWithheldAmt",
    "localIncomeAmt",
    "localityNm",
    "localAbbreviationCdTxt",
    "localDistributionAmt"
})
public class LocalTaxGrpType {

    @XmlElement(name = "LocalTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger localTaxWithheldAmt;
    @XmlElement(name = "LocalIncomeAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger localIncomeAmt;
    @XmlElement(name = "LocalityNm", namespace = "urn:us:gov:treasury:irs:ir")
    protected String localityNm;
    @XmlElement(name = "LocalAbbreviationCdTxt", namespace = "urn:us:gov:treasury:irs:ir")
    protected String localAbbreviationCdTxt;
    @XmlElement(name = "LocalDistributionAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger localDistributionAmt;

    /**
     * Gets the value of the localTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getLocalTaxWithheldAmt() {
        return localTaxWithheldAmt;
    }

    /**
     * Sets the value of the localTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setLocalTaxWithheldAmt(BigInteger value) {
        this.localTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the localIncomeAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getLocalIncomeAmt() {
        return localIncomeAmt;
    }

    /**
     * Sets the value of the localIncomeAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setLocalIncomeAmt(BigInteger value) {
        this.localIncomeAmt = value;
    }

    /**
     * Gets the value of the localityNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocalityNm() {
        return localityNm;
    }

    /**
     * Sets the value of the localityNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocalityNm(String value) {
        this.localityNm = value;
    }

    /**
     * Gets the value of the localAbbreviationCdTxt property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocalAbbreviationCdTxt() {
        return localAbbreviationCdTxt;
    }

    /**
     * Sets the value of the localAbbreviationCdTxt property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocalAbbreviationCdTxt(String value) {
        this.localAbbreviationCdTxt = value;
    }

    /**
     * Gets the value of the localDistributionAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getLocalDistributionAmt() {
        return localDistributionAmt;
    }

    /**
     * Sets the value of the localDistributionAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setLocalDistributionAmt(BigInteger value) {
        this.localDistributionAmt = value;
    }

}
