
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;DescriptionTxt xmlns="urn:us:gov:treasury:irs:ir" xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;The mailing address group is a group that wraps a choice between USAddressGrp/ForeignAddressGrp&lt;/DescriptionTxt&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for AddressGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AddressGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;choice&gt;
 *         &lt;element name="USAddress" type="{urn:us:gov:treasury:irs:ir}USAddressType"/&gt;
 *         &lt;element name="ForeignAddress" type="{urn:us:gov:treasury:irs:ir}ForeignAddressType"/&gt;
 *       &lt;/choice&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AddressGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "usAddress",
    "foreignAddress"
})
public class AddressGrpType {

    @XmlElement(name = "USAddress", namespace = "urn:us:gov:treasury:irs:ir")
    protected USAddressType usAddress;
    @XmlElement(name = "ForeignAddress", namespace = "urn:us:gov:treasury:irs:ir")
    protected ForeignAddressType foreignAddress;

    /**
     * Gets the value of the usAddress property.
     * 
     * @return
     *     possible object is
     *     {@link USAddressType }
     *     
     */
    public USAddressType getUSAddress() {
        return usAddress;
    }

    /**
     * Sets the value of the usAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link USAddressType }
     *     
     */
    public void setUSAddress(USAddressType value) {
        this.usAddress = value;
    }

    /**
     * Gets the value of the foreignAddress property.
     * 
     * @return
     *     possible object is
     *     {@link ForeignAddressType }
     *     
     */
    public ForeignAddressType getForeignAddress() {
        return foreignAddress;
    }

    /**
     * Sets the value of the foreignAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link ForeignAddressType }
     *     
     */
    public void setForeignAddress(ForeignAddressType value) {
        this.foreignAddress = value;
    }

}
