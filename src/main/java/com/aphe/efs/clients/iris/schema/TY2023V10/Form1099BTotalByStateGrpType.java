
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;
import java.math.BigInteger;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Form 1099 B Total by State Group Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2021-08-26&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;The content model for the form 1099 B totals by state&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for Form1099BTotalByStateGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Form1099BTotalByStateGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="StateAbbreviationCd" type="{urn:us:gov:treasury:irs:ir}StateType"/&gt;
 *         &lt;element name="TotalReportedRcpntFormCnt" type="{urn:us:gov:treasury:irs:ir}TotalNumberNonNegativeType" minOccurs="0"/&gt;
 *         &lt;element name="FederalIncomeTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="StateTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="LocalTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="ProceedsAmt" type="{urn:us:gov:treasury:irs:ir}USAmountType" minOccurs="0"/&gt;
 *         &lt;element name="CostOrOtherBasisAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="AccruedMarketDiscountAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="NondeductibleWashSaleLossAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="TYClosedContractProfitLossAmt" type="{urn:us:gov:treasury:irs:ir}USAmountType" minOccurs="0"/&gt;
 *         &lt;element name="PriorYrOpenCntrctProfitLossAmt" type="{urn:us:gov:treasury:irs:ir}USAmountType" minOccurs="0"/&gt;
 *         &lt;element name="CYOpenCntrctProfitLossAmt" type="{urn:us:gov:treasury:irs:ir}USAmountType" minOccurs="0"/&gt;
 *         &lt;element name="CntrctAggregateProfitLossAmt" type="{urn:us:gov:treasury:irs:ir}USAmountType" minOccurs="0"/&gt;
 *         &lt;element name="BarterAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Form1099BTotalByStateGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "stateAbbreviationCd",
    "totalReportedRcpntFormCnt",
    "federalIncomeTaxWithheldAmt",
    "stateTaxWithheldAmt",
    "localTaxWithheldAmt",
    "proceedsAmt",
    "costOrOtherBasisAmt",
    "accruedMarketDiscountAmt",
    "nondeductibleWashSaleLossAmt",
    "tyClosedContractProfitLossAmt",
    "priorYrOpenCntrctProfitLossAmt",
    "cyOpenCntrctProfitLossAmt",
    "cntrctAggregateProfitLossAmt",
    "barterAmt"
})
public class Form1099BTotalByStateGrpType {

    @XmlElement(name = "StateAbbreviationCd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    @XmlSchemaType(name = "string")
    protected StateType stateAbbreviationCd;
    @XmlElement(name = "TotalReportedRcpntFormCnt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger totalReportedRcpntFormCnt;
    @XmlElement(name = "FederalIncomeTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger federalIncomeTaxWithheldAmt;
    @XmlElement(name = "StateTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger stateTaxWithheldAmt;
    @XmlElement(name = "LocalTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger localTaxWithheldAmt;
    @XmlElement(name = "ProceedsAmt", namespace = "urn:us:gov:treasury:irs:ir")
    protected BigInteger proceedsAmt;
    @XmlElement(name = "CostOrOtherBasisAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger costOrOtherBasisAmt;
    @XmlElement(name = "AccruedMarketDiscountAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger accruedMarketDiscountAmt;
    @XmlElement(name = "NondeductibleWashSaleLossAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger nondeductibleWashSaleLossAmt;
    @XmlElement(name = "TYClosedContractProfitLossAmt", namespace = "urn:us:gov:treasury:irs:ir")
    protected BigInteger tyClosedContractProfitLossAmt;
    @XmlElement(name = "PriorYrOpenCntrctProfitLossAmt", namespace = "urn:us:gov:treasury:irs:ir")
    protected BigInteger priorYrOpenCntrctProfitLossAmt;
    @XmlElement(name = "CYOpenCntrctProfitLossAmt", namespace = "urn:us:gov:treasury:irs:ir")
    protected BigInteger cyOpenCntrctProfitLossAmt;
    @XmlElement(name = "CntrctAggregateProfitLossAmt", namespace = "urn:us:gov:treasury:irs:ir")
    protected BigInteger cntrctAggregateProfitLossAmt;
    @XmlElement(name = "BarterAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger barterAmt;

    /**
     * Gets the value of the stateAbbreviationCd property.
     * 
     * @return
     *     possible object is
     *     {@link StateType }
     *     
     */
    public StateType getStateAbbreviationCd() {
        return stateAbbreviationCd;
    }

    /**
     * Sets the value of the stateAbbreviationCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link StateType }
     *     
     */
    public void setStateAbbreviationCd(StateType value) {
        this.stateAbbreviationCd = value;
    }

    /**
     * Gets the value of the totalReportedRcpntFormCnt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTotalReportedRcpntFormCnt() {
        return totalReportedRcpntFormCnt;
    }

    /**
     * Sets the value of the totalReportedRcpntFormCnt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTotalReportedRcpntFormCnt(BigInteger value) {
        this.totalReportedRcpntFormCnt = value;
    }

    /**
     * Gets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getFederalIncomeTaxWithheldAmt() {
        return federalIncomeTaxWithheldAmt;
    }

    /**
     * Sets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setFederalIncomeTaxWithheldAmt(BigInteger value) {
        this.federalIncomeTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the stateTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getStateTaxWithheldAmt() {
        return stateTaxWithheldAmt;
    }

    /**
     * Sets the value of the stateTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setStateTaxWithheldAmt(BigInteger value) {
        this.stateTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the localTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getLocalTaxWithheldAmt() {
        return localTaxWithheldAmt;
    }

    /**
     * Sets the value of the localTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setLocalTaxWithheldAmt(BigInteger value) {
        this.localTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the proceedsAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getProceedsAmt() {
        return proceedsAmt;
    }

    /**
     * Sets the value of the proceedsAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setProceedsAmt(BigInteger value) {
        this.proceedsAmt = value;
    }

    /**
     * Gets the value of the costOrOtherBasisAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCostOrOtherBasisAmt() {
        return costOrOtherBasisAmt;
    }

    /**
     * Sets the value of the costOrOtherBasisAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCostOrOtherBasisAmt(BigInteger value) {
        this.costOrOtherBasisAmt = value;
    }

    /**
     * Gets the value of the accruedMarketDiscountAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getAccruedMarketDiscountAmt() {
        return accruedMarketDiscountAmt;
    }

    /**
     * Sets the value of the accruedMarketDiscountAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setAccruedMarketDiscountAmt(BigInteger value) {
        this.accruedMarketDiscountAmt = value;
    }

    /**
     * Gets the value of the nondeductibleWashSaleLossAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNondeductibleWashSaleLossAmt() {
        return nondeductibleWashSaleLossAmt;
    }

    /**
     * Sets the value of the nondeductibleWashSaleLossAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNondeductibleWashSaleLossAmt(BigInteger value) {
        this.nondeductibleWashSaleLossAmt = value;
    }

    /**
     * Gets the value of the tyClosedContractProfitLossAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTYClosedContractProfitLossAmt() {
        return tyClosedContractProfitLossAmt;
    }

    /**
     * Sets the value of the tyClosedContractProfitLossAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTYClosedContractProfitLossAmt(BigInteger value) {
        this.tyClosedContractProfitLossAmt = value;
    }

    /**
     * Gets the value of the priorYrOpenCntrctProfitLossAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getPriorYrOpenCntrctProfitLossAmt() {
        return priorYrOpenCntrctProfitLossAmt;
    }

    /**
     * Sets the value of the priorYrOpenCntrctProfitLossAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setPriorYrOpenCntrctProfitLossAmt(BigInteger value) {
        this.priorYrOpenCntrctProfitLossAmt = value;
    }

    /**
     * Gets the value of the cyOpenCntrctProfitLossAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCYOpenCntrctProfitLossAmt() {
        return cyOpenCntrctProfitLossAmt;
    }

    /**
     * Sets the value of the cyOpenCntrctProfitLossAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCYOpenCntrctProfitLossAmt(BigInteger value) {
        this.cyOpenCntrctProfitLossAmt = value;
    }

    /**
     * Gets the value of the cntrctAggregateProfitLossAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCntrctAggregateProfitLossAmt() {
        return cntrctAggregateProfitLossAmt;
    }

    /**
     * Sets the value of the cntrctAggregateProfitLossAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCntrctAggregateProfitLossAmt(BigInteger value) {
        this.cntrctAggregateProfitLossAmt = value;
    }

    /**
     * Gets the value of the barterAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getBarterAmt() {
        return barterAmt;
    }

    /**
     * Sets the value of the barterAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setBarterAmt(BigInteger value) {
        this.barterAmt = value;
    }

}
