
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;


/**
 * <p>Java class for TransmissionTypeCdType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <pre>
 * &lt;simpleType name="TransmissionTypeCdType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="O"/&gt;
 *     &lt;enumeration value="C"/&gt;
 *     &lt;enumeration value="R"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "TransmissionTypeCdType", namespace = "urn:us:gov:treasury:irs:ir")
@XmlEnum
public enum TransmissionTypeCdType {

    O,
    C,
    R;

    public String value() {
        return name();
    }

    public static TransmissionTypeCdType fromValue(String v) {
        return valueOf(v);
    }

}
