
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Form 8809 Detail Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2021-10-21&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;The content model for the form 8809 .&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for Form8809DetailType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Form8809DetailType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="IssuerDetail" type="{urn:us:gov:treasury:irs:ir}IssuerDetailType"/&gt;
 *         &lt;element name="ContactPersonInformationGrp" type="{urn:us:gov:treasury:irs:ir}ContactPersonInformationGrpType" minOccurs="0"/&gt;
 *         &lt;element name="IRElectronicSubmissionInd" type="{urn:us:gov:treasury:irs:ir}DigitBooleanType"/&gt;
 *         &lt;element name="IRPaperSubmissionInd" type="{urn:us:gov:treasury:irs:ir}DigitBooleanType"/&gt;
 *         &lt;element name="ApplicableFormTypeCd" maxOccurs="12"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{urn:us:gov:treasury:irs:ir}StringType"&gt;
 *               &lt;enumeration value="1099"/&gt;
 *               &lt;enumeration value="1099QA"/&gt;
 *               &lt;enumeration value="5498"/&gt;
 *               &lt;enumeration value="5498ESA"/&gt;
 *               &lt;enumeration value="5498SA"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="JuratSignatureGrp" type="{urn:us:gov:treasury:irs:ir}JuratSignatureGrpType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Form8809DetailType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "issuerDetail",
    "contactPersonInformationGrp",
    "irElectronicSubmissionInd",
    "irPaperSubmissionInd",
    "applicableFormTypeCd",
    "juratSignatureGrp"
})
public class Form8809DetailType {

    @XmlElement(name = "IssuerDetail", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected IssuerDetailType issuerDetail;
    @XmlElement(name = "ContactPersonInformationGrp", namespace = "urn:us:gov:treasury:irs:ir")
    protected ContactPersonInformationGrpType contactPersonInformationGrp;
    @XmlElement(name = "IRElectronicSubmissionInd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String irElectronicSubmissionInd;
    @XmlElement(name = "IRPaperSubmissionInd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String irPaperSubmissionInd;
    @XmlElement(name = "ApplicableFormTypeCd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected List<String> applicableFormTypeCd;
    @XmlElement(name = "JuratSignatureGrp", namespace = "urn:us:gov:treasury:irs:ir")
    protected JuratSignatureGrpType juratSignatureGrp;

    /**
     * Gets the value of the issuerDetail property.
     * 
     * @return
     *     possible object is
     *     {@link IssuerDetailType }
     *     
     */
    public IssuerDetailType getIssuerDetail() {
        return issuerDetail;
    }

    /**
     * Sets the value of the issuerDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link IssuerDetailType }
     *     
     */
    public void setIssuerDetail(IssuerDetailType value) {
        this.issuerDetail = value;
    }

    /**
     * Gets the value of the contactPersonInformationGrp property.
     * 
     * @return
     *     possible object is
     *     {@link ContactPersonInformationGrpType }
     *     
     */
    public ContactPersonInformationGrpType getContactPersonInformationGrp() {
        return contactPersonInformationGrp;
    }

    /**
     * Sets the value of the contactPersonInformationGrp property.
     * 
     * @param value
     *     allowed object is
     *     {@link ContactPersonInformationGrpType }
     *     
     */
    public void setContactPersonInformationGrp(ContactPersonInformationGrpType value) {
        this.contactPersonInformationGrp = value;
    }

    /**
     * Gets the value of the irElectronicSubmissionInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIRElectronicSubmissionInd() {
        return irElectronicSubmissionInd;
    }

    /**
     * Sets the value of the irElectronicSubmissionInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIRElectronicSubmissionInd(String value) {
        this.irElectronicSubmissionInd = value;
    }

    /**
     * Gets the value of the irPaperSubmissionInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIRPaperSubmissionInd() {
        return irPaperSubmissionInd;
    }

    /**
     * Sets the value of the irPaperSubmissionInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIRPaperSubmissionInd(String value) {
        this.irPaperSubmissionInd = value;
    }

    /**
     * Gets the value of the applicableFormTypeCd property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the Jakarta XML Binding object.
     * This is why there is not a <CODE>set</CODE> method for the applicableFormTypeCd property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getApplicableFormTypeCd().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getApplicableFormTypeCd() {
        if (applicableFormTypeCd == null) {
            applicableFormTypeCd = new ArrayList<String>();
        }
        return this.applicableFormTypeCd;
    }

    /**
     * Gets the value of the juratSignatureGrp property.
     * 
     * @return
     *     possible object is
     *     {@link JuratSignatureGrpType }
     *     
     */
    public JuratSignatureGrpType getJuratSignatureGrp() {
        return juratSignatureGrp;
    }

    /**
     * Sets the value of the juratSignatureGrp property.
     * 
     * @param value
     *     allowed object is
     *     {@link JuratSignatureGrpType }
     *     
     */
    public void setJuratSignatureGrp(JuratSignatureGrpType value) {
        this.juratSignatureGrp = value;
    }

}
