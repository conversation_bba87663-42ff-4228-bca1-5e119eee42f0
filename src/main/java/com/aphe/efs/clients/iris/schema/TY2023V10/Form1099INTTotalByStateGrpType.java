
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;
import java.math.BigInteger;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Form 1099 INT Total by State Group Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2021-10-21&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;The content model for the form 1099 INT totals by state&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for Form1099INTTotalByStateGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Form1099INTTotalByStateGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="StateAbbreviationCd" type="{urn:us:gov:treasury:irs:ir}StateType"/&gt;
 *         &lt;element name="TotalReportedRcpntFormCnt" type="{urn:us:gov:treasury:irs:ir}TotalNumberNonNegativeType" minOccurs="0"/&gt;
 *         &lt;element name="StateTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="LocalTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="InterestIncomeAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="EarlyWithdrawalPenaltyAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="USSavingsBondsTreasObligIntAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="FederalIncomeTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="InvestmentExpenseAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="ForeignTaxesPaidAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="TaxExemptInterestAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="SpcfdPrvtActyBondInterestAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="MarketDiscountAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="BondPremiumAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="TreasuryObligBondPremiumAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="TaxExemptBondPremiumAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Form1099INTTotalByStateGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "stateAbbreviationCd",
    "totalReportedRcpntFormCnt",
    "stateTaxWithheldAmt",
    "localTaxWithheldAmt",
    "interestIncomeAmt",
    "earlyWithdrawalPenaltyAmt",
    "usSavingsBondsTreasObligIntAmt",
    "federalIncomeTaxWithheldAmt",
    "investmentExpenseAmt",
    "foreignTaxesPaidAmt",
    "taxExemptInterestAmt",
    "spcfdPrvtActyBondInterestAmt",
    "marketDiscountAmt",
    "bondPremiumAmt",
    "treasuryObligBondPremiumAmt",
    "taxExemptBondPremiumAmt"
})
public class Form1099INTTotalByStateGrpType {

    @XmlElement(name = "StateAbbreviationCd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    @XmlSchemaType(name = "string")
    protected StateType stateAbbreviationCd;
    @XmlElement(name = "TotalReportedRcpntFormCnt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger totalReportedRcpntFormCnt;
    @XmlElement(name = "StateTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger stateTaxWithheldAmt;
    @XmlElement(name = "LocalTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger localTaxWithheldAmt;
    @XmlElement(name = "InterestIncomeAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger interestIncomeAmt;
    @XmlElement(name = "EarlyWithdrawalPenaltyAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger earlyWithdrawalPenaltyAmt;
    @XmlElement(name = "USSavingsBondsTreasObligIntAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger usSavingsBondsTreasObligIntAmt;
    @XmlElement(name = "FederalIncomeTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger federalIncomeTaxWithheldAmt;
    @XmlElement(name = "InvestmentExpenseAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger investmentExpenseAmt;
    @XmlElement(name = "ForeignTaxesPaidAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger foreignTaxesPaidAmt;
    @XmlElement(name = "TaxExemptInterestAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger taxExemptInterestAmt;
    @XmlElement(name = "SpcfdPrvtActyBondInterestAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger spcfdPrvtActyBondInterestAmt;
    @XmlElement(name = "MarketDiscountAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger marketDiscountAmt;
    @XmlElement(name = "BondPremiumAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger bondPremiumAmt;
    @XmlElement(name = "TreasuryObligBondPremiumAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger treasuryObligBondPremiumAmt;
    @XmlElement(name = "TaxExemptBondPremiumAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger taxExemptBondPremiumAmt;

    /**
     * Gets the value of the stateAbbreviationCd property.
     * 
     * @return
     *     possible object is
     *     {@link StateType }
     *     
     */
    public StateType getStateAbbreviationCd() {
        return stateAbbreviationCd;
    }

    /**
     * Sets the value of the stateAbbreviationCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link StateType }
     *     
     */
    public void setStateAbbreviationCd(StateType value) {
        this.stateAbbreviationCd = value;
    }

    /**
     * Gets the value of the totalReportedRcpntFormCnt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTotalReportedRcpntFormCnt() {
        return totalReportedRcpntFormCnt;
    }

    /**
     * Sets the value of the totalReportedRcpntFormCnt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTotalReportedRcpntFormCnt(BigInteger value) {
        this.totalReportedRcpntFormCnt = value;
    }

    /**
     * Gets the value of the stateTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getStateTaxWithheldAmt() {
        return stateTaxWithheldAmt;
    }

    /**
     * Sets the value of the stateTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setStateTaxWithheldAmt(BigInteger value) {
        this.stateTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the localTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getLocalTaxWithheldAmt() {
        return localTaxWithheldAmt;
    }

    /**
     * Sets the value of the localTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setLocalTaxWithheldAmt(BigInteger value) {
        this.localTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the interestIncomeAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getInterestIncomeAmt() {
        return interestIncomeAmt;
    }

    /**
     * Sets the value of the interestIncomeAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setInterestIncomeAmt(BigInteger value) {
        this.interestIncomeAmt = value;
    }

    /**
     * Gets the value of the earlyWithdrawalPenaltyAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getEarlyWithdrawalPenaltyAmt() {
        return earlyWithdrawalPenaltyAmt;
    }

    /**
     * Sets the value of the earlyWithdrawalPenaltyAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setEarlyWithdrawalPenaltyAmt(BigInteger value) {
        this.earlyWithdrawalPenaltyAmt = value;
    }

    /**
     * Gets the value of the usSavingsBondsTreasObligIntAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getUSSavingsBondsTreasObligIntAmt() {
        return usSavingsBondsTreasObligIntAmt;
    }

    /**
     * Sets the value of the usSavingsBondsTreasObligIntAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setUSSavingsBondsTreasObligIntAmt(BigInteger value) {
        this.usSavingsBondsTreasObligIntAmt = value;
    }

    /**
     * Gets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getFederalIncomeTaxWithheldAmt() {
        return federalIncomeTaxWithheldAmt;
    }

    /**
     * Sets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setFederalIncomeTaxWithheldAmt(BigInteger value) {
        this.federalIncomeTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the investmentExpenseAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getInvestmentExpenseAmt() {
        return investmentExpenseAmt;
    }

    /**
     * Sets the value of the investmentExpenseAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setInvestmentExpenseAmt(BigInteger value) {
        this.investmentExpenseAmt = value;
    }

    /**
     * Gets the value of the foreignTaxesPaidAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getForeignTaxesPaidAmt() {
        return foreignTaxesPaidAmt;
    }

    /**
     * Sets the value of the foreignTaxesPaidAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setForeignTaxesPaidAmt(BigInteger value) {
        this.foreignTaxesPaidAmt = value;
    }

    /**
     * Gets the value of the taxExemptInterestAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTaxExemptInterestAmt() {
        return taxExemptInterestAmt;
    }

    /**
     * Sets the value of the taxExemptInterestAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTaxExemptInterestAmt(BigInteger value) {
        this.taxExemptInterestAmt = value;
    }

    /**
     * Gets the value of the spcfdPrvtActyBondInterestAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getSpcfdPrvtActyBondInterestAmt() {
        return spcfdPrvtActyBondInterestAmt;
    }

    /**
     * Sets the value of the spcfdPrvtActyBondInterestAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setSpcfdPrvtActyBondInterestAmt(BigInteger value) {
        this.spcfdPrvtActyBondInterestAmt = value;
    }

    /**
     * Gets the value of the marketDiscountAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getMarketDiscountAmt() {
        return marketDiscountAmt;
    }

    /**
     * Sets the value of the marketDiscountAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setMarketDiscountAmt(BigInteger value) {
        this.marketDiscountAmt = value;
    }

    /**
     * Gets the value of the bondPremiumAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getBondPremiumAmt() {
        return bondPremiumAmt;
    }

    /**
     * Sets the value of the bondPremiumAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setBondPremiumAmt(BigInteger value) {
        this.bondPremiumAmt = value;
    }

    /**
     * Gets the value of the treasuryObligBondPremiumAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTreasuryObligBondPremiumAmt() {
        return treasuryObligBondPremiumAmt;
    }

    /**
     * Sets the value of the treasuryObligBondPremiumAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTreasuryObligBondPremiumAmt(BigInteger value) {
        this.treasuryObligBondPremiumAmt = value;
    }

    /**
     * Gets the value of the taxExemptBondPremiumAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTaxExemptBondPremiumAmt() {
        return taxExemptBondPremiumAmt;
    }

    /**
     * Sets the value of the taxExemptBondPremiumAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTaxExemptBondPremiumAmt(BigInteger value) {
        this.taxExemptBondPremiumAmt = value;
    }

}
