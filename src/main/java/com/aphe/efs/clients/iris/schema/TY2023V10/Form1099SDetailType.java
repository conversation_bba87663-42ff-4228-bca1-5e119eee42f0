
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigInteger;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Form 1099 S Detail Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2023-01-10&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;The content model for the form 1099 S.&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for Form1099SDetailType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Form1099SDetailType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="TaxYr" type="{urn:us:gov:treasury:irs:ir}YearType"/&gt;
 *         &lt;element name="RecordId" type="{urn:us:gov:treasury:irs:ir}RecordIdType"/&gt;
 *         &lt;element name="IssuerOfficeCd" type="{urn:us:gov:treasury:irs:ir}Code4Type" minOccurs="0"/&gt;
 *         &lt;element name="VoidInd" type="{urn:us:gov:treasury:irs:ir}DigitBooleanType"/&gt;
 *         &lt;element name="CorrectedInd" type="{urn:us:gov:treasury:irs:ir}DigitBooleanType"/&gt;
 *         &lt;element name="PrevSubmittedRecRecipientGrp" type="{urn:us:gov:treasury:irs:ir}PrevSubmittedRecRecipientGrpType" minOccurs="0"/&gt;
 *         &lt;element name="RecipientDetail" type="{urn:us:gov:treasury:irs:ir}RecipientDetailType"/&gt;
 *         &lt;element name="RecipientAccountNum" type="{urn:us:gov:treasury:irs:ir}Text30Type" minOccurs="0"/&gt;
 *         &lt;element name="ClosingDt" type="{urn:us:gov:treasury:irs:ir}DateNoTimeZoneType"/&gt;
 *         &lt;element name="GrossProceedsAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="AddressOrLegalDesc" type="{urn:us:gov:treasury:irs:ir}LineExplanationType"/&gt;
 *         &lt;element name="TransferorRcvdConsiderationInd" type="{urn:us:gov:treasury:irs:ir}DigitBooleanType"/&gt;
 *         &lt;element name="TransferorForeignPersonInd" type="{urn:us:gov:treasury:irs:ir}DigitBooleanType"/&gt;
 *         &lt;element name="BuyerRealEstateTaxAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Form1099SDetailType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "taxYr",
    "recordId",
    "issuerOfficeCd",
    "voidInd",
    "correctedInd",
    "prevSubmittedRecRecipientGrp",
    "recipientDetail",
    "recipientAccountNum",
    "closingDt",
    "grossProceedsAmt",
    "addressOrLegalDesc",
    "transferorRcvdConsiderationInd",
    "transferorForeignPersonInd",
    "buyerRealEstateTaxAmt"
})
public class Form1099SDetailType {

    @XmlElement(name = "TaxYr", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String taxYr;
    @XmlElement(name = "RecordId", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String recordId;
    @XmlElement(name = "IssuerOfficeCd", namespace = "urn:us:gov:treasury:irs:ir")
    protected String issuerOfficeCd;
    @XmlElement(name = "VoidInd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String voidInd;
    @XmlElement(name = "CorrectedInd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String correctedInd;
    @XmlElement(name = "PrevSubmittedRecRecipientGrp", namespace = "urn:us:gov:treasury:irs:ir")
    protected PrevSubmittedRecRecipientGrpType prevSubmittedRecRecipientGrp;
    @XmlElement(name = "RecipientDetail", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected RecipientDetailType recipientDetail;
    @XmlElement(name = "RecipientAccountNum", namespace = "urn:us:gov:treasury:irs:ir")
    protected String recipientAccountNum;
    @XmlElement(name = "ClosingDt", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar closingDt;
    @XmlElement(name = "GrossProceedsAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger grossProceedsAmt;
    @XmlElement(name = "AddressOrLegalDesc", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String addressOrLegalDesc;
    @XmlElement(name = "TransferorRcvdConsiderationInd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String transferorRcvdConsiderationInd;
    @XmlElement(name = "TransferorForeignPersonInd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    protected String transferorForeignPersonInd;
    @XmlElement(name = "BuyerRealEstateTaxAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger buyerRealEstateTaxAmt;

    /**
     * Gets the value of the taxYr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaxYr() {
        return taxYr;
    }

    /**
     * Sets the value of the taxYr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaxYr(String value) {
        this.taxYr = value;
    }

    /**
     * Gets the value of the recordId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRecordId() {
        return recordId;
    }

    /**
     * Sets the value of the recordId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRecordId(String value) {
        this.recordId = value;
    }

    /**
     * Gets the value of the issuerOfficeCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIssuerOfficeCd() {
        return issuerOfficeCd;
    }

    /**
     * Sets the value of the issuerOfficeCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIssuerOfficeCd(String value) {
        this.issuerOfficeCd = value;
    }

    /**
     * Gets the value of the voidInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVoidInd() {
        return voidInd;
    }

    /**
     * Sets the value of the voidInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVoidInd(String value) {
        this.voidInd = value;
    }

    /**
     * Gets the value of the correctedInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorrectedInd() {
        return correctedInd;
    }

    /**
     * Sets the value of the correctedInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorrectedInd(String value) {
        this.correctedInd = value;
    }

    /**
     * Gets the value of the prevSubmittedRecRecipientGrp property.
     * 
     * @return
     *     possible object is
     *     {@link PrevSubmittedRecRecipientGrpType }
     *     
     */
    public PrevSubmittedRecRecipientGrpType getPrevSubmittedRecRecipientGrp() {
        return prevSubmittedRecRecipientGrp;
    }

    /**
     * Sets the value of the prevSubmittedRecRecipientGrp property.
     * 
     * @param value
     *     allowed object is
     *     {@link PrevSubmittedRecRecipientGrpType }
     *     
     */
    public void setPrevSubmittedRecRecipientGrp(PrevSubmittedRecRecipientGrpType value) {
        this.prevSubmittedRecRecipientGrp = value;
    }

    /**
     * Gets the value of the recipientDetail property.
     * 
     * @return
     *     possible object is
     *     {@link RecipientDetailType }
     *     
     */
    public RecipientDetailType getRecipientDetail() {
        return recipientDetail;
    }

    /**
     * Sets the value of the recipientDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link RecipientDetailType }
     *     
     */
    public void setRecipientDetail(RecipientDetailType value) {
        this.recipientDetail = value;
    }

    /**
     * Gets the value of the recipientAccountNum property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRecipientAccountNum() {
        return recipientAccountNum;
    }

    /**
     * Sets the value of the recipientAccountNum property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRecipientAccountNum(String value) {
        this.recipientAccountNum = value;
    }

    /**
     * Gets the value of the closingDt property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getClosingDt() {
        return closingDt;
    }

    /**
     * Sets the value of the closingDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setClosingDt(XMLGregorianCalendar value) {
        this.closingDt = value;
    }

    /**
     * Gets the value of the grossProceedsAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getGrossProceedsAmt() {
        return grossProceedsAmt;
    }

    /**
     * Sets the value of the grossProceedsAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setGrossProceedsAmt(BigInteger value) {
        this.grossProceedsAmt = value;
    }

    /**
     * Gets the value of the addressOrLegalDesc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddressOrLegalDesc() {
        return addressOrLegalDesc;
    }

    /**
     * Sets the value of the addressOrLegalDesc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddressOrLegalDesc(String value) {
        this.addressOrLegalDesc = value;
    }

    /**
     * Gets the value of the transferorRcvdConsiderationInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTransferorRcvdConsiderationInd() {
        return transferorRcvdConsiderationInd;
    }

    /**
     * Sets the value of the transferorRcvdConsiderationInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTransferorRcvdConsiderationInd(String value) {
        this.transferorRcvdConsiderationInd = value;
    }

    /**
     * Gets the value of the transferorForeignPersonInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTransferorForeignPersonInd() {
        return transferorForeignPersonInd;
    }

    /**
     * Sets the value of the transferorForeignPersonInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTransferorForeignPersonInd(String value) {
        this.transferorForeignPersonInd = value;
    }

    /**
     * Gets the value of the buyerRealEstateTaxAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getBuyerRealEstateTaxAmt() {
        return buyerRealEstateTaxAmt;
    }

    /**
     * Sets the value of the buyerRealEstateTaxAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setBuyerRealEstateTaxAmt(BigInteger value) {
        this.buyerRealEstateTaxAmt = value;
    }

}
