
package com.aphe.efs.clients.iris.schema.TY2023V10;

import javax.xml.bind.annotation.*;
import java.math.BigInteger;


/**
 * 
 * 				
 * <pre>
 * &lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;Component xmlns="urn:us:gov:treasury:irs:ir" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;
 * 					&lt;DictionaryEntryNm&gt;Form 1099 MISC Total by State Group Type&lt;/DictionaryEntryNm&gt;
 * 					&lt;MajorVersionNum&gt;1&lt;/MajorVersionNum&gt;
 * 					&lt;MinorVersionNum&gt;0&lt;/MinorVersionNum&gt;
 * 					&lt;VersionEffectiveBeginDt&gt;2021-08-26&lt;/VersionEffectiveBeginDt&gt;
 * 					&lt;VersionDescriptionTxt&gt;Initial version&lt;/VersionDescriptionTxt&gt;
 * 					&lt;DescriptionTxt&gt;The content model for the form 1099 misc total by state&lt;/DescriptionTxt&gt;
 * 				&lt;/Component&gt;
 * </pre>
 * 
 * 			
 * 
 * <p>Java class for Form1099MISCTotalByStateGrpType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Form1099MISCTotalByStateGrpType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="StateAbbreviationCd" type="{urn:us:gov:treasury:irs:ir}StateType"/&gt;
 *         &lt;element name="TotalReportedRcpntFormCnt" type="{urn:us:gov:treasury:irs:ir}TotalNumberNonNegativeType" minOccurs="0"/&gt;
 *         &lt;element name="FederalIncomeTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="StateTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="LocalTaxWithheldAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="RentAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="RoyaltyAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="OtherIncomeAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="FishingBoatProceedsAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="MedicalHealthCarePaymentsAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="SubstitutePaymentsAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="CropInsuranceProceedsAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="AttorneyGrossProceedsPaidAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="FishPurchasedForResaleAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="Section409ADeferralsAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="ExcessParachutePaymentAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *         &lt;element name="NonqlfyDeferredCompensationAmt" type="{urn:us:gov:treasury:irs:ir}USAmountNNType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Form1099MISCTotalByStateGrpType", namespace = "urn:us:gov:treasury:irs:ir", propOrder = {
    "stateAbbreviationCd",
    "totalReportedRcpntFormCnt",
    "federalIncomeTaxWithheldAmt",
    "stateTaxWithheldAmt",
    "localTaxWithheldAmt",
    "rentAmt",
    "royaltyAmt",
    "otherIncomeAmt",
    "fishingBoatProceedsAmt",
    "medicalHealthCarePaymentsAmt",
    "substitutePaymentsAmt",
    "cropInsuranceProceedsAmt",
    "attorneyGrossProceedsPaidAmt",
    "fishPurchasedForResaleAmt",
    "section409ADeferralsAmt",
    "excessParachutePaymentAmt",
    "nonqlfyDeferredCompensationAmt"
})
public class Form1099MISCTotalByStateGrpType {

    @XmlElement(name = "StateAbbreviationCd", namespace = "urn:us:gov:treasury:irs:ir", required = true)
    @XmlSchemaType(name = "string")
    protected StateType stateAbbreviationCd;
    @XmlElement(name = "TotalReportedRcpntFormCnt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger totalReportedRcpntFormCnt;
    @XmlElement(name = "FederalIncomeTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger federalIncomeTaxWithheldAmt;
    @XmlElement(name = "StateTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger stateTaxWithheldAmt;
    @XmlElement(name = "LocalTaxWithheldAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger localTaxWithheldAmt;
    @XmlElement(name = "RentAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger rentAmt;
    @XmlElement(name = "RoyaltyAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger royaltyAmt;
    @XmlElement(name = "OtherIncomeAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger otherIncomeAmt;
    @XmlElement(name = "FishingBoatProceedsAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger fishingBoatProceedsAmt;
    @XmlElement(name = "MedicalHealthCarePaymentsAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger medicalHealthCarePaymentsAmt;
    @XmlElement(name = "SubstitutePaymentsAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger substitutePaymentsAmt;
    @XmlElement(name = "CropInsuranceProceedsAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger cropInsuranceProceedsAmt;
    @XmlElement(name = "AttorneyGrossProceedsPaidAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger attorneyGrossProceedsPaidAmt;
    @XmlElement(name = "FishPurchasedForResaleAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger fishPurchasedForResaleAmt;
    @XmlElement(name = "Section409ADeferralsAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger section409ADeferralsAmt;
    @XmlElement(name = "ExcessParachutePaymentAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger excessParachutePaymentAmt;
    @XmlElement(name = "NonqlfyDeferredCompensationAmt", namespace = "urn:us:gov:treasury:irs:ir")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected BigInteger nonqlfyDeferredCompensationAmt;

    /**
     * Gets the value of the stateAbbreviationCd property.
     * 
     * @return
     *     possible object is
     *     {@link StateType }
     *     
     */
    public StateType getStateAbbreviationCd() {
        return stateAbbreviationCd;
    }

    /**
     * Sets the value of the stateAbbreviationCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link StateType }
     *     
     */
    public void setStateAbbreviationCd(StateType value) {
        this.stateAbbreviationCd = value;
    }

    /**
     * Gets the value of the totalReportedRcpntFormCnt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTotalReportedRcpntFormCnt() {
        return totalReportedRcpntFormCnt;
    }

    /**
     * Sets the value of the totalReportedRcpntFormCnt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTotalReportedRcpntFormCnt(BigInteger value) {
        this.totalReportedRcpntFormCnt = value;
    }

    /**
     * Gets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getFederalIncomeTaxWithheldAmt() {
        return federalIncomeTaxWithheldAmt;
    }

    /**
     * Sets the value of the federalIncomeTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setFederalIncomeTaxWithheldAmt(BigInteger value) {
        this.federalIncomeTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the stateTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getStateTaxWithheldAmt() {
        return stateTaxWithheldAmt;
    }

    /**
     * Sets the value of the stateTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setStateTaxWithheldAmt(BigInteger value) {
        this.stateTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the localTaxWithheldAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getLocalTaxWithheldAmt() {
        return localTaxWithheldAmt;
    }

    /**
     * Sets the value of the localTaxWithheldAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setLocalTaxWithheldAmt(BigInteger value) {
        this.localTaxWithheldAmt = value;
    }

    /**
     * Gets the value of the rentAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getRentAmt() {
        return rentAmt;
    }

    /**
     * Sets the value of the rentAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setRentAmt(BigInteger value) {
        this.rentAmt = value;
    }

    /**
     * Gets the value of the royaltyAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getRoyaltyAmt() {
        return royaltyAmt;
    }

    /**
     * Sets the value of the royaltyAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setRoyaltyAmt(BigInteger value) {
        this.royaltyAmt = value;
    }

    /**
     * Gets the value of the otherIncomeAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getOtherIncomeAmt() {
        return otherIncomeAmt;
    }

    /**
     * Sets the value of the otherIncomeAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setOtherIncomeAmt(BigInteger value) {
        this.otherIncomeAmt = value;
    }

    /**
     * Gets the value of the fishingBoatProceedsAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getFishingBoatProceedsAmt() {
        return fishingBoatProceedsAmt;
    }

    /**
     * Sets the value of the fishingBoatProceedsAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setFishingBoatProceedsAmt(BigInteger value) {
        this.fishingBoatProceedsAmt = value;
    }

    /**
     * Gets the value of the medicalHealthCarePaymentsAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getMedicalHealthCarePaymentsAmt() {
        return medicalHealthCarePaymentsAmt;
    }

    /**
     * Sets the value of the medicalHealthCarePaymentsAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setMedicalHealthCarePaymentsAmt(BigInteger value) {
        this.medicalHealthCarePaymentsAmt = value;
    }

    /**
     * Gets the value of the substitutePaymentsAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getSubstitutePaymentsAmt() {
        return substitutePaymentsAmt;
    }

    /**
     * Sets the value of the substitutePaymentsAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setSubstitutePaymentsAmt(BigInteger value) {
        this.substitutePaymentsAmt = value;
    }

    /**
     * Gets the value of the cropInsuranceProceedsAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCropInsuranceProceedsAmt() {
        return cropInsuranceProceedsAmt;
    }

    /**
     * Sets the value of the cropInsuranceProceedsAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCropInsuranceProceedsAmt(BigInteger value) {
        this.cropInsuranceProceedsAmt = value;
    }

    /**
     * Gets the value of the attorneyGrossProceedsPaidAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getAttorneyGrossProceedsPaidAmt() {
        return attorneyGrossProceedsPaidAmt;
    }

    /**
     * Sets the value of the attorneyGrossProceedsPaidAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setAttorneyGrossProceedsPaidAmt(BigInteger value) {
        this.attorneyGrossProceedsPaidAmt = value;
    }

    /**
     * Gets the value of the fishPurchasedForResaleAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getFishPurchasedForResaleAmt() {
        return fishPurchasedForResaleAmt;
    }

    /**
     * Sets the value of the fishPurchasedForResaleAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setFishPurchasedForResaleAmt(BigInteger value) {
        this.fishPurchasedForResaleAmt = value;
    }

    /**
     * Gets the value of the section409ADeferralsAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getSection409ADeferralsAmt() {
        return section409ADeferralsAmt;
    }

    /**
     * Sets the value of the section409ADeferralsAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setSection409ADeferralsAmt(BigInteger value) {
        this.section409ADeferralsAmt = value;
    }

    /**
     * Gets the value of the excessParachutePaymentAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getExcessParachutePaymentAmt() {
        return excessParachutePaymentAmt;
    }

    /**
     * Sets the value of the excessParachutePaymentAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setExcessParachutePaymentAmt(BigInteger value) {
        this.excessParachutePaymentAmt = value;
    }

    /**
     * Gets the value of the nonqlfyDeferredCompensationAmt property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNonqlfyDeferredCompensationAmt() {
        return nonqlfyDeferredCompensationAmt;
    }

    /**
     * Sets the value of the nonqlfyDeferredCompensationAmt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNonqlfyDeferredCompensationAmt(BigInteger value) {
        this.nonqlfyDeferredCompensationAmt = value;
    }

}
