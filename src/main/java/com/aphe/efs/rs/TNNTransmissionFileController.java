package com.aphe.efs.rs;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheNotFoundException;
import com.aphe.common.util.AESEncryptionUtilStatic;
import com.aphe.common.util.ZipUtil;
import com.aphe.efs.model.EFSTNNTransmissionRecord;
import com.aphe.efs.services.transmission.DataGenUtil;
import com.aphe.efs.services.transmission.TNNTransmissionManager;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;

//@Path("/transmissions/tnn")
//@Service
//@Component
////@Api(hidden = true)

@RestController
@RequestMapping(path = "/rs/api/transmissions/tnn/")
@Tag(name = "TNN Transmissions", description = "API to manage TNN transmission records filed with FIRE")
public class TNNTransmissionFileController {

	private static Logger logger = LoggerFactory.getLogger(TNNTransmissionFileController.class);

	@Autowired
	TNNTransmissionManager tnnTransmissionMgr;

	@Value("${aphe.efs.transmissionFileDir}")
	public String transmissionFileDir;

	@Autowired
	DataGenUtil dataGenUtil;

	@Autowired
	ZipUtil zipUtil;

	@GetMapping(path = "/{transmissionRecordId}/file/")
	// @ApiOperation(value = "Get the transmission file associated with this transmission record")
	public void getTransmissionRecordFile(@PathVariable("transmissionRecordId") long transmissionRecordId, HttpServletResponse response) throws Exception {
		try {
			EFSTNNTransmissionRecord entity = tnnTransmissionMgr.getTransmissionRecord(transmissionRecordId);
			if (entity != null) {
				String fileName = entity.getFileName();
				String filePath = transmissionFileDir + File.separator + fileName;
				response.setStatus(HttpServletResponse.SC_OK);
				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				response.setContentType("application/txt");

				InputStream is = null;
				try {
					is = AESEncryptionUtilStatic.getDecryptorStream(filePath);
					byte[] buffer = new byte[4096];
					int len;
					while ((len = is.read(buffer)) > 0) {
						response.getOutputStream().write(buffer, 0, len);
					}
				} finally {
					if (is != null) {
						try {
							is.close();
						} catch (Exception e) {
						}
					}
				}
				response.flushBuffer();

				//Mark it as downloaded.
				tnnTransmissionMgr.markAsDownloaded(transmissionRecordId);
			} else {
				// TODO: pass the right application error code.
				throw new ApheNotFoundException();
			}
		} catch (ApheNotFoundException nfe) {
			throw nfe;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error downloading file of tnn transmission record."));
		}
	}

	@PostMapping(path = "{transmissionRecordId}/response/", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public void storeResponseFile(@PathVariable("transmissionRecordId") long transmissionRecordId, @RequestParam("file") MultipartFile file) throws ApheException {
		try {
			tnnTransmissionMgr.uploadResponseFile(transmissionRecordId, file);
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error uploading the response file."));
		}
	}

}
