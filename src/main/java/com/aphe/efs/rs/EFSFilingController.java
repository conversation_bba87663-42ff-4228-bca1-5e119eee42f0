package com.aphe.efs.rs;

import com.aphe.common.error.exceptions.ApheDataListValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheNotFoundException;
import com.aphe.common.util.ArrayUtil;
import com.aphe.efs.dto.FilingDataDTO;
import com.aphe.efs.dto.FilingDataDTO1099MISC;
import com.aphe.efs.dto.FilingStatusDTO;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.services.filings.FilingsManager;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(path = "/rs/api/{appId}/filings/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "EFS Filings", description = "API to manage filings on the filing service")
public class EFSFilingController {

	private static Logger logger = LoggerFactory.getLogger(EFSFilingController.class);

	@Autowired
	FilingsManager filingsManager;

	@PutMapping
//	@ApiOperation(value = "Add filings to be processed.", notes = "Validates and stores all the filing requests. "
//			+ "Saves all or none. Does not support updating resources. To update delete unprocessed filings and create new ones..", response = TINMatchRequestStatusDTO.class, responseContainer = "List")
	public List<FilingStatusDTO> addFilings(@PathVariable("appId") String appId, List<FilingDataDTO1099MISC> filignDTOs) throws Exception {
		try {
			List<EFSFiling> filings = filingsManager.addFilings(filignDTOs);
			List<FilingStatusDTO> statuses = filingsManager.convertToStatusDTOs(filings);
			return statuses;
		} catch (ApheDataListValidationException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error adding EFS filings"));
		}
	}

	@GetMapping
//	@ApiOperation(value = "Get filings passed in the query param filingIds", notes = "Gets details of all available filings", response = FilingDataDTO1099MISC.class, responseContainer = "List")
	public List<FilingDataDTO> getFilings(@PathVariable("appId") String appId, @RequestParam ("filingIds") String filingIds) throws Exception {
		try {
			logger.debug("called get with query param");
			List<EFSFiling> entities = new ArrayList<EFSFiling>();
			List<Long> ids = ArrayUtil.stringToLongList(filingIds, ",");
			entities = filingsManager.getFilings(ids);
			List<FilingDataDTO> dtos = filingsManager.convertToDTOs(entities);
			return dtos;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error gettign EFS filings"));
		}
	}

	@GetMapping(path = "{filingId}/")
//	@ApiOperation(value = "Get filing", notes = "Gets details of a filing", response = FilingDataDTO1099MISC.class)
	public FilingDataDTO getFiling(@PathVariable("appId") String appId, @PathVariable("filingId") long filingId) throws Exception {

		try {
//			EFSFiling entity = null;
//			entity = filingsManager.getFiling(filingId);
//			if (entity != null) {
//				FilingDataDTO filing1099MiscDTO = filingsManager.convertToDTO(entity);
//				return filing1099MiscDTO;
//			} else {
				//TODO: pass the right application error code.
				throw new ApheNotFoundException();
//			}
		} catch (ApheNotFoundException nfe) {
			throw nfe;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting EFS filing by id"));
		}
	}

	@GetMapping(path = "status/")
//	@ApiOperation(value = "Get filings passed in the query param filingIds", notes = "Gets details of all available filings", response = TINMatchRequestStatusDTO.class, responseContainer = "List")
	public List<FilingStatusDTO> getFilingsStatus(@PathVariable("appId") String appId, @RequestParam("filingIds") String filingIds) throws Exception {
		try {
			logger.debug("called get with query param");
			List<EFSFiling> entities = new ArrayList<EFSFiling>();
			List<Long> ids = ArrayUtil.stringToLongList(filingIds, ",");
			entities = filingsManager.getFilings(ids);
			List<FilingStatusDTO> dtos = filingsManager.convertToStatusDTOs(entities);
			return dtos;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting EFS filings status by filindIds"));
		}
	}

	@GetMapping(path = "status/{filingId}/")
//	@ApiOperation(value = "Get filing", notes = "Gets details of a filing", response = TINMatchRequestStatusDTO.class)
	public FilingStatusDTO getFilingStatus(@PathVariable("appId") String appId, @PathVariable("filingId") long filingId) throws Exception {
		try {
//			EFSFiling entity = filingsManager.getFiling(filingId);
//			if (entity != null) {
//				TINMatchRequestStatusDTO filingDTO = filingsManager.convertToStatusDTO(entity);
//				return filingDTO;
//			} else {
				//TODO: pass the right application error code.
				throw new ApheNotFoundException();
//			}
		} catch (ApheNotFoundException nfe) {
			throw nfe;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting EFS filings by status by filingId "));
		}

	}

	@DeleteMapping
//	@ApiOperation(value = "Delete a filing", notes = "Deletes filings. Fails the delete of all even if one filing can not be deleted.")
	public void deleteFilings(@PathVariable("appId") String appId, @RequestParam("filingIds") String filingIds) throws Exception {
		try {
			List<Long> ids = ArrayUtil.stringToLongList(filingIds, ",");
			filingsManager.cancelFilings(ids, "Cancelled by Bulk Cancel Rest API");
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error deleting filings by filingIds "));
		}

	}

	@DeleteMapping(path = "{filingId}/")
//	@ApiOperation(value = "Delete a filing", notes = "Deletes a filing if not processed.")
	public void deleteFiling(@PathVariable("appId") String appId, @PathVariable("filingId") long filingId) throws Exception {
		try {
			filingsManager.cancelFiling(filingId, "Cancelled by Rest API");
		} catch (ApheNotFoundException nfe) {
			throw nfe;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error deleting a filing by id"));
		}
	}

}
