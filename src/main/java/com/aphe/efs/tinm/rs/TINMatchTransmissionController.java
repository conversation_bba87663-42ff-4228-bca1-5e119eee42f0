package com.aphe.efs.tinm.rs;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheNotFoundException;
import com.aphe.efs.model.enums.TransmissionStatus;
import com.aphe.efs.tinm.dto.TINMatchTransmissionRecordDTO;
import com.aphe.efs.tinm.model.EFSTINMatchTransmissionRecord;
import com.aphe.efs.tinm.services.TINMatchTransmissionManager;
import com.aphe.efs.tinm.client.tinmatch.TINMatchTransmissionOrchestrator;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(path = "/rs/api/transmissions/tin/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "TINMatch Transmissions", description = "API to manage filings on the filing service")
public class TINMatchTransmissionController {

	private static Logger logger = LoggerFactory.getLogger(TINMatchTransmissionController.class);

	@Autowired
    TINMatchTransmissionManager tinMatchTransmissionManager;

	@Autowired
	TINMatchTransmissionOrchestrator tinMatchTransmissionOrchestrator;

	@GetMapping
	// @ApiOperation(value = "Get the filings that ready to processeds")
	public int getTINMatchRequestsData() throws Exception {
		try {
			int requestsToBeProcessed = tinMatchTransmissionManager.getQueued();
			return requestsToBeProcessed;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting tin match requests for transmission"));
		}
	}

	@PutMapping
	// @ApiOperation(value = "Generate transmission records", response = TINMatchTransmissionRecordDTO.class, responseContainer = "List")
	public List<TINMatchTransmissionRecordDTO> generateTransmission() throws Exception {
		try {
			List<EFSTINMatchTransmissionRecord> generatedRecords = tinMatchTransmissionOrchestrator.createTransmissionRecordsAnyKey();
			List<TINMatchTransmissionRecordDTO> dtos = tinMatchTransmissionManager.convertToDTOs(generatedRecords);
			return dtos;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error generating tin match transmission records "));
		}
	}

	@GetMapping(path = "status/{status}/")
	// @ApiOperation(value = "Get the transmission in a given status", response = TINMatchTransmissionRecordDTO.class, responseContainer = "List")
	public List<TINMatchTransmissionRecordDTO> getTransmissionsByStatus(@PathVariable("status") TransmissionStatus transmissionStatus) throws Exception {
		try {
			List<EFSTINMatchTransmissionRecord> entities = new ArrayList<EFSTINMatchTransmissionRecord>();
			entities = tinMatchTransmissionManager.getTINMatchTransmissionRecords(transmissionStatus);
			List<TINMatchTransmissionRecordDTO> dtos = tinMatchTransmissionManager.convertToDTOs(entities);
			return dtos;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting tin match transmission records by status "));
		}
	}

	@GetMapping(path = "{transmissionRecordId}/")
	// @ApiOperation(value = "Get the transmission in a given status", response = TINMatchTransmissionRecordDTO.class)
	public TINMatchTransmissionRecordDTO getTransmissionRecord(@PathVariable("transmissionRecordId") long transmissionRecordId) throws Exception {
		try {
			EFSTINMatchTransmissionRecord tr = tinMatchTransmissionManager.getTINMatchTransmissionRecords(transmissionRecordId);
			if (tr != null) {
				TINMatchTransmissionRecordDTO dto = tinMatchTransmissionManager.convertToDTO(tr);
				return dto;
			} else {
				// TODO: pass the right application error code.
				throw new ApheNotFoundException();
			}
		} catch (ApheNotFoundException nfe) {
			throw nfe;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error geeting tin match transmission record by id "));
		}
	}

	@PostMapping(path = "{transmissionRecordId}/")
	// @ApiOperation(value = "Update transmission record status", notes = "Update status from Generated to SentToAgency")
	public void updateTransmissionRecord(TINMatchTransmissionRecordDTO tr, @PathVariable("transmissionRecordId") long transmissionRecordId) throws Exception {
		try {
			logger.debug("Updating a tnn transmission record");
			EFSTINMatchTransmissionRecord entity = tinMatchTransmissionManager.getTransmissionRecord(transmissionRecordId);
			if (entity != null) {
				// Call service to update the status.
				tinMatchTransmissionManager.updateTransmissionRecordStatus(tr);
			} else {
				// TODO: pass the right application error code.
				throw new ApheNotFoundException();
			}
		} catch (ApheNotFoundException nfe) {
			throw nfe;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error updating the stauts of tin match transmission record "));
		}
	}

	@PostMapping(path = "{transmissionRecordId}/response/", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	// @ApiOperation(value = "Upload the response file to process the response and change the stauts to response received", notes = "Update status from Generated to SentToAgency")
	public void processResponse(@PathVariable("transmissionRecordId") long transmissionRecordId, @RequestParam MultipartFile file) throws Exception {
		try {
			InputStream inputStream = file.getInputStream();
			tinMatchTransmissionManager.processResponseFile(transmissionRecordId, inputStream, false);
		} catch (ApheNotFoundException nfe) {
			throw nfe;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error uploading response file for tin match transmission record "));
		}
	}

	@DeleteMapping(path = "{transmissionRecordId}/")
	// @ApiOperation(value = "Delete a tin match transmission", notes = "Deletes a transmission record. A tranmission that is accepted or rejected can not be deleted")
	public void deleteTransmissionRecord(@PathVariable("transmissionRecordId") long transmissionRecordId) throws Exception {
		try {
			tinMatchTransmissionManager.deleteTransmissionRecord(transmissionRecordId);
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error deleting tnn transmission record by id "));
		}
	}
}
