package com.aphe.efs.tinm.model;

import com.aphe.common.model.BaseEntity;
import com.aphe.common.util.EncryptionUtil;
import com.aphe.common.util.StringUtil;
import com.aphe.efs.tinm.model.enums.TINMatchRequestStatus;
import com.aphe.efs.tinm.model.enums.TINMatchTINType;
import org.springframework.security.access.prepost.PreAuthorize;

import jakarta.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "efs_tinmatchrequests")
@EntityListeners(EFSTINMatchRequestEncryptionListener.class)
public class EFSTINMatchRequest extends BaseEntity {
	
	private String appId;

	private String clientRefId;

	private String clientDomainId;
	
	@Enumerated(EnumType.STRING)
	private TINMatchTINType entityType;
	
	private String tin;
	
	private String tinEncrypted;

	private String tinEncrypted1;
	
	private String entityName;

	@Enumerated(EnumType.STRING)
	private TINMatchRequestStatus status;

	private String accountNumber;

	@Temporal(TemporalType.TIMESTAMP)
	private Date statusChangeDate;

	private String statusChangeDesc;

	private int tinMatchResult;

	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@JoinColumn(name = "REQUEST_ID")
	private List<EFSTINMatchRequestStatusEntity> requestStatusHistory;

	@Transient
	private String tinPlain;


	/**
	 * 
	 * "0" – indicates the name/TIN combination matches IRS records.
	 * "1" - indicates TIN was missing or TIN is not a 9 digit number.
	 * "2" – indicates TIN entered is not currently issued.
	 * "3" – indicates the name/TIN combination do not match IRS records.
	 * "4" - indicates an invalid TIN Matching request.
	 * "5" – indicates a duplicate TIN Matching request.
	 * "6" - (matched on SSN), when the TIN type is (3), unknown, and a Matching TIN and name control is found only on the NAP DM1 database.
	 * "7" - (matched on EIN), when the TIN type is (3), unknown, and a matching TIN and name control is found only on the EIN/NC database.
	 * "8" - (matched on EIN and SSN), when the TIN type is (3), unknown, and matching TIN and name control is found only on both the EIN/NC and NAP DM1 databases.
	 * 
	 */
	
	@PreAuthorize("hasAuthority('superadmin')")
	public String getTINPlain(){
		if(tinPlain == null && StringUtil.isNotEmpty(tinEncrypted)){
			tinPlain = new EncryptionUtil().decrypt(tinEncrypted);
			return tinPlain;
		}
		return tinPlain;
	}

	@PreAuthorize("hasAuthority('superadmin')")
	public void setTINPlain(String tinPlain){
		this.tinPlain = tinPlain;
	}




	public String getClientRefId() {
		return clientRefId;
	}

	public void setClientRefId(String clientRefId) {
		this.clientRefId = clientRefId;
	}

	public TINMatchRequestStatus getStatus() {
		return status;
	}

	public void setStatus(TINMatchRequestStatus status) {
		this.status = status;
	}

	public TINMatchTINType getEntityType() {
		return entityType;
	}

	public void setEntityType(TINMatchTINType entityType) {
		this.entityType = entityType;
	}

	public String getTin() {
		return tin;
	}

	public void setTin(String tin) {
		this.tin = tin;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public Date getStatusChangeDate() {
		return statusChangeDate;
	}

	public void setStatusChangeDate(Date statusChangeDate) {
		this.statusChangeDate = statusChangeDate;
	}

	public int getTinMatchResult() {
		return tinMatchResult;
	}

	public void setTinMatchResult(int tinMatchResult) {
		this.tinMatchResult = tinMatchResult;
	}

	public String getTinEncrypted() {
		return tinEncrypted;
	}

	public void setTinEncrypted(String tinEncrypted) {
		this.tinEncrypted = tinEncrypted;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getClientDomainId() {
		return clientDomainId;
	}

	public void setClientDomainId(String clientDomainId) {
		this.clientDomainId = clientDomainId;
	}

	public List<EFSTINMatchRequestStatusEntity> getRequestStatusHistory() {
		return requestStatusHistory;
	}

	public void setRequestStatusHistory(List<EFSTINMatchRequestStatusEntity> requestStatusHistory) {
		this.requestStatusHistory = requestStatusHistory;
	}

	public String getStatusChangeDesc() {
		return statusChangeDesc;
	}

	public void setStatusChangeDesc(String statusChangeDesc) {
		this.statusChangeDesc = statusChangeDesc;
	}

	public String getTinEncrypted1() {
		return tinEncrypted1;
	}

	public void setTinEncrypted1(String tinEncrypted1) {
		this.tinEncrypted1 = tinEncrypted1;
	}
}
