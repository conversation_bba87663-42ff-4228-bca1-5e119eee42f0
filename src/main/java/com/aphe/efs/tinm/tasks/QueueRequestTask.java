package com.aphe.efs.tinm.tasks;

import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import com.aphe.efs.tinm.services.TINMatchTransmissionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Profile({"dev | qa | sandbox | prod"})
public class QueueRequestTask extends GenericBackgroundTask {

	Logger logger = LoggerFactory.getLogger(QueueRequestTask.class);

	@Autowired
    TINMatchTransmissionManager tinMatchTransmissionManager;

	@Scheduled(initialDelayString = "${aphe.efs.tin.queueRequestsTask.initialDelay}", fixedDelayString = "${aphe.efs.tin.queueRequestsTask.fixedDelay}")
	public void executeTask() {

		boolean pauseTasks = isPauseTasks("domainservice-queue-tinmatch-requests-job");
		if(pauseTasks) {
			return;
		}

//		//logger.info("Start of executing the taskName=QueueTINMatchRequestTask status=Begin");

		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		try {
//			logger.debug("Queueing TNN Filings");
			tinMatchTransmissionManager.markQueued();
		} catch (Exception e) {
			logger.error("Error while marking filings to queueed", e);
		}

		CronJobAuthenticationUtil.cleanAuthentication();
//		//logger.info("Start of executing the taskName=QueueTINMatchRequestTask status=End");
	}

}
