package com.aphe.efs.tinm.services;

import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.util.PropertiesManager;
import com.aphe.efs.model.enums.TransmissionStatus;
import com.aphe.efs.services.CommonEFSManager;
import com.aphe.efs.services.EFSMapper;
import com.aphe.efs.services.transmission.DataGenUtil;
import com.aphe.efs.tinm.dto.TINMatchTransmissionRecordDTO;
import com.aphe.efs.tinm.model.EFSTINMatchRequest;
import com.aphe.efs.tinm.model.EFSTINMatchTransmissionRecord;
import com.aphe.efs.tinm.model.enums.TINMatchRequestStatus;
import com.aphe.efs.tinm.repo.TINMatchRequestDAO;
import com.aphe.efs.tinm.repo.TINMatchTransmissionRecordDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

//import org.modelmapper.ModelMapper;
//import org.modelmapper.PropertyMap;

@Service
@Component
@PreAuthorize("hasAuthority('superadmin')")
public class TINMatchTransmissionManager extends CommonEFSManager {

	private Logger logger = LoggerFactory.getLogger(TINMatchTransmissionManager.class);
	
	@Autowired
    EFSTINMatchRequestManager EFSTINMatchRequestManager;


	@Autowired
	TINMatchRequestDAO tinMatchRequestDAO;

	@Autowired
	TINMatchTransmissionRecordDAO tinTransmissionRecordDAO;

	@Autowired
	DataGenUtil dataGenUtil;
	
	@Autowired
	EFSMapper efsMapper;
	
	/**
	 * Marks requests as queued.
	 * 
	 * @return
	 */
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public void markQueued() throws Exception {
		EFSTINMatchRequestManager.updateTINMatchRequestStatus(TINMatchRequestStatus.Received, TINMatchRequestStatus.Queued, "Marking tin match requests as queued");
//		logger.debug("Number of filings by status :" + TINMatchRequestStatus.Received);
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public int getQueued() {
		List<EFSTINMatchRequest> requestsToBeProcessed = tinMatchRequestDAO.findByStatus(TINMatchRequestStatus.Queued);
		return requestsToBeProcessed.size();
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public List<EFSTINMatchRequest> getByStatusIn(List<TINMatchRequestStatus> statuses) {
		List<EFSTINMatchRequest> requestsToBeProcessed = tinMatchRequestDAO.findByStatusIn(statuses);
		return requestsToBeProcessed;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTINMatchTransmissionRecord addOrUpdate(EFSTINMatchTransmissionRecord entity) {
		if (entity.getId() == null || entity.getId() <= 0) {
			return tinTransmissionRecordDAO.save(entity);
		} else {
			EFSTINMatchTransmissionRecord existingEntity = tinTransmissionRecordDAO.findById(entity.getId()).orElse(null);
			return tinTransmissionRecordDAO.mergeAndSave(entity, existingEntity);
		}
	}
	
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTINMatchTransmissionRecord getTINMatchTransmissionRecords(long trId) {
		EFSTINMatchTransmissionRecord tr = tinTransmissionRecordDAO.findById(trId).orElse(null);
		return tr;
	}

	
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public List<EFSTINMatchTransmissionRecord> getTINMatchTransmissionRecords(TransmissionStatus transmissionStatus) {
		Iterable<EFSTINMatchTransmissionRecord> allRecords = tinTransmissionRecordDAO.findAll();
		List<EFSTINMatchTransmissionRecord> matchedRecords = new ArrayList<EFSTINMatchTransmissionRecord>();
		for (EFSTINMatchTransmissionRecord t : allRecords) {
			if (t.getTransmissionStatus() == transmissionStatus) {
				matchedRecords.add(t);
			}
		}
		return matchedRecords;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public List<EFSTINMatchTransmissionRecord> getTINMatchTransmissionRecords(List<TransmissionStatus> transmissionStatuses) {
		Iterable<EFSTINMatchTransmissionRecord> allRecords = tinTransmissionRecordDAO.findByTransmissionStatusIn(transmissionStatuses);
		List<EFSTINMatchTransmissionRecord> matchedRecords = new ArrayList<EFSTINMatchTransmissionRecord>();
		for (EFSTINMatchTransmissionRecord t : allRecords) {
			if (transmissionStatuses.contains(t.getTransmissionStatus())) {
				matchedRecords.add(t);
			}
		}
		return matchedRecords;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTINMatchTransmissionRecord getTransmissionRecord(long transmissionRecordId) {
		return tinTransmissionRecordDAO.findById(transmissionRecordId).orElse(null);
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public void deleteTransmissionRecord(long transmissionRecordId) throws Exception {
		EFSTINMatchTransmissionRecord tr = tinTransmissionRecordDAO.findById(transmissionRecordId).orElse(null);

		if (tr.getTransmissionStatus() != TransmissionStatus.Response_Received && tr.getTransmissionStatus() != TransmissionStatus.Sent_To_Agency) {
			List<Long> requests = new ArrayList<Long>(tr.getRequestIds());
			EFSTINMatchRequestManager.updateTINMatchRequestStatus(requests, TINMatchRequestStatus.Queued, "putting back on queue");
			tinTransmissionRecordDAO.delete(tr);
		} else {
			throw new RuntimeException("Transmission record is already either accepted or rejected");
		}
	}

	/**
	 * Update transmssion record status. Also provide failed filings
	 * allows chanding status from Generated to Sent_To_Agency;
	 * allows Sent_To_Agecny to Response_Received
	 * 
	 * @param
	 */
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public void processResponseFile(long recordId, InputStream inputStream, boolean simulate) throws Exception {
		EFSTINMatchTransmissionRecord tr = tinTransmissionRecordDAO.findById(recordId).orElse(null);
		if (tr == null) {
			throw new RuntimeException("Record not found");
		} else if (tr.getTransmissionStatus() == TransmissionStatus.Sent_To_Agency) {
			//Go through each line and update the TINMatchRequest result and status of the request.

			Set<Long> requestIds = tr.getRequestIds();
			HashMap<Long, Integer> requestIdsAndResults = new HashMap<>();
			
			BufferedReader br = null;
			try{
				br = new BufferedReader(new InputStreamReader(inputStream));
				String line = null;
				while ((line = br.readLine()) != null) {
					String[] values = line.split(";");
					String tinSent = values[1];
					long requestId = Long.parseLong(values[3]);
					int result = simulate ? getRandomTINMatchResult(tinSent) : Integer.parseInt(values[4]);
					requestIdsAndResults.put(requestId, result);
				}
				br.close();
			}finally{
				if(br != null)
					br.close();	
			}

			
			//Check if request ids and match on both sides.
			List<Long> responseIds = new ArrayList<>(requestIdsAndResults.keySet());
			
			List<Long> retainAllCheck = new ArrayList<>(requestIds);
			List<Long> removeAllCheck = new ArrayList<>(requestIds);
			
			retainAllCheck.retainAll(responseIds);
			removeAllCheck.removeAll(requestIds);
			if(retainAllCheck.size() != requestIds.size() || removeAllCheck.size() != 0){
				//Something is wrong with the file. Response file either contained more responses or less responses.
				throw new RuntimeException("Content of response file doesn't match with the request.");
			}
			

			for (Long requestId : requestIdsAndResults.keySet()) {
				Integer result = requestIdsAndResults.get(requestId);
				EFSTINMatchRequestManager.receiveResponse(requestId, result);
			}
			
			tr.setTransmissionStatus(TransmissionStatus.Response_Received);
			tr.setStatusChangeDate(new Date());
			tinTransmissionRecordDAO.save(tr);

		} else {
			throw new RuntimeException("Unsupported status change");
		}
	}

	private int getRandomTINMatchResult(String tinSent) {

        if(PropertiesManager.isSandbox() ) {
            return 0;
        }

		//Get last digit of TIN
		int lastDigit = Integer.parseInt(tinSent.substring(tinSent.length() - 1));
		// if last digit is 9, set it to 0
		if (lastDigit == 9) {
			lastDigit = 0;
		}
		return lastDigit;
	}



	/**
	 * Update transmssion record status. Also provide failed filings
	 * 	allows chanding status from Generated to Sent_To_Agency;
	 *  allows Sent_To_Agecny to Response_Received
	 *  
	 * @param newTr
	 */
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public void updateTransmissionRecordStatus(TINMatchTransmissionRecordDTO newTr) throws Exception {
		EFSTINMatchTransmissionRecord tr = tinTransmissionRecordDAO.findById(Long.parseLong(newTr.transmissionId)).orElse(null);
		if(tr == null){
			throw new RuntimeException("Record not found");
		}
		if(tr.getTransmissionStatus() == TransmissionStatus.Generated && newTr.transmissionStatus == TransmissionStatus.Sent_To_Agency){
			markSentToAgency(tr.getId(), newTr.ackNumber);
		}else if(tr.getTransmissionStatus() == TransmissionStatus.Sent_To_Agency &&  newTr.transmissionStatus == TransmissionStatus.Response_Received){
			if(newTr.isRejected == null){
				throw new ApheDataValidationException("isRejected", "Invalid data. isRejected can not be null.");
			}
			if(newTr.isRejected.booleanValue() == false) {
				throw new RuntimeException("Unsupported status change. You need to upload a file when marking it as accepted.");
			} else {
				markRejected(tr.getId());
			}

		}else{
			throw new RuntimeException("Unsupported status change");
		}
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTINMatchTransmissionRecord markSentToAgency(long transmissionRecordId, String ackNumber) throws Exception {
		EFSTINMatchTransmissionRecord tr = tinTransmissionRecordDAO.findById(transmissionRecordId).orElse(null);
		List<Long> requestIds = new ArrayList<Long>(tr.getRequestIds());
		EFSTINMatchRequestManager.updateTINMatchRequestStatus(requestIds, TINMatchRequestStatus.Sent_To_Agency, "transmitted to agency");
		tr.setTransmissionStatus(TransmissionStatus.Sent_To_Agency);
		if(ackNumber != null) {
			tr.setAckNumber(ackNumber);
		}
		tr.setStatusChangeDate(new Date());
		tr = tinTransmissionRecordDAO.save(tr);
		return tr;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTINMatchTransmissionRecord markRejected(long transmissionRecordId) throws Exception {
		EFSTINMatchTransmissionRecord tr = tinTransmissionRecordDAO.findById(transmissionRecordId).orElse(null);
		List<Long> allRequestIds = new ArrayList<>(tr.getRequestIds());

		EFSTINMatchRequestManager.updateTINMatchRequestStatus(allRequestIds, TINMatchRequestStatus.Queued,
					"Transmission Record id=" + transmissionRecordId + " has been rejeced. Marking all to be processed again.");

		tr.setTransmissionStatus(TransmissionStatus.Response_Received);
		tr.setStatusChangeDate(new Date());
		tr = tinTransmissionRecordDAO.save(tr);

		return tr;
	}



	public TINMatchTransmissionRecordDTO convertToDTO(EFSTINMatchTransmissionRecord entity) {
		return efsMapper.toDTO(entity);
	}

	public List<TINMatchTransmissionRecordDTO> convertToDTOs(List<EFSTINMatchTransmissionRecord> entities) {
		List<TINMatchTransmissionRecordDTO> dtos = new ArrayList<TINMatchTransmissionRecordDTO>();
		for (EFSTINMatchTransmissionRecord entity : entities) {
			dtos.add(convertToDTO(entity));
		}
		return dtos;
	}
}
