package com.aphe.efs.tinm.dto;

import com.aphe.efs.tinm.model.enums.TINMatchTINType;
import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

public class TINMatchRequestDTO {

	public long id;

	@NotNull
	@TrimLength(min = 1, max = 100)
	public String appId;

	@NotNull
	@TrimLength(min = 1, max = 50)
	public String clientRefId;

	@NotNull
	@TrimLength(min = 1, max = 50)
	public String clientDomainId;

	@NotNull
	public TINMatchTINType entityType;

	@NotNull
	@TrimLength(min = 9, max = 9)
	@Pattern(regexp = "[0-9]{9}")
	public String tin;

	@NotNull
	@TrimLength(min = 1, max = 40)
	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Can only contain alpha numeric characters and '-' and '&'")
	public String entityName;

}
