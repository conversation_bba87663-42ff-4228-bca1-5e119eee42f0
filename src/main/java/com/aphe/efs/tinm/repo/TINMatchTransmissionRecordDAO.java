package com.aphe.efs.tinm.repo;

import com.aphe.common.data.repository.ApheCustomRepository;
import com.aphe.efs.model.enums.TransmissionStatus;
import com.aphe.efs.tinm.model.EFSTINMatchTransmissionRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TINMatchTransmissionRecordDAO extends JpaRepository<EFSTINMatchTransmissionRecord, Long>, ApheCustomRepository<EFSTINMatchTransmissionRecord> {

    public List<EFSTINMatchTransmissionRecord> findByTransmissionStatusIn(List<TransmissionStatus> transmissionStatuses);

}
