package com.aphe.efs.graphql;

import com.aphe.efs.dto.FilingRecordDTO;
import com.aphe.efs.model.EFSTNNIRISRecordIdentifier;
import com.aphe.efs.model.EFSTNNTransmissionRecord;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsData;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@DgsComponent
public class EFSTransmissionRecordResolver {

    @DgsData(parentType = "TNNTransmissionRecord", field = "filingRecords")
    public List<FilingRecordDTO> filingRecords(DgsDataFetchingEnvironment dfe) {
        EFSTNNTransmissionRecord record = dfe.getSource();

        if ("iris".equalsIgnoreCase(record.getFormatType())) {
            List<EFSTNNIRISRecordIdentifier> recordIdentifiers = record.getRecordIdentifiers();
            Collections.sort(recordIdentifiers, Comparator.comparing(EFSTNNIRISRecordIdentifier::getGroupIdInt)
                    .thenComparing(EFSTNNIRISRecordIdentifier::getRecordIdInt));
            return recordIdentifiers.stream().map(f -> {
                return new FilingRecordDTO(f.getGroupId() + "|" + f.getRecordId(),  Long.toString(f.getFilingId()));
            }).collect(Collectors.toList());
        } else {
            Map<Long, Long> filingSeqNumbersMap = record.getFilingSeqNumbers();
            List<FilingRecordDTO> seqToFilingList = new ArrayList<>();
            for (Long filingId : filingSeqNumbersMap.keySet()) {
                Long seq = filingSeqNumbersMap.get(filingId);
                seqToFilingList.add(new FilingRecordDTO(Long.toString(seq), Long.toString(filingId)));
            }
            return seqToFilingList;
        }
    }
}
