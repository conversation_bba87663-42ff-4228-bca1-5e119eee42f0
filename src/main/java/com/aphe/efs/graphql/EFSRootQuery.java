package com.aphe.efs.graphql;

import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.model.EFSTNNTransmissionRecord;
import com.aphe.efs.model.enums.TransmissionStatus;
import com.aphe.efs.services.filings.FilingsManager;
import com.aphe.efs.services.transmission.TNNTransmissionManager;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsQuery;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@DgsComponent
public class EFSRootQuery {

	private static final Logger logger = LoggerFactory.getLogger(EFSRootQuery.class);

	@Autowired
	TNNTransmissionManager tnnMgr;

	@Autowired
	FilingsManager efsFilingsMgr;

	@DgsQuery
	public List<EFSTNNTransmissionRecord> tnnTransmissionRecordsByStatusIn(@InputArgument("statuses") List<String> statuses) throws Exception {
		try {
			List<TransmissionStatus> enumStatuses = statuses.stream().map(status -> TransmissionStatus.valueOf(status)).collect(Collectors.toList());
			return tnnMgr.getTransmissionRecords(enumStatuses);
		} catch (Exception e) {
			String message = "Error getting transmission records by status";
			logger.error(message, e);
			throw new Exception(message);
		}
	}

	@DgsQuery
	public EFSTNNTransmissionRecord tnnTransmissionRecordById(@InputArgument("id") String id) throws Exception {
		try {
			return tnnMgr.getTransmissionRecord(Long.parseLong(id));
		} catch (Exception e) {
			String message = "Error getting transmission record by id";
			logger.error(message, e);
			throw new Exception(message);
		}
	}

	@DgsQuery
	public long filingsToBeProcessed(String jurisdiction) throws Exception {
		try {
			return tnnMgr.getQueued(jurisdiction);
		} catch (Exception e) {
			String message = "Error filings to be processed.";
			logger.error(message, e);
			throw new Exception(message);
		}
	}

	@DgsQuery
	public List<EFSFiling> efsFilingsByStatus(@InputArgument("filingStatus") List<String> filingStatuses) throws Exception {
		try {
			return efsFilingsMgr.getFilingsByStatus(filingStatuses);
		} catch (Exception e) {
			logger.error("Error getting filings by filingIds. " + e.getMessage(), e);
			throw e;
		}
	}

}