package com.aphe.efs.dto;

import com.aphe.common.util.StringUtil;
import com.aphe.efs.model.enums.*;
import org.hibernate.validator.constraints.TrimLength;
import org.json.JSONException;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * Class captures the elements that are common to all types of filings.
 * 
 * <AUTHOR>
 *
 */
public class FilingDataDTO {

	public long id;

	@NotNull
	@TrimLength(min = 1, max = 100)
	public String appId;

	@NotNull
	@TrimLength(min = 1, max = 50)
	public String clientRefId;

	@NotNull
	@TrimLength(min = 1, max = 100)
	public String clientDomainId;

	@NotNull
	public Boolean testFiling;

	@NotNull
	public FilingYear filingYear;

	@NotNull
	public FilingReturnType filingReturnType;
	
	@NotNull
	public Boolean reportUsingCFSF;

	@NotNull
	public Boolean lastFilingIndicator;

	@NotNull
	public String payerEntityType;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payer first name can only contain alpha numeric characters and '-' and '&'")
	public String payerFirstName;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]*)", message = "Payer middle name can only contain alpha numeric characters and '-' and '&'")
	public String payerMiddleName;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payer last name can only contain alpha numeric characters and '-' and '&'")
	public String payerLastName;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payer suffix name can only contain alpha numeric characters and '-' and '&'")
	public String payerNameSuffix;

	// TODO : Come up new regEx for punctuation The current RegEx is excluding "-". While that is good for PhoneAndExt, not good for names. May need to list explict chars
	@NotNull(message = "Payer name is required")
	@TrimLength(min = 1, max = 40)
	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payer name can only contain alpha numeric characters and '-' and '&'")
	public String payerName1;

	/**
	 * continuation or transfer agent name or second payer name
	 */
	@TrimLength(min = 0, max = 40)
	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payer name can only contain alpha numeric characters and '-' and '&'")
	public String payerName2;

	@NotNull(message = "Payer TIN type is required")
	public TinType payerTinType;

	@NotNull(message = "Payer TIN is required")
	@TrimLength(min = 9, max = 9)
	@Pattern(regexp = "[0-9]{9}", message = "Payer TIN must be nine digits.")
	@Pattern(regexp = "^((?!*********|*********|*********|*********|*********|*********|*********|*********|*********|*********).)*$", message = "Payer TIN can not be same number repeating or a sequence of numbers.")
	public String payerTin;

	@NotNull
	public Boolean transferAgent;

	@NotNull(message = "Payer address is required")
	@Valid
	public FilingAddressDTO payerAddress;

	@TrimLength(min = 0, max = 15)
	public String payerPhoneAndExt;

	@TrimLength(min = 0, max = 75)
	public String payerEmailAddress;

	@NotNull
	public CorrectionType correctionType;

	@NotNull
	public String payeeEntityType;


	@NotNull(message = "Payee TIN type is required")
	public TinType payeeTinType;

	@NotNull(message = "Payee TIN is required")
	@TrimLength(min = 9, max = 9)
	@Pattern(regexp = "[0-9]{9}", message = "Payee TIN must be nine digits.")
	@Pattern(regexp = "^((?!*********|*********|*********|*********|*********|*********|*********|*********|*********|*********).)*$", message = "Payee TIN can not be same number repeating or a sequence of numbers.")
	public String payeeTin;

	@TrimLength(min = 0, max = 20, message = "Account number on a filing can not be more than 20 characters. Please edit your filing and update the account number.")
	public String payeeAccountNumber;

	@TrimLength(min = 0, max = 4)
	public String payerOfficeCode;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payee first name can only contain alpha numeric characters and '-' and '&'")
	public String payeeFirstName;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payee last name can only contain alpha numeric characters and '-' and '&'")
	public String payeeLastName;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payee middle name can only contain alpha numeric characters and '-' and '&'")
	public String payeeMiddleName;

	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payee suffix can only contain alpha numeric characters and '-' and '&'")
	public String payeeNameSuffix;


	@NotNull(message = "Payee name is required")
	@TrimLength(min = 1, max = 40)
	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payee name can only contain alpha numeric characters and '-' and '&'")
	public String payeeName1;

	@TrimLength(min = 0, max = 40)
	@Pattern(regexp = "([A-Za-z0-9\\-& ]+)", message = "Payee name can only contain alpha numeric characters and '-' and '&'")
	public String payeeName2;

	@NotNull(message = "Payee address is required")
	@Valid
	public FilingAddressDTO payeeAddress;

	@NotNull
	public Boolean stateFiling;

	public StateCode reportingStateCode;

	public StateFilingMethod stateFilingMethod;

	public Date filingDate;

	protected BigDecimal getBigDecimal(String value) throws JSONException {
		if(StringUtil.isEmpty(value)) {
			return null;
		}
		try {
			return new BigDecimal(value.toString());
		} catch (Exception var4) {
			return BigDecimal.ZERO;
		}
	}


	protected boolean isFedTaxWithheldHigh(BigDecimal allAmounts, BigDecimal fedTaxWithheld) {
		if(allAmounts == null) {
			allAmounts = BigDecimal.ZERO.ZERO;
		}
		if(fedTaxWithheld == null) {
			fedTaxWithheld = BigDecimal.ZERO.ZERO;
		}
		allAmounts = allAmounts.divide(new BigDecimal("3.00"), 2, RoundingMode.HALF_UP);
		return allAmounts.compareTo(fedTaxWithheld) < 0;
	}


}
