package com.aphe.efs.model;

import com.aphe.common.persistence.JsonConverter;
import com.aphe.efs.model.enums.StateCode;
import org.json.JSONObject;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "efs_filingstnnoid")
@DiscriminatorValue("1099-OID")
public class EFSFiling1099OID extends EFSFiling {
	
	@Column(name = "FILINGDATA", columnDefinition = "JSON")
	@Convert(converter = JsonConverter.class)
	private JSONObject filingData = new JSONObject();

	public Boolean isSecondTinNotice() {
		return getFilingDataBooleanValue(filingData, "secondTinNotice");
	}
	public Boolean getSecondTinNotice() {
		return getFilingDataBooleanValue(filingData, "secondTinNotice");
	}

	public Boolean isFatcaFilingRequirementIndicator() {
		return getFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator");
	}
	public Boolean getFatcaFilingRequirementIndicator() {
		return getFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator");
	}

	public BigDecimal getOid() {
		return getFilingDataBigDecimalValue(filingData, "oid");
	}

	public BigDecimal getPeriodInterest() {
		return getFilingDataBigDecimalValue(filingData, "periodInterest");
	}

	public BigDecimal getEarlyWithdrawalPenalty() {
		return getFilingDataBigDecimalValue(filingData, "earlyWithdrawalPenalty");
	}

	public BigDecimal getFederalTaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "federalTaxWithheld");
	}

	public BigDecimal getMarketDiscount() {
		return getFilingDataBigDecimalValue(filingData, "marketDiscount");
	}

	public BigDecimal getAcquisitionPremium() {
		return getFilingDataBigDecimalValue(filingData, "acquisitionPremium");
	}
	
	public String getDescription() {
		return getFilingDataStringValue(filingData, "description");
	}

	public BigDecimal getOidOnUSTreasury() {
		return getFilingDataBigDecimalValue(filingData, "oidOnUSTreasury");
	}

	public BigDecimal getInvestmentExpenses() {
		return getFilingDataBigDecimalValue(filingData, "investmentExpenses");
	}

	public BigDecimal getBondPremium() {
		return getFilingDataBigDecimalValue(filingData, "bondPremium");
	}

	public BigDecimal getTaxExemptOID() {
		return getFilingDataBigDecimalValue(filingData, "taxExemptOID");
	}

	public StateCode getState1Code() {
		return getFilingDataStateCodeValue(filingData, "state1Code");
	}

	public BigDecimal getState1Income() {
		return getFilingDataBigDecimalValue(filingData, "state1Income");
	}

	public String getState1EIN() {
		return getFilingDataStringValue(filingData, "state1EIN");
	}

	public BigDecimal getState1TaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "state1TaxWithheld");
	}

	public BigDecimal getLocalTaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "localTaxWithheld");
	}

	public String getStateSpecialData() {
		return getFilingDataStringValue(filingData, "stateSpecialData");
	}

	/**
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 * Setter Methods
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 */

	public void setSecondTinNotice(Boolean secondTinNotice) {
		setFilingDataBooleanValue(filingData, "secondTinNotice", secondTinNotice);
	}

	public void setFatcaFilingRequirementIndicator(Boolean fatcaFilingRequirementIndicator) {
		setFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator", fatcaFilingRequirementIndicator);
	}

	public void setOid(BigDecimal oid) {
		setFilingDataBigDecimalValue(filingData, "oid", oid);
	}

	public void setPeriodInterest(BigDecimal periodInterest) {
		setFilingDataBigDecimalValue(filingData, "periodInterest", periodInterest);
	}
	
	public void setEarlyWithdrawalPenalty(BigDecimal earlyWithdrawalPenalty) {
		setFilingDataBigDecimalValue(filingData, "earlyWithdrawalPenalty", earlyWithdrawalPenalty);
	}

	public void setFederalTaxWithheld(BigDecimal federalTaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "federalTaxWithheld", federalTaxWithheld);
	}

	public void setMarketDiscount(BigDecimal marketDiscount) {
		setFilingDataBigDecimalValue(filingData, "marketDiscount", marketDiscount);
	}

	public void setAcquisitionPremium(BigDecimal acquisitionPremium) {
		setFilingDataBigDecimalValue(filingData, "acquisitionPremium", acquisitionPremium);
	}

	public void setDescription(String description) {
		setFilingDataStringValue(filingData, "description", description);
	}

	public void setOidOnUSTreasury(BigDecimal oidOnUSTreasury) {
		setFilingDataBigDecimalValue(filingData, "oidOnUSTreasury", oidOnUSTreasury);
	}
	
	public void setInvestmentExpenses(BigDecimal investmentExpenses) {
		setFilingDataBigDecimalValue(filingData, "investmentExpenses", investmentExpenses);
	}

	public void setBondPremium(BigDecimal bondPremium) {
		setFilingDataBigDecimalValue(filingData, "bondPremium", bondPremium);
	}

	public void setTaxExemptOID(BigDecimal taxExemptOID) {
		setFilingDataBigDecimalValue(filingData, "taxExemptOID", taxExemptOID);
	}

	public void setState1Code(String state1Code) {
		setFilingDataStringValue(filingData, "state1Code", state1Code);
	}

	public void setState1EIN(String state1EIN) {
		setFilingDataStringValue(filingData, "state1EIN", state1EIN);
	}

	public void setState1TaxWithheld(BigDecimal state1TaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "state1TaxWithheld", state1TaxWithheld);
	}

	public void setLocalTaxWithheld(BigDecimal localTaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "localTaxWithheld", localTaxWithheld);
	}

	public void setStateSpecialData(String stateSpecialData) {
		setFilingDataStringValue(filingData, "stateSpecialData", stateSpecialData);
	}

}
