package com.aphe.efs.model;

import com.aphe.common.persistence.JsonConverter;
import com.aphe.efs.model.enums.StateCode;
import org.json.JSONObject;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "efs_filingstnnnec")
@DiscriminatorValue("1099-NEC")
public class EFSFiling1099NEC extends EFSFiling {
	
	@Column(name = "FILINGDATA", columnDefinition = "JSON")
	@Convert(converter = JsonConverter.class)
	private JSONObject filingData = new JSONObject();
	
	
	public JSONObject getJSONFilingData() {
		return filingData;
	}

	public boolean isOnlyDirectSalesIndicator() {
		if (isDirectSalesIndicator() && isDirectSalesIndicator().booleanValue() == true && allAmountsAreZero())
			return true;
		return false;
	}

	public boolean allAmountsAreZero() {
		return getNonEmployeeComp().compareTo(BigDecimal.ZERO) <= 0
				&& getFederalTaxWithheld().compareTo(BigDecimal.ZERO) <= 0;
	}


	public Boolean isSecondTinNotice() {
		return getFilingDataBooleanValue(filingData, "secondTinNotice");
	}
	public Boolean getSecondTinNotice() {
		return getFilingDataBooleanValue(filingData, "secondTinNotice");
	}

	public Boolean isFatcaFilingRequirementIndicator() {
		return getFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator");
	}
	public Boolean getFatcaFilingRequirementIndicator() {
		return getFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator");
	}

	public Boolean isDirectSalesIndicator() {
		return getFilingDataBooleanValue(filingData, "directSalesIndicator");
	}
	public Boolean getDirectSalesIndicator() {
		return getFilingDataBooleanValue(filingData, "directSalesIndicator");
	}

	public BigDecimal getNonEmployeeComp() {
		return getFilingDataBigDecimalValue(filingData, "nonEmployeeComp");
	}

	public BigDecimal getFederalTaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "federalTaxWithheld");
	}

	public StateCode getState1Code() {
		return getFilingDataStateCodeValue(filingData, "state1Code");
	}

	public String getState1EIN() {
		return getFilingDataStringValue(filingData, "state1EIN");
	}

	public BigDecimal getState1TaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "state1TaxWithheld");
	}

	public BigDecimal getState1Income() {
		return getFilingDataBigDecimalValue(filingData, "state1Income");
	}

	public BigDecimal getLocalTaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "localTaxWithheld");
	}

	public String getStateSpecialData() {
		return getFilingDataStringValue(filingData, "stateSpecialData");
	}

	/**
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 * Setter Methods
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 */

	public void setSecondTinNotice(Boolean secondTinNotice) {
		setFilingDataBooleanValue(filingData, "secondTinNotice", secondTinNotice);
	}

	public void setFatcaFilingRequirementIndicator(Boolean fatcaFilingRequirementIndicator) {
		setFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator", fatcaFilingRequirementIndicator);
	}

	public void setNonEmployeeComp(BigDecimal nonEmployeeComp) {
		setFilingDataBigDecimalValue(filingData, "nonEmployeeComp", nonEmployeeComp);
	}

	public void setFederalTaxWithheld(BigDecimal federalTaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "federalTaxWithheld", federalTaxWithheld);
	}

	public void setState1Code(String state1Code) {
		setFilingDataStringValue(filingData, "state1Code", state1Code);
	}

	public void setState1EIN(String state1EIN) {
		setFilingDataStringValue(filingData, "state1EIN", state1EIN);
	}

	public void setState1TaxWithheld(BigDecimal state1TaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "state1TaxWithheld", state1TaxWithheld);
	}

	public void setState1Income(BigDecimal state1Income) {
		setFilingDataBigDecimalValue(filingData, "state1Income", state1Income);
	}

	public void setLocalTaxWithheld(BigDecimal localTaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "localTaxWithheld", localTaxWithheld);
	}

	public void setStateSpecialData(String stateSpecialData) {
		setFilingDataStringValue(filingData, "stateSpecialData", stateSpecialData);
	}

	public void setDirectSalesIndicator(Boolean directSalesIndicator) {
		setFilingDataBooleanValue(filingData, "directSalesIndicator", directSalesIndicator);
	}

}
