package com.aphe.efs.model;

import com.aphe.common.persistence.JsonConverter;
import com.aphe.efs.model.enums.FilingYear;
import com.aphe.efs.model.enums.StateCode;
import org.json.JSONObject;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "efs_filingstnnmisc2020")
@DiscriminatorValue("1099-Misc")
public class EFSFiling1099MISC extends EFSFiling {
	
	@Column(name = "FILINGDATA", columnDefinition = "JSON")
	@Convert(converter = JsonConverter.class)
	private JSONObject filingData = new JSONObject();
	
	
	public JSONObject getJSONFilingData() {
		return filingData;
	}


	public boolean isOnlyDirectSalesIndicator() {
		if (isDirectSalesIndicator() && isDirectSalesIndicator().booleanValue() == true && allAmountsAreZero())
			return true;
		return false;
	}

	public boolean allAmountsAreZero() {
		if(FilingYear.Y2019 == getFilingYear()) {
			return getRents().compareTo(BigDecimal.ZERO) <= 0 && getRoyalties().compareTo(BigDecimal.ZERO) <= 0 && getOtherIncome().compareTo(BigDecimal.ZERO) <= 0
					&& getFederalTaxWithheld().compareTo(BigDecimal.ZERO) <= 0 && getFishingBoatProceeds().compareTo(BigDecimal.ZERO) <= 0
					&& getMedicalPayments().compareTo(BigDecimal.ZERO) <= 0 && getNonEmployeeComp().compareTo(BigDecimal.ZERO) <= 0
					&& getSubstitutePaymentsForDivInt().compareTo(BigDecimal.ZERO) <= 0
					&& getCropInsuranceProceeds().compareTo(BigDecimal.ZERO) <= 0 && getGoldenParachutePayment().compareTo(BigDecimal.ZERO) <= 0
					&& getProceedsPaidToAttorney().compareTo(BigDecimal.ZERO) <= 0 && getFour09Deferrals().compareTo(BigDecimal.ZERO) <= 0
					&& getFour09Income().compareTo(BigDecimal.ZERO) <= 0;
		} else if (FilingYear.Y2020 == getFilingYear()) {
			return getRents().compareTo(BigDecimal.ZERO) <= 0 && getRoyalties().compareTo(BigDecimal.ZERO) <= 0 && getOtherIncome().compareTo(BigDecimal.ZERO) <= 0
					&& getFederalTaxWithheld().compareTo(BigDecimal.ZERO) <= 0 && getFishingBoatProceeds().compareTo(BigDecimal.ZERO) <= 0
					&& getMedicalPayments().compareTo(BigDecimal.ZERO) <= 0 && getSubstitutePaymentsForDivInt().compareTo(BigDecimal.ZERO) <= 0
					&& getCropInsuranceProceeds().compareTo(BigDecimal.ZERO) <= 0 && getGoldenParachutePayment().compareTo(BigDecimal.ZERO) <= 0
					&& getProceedsPaidToAttorney().compareTo(BigDecimal.ZERO) <= 0 && getFour09Deferrals().compareTo(BigDecimal.ZERO) <= 0
					&& getNonQualifiedDeferredComp().compareTo(BigDecimal.ZERO) <= 0;
		} else if (FilingYear.Y2021 == getFilingYear() || FilingYear.Y2022 == getFilingYear() || FilingYear.Y2023 == getFilingYear() || FilingYear.Y2024 == getFilingYear()) {
			return getRents().compareTo(BigDecimal.ZERO) <= 0 && getRoyalties().compareTo(BigDecimal.ZERO) <= 0 && getOtherIncome().compareTo(BigDecimal.ZERO) <= 0
					&& getFederalTaxWithheld().compareTo(BigDecimal.ZERO) <= 0 && getFishingBoatProceeds().compareTo(BigDecimal.ZERO) <= 0
					&& getMedicalPayments().compareTo(BigDecimal.ZERO) <= 0 && getSubstitutePaymentsForDivInt().compareTo(BigDecimal.ZERO) <= 0
					&& getCropInsuranceProceeds().compareTo(BigDecimal.ZERO) <= 0 && getGoldenParachutePayment().compareTo(BigDecimal.ZERO) <= 0
					&& getProceedsPaidToAttorney().compareTo(BigDecimal.ZERO) <= 0 && getFishPurchasedForResale().compareTo(BigDecimal.ZERO) <= 0
					&& getFour09Deferrals().compareTo(BigDecimal.ZERO) <= 0 && getNonQualifiedDeferredComp().compareTo(BigDecimal.ZERO) <= 0;
		} else {
			throw new RuntimeException("Invlaid year");
		}
	}

	public Boolean isSecondTinNotice() {
		return getFilingDataBooleanValue(filingData, "secondTinNotice");
	}

	public Boolean getSecondTinNotice() {
		return getFilingDataBooleanValue(filingData, "secondTinNotice");
	}
	public Boolean isDirectSalesIndicator() {
		return getFilingDataBooleanValue(filingData, "directSalesIndicator");
	}
	public Boolean getDirectSalesIndicator() {
		return getFilingDataBooleanValue(filingData, "directSalesIndicator");
	}

	public Boolean isFatcaFilingRequirementIndicator() {
		return getFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator");
	}
	public Boolean getFatcaFilingRequirementIndicator() {
		return getFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator");
	}

	public BigDecimal getRents() {
		return getFilingDataBigDecimalValue(filingData, "rents");
	}

	public BigDecimal getRoyalties() {
		return getFilingDataBigDecimalValue(filingData, "royalties");
	}

	public BigDecimal getOtherIncome() {
		return getFilingDataBigDecimalValue(filingData, "otherIncome");
	}

	public BigDecimal getFederalTaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "federalTaxWithheld");
	}

	public BigDecimal getFishingBoatProceeds() {
		return getFilingDataBigDecimalValue(filingData, "fishingBoatProceeds");
	}

	public BigDecimal getMedicalPayments() {
		return getFilingDataBigDecimalValue(filingData, "medicalPayments");
	}

	public BigDecimal getSubstitutePaymentsForDivInt() {
		return getFilingDataBigDecimalValue(filingData, "substitutePaymentsForDivInt");
	}

	public BigDecimal getCropInsuranceProceeds() {
		return getFilingDataBigDecimalValue(filingData, "cropInsuranceProceeds");
	}

	public BigDecimal getProceedsPaidToAttorney() {
		return getFilingDataBigDecimalValue(filingData, "proceedsPaidToAttorney");
	}

	public BigDecimal getFishPurchasedForResale() {
		return getFilingDataBigDecimalValue(filingData, "fishPurchasedForResale");
	}

	public BigDecimal getNonEmployeeComp() {
		return getFilingDataBigDecimalValue(filingData, "nonEmployeeComp");
	}

	public BigDecimal getFour09Income() {
		return getFilingDataBigDecimalValue(filingData, "four09Income");
	}

	public BigDecimal getFour09Deferrals() {
		return getFilingDataBigDecimalValue(filingData, "four09Deferrals");
	}

	public BigDecimal getGoldenParachutePayment() {
		return getFilingDataBigDecimalValue(filingData, "goldenParachutePayment");
	}

	public BigDecimal getNonQualifiedDeferredComp() {
		return getFilingDataBigDecimalValue(filingData, "nonQualifiedDeferredComp");
	}

	public StateCode getState1Code() {
		return getFilingDataStateCodeValue(filingData, "state1Code");
	}

	public String getState1EIN() {
		return getFilingDataStringValue(filingData, "state1EIN");
	}

	public BigDecimal getState1TaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "state1TaxWithheld");
	}

	public BigDecimal getState1Income() {
		return getFilingDataBigDecimalValue(filingData, "state1Income");
	}

	public BigDecimal getLocalTaxWithheld() {
		return getFilingDataBigDecimalValue(filingData, "localTaxWithheld");
	}

	public String getStateSpecialData() {
		return getFilingDataStringValue(filingData, "stateSpecialData");
	}

	/**
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 * Setter Methods
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 */

	public void setSecondTinNotice(Boolean secondTinNotice) {
		setFilingDataBooleanValue(filingData, "secondTinNotice", secondTinNotice);
	}

	public void setDirectSalesIndicator(Boolean directSalesIndicator) {
		setFilingDataBooleanValue(filingData, "directSalesIndicator", directSalesIndicator);
	}

	public void setFatcaFilingRequirementIndicator(Boolean fatcaFilingRequirementIndicator) {
		setFilingDataBooleanValue(filingData, "fatcaFilingRequirementIndicator", fatcaFilingRequirementIndicator);
	}

	public void setRents(BigDecimal rents) {
		setFilingDataBigDecimalValue(filingData, "rents", rents);
	}

	public void setRoyalties(BigDecimal royalties) {
		setFilingDataBigDecimalValue(filingData, "royalties", royalties);
	}

	public void setOtherIncome(BigDecimal otherIncome) {
		setFilingDataBigDecimalValue(filingData, "otherIncome", otherIncome);
	}

	public void setFederalTaxWithheld(BigDecimal federalTaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "federalTaxWithheld", federalTaxWithheld);
	}

	public void setFishingBoatProceeds(BigDecimal fishingBoatProceeds) {
		setFilingDataBigDecimalValue(filingData, "fishingBoatProceeds", fishingBoatProceeds);
	}

	public void setMedicalPayments(BigDecimal medicalPayments) {
		setFilingDataBigDecimalValue(filingData, "medicalPayments", medicalPayments);
	}

	public void setSubstitutePaymentsForDivInt(BigDecimal substitutePaymentsForDivInt) {
		setFilingDataBigDecimalValue(filingData, "substitutePaymentsForDivInt", substitutePaymentsForDivInt);
	}

	public void setCropInsuranceProceeds(BigDecimal cropInsuranceProceeds) {
		setFilingDataBigDecimalValue(filingData, "cropInsuranceProceeds", cropInsuranceProceeds);
	}

	public void setProceedsPaidToAttorney(BigDecimal proceedsPaidToAttorney) {
		setFilingDataBigDecimalValue(filingData, "proceedsPaidToAttorney", proceedsPaidToAttorney);
	}

	public void setFishPurchasedForResale(BigDecimal fishPurchasedForResale) {
		setFilingDataBigDecimalValue(filingData, "fishPurchasedForResale", fishPurchasedForResale);
	}

	public void setFour09Deferrals(BigDecimal four09Deferrals) {
		setFilingDataBigDecimalValue(filingData, "four09Deferrals", four09Deferrals);
	}

	public void setFour09Income(BigDecimal four09Income) {
		setFilingDataBigDecimalValue(filingData, "four09Income", four09Income);
	}

	public void setNonEmployeeComp(BigDecimal nonEmployeeComp) {
		setFilingDataBigDecimalValue(filingData, "nonEmployeeComp", nonEmployeeComp);
	}


	public void setGoldenParachutePayment(BigDecimal goldenParachutePayment) {
		setFilingDataBigDecimalValue(filingData, "goldenParachutePayment", goldenParachutePayment);
	}

	public void setNonQualifiedDeferredComp(BigDecimal nonQualifiedDeferredComp) {
		setFilingDataBigDecimalValue(filingData, "nonQualifiedDeferredComp", nonQualifiedDeferredComp);
	}

	public void setState1Code(String state1Code) {
		setFilingDataStringValue(filingData, "state1Code", state1Code);
	}

	public void setState1EIN(String state1EIN) {
		setFilingDataStringValue(filingData, "state1EIN", state1EIN);
	}

	public void setState1TaxWithheld(BigDecimal state1TaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "state1TaxWithheld", state1TaxWithheld);
	}

	public void setState1Income(BigDecimal state1Income) {
		setFilingDataBigDecimalValue(filingData, "state1Income", state1Income);
	}

	public void setLocalTaxWithheld(BigDecimal localTaxWithheld) {
		setFilingDataBigDecimalValue(filingData, "localTaxWithheld", localTaxWithheld);
	}

	public void setStateSpecialData(String stateSpecialData) {
		setFilingDataStringValue(filingData, "stateSpecialData", stateSpecialData);
	}

}
