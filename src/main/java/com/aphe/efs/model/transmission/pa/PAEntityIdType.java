package com.aphe.efs.model.transmission.pa;

public enum PAEntityIdType {
    EIN("001"),
    SSN("002"),
    TE("007"),
    TID("100"),
    PM("008"),
    PA("006"),
    ITIN("009");

    private String code;

    private PAEntityIdType(String code) {
        this.code = code;
    }

    //get entity by name
    public static PAEntityIdType getByName(String name) {
        for (PAEntityIdType entityType : PAEntityIdType.values()) {
            if (entityType.name().equals(name)) {
                return entityType;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
