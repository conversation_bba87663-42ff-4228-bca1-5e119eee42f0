package com.aphe.efs.model;

import com.aphe.common.model.BaseEntity;
import com.aphe.common.util.EncryptionUtil;
import com.aphe.common.util.StringUtil;
import com.aphe.efs.model.enums.*;
import org.eclipse.persistence.annotations.Cache;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "efs_filings")
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorValue("")
@EntityListeners(EFSFilingEncryptionListener.class)
@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true, expiry = 600000)
@NamedQueries({
	@NamedQuery(name = "EFSFiling.findByStatusAndFilingDate",
		query = "SELECT f FROM EFSFiling f WHERE f.status = :filingStatus and (f.filingDate <= :filingDate OR f.filingDate is null)"),
	@NamedQuery(name = "EFSFiling.findByIdIn",
		query = "SELECT f FROM EFSFiling f WHERE f.id IN :filingIds"),
	@NamedQuery(name = "EFSFiling.findFilingsToBeEncrypted",
		query = "SELECT f FROM EFSFiling f WHERE f.payeeTinEncrypted1 is null and f.payerTinEncrypted1 is null and f.filingYear = :filingYear"),
	@NamedQuery(name = "EFSFiling.updateEncryptedTINs",
		query = "UPDATE EFSFiling f SET f.payerTinEncrypted1 = :payerTinEncrypted, f.payeeTinEncrypted1 = :payeeTinEncrypted WHERE f.id = :id")
})
public class EFSFiling extends BaseEntity {

	@Transient
	Logger logger = LoggerFactory.getLogger(EFSFiling.class);

	private String appId;
	private String clientRefId;
	private String clientDomainId;
	private boolean isTestFiling;

	@Enumerated(EnumType.STRING)
	private FilingYear filingYear;

	// TODO : 2 CFSF is not supported for all forms. Throw an exception if this flag is used with unsupported forms
	private boolean reportUsingCFSF;

	private boolean lastFilingIndicator;

	@Column(name = "DTYPE", insertable = false, updatable = false)
	private String filingType;

	@Enumerated(EnumType.STRING)
	private FilingReturnType filingReturnType;
	@Enumerated(EnumType.STRING)
	private CorrectionType correctionType; // original, correction1, correction2
	private boolean isStateFiling;
	@Enumerated(EnumType.STRING)
	private StateCode reportingStateCode;
	@Enumerated(EnumType.STRING)
	private StateFilingMethod stateFilingMethod;

	@Enumerated(EnumType.STRING)
	private EntityType payerEntityType;
	private String payerFirstName;
	private String payerMiddleName;
	private String payerLastName;
	private String payerNameSuffix;
	private String payerName1;
	private String payerName2;
	@Enumerated(EnumType.STRING)
	private TinType payerTinType;
	private String payerTin;
	private String payerTinEncrypted;
	private String payerTinEncrypted1;
	@OneToOne(cascade = CascadeType.ALL)
	private EFSFilingAddress payerAddress;
	private String payerPhoneAndExt;
	private String payerEmailAddress;
	private String payerOfficeCode;

	private boolean isTransferAgent;

	@Enumerated(EnumType.STRING)
	private EntityType payeeEntityType;
	private String payeeFirstName;
	private String payeeLastName;
	private String payeeMiddleName;
	private String payeeNameSuffix;
	private String payeeName1;
	private String payeeName2;
	@Enumerated(EnumType.STRING)
	private TinType payeeTinType;
	private String payeeTin;
	private String payeeTinEncrypted;
	private String payeeTinEncrypted1;
	@OneToOne(cascade = CascadeType.ALL)
	private EFSFilingAddress payeeAddress;
	private String payeeAccountNumber;

	private Date filingDate;

	@Enumerated(EnumType.STRING)
	private FilingStatus status;
	@Temporal(TemporalType.TIMESTAMP)
	private Date statusChangeDate;
	private String statusChangeDesc;

	private String uniqueRecordId;

	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	@JoinColumn(name = "FILING_ID")
	private List<EFSFilingStatusEntity> filingStatusHistory;



	public String getCalendarYear() {
		return filingYear.getYear().substring(filingYear.getYear().length()-2);
	}

	protected Boolean getFilingDataBooleanValue(JSONObject filingData, String key) {
		if (filingData.has(key)) {
			return filingData.getBoolean(key);
		}
		return null;
	}

	protected void setFilingDataBooleanValue(JSONObject filingData, String key, Boolean value) {
		if (value != null) {
			filingData.put(key, value.booleanValue());
		} else {
			filingData.remove(key);
		}
	}

	protected BigDecimal getFilingDataBigDecimalValue(JSONObject filingData, String key) {
		if (filingData.has(key)) {
			return filingData.getBigDecimal(key);
		}
		return null;
	}

	protected void setFilingDataBigDecimalValue(JSONObject filingData, String key, BigDecimal value) {
		if (value != null) {
			filingData.put(key, value);
		} else {
			filingData.remove(key);
		}
	}

	protected String getFilingDataStringValue(JSONObject filingData, String key) {
		if (filingData.has(key)) {
			Object value = filingData.get(key);
			if (value instanceof String) {
				return (String) value;
			}
		}
		return null;
	}

	protected void setFilingDataStringValue(JSONObject filingData, String key, String value) {
		if (value != null) {
			filingData.put(key, value);
		} else {
			filingData.remove(key);
		}
	}

	protected StateCode getFilingDataStateCodeValue(JSONObject filingData, String key) {
		if (filingData.has(key)) {
			Object value = filingData.get(key);
			if (value instanceof String) {
				StateCode code = StateCode.valueOf((String) value);
				return code;
			}
		}
		return null;
	}

	protected void setFilingDataSateCodeValue(JSONObject filingData, String key, StateCode value) {
		if (value != null) {
			filingData.put(key, value.name());
		} else {
			throw new RuntimeException("Null value");
		}
	}

	@PreAuthorize("hasAuthority('superadmin')")
	public String getPayerTinPlain() {
		if (StringUtil.isNotEmpty(payerTinEncrypted)) {
			return new EncryptionUtil().decrypt(payerTinEncrypted);
		}
		return null;
	}

	@PreAuthorize("hasAuthority('superadmin')")
	public String getPayerTinPlain(EncryptionUtil encryptionUtil) {
		if (StringUtil.isNotEmpty(payerTinEncrypted)) {
			return encryptionUtil.decrypt(payerTinEncrypted);
		}
		return null;
	}

	@PreAuthorize("hasAuthority('superadmin')")
	public String getPayeeTinPlain() {
		if (StringUtil.isNotEmpty(payeeTinEncrypted)) {
			return new EncryptionUtil().decrypt(payeeTinEncrypted);
		}
		return null;
	}

	@PreAuthorize("hasAuthority('superadmin')")
	public String getPayeeTinPlain(EncryptionUtil encryptionUtil) {
		if (StringUtil.isNotEmpty(payeeTinEncrypted)) {
			return encryptionUtil.decrypt(payeeTinEncrypted);
		}
		return null;
	}

	public FilingYear getFilingYear() {
		return filingYear;
	}

	public CorrectionType getCorrectionType() {
		return correctionType;
	}

	public boolean isReportUsingCFSF() {
		return reportUsingCFSF;
	}

	public String getPayerName1() {
		return payerName1;
	}

	public String getPayerName2() {
		return payerName2;
	}

	public boolean isTransferAgent() {
		return isTransferAgent;
	}

	public String getPayerTin() {
		return payerTin;
	}

	public EFSFilingAddress getPayerAddress() {
		return payerAddress;
	}

	public TinType getPayeeTinType() {
		return payeeTinType;
	}

	public String getPayeeTin() {
		return payeeTin;
	}

	public String getPayeeName1() {
		return payeeName1;
	}

	public String getPayeeName2() {
		return payeeName2;
	}

	public String getPayeeAccountNumber() {
		return payeeAccountNumber;
	}

	public String getPayerOfficeCode() {
		return payerOfficeCode;
	}

	public EFSFilingAddress getPayeeAddress() {
		return payeeAddress;
	}

	public void setFilingYear(FilingYear filingYear) {
		this.filingYear = filingYear;
	}

	public void setCorrectionType(CorrectionType correctionType) {
		this.correctionType = correctionType;
	}

	public void setReportUsingCFSF(boolean reportUsingCFSF) {
		this.reportUsingCFSF = reportUsingCFSF;
	}

	public void setPayerName1(String payerName1) {
		this.payerName1 = payerName1;
	}

	public void setPayerName2(String payerName2) {
		this.payerName2 = payerName2;
	}

	public void setTransferAgent(boolean isTransferAgent) {
		this.isTransferAgent = isTransferAgent;
	}

	public void setPayerTin(String payerTin) {
		this.payerTin = payerTin;
	}

	public void setPayerAddress(EFSFilingAddress payerAddress) {
		this.payerAddress = payerAddress;
	}

	public void setPayeeTinType(TinType payeeTinType) {
		this.payeeTinType = payeeTinType;
	}

	public void setPayeeTin(String payeeTin) {
		this.payeeTin = payeeTin;
	}

	public void setPayeeName1(String payeeName1) {
		this.payeeName1 = payeeName1;
	}

	public void setPayeeName2(String payeeName2) {
		this.payeeName2 = payeeName2;
	}

	public void setPayeeAccountNumber(String payeeAccountNumber) {
		this.payeeAccountNumber = payeeAccountNumber;
	}

	public void setPayerOfficeCode(String payerOfficeCode) {
		this.payerOfficeCode = payerOfficeCode;
	}

	public void setPayeeAddress(EFSFilingAddress payeeAddress) {
		this.payeeAddress = payeeAddress;
	}

	public FilingStatus getStatus() {
		return status;
	}

	public void setStatus(FilingStatus status) {
		this.status = status;
	}

	public String getClientRefId() {
		return clientRefId;
	}

	public void setClientRefId(String clientRefId) {
		this.clientRefId = clientRefId;
	}

	public boolean isTestFiling() {
		return isTestFiling;
	}

	public void setTestFiling(boolean isTestFiling) {
		this.isTestFiling = isTestFiling;
	}

	public String getFilingType() {
		return filingType;
	}

	public void setFilingType(String filingType) {
		this.filingType = filingType;
	}

	public String getPayerPhoneAndExt() {
		return payerPhoneAndExt;
	}

	public void setPayerPhoneAndExt(String payerPhoneAndExt) {
		this.payerPhoneAndExt = payerPhoneAndExt;
	}

	public Date getStatusChangeDate() {
		return statusChangeDate;
	}

	public void setStatusChangeDate(Date statusChangeDate) {
		this.statusChangeDate = statusChangeDate;
	}

	public String getPayeeTinEncrypted() {
		return payeeTinEncrypted;
	}

	public void setPayeeTinEncrypted(String payeeTinEncrypted) {
		this.payeeTinEncrypted = payeeTinEncrypted;
	}

	public String getPayerTinEncrypted() {
		return payerTinEncrypted;
	}

	public void setPayerTinEncrypted(String payerTinSecure) {
		this.payerTinEncrypted = payerTinSecure;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getStatusChangeDesc() {
		return statusChangeDesc;
	}

	public void setStatusChangeDesc(String statusChangeDesc) {
		this.statusChangeDesc = statusChangeDesc;
	}

	public List<EFSFilingStatusEntity> getFilingStatusHistory() {
		return filingStatusHistory;
	}

	public void setFilingStatusHistory(List<EFSFilingStatusEntity> filingStatusHistory) {
		this.filingStatusHistory = filingStatusHistory;
	}

	public FilingReturnType getFilingReturnType() {
		return filingReturnType;
	}

	public void setFilingReturnType(FilingReturnType filingReturnType) {
		this.filingReturnType = filingReturnType;
	}

	public boolean isLastFilingIndicator() {
		return lastFilingIndicator;
	}

	public void setLastFilingIndicator(boolean lastFilingIndicator) {
		this.lastFilingIndicator = lastFilingIndicator;
	}

	public String getClientDomainId() {
		return clientDomainId;
	}

	public void setClientDomainId(String clientDomainId) {
		this.clientDomainId = clientDomainId;
	}

	public boolean isStateFiling() {
		return isStateFiling;
	}

	public void setStateFiling(boolean stateFiling) {
		isStateFiling = stateFiling;
	}

	public StateCode getReportingStateCode() {
		return reportingStateCode;
	}

	public void setReportingStateCode(StateCode reportingStateCode) {
		this.reportingStateCode = reportingStateCode;
	}

	public StateFilingMethod getStateFilingMethod() {
		return stateFilingMethod;
	}

	public void setStateFilingMethod(StateFilingMethod stateFilingMethod) {
		this.stateFilingMethod = stateFilingMethod;
	}

	public String getPayeeFirstName() {
		return payeeFirstName;
	}

	public void setPayeeFirstName(String payeeFirstName) {
		this.payeeFirstName = payeeFirstName;
	}

	public String getPayeeLastName() {
		return payeeLastName;
	}

	public void setPayeeLastName(String payeeLastName) {
		this.payeeLastName = payeeLastName;
	}

	public String getPayeeMiddleName() {
		return payeeMiddleName;
	}

	public void setPayeeMiddleName(String payeeMiddleName) {
		this.payeeMiddleName = payeeMiddleName;
	}

	public TinType getPayerTinType() {
		return payerTinType;
	}

	public void setPayerTinType(TinType payerTinType) {
		this.payerTinType = payerTinType;
	}

	public String getPayerFirstName() {
		return payerFirstName;
	}

	public void setPayerFirstName(String payerFirstName) {
		this.payerFirstName = payerFirstName;
	}

	public String getPayerMiddleName() {
		return payerMiddleName;
	}

	public void setPayerMiddleName(String payerMiddleName) {
		this.payerMiddleName = payerMiddleName;
	}

	public String getPayerLastName() {
		return payerLastName;
	}

	public void setPayerLastName(String payerLastName) {
		this.payerLastName = payerLastName;
	}

	public String getPayerEmailAddress() {
		return payerEmailAddress;
	}

	public void setPayerEmailAddress(String payerEmailAddress) {
		this.payerEmailAddress = payerEmailAddress;
	}

	public EntityType getPayerEntityType() {
		return payerEntityType;
	}

	public void setPayerEntityType(EntityType payerEntityType) {
		this.payerEntityType = payerEntityType;
	}

	public String getPayerNameSuffix() {
		return payerNameSuffix;
	}

	public void setPayerNameSuffix(String payerNameSuffix) {
		this.payerNameSuffix = payerNameSuffix;
	}

	public EntityType getPayeeEntityType() {
		return payeeEntityType;
	}

	public void setPayeeEntityType(EntityType payeeEntityType) {
		this.payeeEntityType = payeeEntityType;
	}

	public String getPayeeNameSuffix() {
		return payeeNameSuffix;
	}

	public void setPayeeNameSuffix(String payeeNameSuffix) {
		this.payeeNameSuffix = payeeNameSuffix;
	}

	public String getUniqueRecordId() {
		return uniqueRecordId;
	}

	public void setUniqueRecordId(String uniqueRecordId) {
		this.uniqueRecordId = uniqueRecordId;
	}

	public String getPayerTinEncrypted1() {
		return payerTinEncrypted1;
	}

	public void setPayerTinEncrypted1(String payerTinEncrypted1) {
		this.payerTinEncrypted1 = payerTinEncrypted1;
	}

	public String getPayeeTinEncrypted1() {
		return payeeTinEncrypted1;
	}

	public void setPayeeTinEncrypted1(String payeeTinEncrypted1) {
		this.payeeTinEncrypted1 = payeeTinEncrypted1;
	}

	public Date getFilingDate() {
		return filingDate;
	}

	public void setFilingDate(Date filingDate) {
		this.filingDate = filingDate;
	}
}
