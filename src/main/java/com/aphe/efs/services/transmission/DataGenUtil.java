package com.aphe.efs.services.transmission;

import com.aphe.common.util.AESEncryptionUtilStatic;
import com.aphe.efs.clients.iris.schema.TY2023V10.IRTransmissionType;
import com.aphe.efs.model.transmission.BRecordSpecialDataEntries;
import com.aphe.efs.model.transmission.Record1099;
import com.aphe.efs.model.transmission.pa.PARecord1099;
import com.aphe.efs.model.transmission.pa.PARecord1099MISCNEC;
import com.aphe.efs.model.transmission.pa.PARecord1099MISCNECCorr;
import com.aphe.efs.tinm.model.EFSTINMatchRequest;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import org.apache.commons.io.FilenameUtils;
import org.beanio.BeanIOConfigurationException;
import org.beanio.BeanWriter;
import org.beanio.StreamFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Marshaller;
import javax.xml.namespace.QName;
import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
public class DataGenUtil {
	
	private static final Logger logger = LoggerFactory.getLogger(DataGenUtil.class);

	@Value("${aphe.efs.transmissionFileDir}")
	public String transmissionFileDir;

	// public static final String TRANSMISSION_FILE_DIR = "transmissionFileDir";

	public static final int MAX_A_RECORDS_IN_FILE = 90000;

	public static final int PA_MAX_RECORDS_IN_FILE = 25000;

	public String generateString(BRecordSpecialDataEntries record) throws Exception {
		StreamFactory factory = StreamFactory.newInstance();
		InputStream stream = DataGenUtil.class.getResourceAsStream("/transmission/1099specialdataentries.xml");
		try {
			factory.load(stream);
		} catch (BeanIOConfigurationException | IOException e) {
			e.printStackTrace();
			throw e;
		}

		BufferedWriter bw = null;
		StringWriter sw = null;
		try {
			sw = new StringWriter();
			bw = new BufferedWriter(new PrintWriter(sw));
			BeanWriter out = factory.createWriter("1099specialdata", bw);
			out.write(record);
			out.flush();
			out.close();
		} catch (Exception e) {
			logger.error("Error encrypting file", e);
			throw e;
		} finally {
			try {
				if (bw != null) {
					bw.close();
				}
			} catch (IOException e) {
				logger.error("Stream close error", e);
				throw e;
			}
		}
		return sw.toString();
	}



	public String generateFile(List<Record1099> records, String fileName) throws Exception {
		// String transmissionFileDir = PropertiesManager.getProperty("app", TRANSMISSION_FILE_DIR);
		StreamFactory factory = StreamFactory.newInstance();
		InputStream stream = DataGenUtil.class.getResourceAsStream("/transmission/1099transmissionmapping.xml");
		try {
			factory.load(stream);
		} catch (BeanIOConfigurationException | IOException e) {
			e.printStackTrace();
			throw e;
		}
		File dir = new File(transmissionFileDir);
		dir.mkdirs();

		String transmissionFilePath = transmissionFileDir + File.separator + fileName;

		OutputStream os = null;
		BufferedWriter bw = null;
		try {
			os = AESEncryptionUtilStatic.getEncryptorStream(transmissionFilePath);
			bw = new BufferedWriter(new PrintWriter(os));
			BeanWriter out = factory.createWriter("1099trans", bw);
			for (Record1099 record : records) {
				out.write(record);
			}
			out.flush();
			out.close();
		} catch (Exception e) {
			logger.error("Error encrypting file", e);
			throw e;
		} finally {
			try {
				if (os != null) {
					os.close();
				}
				if (bw != null) {
					bw.close();
				}
			} catch (IOException e) {
				logger.error("Stream close error", e);
				throw e;
			}
		}
		return fileName;
	}


	public String generateIRISFile(IRTransmissionType irisTransmission, String fileName) throws Exception {
		// String transmissionFileDir = PropertiesManager.getProperty("app", TRANSMISSION_FILE_DIR);
		StreamFactory factory = StreamFactory.newInstance();
		File dir = new File(transmissionFileDir);
		dir.mkdirs();
		String transmissionFilePath = transmissionFileDir + File.separator + fileName;

		OutputStream os = null;
		BufferedWriter bw = null;
		try {
			os = AESEncryptionUtilStatic.getEncryptorStream(transmissionFilePath);
//			os = new FileOutputStream(new File(transmissionFilePath));
			bw = new BufferedWriter(new PrintWriter(os));

			JAXBElement<IRTransmissionType> jaxbElement = new JAXBElement<IRTransmissionType>(new QName("", "IRTransmission"), IRTransmissionType.class, irisTransmission);
			JAXBContext jaxbContext = JAXBContext.newInstance(IRTransmissionType.class);
			Marshaller jaxbMarshaller = jaxbContext.createMarshaller();
			jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
			jaxbMarshaller.marshal(jaxbElement, bw);

		} catch (Exception e) {
			logger.error("Error encrypting file", e.getMessage());
			throw e;
		} finally {
			try {
				if (os != null) {
					os.close();
				}
				if (bw != null) {
					bw.close();
				}
			} catch (IOException e) {
				logger.error("Stream close error", e);
				throw e;
			}
		}
		return fileName;
	}



	public String generatePAFile(List<PARecord1099> records, String fileName, boolean corrections) throws Exception {
		File dir = new File(transmissionFileDir);
		dir.mkdirs();
		String transmissionFilePath = transmissionFileDir + File.separator + fileName;

		OutputStream os = null;
		BufferedWriter bw = null;
		try {
			os = AESEncryptionUtilStatic.getEncryptorStream(transmissionFilePath);
			bw = new BufferedWriter(new PrintWriter(os));

			StatefulBeanToCsv beanWriter;

			if(corrections) {
				ColumnPositionMappingStrategy<PARecord1099MISCNECCorr> mappingStrategy = new ColumnPositionMappingStrategy<>();
				mappingStrategy.setType(PARecord1099MISCNECCorr.class);
				beanWriter = new StatefulBeanToCsvBuilder(bw).withMappingStrategy(mappingStrategy).build();
			} else {
				ColumnPositionMappingStrategy<PARecord1099MISCNEC> mappingStrategy = new ColumnPositionMappingStrategy<>();
				mappingStrategy.setType(PARecord1099MISCNEC.class);
				beanWriter = new StatefulBeanToCsvBuilder(bw).withMappingStrategy(mappingStrategy).build();
			}
			beanWriter.write(records);
		} catch (Exception e) {
			logger.error("Error encrypting file", e);
			throw e;
		} finally {
			try {
				if (bw != null) {
					bw.flush();
					bw.close();
				}
				if (os != null) {
					os.flush();
					os.close();
				}
			} catch (IOException e) {
				logger.error("Stream close error", e);
				throw e;
			}
		}
		return fileName;
	}


	public String compressFile(String folderName, String fileName) {
		byte[] buffer = new byte[4096];
		try {
			String zipFilePath = folderName + File.separator + fileName + ".zip";
			String transmissionFilePath = folderName + File.separator + fileName;
			ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFilePath));
			ZipEntry ze = new ZipEntry(fileName);
			zos.putNextEntry(ze);
			File transmissionFile = new File(transmissionFilePath);
			FileInputStream in = new FileInputStream(transmissionFile);
			int len;
			while ((len = in.read(buffer)) > 0) {
				zos.write(buffer, 0, len);
			}
			in.close();
			zos.closeEntry();
			// remember close it
			zos.close();
			transmissionFile.delete();
			return zipFilePath;
		} catch (IOException ioe) {
			ioe.printStackTrace();
		}
		return null;
	}

	private static final String TIN_SEPARATOR = ";";

	public String generateTINMatchFile(List<EFSTINMatchRequest> requests, String fileName) throws Exception {
		File dir = new File(transmissionFileDir);
		dir.mkdirs();
		String transmissionFilePath = transmissionFileDir + File.separator + fileName;

		OutputStream os = null;
		BufferedWriter bw = null;
		try {
			os = AESEncryptionUtilStatic.getEncryptorStream(transmissionFilePath);
			bw = new BufferedWriter(new PrintWriter(os));
			for (EFSTINMatchRequest record : requests) {
				bw.write(getTINMatchRequestLine(record));
				bw.newLine();
			}
			bw.flush();
		} catch (Exception e) {
			logger.error("Error generating TIN Match file", e);
			throw e;
		} finally {
			try {
				if (os != null) {
					os.close();
				}
				if (bw != null) {
					bw.close();
				}
			} catch (IOException e) {
				logger.error("Stream close error", e);
				throw e;
			}
		}
		return fileName;
	}

	public String generateTINMatchResponseFile(List<EFSTINMatchRequest> requests, String fileName, HashMap<EFSTINMatchRequest, Integer> results) throws Exception {
		File dir = new File(transmissionFileDir);
		dir.mkdirs();
		String transmissionFilePath = transmissionFileDir + File.separator + fileName;

		OutputStream os = null;
		BufferedWriter bw = null;
		try {
			os = AESEncryptionUtilStatic.getEncryptorStream(transmissionFilePath);
			bw = new BufferedWriter(new PrintWriter(os));
			for (EFSTINMatchRequest record : requests) {
				EFSTINMatchRequest matchedRequest = results.keySet().stream().filter(r -> r.getId().equals(record.getId())).findFirst().orElse(null);
				if(matchedRequest != null) {
					bw.write(getTINMatchResponseLine(matchedRequest, results.get(matchedRequest)));
					bw.newLine();
				}
			}
			bw.flush();
		} catch (Exception e) {
			logger.error("Error generating TIN Match file", e);
			throw e;
		} finally {
			try {
				if (os != null) {
					os.close();
				}
				if (bw != null) {
					bw.close();
				}
			} catch (IOException e) {
				logger.error("Stream close error", e);
				throw e;
			}
		}
		return fileName;
	}

	public String getTINMatchRequestLine(EFSTINMatchRequest req) {
		StringBuffer line = new StringBuffer(100);
		line.append(
				req.getEntityType().getTinTypeCode()).append(TIN_SEPARATOR)
				.append(req.getTINPlain()).append(TIN_SEPARATOR)
				.append(req.getEntityName()).append(TIN_SEPARATOR)
				.append(req.getId()).append(TIN_SEPARATOR);
		return line.toString();
	}


	public String getTINMatchResponseLine(EFSTINMatchRequest req, int response) {
		StringBuffer line = new StringBuffer(100);
		line.append(
				req.getEntityType().getTinTypeCode()).append(TIN_SEPARATOR)
				.append(req.getTINPlain()).append(TIN_SEPARATOR)
				.append(req.getEntityName()).append(TIN_SEPARATOR)
				.append(req.getId()).append(TIN_SEPARATOR)
				.append(response).append(TIN_SEPARATOR);
		return line.toString();
	}



	public String decryptFile(String filePath) throws Exception {
		String nameWithoutExtension = FilenameUtils.removeExtension(filePath);
		String extension = FilenameUtils.getExtension(filePath);
		File decryptedFile = new File(nameWithoutExtension + "_pln" + "." + extension);

		InputStream is = null;
		try (OutputStream out = new FileOutputStream(decryptedFile)) {
			is = AESEncryptionUtilStatic.getDecryptorStream(filePath);
			byte[] buffer = new byte[4096];
			int len;
			while ((len = is.read(buffer)) > 0) {
				out.write(buffer, 0, len);
			}
			out.flush();
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (Exception e) {
				}
			}
		}
		return decryptedFile.getAbsolutePath();
	}

}
