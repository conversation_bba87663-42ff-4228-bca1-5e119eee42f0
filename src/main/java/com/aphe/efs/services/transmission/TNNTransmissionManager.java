package com.aphe.efs.services.transmission;

import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.efs.dto.FilingResponse;
import com.aphe.efs.dto.SubmissionResponse;
import com.aphe.efs.dto.TNNTransmissionRecordDTO;
import com.aphe.efs.model.EFSFiling;
import com.aphe.efs.model.EFSTNNIRISRecordIdentifier;
import com.aphe.efs.model.EFSTNNTransmissionRecord;
import com.aphe.efs.model.enums.FilingStatus;
import com.aphe.efs.model.enums.StateCode;
import com.aphe.efs.model.enums.TransmissionStatus;
import com.aphe.efs.repo.FilingDAO;
import com.aphe.efs.repo.TNNTransmissionRecordDAO;
import com.aphe.efs.services.CommonEFSManager;
import com.aphe.efs.services.EFSMapper;
import com.aphe.efs.services.filings.FilingsManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Component
public class TNNTransmissionManager extends CommonEFSManager {

	private Logger logger = LoggerFactory.getLogger(TNNTransmissionManager.class);

	@Autowired
	FilingDAO filingDAO;

	@Autowired
	FilingsManager filingsMgr;

	@Autowired
	TNNTransmissionRecordDAO transmissionRecordDAO;

	@Autowired
	EFSMapper efsMapper;

	@Value("${aphe.efs.transmissionFileDir}")
	public String transmissionFileDir;

	@Value("${aphe.efs.responseDir}")
	private String responsesFolder;



	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public void markQueued() throws Exception {
		filingsMgr.queueFilings(FilingStatus.Received, "Marking filings as queued");
	}


	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public int getQueued(String jurisdiction) {
		List<EFSFiling> filingsToBeProcessed = filingDAO.findByStatus(FilingStatus.Queued);
		filingsToBeProcessed = filingsToBeProcessed.stream().filter(f -> matchesJurisdiction(jurisdiction, f)).toList();
		return filingsToBeProcessed.size();
	}

	public boolean matchesJurisdiction(String jurisdiction, EFSFiling f) {
		if(jurisdiction == null)
			return true;
		StateCode reportingStateCode = f.getReportingStateCode();
		String stateName = reportingStateCode != null ? reportingStateCode.name() : "FD";
		return stateName.equalsIgnoreCase(jurisdiction);
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public List<EFSFiling> findByStatus(FilingStatus filingStatus) {
		List<EFSFiling> filingsToBeProcessed = filingDAO.findByStatus(filingStatus);
		return filingsToBeProcessed;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public List<EFSFiling> findByStatusIn(List<FilingStatus> filingStatuses) {
		List<EFSFiling> filingsToBeProcessed = filingDAO.findByStatusIn(filingStatuses);
		return filingsToBeProcessed;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTNNTransmissionRecord addOrUpdate(EFSTNNTransmissionRecord entity) {
		if (entity.getId() == null || entity.getId() <= 0) {
			return transmissionRecordDAO.save(entity);
		} else {
			EFSTNNTransmissionRecord existingEntity = transmissionRecordDAO.findById(entity.getId()).orElse(null);
			return transmissionRecordDAO.mergeAndSave(entity, existingEntity);
		}
	}

	/**
	 * Get transmission records by certain status
	 * @param transmissionStatuses
	 * @return
	 */
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public List<EFSTNNTransmissionRecord> getTransmissionRecords(List<TransmissionStatus> transmissionStatuses) {
		Iterable<EFSTNNTransmissionRecord> allRecords = transmissionRecordDAO.findByTransmissionStatusIn(transmissionStatuses);
		List<EFSTNNTransmissionRecord> matchedRecords = new ArrayList<EFSTNNTransmissionRecord>();
		for (EFSTNNTransmissionRecord t : allRecords) {
			if (transmissionStatuses.contains(t.getTransmissionStatus())) {
				matchedRecords.add(t);
			}
		}
		return matchedRecords;
	}

	/**
	 * Get transmission record by Id.
	 * @param transmissionRecordId
	 * @return
	 */
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTNNTransmissionRecord getTransmissionRecord(long transmissionRecordId) {
		return transmissionRecordDAO.findById(transmissionRecordId).orElse(null);
	}


	/**
	 * Delete transmission record by id
	 * @param transmissionRecordId
	 */
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public void deleteTransmissionRecord(long transmissionRecordId) throws Exception {
		EFSTNNTransmissionRecord tr = transmissionRecordDAO.findById(transmissionRecordId).orElse(null);

		if(tr == null){
			throw new RuntimeException("Record not found");
		}

		if(tr.getTransmissionStatus() != TransmissionStatus.Response_Received
				&& tr.getTransmissionStatus() != TransmissionStatus.Sent_To_Agency
		){
			List<Long> filingIds = Collections.emptyList();
			if("iris".equalsIgnoreCase(tr.getFormatType())) {
				filingIds = new ArrayList<Long>(tr.getRecordIdentifiers().stream().map(r -> r.getFilingId()).toList());
			} else {
				filingIds = new ArrayList<Long>(tr.getFilingSeqNumbers().keySet());
			}
//			filingDAO.setStatus(FilingStatus.Received, new Date(),  filingsIds);
			filingsMgr.updateFilingStatus(filingIds, FilingStatus.Queued, "Transmission Record id=" + transmissionRecordId + " deleted");
			transmissionRecordDAO.delete(tr);
		}else{
			throw new RuntimeException("Transmission record is already either accepted or rejected or sent to agency.");
		}

	}

	/**
	 * Update transmssion record status. Also provide failed filings
	 * 	allows chanding status from Generated to Sent_To_Agency;
	 *  allows Sent_To_Agecny to Response_Received
	 *
	 * @param newTr
	 */
	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public void updateTransmissionRecordStatus(TNNTransmissionRecordDTO newTr) throws Exception {
		EFSTNNTransmissionRecord tr = transmissionRecordDAO.findById(Long.parseLong(newTr.transmissionId)).orElse(null);
		if(tr == null){
			throw new RuntimeException("Record not found");
		}

		if((tr.getTransmissionStatus() == TransmissionStatus.Generated || tr.getTransmissionStatus() == TransmissionStatus.Downloaded)
				&& newTr.transmissionStatus == TransmissionStatus.Sent_To_Agency){
			markSentToAgency(tr.getId(), newTr.ackNumber);
		}else if(tr.getTransmissionStatus() == TransmissionStatus.Sent_To_Agency
				&&  newTr.transmissionStatus == TransmissionStatus.Response_Received){

			//If IRIS, do things differently...
			if("iris".equalsIgnoreCase(tr.getFormatType())) {

				if(newTr.isRejected == null){
					throw new ApheDataValidationException("isRejected", "Invalid data. isRejected can not be null.");
				}

				Map<String, List<FilingResponse>> filingResponsesBySubmissionId = newTr.filingResponses.stream().collect(
						Collectors.groupingBy(filingResponse -> filingResponse.recordId));

				List<EFSTNNIRISRecordIdentifier> recordIdentifiers = tr.getRecordIdentifiers();
				Map<String, List<EFSTNNIRISRecordIdentifier>> recordIdentifiersByGroupId = recordIdentifiers.stream().collect(
						Collectors.groupingBy(EFSTNNIRISRecordIdentifier::getGroupId));
				if(newTr.isRejected.booleanValue() == true) {
					//Create a submission responses and set all of them as Rejected...
					List<SubmissionResponse> submissionResponses = recordIdentifiersByGroupId.keySet().stream()
							.map(s -> new SubmissionResponse(s, "Rejected"))
							.collect(Collectors.toList());
					newTr.submissionResponses = submissionResponses;
				}

				Map<Long, FilingResponse> acceptedFilings = new HashMap<>();
				Map<Long, String> rejectedFilings = new HashMap<>();
				Map<Long, String> goodFilingIds = new HashMap<>();
				for(SubmissionResponse submissionResponse : newTr.submissionResponses) {
					String groupId = submissionResponse.submissionId;
					List<EFSTNNIRISRecordIdentifier> recordIdentifiersForGroup = recordIdentifiersByGroupId.get(groupId);
					if(submissionResponse.status.equalsIgnoreCase("Accepted")) {
						//Build a list of filings to be accepted, with the status message... for the case of accepted with errors.
						for (EFSTNNIRISRecordIdentifier recordIdentifier : recordIdentifiersForGroup) {
							String recordId = recordIdentifier.getRecordId();
							String uniqueRecordId = groupId + "|" + recordId;
							List<FilingResponse> filingResponsesForRecord = filingResponsesBySubmissionId.get(uniqueRecordId);
							if (filingResponsesForRecord == null || filingResponsesForRecord.size() == 0 || filingResponsesForRecord.size() > 1) {
								throw new RuntimeException("Incorrect number of filing responses found for recordId=" + uniqueRecordId);
							} else {
								FilingResponse filingResponse = filingResponsesForRecord.get(0);
								List<String> messages = filingResponse.messages;
								if (messages != null && messages.size() > 0) {
									throw new RuntimeException("Messages found for a filing that was accepted. This should not happen.");
								} else {
									acceptedFilings.put(recordIdentifier.getFilingId(), filingResponse);
								}
							}
						}
					}else if (submissionResponse.status.equalsIgnoreCase("Accepted_With_Errors")) {
						throw new RuntimeException("Accepted_With_Errors status is not supported for IRIS submissions. Review the errors.");
//						for (EFSTNNIRISRecordIdentifier recordIdentifier : recordIdentifiersForGroup) {
//							String recordId = recordIdentifier.getRecordId();
//							String uniqueRecordId = groupId + "|" + recordId;
//							List<FilingResponse> filingResponsesForRecord = filingResponsesBySubmissionId.get(uniqueRecordId);
//							if (filingResponsesForRecord == null || filingResponsesForRecord.size() == 0 || filingResponsesForRecord.size() > 1) {
//								throw new RuntimeException("Incorrect number of filing responses found for recordId=" + uniqueRecordId);
//							} else {
//								FilingResponse filingResponse = filingResponsesForRecord.get(0);
//								List<String> messages = filingResponse.messages;
//								if (messages != null && messages.size() > 0) {
//									acceptedFilings.put(recordIdentifier.getFilingId(), "Accepted with errors : " + String.join(", ", messages));
//								} else {
//									acceptedFilings.put(recordIdentifier.getFilingId(), "Accepted");
//								}
//							}
//						}
					} else if(submissionResponse.status.equalsIgnoreCase("Rejected")) {
                        for (EFSTNNIRISRecordIdentifier recordIdentifier : recordIdentifiersForGroup) {
                            String recordId = recordIdentifier.getRecordId();
                            String uniqueRecordId = groupId + "|" + recordId;
                            List<FilingResponse> filingResponsesForRecord = filingResponsesBySubmissionId.get(uniqueRecordId);
							if (filingResponsesForRecord == null || filingResponsesForRecord.size() == 0 || filingResponsesForRecord.size() > 1) {
								throw new RuntimeException("Incorrect number of filing responses found for recordId=" + uniqueRecordId);
							} else {
								FilingResponse filingResponse = filingResponsesForRecord.get(0);
								List<String> messages = filingResponse.messages;
								if (messages != null && messages.size() > 0) {
									rejectedFilings.put(recordIdentifier.getFilingId(), "Rejected : " + String.join(", ", messages));
								} else {
									goodFilingIds.put(recordIdentifier.getFilingId(), "Re-queueing filing for another attempt");
								}
							}
                        }
                    } else {
						throw new RuntimeException("Unknown status for the submission response. " + submissionResponse.status);
                    }
				}

				long totalRecordCount = acceptedFilings.size() + rejectedFilings.size() + goodFilingIds.size();
				if(totalRecordCount != recordIdentifiers.size()) {
					throw new RuntimeException("Mismatch in the number of records in the transmission and the number of records in the response.");
				}


                if(acceptedFilings.size() > 0) {
                    filingsMgr.acceptFilingsWithErrors(tr.getAckNumber(), acceptedFilings);
                }
                if(rejectedFilings.size() > 0) {
                    filingsMgr.rejectFilings(rejectedFilings);
                }
                if(goodFilingIds.size() > 0) {
                    filingsMgr.receiveFilings(goodFilingIds);
                }

                tr.setTransmissionStatus(TransmissionStatus.Response_Received);
                tr.setStatusChangeDate(new Date());
                tr = transmissionRecordDAO.save(tr);

			} else {
				if(newTr.isRejected == null){
					throw new ApheDataValidationException("isRejected", "Invalid data. isRejected can not be null.");
				}
				if(newTr.isRejected.booleanValue() == false) {
					markAccepted(tr.getId());
				} else {
					Map<Long, String> failedFilingsMap = new HashMap<>();
					//Only get the ones that have failed...
					List<FilingResponse> failedFilings = newTr.filingResponses.stream()
							.filter(filingResponse -> filingResponse.messages != null && filingResponse.messages.size() > 0)
							.collect(Collectors.toList());
					for(FilingResponse filingResponse : failedFilings) {
						failedFilingsMap.put(Long.parseLong(filingResponse.recordId), filingResponse.messages.get(0));
					}
					markRejected(tr.getId(), failedFilingsMap);
				}
			}
		}else{
			throw new RuntimeException("Unsupported status change");
		}
	}


	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTNNTransmissionRecord markSentToAgency(long transmissionRecordId, String ackNumber) throws Exception {
		EFSTNNTransmissionRecord tr = transmissionRecordDAO.findById(transmissionRecordId).orElse(null);

		List<Long> filingIds = Collections.emptyList();
		if("iris".equalsIgnoreCase(tr.getFormatType())) {
			filingIds = new ArrayList<Long>(tr.getRecordIdentifiers().stream().map(r -> r.getFilingId()).toList());
		} else {
			filingIds = new ArrayList<Long>(tr.getFilingSeqNumbers().keySet());
		}
//		List<Long> filingIds = new ArrayList<Long>(tr.getFilingSeqNumbers().keySet());
		filingsMgr.updateFilingStatus(filingIds, FilingStatus.Sent_To_Agency, "Transmission Record id=" + transmissionRecordId + " has been sent to agency");
		tr.setTransmissionStatus(TransmissionStatus.Sent_To_Agency);
		tr.setStatusChangeDate(new Date());
		tr.setAckNumber(ackNumber);
		tr = transmissionRecordDAO.save(tr);
		return tr;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public EFSTNNTransmissionRecord markAsDownloaded(long transmissionRecordId) throws Exception {
		EFSTNNTransmissionRecord tr = transmissionRecordDAO.findById(transmissionRecordId).orElse(null);
		if(tr != null && tr.getTransmissionStatus() == TransmissionStatus.Generated) {
			tr.setTransmissionStatus(TransmissionStatus.Downloaded);
			tr.setStatusChangeDate(new Date());
			tr = transmissionRecordDAO.save(tr);
			return tr;
		}
		return null;
	}


	@PreAuthorize("hasAuthority('superadmin')")
	private EFSTNNTransmissionRecord markAccepted(long transmissionRecordId) throws Exception {
		EFSTNNTransmissionRecord tr = transmissionRecordDAO.findById(transmissionRecordId).orElse(null);

		Map<Long, FilingResponse> acceptedFilings = new HashMap<>();
		tr.getFilingSeqNumbers().entrySet().stream().forEach(filingSeqNumber -> {
			acceptedFilings.put(filingSeqNumber.getKey(), new FilingResponse(Long.toString(filingSeqNumber.getValue()), new ArrayList<>()));
		});
		filingsMgr.acceptFilingsWithErrors(tr.getAckNumber(), acceptedFilings);
//		List<Long> filingIds = new ArrayList<Long>(tr.getFilingSeqNumbers().keySet());
//		filingsMgr.updateFilingStatus(filingIds, FilingStatus.Accepted, "Transmission Record id=" + transmissionRecordId + " has been accepted");

		tr.setTransmissionStatus(TransmissionStatus.Response_Received);
		tr.setStatusChangeDate(new Date());
		tr = transmissionRecordDAO.save(tr);
		return tr;
	}


	/**
	 * When a transmission has errors because of bad filings, entire file is rejected. We need mark the bad filings as rejected or to be reviewed and all other should go back to Received so that they can be tried with another
	 * transmission.
	 *
	 * @param transmissionRecordId
	 * @param failedSequenceNumbers
	 * @return
	 */
	@PreAuthorize("hasAuthority('superadmin')")
	private EFSTNNTransmissionRecord markRejected(long transmissionRecordId, Map<Long, String> failedSequenceNumbers) throws Exception {

		EFSTNNTransmissionRecord tr = transmissionRecordDAO.findById(transmissionRecordId).orElse(null);
		//Go through all transmission record filing ids.. If they are in the failed list... mark them rejected.. other wise mark them as received.
		Map<Long, String> failedFilings = new HashMap<>();
		List<Long> goodFilingIds = new ArrayList<Long>();

		Map<Long, Long> allFilingIds = tr.getFilingSeqNumbers();
		if (failedSequenceNumbers.size() > 0) {
			for (Long filingId : allFilingIds.keySet()) {
				long seqNumber = allFilingIds.get(filingId);
				if (failedSequenceNumbers.get(seqNumber) != null) {
					failedFilings.put(filingId, failedSequenceNumbers.get(seqNumber));
				} else {
					goodFilingIds.add(filingId);
				}
			}
		} else {
			goodFilingIds.addAll(allFilingIds.keySet());
		}

		if(goodFilingIds.size() > 0) {
			filingsMgr.updateFilingStatus(goodFilingIds, FilingStatus.Received, "Transmission Record id=" + transmissionRecordId + " has been rejeced. Marking good filings to be processed again.");
		}

		if(failedFilings.size() > 0) {
			filingsMgr.rejectFilings(failedFilings);
		}

		tr.setTransmissionStatus(TransmissionStatus.Response_Received);
		tr.setStatusChangeDate(new Date());
		tr = transmissionRecordDAO.save(tr);

		return tr;
	}


	@PreAuthorize("hasAuthority('superadmin')")
	public String uploadResponseFile(long transmissionRecordId, MultipartFile file) throws Exception {
		try {
			EFSTNNTransmissionRecord tr = transmissionRecordDAO.findById(transmissionRecordId).orElse(null);
			String jurisdiction = tr.getJurisdiction() == null ? tr.getId().toString() : tr.getJurisdiction();
			String responseFolder = transmissionFileDir + File.separator + responsesFolder + File.separator + jurisdiction.toLowerCase();
			File responseDir = new File(responseFolder);
			if (!responseDir.exists()) {
				responseDir.mkdirs();
			}

			Path targetLocation = Paths.get(responseFolder).toAbsolutePath().normalize();
			Path filePath = targetLocation.resolve(StringUtils.cleanPath(file.getOriginalFilename()));

			Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
			return file.getOriginalFilename();
		} catch (IOException e) {
			throw new ApheException("Could not store file.");
		}
	}


	public TNNTransmissionRecordDTO convertToDTO(EFSTNNTransmissionRecord entity) {
//		ModelMapper modelMapper = new ModelMapper();
//
//		PropertyMap<EFSTNNTransmissionRecord, TNNTransmissionRecordDTO> transmissionRecordMap = new PropertyMap<EFSTNNTransmissionRecord, TNNTransmissionRecordDTO>() {
//			protected void configure() {
//				map().setNumberOfFiligs(source.getFilingSeqNumbers().size());
//			}
//		};
//		modelMapper.addMappings(transmissionRecordMap);
//		return modelMapper.map(entity, TNNTransmissionRecordDTO.class);
		return efsMapper.toDTO(entity);
	}

	public List<TNNTransmissionRecordDTO> convertToDTOs(List<EFSTNNTransmissionRecord> entities) {
		List<TNNTransmissionRecordDTO> dtos = new ArrayList<TNNTransmissionRecordDTO>();
		for (EFSTNNTransmissionRecord entity : entities) {
			dtos.add(convertToDTO(entity));
		}
		return dtos;
	}



	@Transactional
	public List<EFSFiling> findByIds(List<Long> filingIds) {
		List<EFSFiling> filings = filingDAO.findByIdIn(filingIds);
		if(filings.size() != filingIds.size()){
			throw new RuntimeException("Not all filings were found");
		}
		return filings;
	}

}
