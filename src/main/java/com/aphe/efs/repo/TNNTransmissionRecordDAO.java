package com.aphe.efs.repo;

import com.aphe.common.data.repository.ApheCustomRepository;
import com.aphe.efs.model.EFSTNNTransmissionRecord;
import com.aphe.efs.model.enums.TransmissionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TNNTransmissionRecordDAO extends JpaRepository<EFSTNNTransmissionRecord, Long>, ApheCustomRepository<EFSTNNTransmissionRecord> {

    public List<EFSTNNTransmissionRecord> findByTransmissionStatusIn(List<TransmissionStatus> transmissionStatuses);
	
}
