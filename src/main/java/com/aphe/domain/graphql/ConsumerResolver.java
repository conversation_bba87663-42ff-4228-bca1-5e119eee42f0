package com.aphe.domain.graphql;

import com.aphe.domain.model.Consumer;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsData;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class ConsumerResolver {

	@DgsData(parentType = "Consumer", field = "name")
	public String name(DgsDataFetchingEnvironment dfe) {
		Consumer c = dfe.getSource();
		return (c.getFirstName() + " " + c.getLastName()).trim();
	}
	
}
