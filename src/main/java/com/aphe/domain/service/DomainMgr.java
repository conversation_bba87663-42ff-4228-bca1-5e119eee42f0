package com.aphe.domain.service;

import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.graphql.PagedResult;
import com.aphe.common.security.jwt.ApheAuthContext;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.EncryptionUtil;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.SSLUtil;
import com.aphe.contractor.dto.read.FilingDTO;
import com.aphe.contractor.dto.read.PayerDTO;
import com.aphe.contractor.model.enums.FilingStatus;
import com.aphe.contractor.model.enums.FilingYear;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.PayerManager;
import com.aphe.domain.dto.AddEditDomainInput;
import com.aphe.domain.dto.DomainDTO;
import com.aphe.domain.model.Domain;
import com.aphe.domain.repo.DomainRepository;
import org.hibernate.validator.constraints.ValidationErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import jakarta.persistence.EntityManager;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DomainMgr extends CommonDomainManager {

    @Autowired
    DomainRepository domainRepo;

	@Autowired
    PayerManager payerManager;

    @Autowired
    FilingManager filingManager;

    @Autowired
    ConvertUtil convertUtil;

    @Value("${aphe.auth.tokenValidationURLLocal}")
    private String tokenValidationURL;

    @Value("${aphe.auth.domainUpdatePath}")
    private String domainUpdatePath;

    @Autowired
    EntityManager em;

    @Value("${aphe.hashSalt}")
    private String hashSalt;

    @Autowired
    FilingManager filingMgr;

    @PreAuthorize("hasAuthority('superadmin')")
    public void flush() throws ApheForbiddenException {
        em.getEntityManagerFactory().getCache().evictAll();
    }

    public DomainDTO getDomain() throws ApheForbiddenException {
        Long domainId = getCurrentDomainId();
        return getDomain(domainId);
    }

    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAIN')")
    public DomainDTO getDomain(Long domainId) throws ApheForbiddenException {
        Domain d = domainRepo.findById(domainId).orElse(null);
        return convertToDTO(d);
    }

    @Transactional
    @PreAuthorize("hasPermission(#dto.id, 'DOMAIN', 'CREATE_DOMAIN')")
    public long createDomain(AddEditDomainInput dto) throws ApheForbiddenException, ApheDataValidationException {

        ValidationErrors errors = getValidationErrors(dto);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors, "Invalid input");
        }

        //Commenting this out as 'getLoggedInUserEmail' may not be an email address for SSO users.
//        if (dto.emailAddress == null) {
//            dto.emailAddress = getLoggedInUserEmail();
//        }

        Domain d = domainRepo.findById(dto.id).orElse(null);

        if (d != null) {
            throw new ApheDataValidationException("id", "Domain already exists. Call update domain to update the domain.");
        }

        Domain entity = convertToEntity(dto);
        Domain savedT1 = domainRepo.mergeAndSave(entity, d);
        return savedT1.getId();
    }

    @Transactional
    @PreAuthorize("hasPermission(#dto.id, 'DOMAIN', 'UPDATE_DOMAIN')")
    public long updateDomain(AddEditDomainInput dto) throws ApheDataValidationException, ApheForbiddenException {
        ValidationErrors errors = getValidationErrors(dto);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors, "Invalid input");
        }

        // check if there are any pending filings..
        // We are keeping this check even though we relaxed domain update code.
        boolean hasPendingFilings = filingManager.hasPendingFilings(dto.id);
        if (hasPendingFilings) {
            throw new ApheDataValidationException("name", "Domain has pending filings. You can not change account info, when your filings are being processed.");
        }

//			TODO: Detect EIN change or business name change.. address change too??
//			if (payerManager.hasNonDraftFilings(Long.toString(dto.id))) {
//				//If you are chanding anything???
//				//throw new ApheDataValidationException("id", "There are some pending filings. You can not change Payer info, when your filings are being processed.");
//			}

        Domain d = domainRepo.findById(dto.id).orElse(null);

        if (d != null && dto.domainType == null) {
            dto.domainType = d.getDomainType();
        }

        if (d != null && dto.domainType != null && !d.getDomainType().equalsIgnoreCase(dto.domainType)) {
            throw new ApheDataValidationException("domainType", "Domain type doesn't match with source entity");
        }

        Domain entity = convertToEntity(dto);

        //Delete custom attributes if the key is empty or null
        if(entity.getCustomAttributes() != null) {
            entity.setCustomAttributes(
                    entity.getCustomAttributes().stream().filter(e -> e.key != null && e.key.trim().length() > 0).collect(Collectors.toList())
            );
        }

        if(d != null) {
            boolean hasProcessedFilings = filingManager.hasProcessedFilings(dto.id);
            if(hasProcessedFilings) {
                boolean hasTINChanged = hasTINChanged(d, entity);
                if(hasTINChanged) {
                    throw new ApheDataValidationException("tin", "TIN can not be changed after you have submitted filings. Please create a new account if your TIN has changed.");
                }
            }
        }

        // set the id of new domain if the address is also being update so that we don't create a new address id.
        if (entity.getAddress() != null && d != null && d.getAddress() != null) {
            entity.getAddress().setId(d.getAddress().getId());
            entity.getAddress().setVersion(d.getAddress().getVersion());
            entity.getAddress().setAddressVerificateionState(d.getAddress().getAddressVerificateionState());
        }

        Domain savedT1 = domainRepo.mergeAndSave(entity, d);

        return savedT1.getId();
    }

    private boolean hasTINChanged(Domain existingEntity, Domain newEntity) {
        if(newEntity.getTin() != null) {
            String newTIN = newEntity.getTin() != null ? newEntity.getTin().trim() : "";
            newTIN = newTIN.replaceAll("-","");
            String existingTIN = existingEntity.getTin() != null ? existingEntity.getTin().trim() : "";
            existingTIN = existingTIN.replaceAll("-","");
            boolean maskedTINsDontMatch = !newTIN.equalsIgnoreCase(existingTIN);
            if(maskedTINsDontMatch) {
                String existingPlainTIn = existingEntity.getTinPlain();
                return !newTIN.equalsIgnoreCase(existingPlainTIn);
            }
            return false;
        }
        return false;
    }

    public boolean updateDomainOnAuth(DomainDTO dto) {
        try {
            if (PropertiesManager.isDev()) {
                SSLUtil.turnOffSslChecking();
            }

            RestTemplate restTemplate = new RestTemplate();
            restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));

            String token = ApheAuthContext.getToken();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add(JwtUtil.AUTH_HEADER_NAME, JwtUtil.TOKEN_PREFIX + token);

            String resourceUrl = tokenValidationURL + domainUpdatePath;
            HttpEntity<DomainDTO> httpEntity = new HttpEntity<>(dto, headers);
            ResponseEntity<DomainDTO> response = restTemplate.exchange(resourceUrl, HttpMethod.PUT, httpEntity, DomainDTO.class);
            if (response.getStatusCode() == HttpStatus.OK) {
                return true;
            }
        } catch (Exception e) {
            logger.error("Error creating domain on domain service", e);
            return false;
        } finally {
            try {
                // SSLUtil.turnOnSslChecking();
            } catch (Exception e) {
                logger.error("Error turning on SSL cert checking", e);
            }
        }
        return false;
    }


    private Domain convertToEntity(AddEditDomainInput dto) {
        return convertUtil.convertCreateDomainDTOToEntity(dto);
    }

    private DomainDTO convertToDTO(Domain entity) {
        return convertUtil.convertDomainToDTO(entity);
    }

    @Transactional
	public void reEncryptTin(Long domainId, EncryptionUtil encryptionUtil) {
		Domain d = domainRepo.findById(domainId).orElse(null);
		if (d == null)
			return;
		String plainTin = d.getTinPlain();
		if (plainTin != null && plainTin.length() > 0) {
            String tinEncrypted = encryptionUtil.hashString(plainTin, hashSalt);
            domainRepo.updateEncryptedTIN(d.getId(), tinEncrypted);
		}
	}


    public Map<Long, String> getDomainNames(List<Long> domainIds) {
        Iterable<Domain> domains = domainRepo.findAllById(domainIds);
        Map<Long, String> domainNames = new HashMap<>();
        for (Domain domain : domains) {
            domainNames.put(domain.getId(), domain.getDisplayName());
        }
        return domainNames;
    }

    public boolean getDuplicateDomains(Long domainId, String filingYear) {
        boolean hasDuplicateDomainsWithFilings = false;

        Domain theDomain = domainRepo.findById(domainId).orElse(null);
        if (theDomain == null) {
            return false;
        }
        String payerEncryptedTIN = theDomain.getTinEncrypted1();

        if(payerEncryptedTIN != null && payerEncryptedTIN.length() > 0) {
            List<Domain> duplicateDomains = domainRepo.findByTinEncrypted1(payerEncryptedTIN);
            //Filter out inactive domains and test domains.
            duplicateDomains = duplicateDomains.stream().filter(d -> d.getIsTestAccount() == null || d.getIsTestAccount().booleanValue() == false).collect(Collectors.toList());
            duplicateDomains = duplicateDomains.stream().filter(d -> d.getIsActive() == null || d.getIsActive().booleanValue() == true).collect(Collectors.toList());
            duplicateDomains = duplicateDomains.stream().filter(d -> d.getId() != domainId).collect(Collectors.toList());

            if (duplicateDomains.size() > 0) {
                try {
                    //see if these duplicated domains have any submitted filings for this filingYear.
                    List<FilingStatus> filingStatuses = FilingStatus.nonDraftStatuses;
                    List<FilingYear> filingYears = new ArrayList<>();
                    filingYears.add(FilingYear.getFilingYearForYear(filingYear));
                    for (Domain d : duplicateDomains) {
                        PayerDTO payerDTO = payerManager.getPayerByDomainId(d.getId());
                        PagedResult<FilingDTO> filings = filingMgr.getFilingsByPayerIdStatusAndFilingYear(payerDTO.id, filingStatuses, filingYears, 1, 0);
                        if (filings.data != null && filings.data.size() > 0) {
                            hasDuplicateDomainsWithFilings = true;
                            break;
                        }
                    }
                } catch (Exception e) {
                    logger.error("Error getting filings for domain: " + domainId, e);
                }
            }
        }
        return hasDuplicateDomainsWithFilings;
    }

}
