package com.aphe.common.config.rest;

import com.aphe.common.config.model.ConfigProperty;
import com.aphe.common.config.model.PropertyDataType;
import com.aphe.common.config.service.DynamicPropertyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST API for managing dynamic configuration properties
 * Provides endpoints for CRUD operations on configuration properties
 */
@RestController
@RequestMapping("/rs/api/config/properties")
@Tag(name = "Dynamic Properties", description = "Dynamic configuration property management")
@PreAuthorize("hasAuthority('superadmin')")
public class DynamicPropertyController {

    private static final Logger logger = LoggerFactory.getLogger(DynamicPropertyController.class);

    @Autowired
    private DynamicPropertyService dynamicPropertyService;

    @GetMapping
    @Operation(summary = "Get all properties", description = "Retrieve all active properties")
    public ResponseEntity<List<ConfigProperty>> getAllProperties() {
        try {
            List<ConfigProperty> properties = dynamicPropertyService.getAllProperties();
            return ResponseEntity.ok(properties);
        } catch (Exception e) {
            logger.error("Error retrieving properties: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/{key}")
    @Operation(summary = "Get property by key", description = "Retrieve a specific property by its key")
    public ResponseEntity<ConfigProperty> getProperty(
            @Parameter(description = "Property key") @PathVariable String key) {
        try {
            ConfigProperty property = dynamicPropertyService.getConfigProperty(key);
            if (property != null) {
                return ResponseEntity.ok(property);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error retrieving property {}: {}", key, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get property value by key
     */
    @GetMapping("/{key}/value")
    @Operation(summary = "Get property value", description = "Retrieve only the value of a specific property")
    public ResponseEntity<Map<String, Object>> getPropertyValue(
            @Parameter(description = "Property key") @PathVariable String key) {
        try {
            String value = dynamicPropertyService.getProperty(key);
            Map<String, Object> response = new HashMap<>();
            response.put("key", key);
            response.put("value", value);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error retrieving property value {}: {}", key, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping
    @Operation(summary = "Create property", description = "Create a new configuration property")
    public ResponseEntity<ConfigProperty> createProperty(@Valid @RequestBody ConfigProperty property) {
        try {
            ConfigProperty saved = dynamicPropertyService.saveProperty(property);
            return ResponseEntity.status(HttpStatus.CREATED).body(saved);
        } catch (IllegalArgumentException e) {
            logger.error("Validation error creating property: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error creating property: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    @PostMapping("/refresh")
    @Operation(summary = "Refresh properties from database", description = "Refreshes the properties from database")
    public ResponseEntity<Boolean> refreshProperties() {
        try {
            dynamicPropertyService.refreshProperties();
            return ResponseEntity.ok(true);
        } catch (IllegalArgumentException e) {
            logger.error("Validation error creating property: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error creating property: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update property value only
     */
    @PatchMapping("/{key}")
    @Operation(summary = "Update property value", description = "Update only the value of a configuration property")
    public ResponseEntity<Map<String, Object>> updatePropertyValue(
            @Parameter(description = "Property key") @PathVariable String key,
            @RequestBody Map<String, String> request) {
        try {
            String value = request.get("value");
            String modifiedBy = request.getOrDefault("modifiedBy", "api-user");
            
            boolean updated = dynamicPropertyService.updatePropertyValue(key, value, modifiedBy);
            
            Map<String, Object> response = new HashMap<>();
            response.put("key", key);
            response.put("updated", updated);
            
            if (updated) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalArgumentException e) {
            logger.error("Validation error updating property value: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating property value: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete property
     */
    @DeleteMapping("/{key}")
    @Operation(summary = "Delete property", description = "Delete a configuration property")
    public ResponseEntity<Map<String, Object>> deleteProperty(
            @Parameter(description = "Property key") @PathVariable String key) {
        try {
            boolean deleted = dynamicPropertyService.deleteProperty(key);
            
            Map<String, Object> response = new HashMap<>();
            response.put("key", key);
            response.put("deleted", deleted);
            
            if (deleted) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error deleting property: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/search")
    @Operation(summary = "Search properties", description = "Search properties by key or description")
    public ResponseEntity<List<ConfigProperty>> searchProperties(
            @Parameter(description = "Search term") @RequestParam String q) {
        try {
            List<ConfigProperty> properties = dynamicPropertyService.searchProperties(q);
            return ResponseEntity.ok(properties);
        } catch (Exception e) {
            logger.error("Error searching properties: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get property data types
     */
    @GetMapping("/data-types")
    @Operation(summary = "Get property data types", description = "Retrieve all supported property data types")
    public ResponseEntity<PropertyDataType[]> getDataTypes() {
        return ResponseEntity.ok(PropertyDataType.values());
    }

    @GetMapping("/statistics")
    @Operation(summary = "Get statistics", description = "Retrieve property statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = dynamicPropertyService.getStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            logger.error("Error retrieving statistics: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
