package com.aphe.common.config.model;

import com.aphe.common.model.BaseEntity;
import org.eclipse.persistence.annotations.Cache;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;

/**
 * Entity for storing dynamic configuration properties
 * Allows runtime modification of application properties without redeployment
 */
@Entity
@Table(name = "config")
@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true)
public class ConfigProperty extends BaseEntity {

    @NotBlank
    @Size(max = 500)
    @Column(name = "name", nullable = false, length = 500)
    private String name;

    @Column(name = "value", columnDefinition = "TEXT")
    private String value;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private PropertyDataType type = PropertyDataType.STRING;

    @Size(max = 1000)
    @Column(name = "description", length = 1000)
    private String description;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "is_encrypted", nullable = false)
    private Boolean isEncrypted = false;

    @Column(name = "requires_restart", nullable = false)
    private Boolean requiresRestart = false;

    /**
     * Last time this property was modified
     */
    @Column(name = "last_modified")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModified;

    /**
     * User who last modified this property
     */
    @Size(max = 100)
    @Column(name = "modified_by", length = 100, nullable = false)
    private String modifiedBy;

    // Constructors
    public ConfigProperty() {
        this.lastModified = new Date();
    }

    public ConfigProperty(String name, String value) {
        this();
        this.name = name;
        this.value = value;
    }

    public ConfigProperty(String name, String value, PropertyDataType type) {
        this(name, value);
        this.type = type;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
        this.lastModified = new Date();
    }

    public PropertyDataType getType() {
        return type;
    }

    public void setType(PropertyDataType type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Boolean getIsEncrypted() {
        return isEncrypted;
    }

    public void setIsEncrypted(Boolean isEncrypted) {
        this.isEncrypted = isEncrypted;
    }

    public Boolean getRequiresRestart() {
        return requiresRestart;
    }

    public void setRequiresRestart(Boolean requiresRestart) {
        this.requiresRestart = requiresRestart;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    @Override
    public String toString() {
        return "ConfigProperty{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", value='" + (isEncrypted ? "***ENCRYPTED***" : value) + '\'' +
                ", type=" + type +
                ", isActive=" + isActive +
                ", requiresRestart=" + requiresRestart +
                ", lastModified=" + lastModified +
                ", modifiedBy='" + modifiedBy + '\'' +
                '}';
    }
}
