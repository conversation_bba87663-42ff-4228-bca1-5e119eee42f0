package com.aphe.common.config.repository;

import com.aphe.common.config.model.ConfigProperty;
import com.aphe.common.config.model.PropertyDataType;
import com.aphe.common.data.repository.ApheCustomRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository for managing dynamic configuration properties
 */
@Repository
public interface ConfigPropertyRepository extends JpaRepository<ConfigProperty, Long>, ApheCustomRepository<ConfigProperty> {

    /**
     * Find a property by key
     */
    Optional<ConfigProperty> findByName(String propertyKey);

    /**
     * Find a property by key, only if active
     */
    Optional<ConfigProperty> findByNameAndIsActiveTrue(String propertyKey);

    /**
     * Find all active properties
     */
    List<ConfigProperty> findByIsActiveTrue();

    /**
     * Find all active properties ordered by key
     */
    List<ConfigProperty> findByIsActiveTrueOrderByNameAsc();

    /**
     * Find all properties by data type
     */
    List<ConfigProperty> findByTypeAndIsActiveTrue(PropertyDataType dataType);

    /**
     * Find all properties that require restart
     */
    List<ConfigProperty> findByRequiresRestartTrueAndIsActiveTrue();

    /**
     * Find all encrypted properties
     */
    List<ConfigProperty> findByIsEncryptedTrueAndIsActiveTrue();

    /**
     * Find properties modified after a specific date
     */
    @Query("SELECT cp FROM ConfigProperty cp WHERE cp.lastModified > :date AND cp.isActive = :isActive")
    List<ConfigProperty> findModifiedAfter(@Param("date") Date date, @Param("isActive") Boolean isActive);

    /**
     * Find properties by key pattern (using LIKE)
     */
    @Query("SELECT cp FROM ConfigProperty cp WHERE cp.name LIKE :pattern AND cp.isActive = :isActive")
    List<ConfigProperty> findByNamePattern(@Param("pattern") String pattern, @Param("isActive") Boolean isActive);

    /**
     * Count all active properties
     */
    long countByIsActiveTrue();

    /**
     * Update property value by key
     */
    @Modifying
    @Query("UPDATE ConfigProperty cp SET cp.value = :value, cp.lastModified = :lastModified, cp.modifiedBy = :modifiedBy WHERE cp.name = :name")
    int updatePropertyValue(@Param("name") String name,
                           @Param("value") String value, 
                           @Param("lastModified") Date lastModified, 
                           @Param("modifiedBy") String modifiedBy);

    /**
     * Activate/deactivate property by key
     */
    @Modifying
    @Query("UPDATE ConfigProperty cp SET cp.isActive = :isActive, cp.lastModified = :lastModified, cp.modifiedBy = :modifiedBy WHERE cp.name = :name")
    int updatePropertyStatus(@Param("name") String name,
                            @Param("isActive") Boolean isActive, 
                            @Param("lastModified") Date lastModified, 
                            @Param("modifiedBy") String modifiedBy);

    /**
     * Delete inactive properties older than specified date
     */
    @Modifying
    @Query("DELETE FROM ConfigProperty cp WHERE cp.isActive = :isActive AND cp.lastModified < :date")
    int deleteInactivePropertiesOlderThan(@Param("date") Date date, @Param("isActive") Boolean isActive);

    /**
     * Search properties by key or description
     */
    @Query("SELECT cp FROM ConfigProperty cp WHERE (cp.name LIKE %:searchTerm% OR cp.description LIKE %:searchTerm%) AND cp.isActive = :isActive")
    List<ConfigProperty> searchProperties(@Param("searchTerm") String searchTerm, @Param("isActive") Boolean isActive);

    /**
     * Find properties by multiple criteria
     */
    @Query("SELECT cp FROM ConfigProperty cp WHERE " +
           "(:dataType IS NULL OR cp.type = :dataType) AND " +
           "(:isEncrypted IS NULL OR cp.isEncrypted = :isEncrypted) AND " +
           "(:requiresRestart IS NULL OR cp.requiresRestart = :requiresRestart) AND " +
           "cp.isActive = :isActive " +
           "ORDER BY cp.name ASC")
    List<ConfigProperty> findByCriteria(@Param("dataType") PropertyDataType dataType,
                                       @Param("isEncrypted") Boolean isEncrypted,
                                       @Param("requiresRestart") Boolean requiresRestart,
                                       @Param("isActive") Boolean isActive);

    /**
     * Check if property exists by key
     */
    boolean existsByName(String name);

    /**
     * Get property statistics
     */
    @Query("SELECT " +
           "COUNT(cp) as total, " +
           "SUM(CASE WHEN cp.isActive = true THEN 1 ELSE 0 END) as active, " +
           "SUM(CASE WHEN cp.isEncrypted = true THEN 1 ELSE 0 END) as encrypted, " +
           "SUM(CASE WHEN cp.requiresRestart = true THEN 1 ELSE 0 END) as requiresRestart " +
           "FROM ConfigProperty cp")
    Object[] getPropertyStatistics();
}
