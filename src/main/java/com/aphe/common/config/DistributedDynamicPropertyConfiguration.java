package com.aphe.common.config;

import com.aphe.common.config.repository.ConfigPropertyRepository;
import com.aphe.common.config.service.DynamicPropertyService;
import com.aphe.common.config.service.PropertyChangeEvent;
import com.aphe.common.config.service.PropertyDeleteEvent;
import com.aphe.common.config.source.DistributedDatabasePropertySource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import jakarta.annotation.PostConstruct;

/**
 * Configuration class for distributed dynamic property management with Redis
 * Only activated when Redis is available and distributed properties are enabled
 */
@Configuration
@ConditionalOnClass({RedisTemplate.class, RedisMessageListenerContainer.class})
@ConditionalOnProperty(name = "aphe.config.dynamic.distributed.enabled", havingValue = "true", matchIfMissing = true)
public class DistributedDynamicPropertyConfiguration implements ApplicationListener<ApplicationReadyEvent> {

    private static final Logger logger = LoggerFactory.getLogger(DistributedDynamicPropertyConfiguration.class);

    @Autowired
    private ConfigurableEnvironment environment;

    @Autowired
    private ConfigPropertyRepository configPropertyRepository;

    @Autowired
    private DynamicPropertyService dynamicPropertyService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;

    private DistributedDatabasePropertySource distributedDatabasePropertySource;

    @PostConstruct
    public void init() {
        logger.info("Initializing Distributed Dynamic Property Configuration");
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        logger.info("Application ready - setting up distributed database property source");
        setupDistributedDatabasePropertySource();
    }

    /**
     * Setup distributed database property source and add it to Spring Environment
     */
    private void setupDistributedDatabasePropertySource() {
        try {
            // Create distributed database property source
            distributedDatabasePropertySource = new DistributedDatabasePropertySource(
                "databasePropertySource", 
                configPropertyRepository,
                redisTemplate,
                redisMessageListenerContainer
            );

            // Check if there's already a property source with the same name (from EnvironmentPostProcessor)
            MutablePropertySources propertySources = environment.getPropertySources();
            if (propertySources.contains("databasePropertySource")) {
                // Replace the early property source with the distributed one
                propertySources.replace("databasePropertySource", distributedDatabasePropertySource);
                logger.info("Replaced early database property source with distributed implementation");
            } else {
                // Add new property source
                propertySources.addAfter("systemProperties", distributedDatabasePropertySource);
                logger.info("Added new distributed database property source to environment");
            }

            logger.info("Distributed database property source configured with {} properties on node {}", 
                       distributedDatabasePropertySource.getCacheSize(),
                       distributedDatabasePropertySource.getNodeId());

        } catch (Exception e) {
            logger.error("Error setting up distributed database property source: {}", e.getMessage(), e);
            // Fallback to regular database property source
            logger.warn("Falling back to non-distributed property source");
        }
    }

    /**
     * Handle property change events by refreshing the distributed cache
     */
    @EventListener
    public void handlePropertyChange(PropertyChangeEvent event) {
        logger.info("Property changed: {} = {}", 
                   event.getPropertyKey(), 
                   event.getProperty().getIsEncrypted() ? "***ENCRYPTED***" : event.getNewValue());

        // The distributed cache notification is already handled in the service layer
        // This event listener is kept for backward compatibility and logging

        // Log if restart is required
        if (event.getProperty().getRequiresRestart()) {
            logger.warn("Property {} requires application restart to take effect", event.getPropertyKey());
        }
    }

    /**
     * Handle property deletion events
     */
    @EventListener
    public void handlePropertyDelete(PropertyDeleteEvent event) {
        logger.info("Property deleted: {}", event.getPropertyKey());

        // The distributed cache notification is already handled in the service layer
        // This event listener is kept for backward compatibility and logging
    }

    /**
     * Bean for accessing dynamic properties in a convenient way
     */
    @Bean
    public DynamicPropertyAccessor dynamicPropertyAccessor() {
        return new DynamicPropertyAccessor(dynamicPropertyService, environment);
    }

    /**
     * Utility class for convenient access to dynamic properties
     */
    public static class DynamicPropertyAccessor {
        
        private final DynamicPropertyService dynamicPropertyService;
        private final ConfigurableEnvironment environment;

        public DynamicPropertyAccessor(DynamicPropertyService dynamicPropertyService, 
                                     ConfigurableEnvironment environment) {
            this.dynamicPropertyService = dynamicPropertyService;
            this.environment = environment;
        }

        /**
         * Get property value with fallback to Spring Environment
         */
        public String getProperty(String key) {
            // Try dynamic property service first
            String value = dynamicPropertyService.getProperty(key);
            if (value != null) {
                return value;
            }

            // Fallback to Spring Environment (application.yml, etc.)
            return environment.getProperty(key);
        }

        /**
         * Get property value with default
         */
        public String getProperty(String key, String defaultValue) {
            String value = getProperty(key);
            return value != null ? value : defaultValue;
        }

        /**
         * Get typed property value
         */
        public <T> T getProperty(String key, Class<T> targetType) {
            // Try dynamic property service first
            T value = dynamicPropertyService.getTypedProperty(key, targetType);
            if (value != null) {
                return value;
            }

            // Fallback to Spring Environment
            return environment.getProperty(key, targetType);
        }

        /**
         * Get typed property value with default
         */
        public <T> T getProperty(String key, Class<T> targetType, T defaultValue) {
            T value = getProperty(key, targetType);
            return value != null ? value : defaultValue;
        }

        /**
         * Check if property exists
         */
        public boolean containsProperty(String key) {
            return getProperty(key) != null;
        }

        /**
         * Get integer property
         */
        public Integer getIntProperty(String key) {
            return getProperty(key, Integer.class);
        }

        /**
         * Get integer property with default
         */
        public Integer getIntProperty(String key, Integer defaultValue) {
            return getProperty(key, Integer.class, defaultValue);
        }

        /**
         * Get boolean property
         */
        public Boolean getBooleanProperty(String key) {
            return getProperty(key, Boolean.class);
        }

        /**
         * Get boolean property with default
         */
        public Boolean getBooleanProperty(String key, Boolean defaultValue) {
            return getProperty(key, Boolean.class, defaultValue);
        }

        /**
         * Get long property
         */
        public Long getLongProperty(String key) {
            return getProperty(key, Long.class);
        }

        /**
         * Get long property with default
         */
        public Long getLongProperty(String key, Long defaultValue) {
            return getProperty(key, Long.class, defaultValue);
        }

        /**
         * Get double property
         */
        public Double getDoubleProperty(String key) {
            return getProperty(key, Double.class);
        }

        /**
         * Get double property with default
         */
        public Double getDoubleProperty(String key, Double defaultValue) {
            return getProperty(key, Double.class, defaultValue);
        }

        /**
         * Refresh distributed cache across all nodes
         */
        public void refreshDistributedCache() {
            dynamicPropertyService.refreshDistributedCache();
        }
    }

    /**
     * Get the distributed database property source (for monitoring/debugging)
     */
    public DistributedDatabasePropertySource getDistributedDatabasePropertySource() {
        return distributedDatabasePropertySource;
    }
}
