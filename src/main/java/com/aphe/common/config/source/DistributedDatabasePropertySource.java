package com.aphe.common.config.source;

import com.aphe.common.config.model.ConfigProperty;
import com.aphe.common.config.repository.ConfigPropertyRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Enhanced PropertySource that loads configuration from database with Redis-based distributed cache invalidation
 * Supports multi-node environments by using Redis pub/sub for cache coordination
 */
public class DistributedDatabasePropertySource extends PropertySource<ConfigPropertyRepository> {

    private static final Logger logger = LoggerFactory.getLogger(DistributedDatabasePropertySource.class);

    private final ConfigPropertyRepository repository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisMessageListenerContainer redisMessageListenerContainer;
    
    private final Map<String, String> propertyCache = new ConcurrentHashMap<>();
    private volatile long lastCacheUpdate = 0;
    private static final long CACHE_TTL = 300000; // 5 minutes (longer since we have Redis invalidation)
    
    // Redis channels for distributed coordination
    private static final String CACHE_INVALIDATION_CHANNEL = "dynamic-properties:cache-invalidation";
    private static final String PROPERTY_CHANGE_CHANNEL = "dynamic-properties:property-change";
    
    // Unique node identifier to avoid processing our own messages
    private final String nodeId = UUID.randomUUID().toString();
    
    // Static reference for transition from early property source
    private static DistributedDatabasePropertySource instance;

    public DistributedDatabasePropertySource(String name, 
                                           ConfigPropertyRepository repository,
                                           RedisTemplate<String, Object> redisTemplate,
                                           RedisMessageListenerContainer redisMessageListenerContainer) {
        super(name);
        this.repository = repository;
        this.redisTemplate = redisTemplate;
        this.redisMessageListenerContainer = redisMessageListenerContainer;
        instance = this;
        
        setupRedisListeners();
        refreshCache();
    }

    @Override
    public Object getProperty(String name) {
        // Check cache first
        if (isCacheValid()) {
            String cachedValue = propertyCache.get(name);
            if (cachedValue != null) {
                return cachedValue;
            }
        }

        // Fallback to database lookup
        return getPropertyFromDatabase(name);
    }

    @Override
    public boolean containsProperty(String name) {
        return getProperty(name) != null;
    }

    /**
     * Get property from database
     */
    private String getPropertyFromDatabase(String name) {
        try {
            var property = repository.findByNameAndIsActiveTrue(name);
            if (property.isPresent()) {
                String value = property.get().getValue();
                propertyCache.put(name, value);
                return value;
            }

            return null;
        } catch (Exception e) {
            logger.error("Error retrieving property {} from database: {}", name, e.getMessage());
            return null;
        }
    }

    /**
     * Refresh the property cache from database
     */
    public void refreshCache() {
        try {
            logger.debug("Refreshing database property cache");
            
            List<ConfigProperty> properties = repository.findByIsActiveTrue();

            propertyCache.clear();
            for (ConfigProperty property : properties) {
                propertyCache.put(property.getPropertyKey(), property.getPropertyValue());
            }

            lastCacheUpdate = System.currentTimeMillis();
            logger.debug("Refreshed database property cache with {} properties", properties.size());
            
        } catch (Exception e) {
            logger.error("Error refreshing database property cache: {}", e.getMessage());
        }
    }

    /**
     * Refresh cache and notify other nodes via Redis
     */
    public void refreshCacheAndNotify(String reason) {
        refreshCache();
        notifyOtherNodes("CACHE_REFRESH", reason);
    }

    /**
     * Invalidate specific property and notify other nodes
     */
    public void invalidateProperty(String propertyKey) {
        propertyCache.remove(propertyKey);
        notifyOtherNodes("PROPERTY_INVALIDATE", propertyKey);
        logger.debug("Invalidated property: {}", propertyKey);
    }

    /**
     * Notify other nodes about cache changes via Redis pub/sub
     */
    private void notifyOtherNodes(String action, String data) {
        try {
            Map<String, Object> message = Map.of(
                "nodeId", nodeId,
                "action", action,
                "data", data,
                "timestamp", System.currentTimeMillis()
            );
            
            redisTemplate.convertAndSend(CACHE_INVALIDATION_CHANNEL, message);
            logger.debug("Sent cache invalidation message: {} - {}", action, data);
            
        } catch (Exception e) {
            logger.error("Error sending cache invalidation message: {}", e.getMessage());
        }
    }

    /**
     * Setup Redis listeners for distributed cache coordination
     */
    private void setupRedisListeners() {
        try {
            // Cache invalidation listener
            MessageListenerAdapter cacheInvalidationListener = new MessageListenerAdapter(this, "handleCacheInvalidationMessage");
            redisMessageListenerContainer.addMessageListener(cacheInvalidationListener, new ChannelTopic(CACHE_INVALIDATION_CHANNEL));
            
            // Property change listener
            MessageListenerAdapter propertyChangeListener = new MessageListenerAdapter(this, "handlePropertyChangeMessage");
            redisMessageListenerContainer.addMessageListener(propertyChangeListener, new ChannelTopic(PROPERTY_CHANGE_CHANNEL));
            
            logger.info("Redis listeners setup for distributed cache invalidation");
            
        } catch (Exception e) {
            logger.error("Error setting up Redis listeners: {}", e.getMessage());
        }
    }

    /**
     * Handle cache invalidation messages from other nodes
     */
    @SuppressWarnings("unchecked")
    public void handleCacheInvalidationMessage(Object message) {
        try {
            if (message instanceof Map) {
                Map<String, Object> msg = (Map<String, Object>) message;
                String senderNodeId = (String) msg.get("nodeId");
                
                // Ignore messages from ourselves
                if (nodeId.equals(senderNodeId)) {
                    return;
                }
                
                String action = (String) msg.get("action");
                String data = (String) msg.get("data");
                
                logger.debug("Received cache invalidation message from node {}: {} - {}", senderNodeId, action, data);
                
                switch (action) {
                    case "CACHE_REFRESH":
                        refreshCache();
                        logger.info("Cache refreshed due to notification from node {}: {}", senderNodeId, data);
                        break;
                        
                    case "PROPERTY_INVALIDATE":
                        propertyCache.remove(data);
                        logger.debug("Invalidated property {} due to notification from node {}", data, senderNodeId);
                        break;
                        
                    default:
                        logger.debug("Unknown cache invalidation action: {}", action);
                }
            }
        } catch (Exception e) {
            logger.error("Error handling cache invalidation message: {}", e.getMessage());
        }
    }

    /**
     * Handle property change messages from other nodes
     */
    @SuppressWarnings("unchecked")
    public void handlePropertyChangeMessage(Object message) {
        try {
            if (message instanceof Map) {
                Map<String, Object> msg = (Map<String, Object>) message;
                String senderNodeId = (String) msg.get("nodeId");
                
                // Ignore messages from ourselves
                if (nodeId.equals(senderNodeId)) {
                    return;
                }
                
                String propertyKey = (String) msg.get("propertyKey");
                String newValue = (String) msg.get("newValue");
                
                logger.debug("Received property change message from node {}: {} = {}", senderNodeId, propertyKey, newValue);
                
                // Update cache with new value
                if (newValue != null) {
                    propertyCache.put(propertyKey, newValue);
                } else {
                    propertyCache.remove(propertyKey);
                }
                
                logger.debug("Updated cached property {} due to notification from node {}", propertyKey, senderNodeId);
            }
        } catch (Exception e) {
            logger.error("Error handling property change message: {}", e.getMessage());
        }
    }

    /**
     * Notify other nodes about specific property changes
     */
    public void notifyPropertyChange(String propertyKey, String newValue) {
        try {
            Map<String, Object> message = Map.of(
                "nodeId", nodeId,
                "propertyKey", propertyKey,
                "newValue", newValue != null ? newValue : "",
                "timestamp", System.currentTimeMillis()
            );
            
            redisTemplate.convertAndSend(PROPERTY_CHANGE_CHANNEL, message);
            logger.debug("Sent property change notification: {} = {}", propertyKey, newValue);
            
        } catch (Exception e) {
            logger.error("Error sending property change notification: {}", e.getMessage());
        }
    }

    /**
     * Check if cache is still valid
     */
    private boolean isCacheValid() {
        return (System.currentTimeMillis() - lastCacheUpdate) < CACHE_TTL;
    }

    /**
     * Get all property names in cache
     */
    public String[] getPropertyNames() {
        if (!isCacheValid()) {
            refreshCache();
        }
        return propertyCache.keySet().toArray(new String[0]);
    }

    /**
     * Get cache size
     */
    public int getCacheSize() {
        return propertyCache.size();
    }

    /**
     * Get last cache update time
     */
    public long getLastCacheUpdate() {
        return lastCacheUpdate;
    }

    /**
     * Clear cache (force refresh on next access)
     */
    public void clearCache() {
        propertyCache.clear();
        lastCacheUpdate = 0;
        logger.debug("Cleared database property cache");
    }

    /**
     * Get the current instance
     */
    public static DistributedDatabasePropertySource getInstance() {
        return instance;
    }

    /**
     * Merge properties from early property source
     */
    public void mergeEarlyProperties(Map<String, String> earlyProperties) {
        if (earlyProperties != null && !earlyProperties.isEmpty()) {
            logger.info("Merging {} early properties into distributed database property source", earlyProperties.size());
            refreshCache();
        }
    }

    /**
     * Get node ID for debugging
     */
    public String getNodeId() {
        return nodeId;
    }

    @PreDestroy
    public void cleanup() {
        logger.info("Cleaning up distributed database property source for node {}", nodeId);
    }

    @Override
    public String toString() {
        return "DistributedDatabasePropertySource{" +
                "name='" + getName() + '\'' +
                ", nodeId='" + nodeId + '\'' +
                ", cacheSize=" + propertyCache.size() +
                ", lastCacheUpdate=" + lastCacheUpdate +
                '}';
    }
}
