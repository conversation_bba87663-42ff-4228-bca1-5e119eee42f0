package com.aphe.common.config.source;

import com.aphe.common.config.model.ConfigProperty;
import com.aphe.common.config.repository.ConfigPropertyRepository;
import com.aphe.common.util.EncryptionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.PropertySource;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.List;

public class DatabasePropertySource extends PropertySource<ConfigPropertyRepository> {

    private static final Logger logger = LoggerFactory.getLogger(DatabasePropertySource.class);

    private final ConfigPropertyRepository repository;
    private final StringRedisTemplate redisTemplate;
    private final String appName;
    private final String redisKeyPrefix;
    private static final Duration TTL_DURATION = Duration.ofMinutes(30);

    public DatabasePropertySource(String name, String appName, ConfigPropertyRepository repository, StringRedisTemplate redisTemplate) {
        super(name);
        this.repository = repository;
        this.redisTemplate = redisTemplate;
        this.appName = appName;
        this.redisKeyPrefix = "config:" + appName + ":";
        refreshCache();
    }

    @Override
    public Object getProperty(String name) {
        String redisKey = redisKeyPrefix + name;

        // Try Redis cache first
        String cachedValue = redisTemplate.opsForValue().get(redisKey);
        if (cachedValue != null) {
            return cachedValue;
        }

        // Fallback to database
        return getPropertyFromDatabase(name);
    }

    @Override
    public boolean containsProperty(String name) {
        return getProperty(name) != null;
    }

    private String getPropertyFromDatabase(String name) {
        try {
            var property = repository.findByNameAndIsActiveTrue(name);
            if (property.isPresent()) {
                String value = property.get().getValue();

                if (property.get().getIsEncrypted()) {
                    value = new EncryptionUtil().decryptWithDefault305(value);
                }
                if (value != null) {
                    redisTemplate.opsForValue().set(redisKeyPrefix + name, value, TTL_DURATION);
                }
                return value;
            }
        } catch (Exception e) {
            logger.error("Error retrieving property {} from database: {}", name, e.getMessage());
        }
        return null;
    }

    public void refreshCache() {
        try {
            logger.debug("Refreshing Redis-backed config cache for app '{}'", appName);

            //First clear the cache.
            clearCache();

            List<ConfigProperty> properties = repository.findByIsActiveTrue();
            for (ConfigProperty property : properties) {
                String value = property.getValue();
                if (property.getIsEncrypted()) {
                    //We may not be able to encrypt all the time. So, be ready to handle null values.
                    value = new EncryptionUtil().decryptWithDefault305(value);
                }
                if (value != null) {
                    redisTemplate.opsForValue().set(redisKeyPrefix + property.getName(), value, TTL_DURATION);
                }
            }

            logger.debug("Refreshed Redis config cache with {} properties for app '{}'", properties.size(), appName);
        } catch (Exception e) {
            logger.error("Error refreshing Redis config cache: {}", e.getMessage());
        }
    }

    public void clearCache() {
        try {
            var keys = redisTemplate.keys(redisKeyPrefix + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                logger.debug("Cleared Redis config cache ({} entries) for app '{}'", keys.size(), appName);
            }
        } catch (Exception e) {
            logger.error("Error clearing Redis config cache: {}", e.getMessage());
        }
    }

    /**
     * Get number of cached properties currently in Redis
     */
    public int getCacheSize() {
        try {
            var keys = redisTemplate.keys(redisKeyPrefix + "*");
            return (keys != null) ? keys.size() : 0;
        } catch (Exception e) {
            logger.error("Error retrieving Redis config cache size: {}", e.getMessage());
            return -1;
        }
    }

    @Override
    public String toString() {
        return "DatabasePropertySource{" +
                "name='" + getName() + '\'' +
                ", appName='" + appName + '\'' +
                ", redisKeyPrefix='" + redisKeyPrefix + '\'' +
                '}';
    }
}