package com.aphe.common.config.source;

import com.aphe.common.config.model.ConfigProperty;
import com.aphe.common.config.repository.ConfigPropertyRepository;
import com.aphe.common.util.EncryptionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.PropertySource;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Custom PropertySource that loads configuration from database
 * Integrates with Spring's Environment to provide dynamic properties
 * Loading order: cache -> database -> fallback to application properties
 */
public class DatabasePropertySource extends PropertySource<ConfigPropertyRepository> {

    private static final Logger logger = LoggerFactory.getLogger(DatabasePropertySource.class);

    private final ConfigPropertyRepository repository;
    private final Map<String, String> propertyCache = new ConcurrentHashMap<>();
    private volatile long lastCacheUpdate = 0;
    private static final long CACHE_TTL = 30*60*1000; // 30 minutes

    public DatabasePropertySource(String name, ConfigPropertyRepository repository) {
        super(name);
        this.repository = repository;
        refreshCache();
    }

    @Override
    public Object getProperty(String name) {
        // Check cache first
        if (isCacheValid()) {
            String cachedValue = propertyCache.get(name);
            if (cachedValue != null) {
                return cachedValue;
            }
        }

        // Fallback to database lookup
        return getPropertyFromDatabase(name);
    }

    @Override
    public boolean containsProperty(String name) {
        return getProperty(name) != null;
    }

    /**
     * Get property from database
     */
    private String getPropertyFromDatabase(String name) {
        try {
            var property = repository.findByNameAndIsActiveTrue(name);
            if (property.isPresent()) {
                String value = property.get().getValue();


                //If this is an encrypted value, decrypt it.
                if (property.get().getIsEncrypted()) {
                    value = new EncryptionUtil().decryptWithDefault305(value);
                }

                propertyCache.put(name, value);
                return value;
            }

            return null;
        } catch (Exception e) {
            logger.error("Error retrieving property {} from database: {}", name, e.getMessage());
            return null;
        }
    }

    /**
     * Refresh the property cache from database
     */
    public void refreshCache() {
        try {
            logger.debug("Refreshing database property cache");

            List<ConfigProperty> properties = repository.findByIsActiveTrue();

            propertyCache.clear();
            for (ConfigProperty property : properties) {
                //If this is an encrypted value, decrypt it and put it in cache.
                if (property.getIsEncrypted()) {
                    String decryptedValue = new EncryptionUtil().decryptWithDefault305(property.getValue());
                    propertyCache.put(property.getName(), decryptedValue);
                } else {
                    propertyCache.put(property.getName(), property.getValue());
                }
            }

            lastCacheUpdate = System.currentTimeMillis();
            logger.debug("Refreshed database property cache with {} properties", properties.size());

        } catch (Exception e) {
            logger.error("Error refreshing database property cache: {}", e.getMessage());
        }
    }

    /**
     * Check if cache is still valid
     */
    private boolean isCacheValid() {
        return (System.currentTimeMillis() - lastCacheUpdate) < CACHE_TTL;
    }

    /**
     * Get all property names in cache
     */
    public String[] getPropertyNames() {
        if (!isCacheValid()) {
            refreshCache();
        }
        return propertyCache.keySet().toArray(new String[0]);
    }

    /**
     * Get cache size
     */
    public int getCacheSize() {
        return propertyCache.size();
    }

    /**
     * Get last cache update time
     */
    public long getLastCacheUpdate() {
        return lastCacheUpdate;
    }

    /**
     * Clear cache (force refresh on next access)
     */
    public void clearCache() {
        propertyCache.clear();
        lastCacheUpdate = 0;
        logger.debug("Cleared database property cache");
    }

    @Override
    public String toString() {
        return "DatabasePropertySource{" +
                "name='" + getName() + '\'' +
                ", cacheSize=" + propertyCache.size() +
                ", lastCacheUpdate=" + lastCacheUpdate +
                '}';
    }
}
