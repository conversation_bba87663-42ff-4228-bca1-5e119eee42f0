package com.aphe.common.config;

import com.aphe.common.config.repository.ConfigPropertyRepository;
import com.aphe.common.config.service.PropertyChangeEvent;
import com.aphe.common.config.service.PropertyDeleteEvent;
import com.aphe.common.config.source.DatabasePropertySource;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * Configuration class for dynamic property management
 * Integrates database-backed properties with Spring's Environment
 */
@Configuration
public class DynamicPropertyConfiguration implements ApplicationListener<ApplicationReadyEvent> {

    private static final Logger logger = LoggerFactory.getLogger(DynamicPropertyConfiguration.class);

    @Autowired
    private ConfigurableEnvironment environment;

    @Autowired
    private ConfigPropertyRepository configPropertyRepository;

    @Autowired private StringRedisTemplate redisTemplate;

    @Value("${appName}")
    private String appName;

    private DatabasePropertySource databasePropertySource;

    @PostConstruct
    public void init() {
        logger.info("Initializing Dynamic Property Configuration");
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        logger.info("Application ready - setting up full database property source");
        setupDatabasePropertySource();
    }

    /**
     * Setup database property source and add it to Spring Environment
     * Loading order: database cache -> database -> application properties
     */
    private void setupDatabasePropertySource() {
        try {
            // Create database property source
            databasePropertySource = new DatabasePropertySource(
                "databasePropertySource",
                appName,
                configPropertyRepository, redisTemplate
            );

            // Check if there's already a property source with the same name (from EnvironmentPostProcessor)
            MutablePropertySources propertySources = environment.getPropertySources();
            // Add new property source
            propertySources.addAfter("systemProperties", databasePropertySource);
            logger.info("Added new database property source to environment");

            logger.info("Database property source configured with {} properties",
                       databasePropertySource.getCacheSize());

        } catch (Exception e) {
            logger.error("Error setting up database property source: {}", e.getMessage(), e);
        }
    }

    /**
     * Handle property change events by refreshing the property source cache
     */
    @EventListener
    public void handlePropertyChange(PropertyChangeEvent event) {
        logger.info("Property changed: {} = {}", 
                   event.getPropertyKey(), 
                   event.getProperty().getIsEncrypted() ? "***ENCRYPTED***" : event.getNewValue());

        // Refresh database property source cache
        if (databasePropertySource != null) {
            databasePropertySource.refreshCache();
        }

        // Log if restart is required
        if (event.getProperty().getRequiresRestart()) {
            logger.warn("Property {} requires application restart to take effect", event.getPropertyKey());
        }
    }

    /**
     * Handle property deletion events
     */
    @EventListener
    public void handlePropertyDelete(PropertyDeleteEvent event) {
        logger.info("Property deleted: {}", event.getPropertyKey());

        // Refresh database property source cache
        if (databasePropertySource != null) {
            databasePropertySource.refreshCache();
        }
    }

}
