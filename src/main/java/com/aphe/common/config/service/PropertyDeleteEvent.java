package com.aphe.common.config.service;

import org.springframework.context.ApplicationEvent;

/**
 * Event published when a configuration property is deleted
 */
public class PropertyDeleteEvent extends ApplicationEvent {

    private final String propertyKey;

    public PropertyDeleteEvent(Object source, String propertyKey) {
        super(source);
        this.propertyKey = propertyKey;
    }

    public String getPropertyKey() {
        return propertyKey;
    }

    @Override
    public String toString() {
        return "PropertyDeleteEvent{" +
                "propertyKey='" + propertyKey + '\'' +
                '}';
    }
}
