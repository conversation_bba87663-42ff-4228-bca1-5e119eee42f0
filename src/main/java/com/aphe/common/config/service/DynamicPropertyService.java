package com.aphe.common.config.service;

import com.aphe.common.config.model.ConfigProperty;
import com.aphe.common.config.model.PropertyDataType;
import com.aphe.common.config.repository.ConfigPropertyRepository;
import com.aphe.common.config.source.DistributedDatabasePropertySource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Service for managing dynamic configuration properties
 * Provides CRUD operations and property validation
 */
@Service
@Transactional
public class DynamicPropertyService {

    private static final Logger logger = LoggerFactory.getLogger(DynamicPropertyService.class);

    @Autowired
    private ConfigPropertyRepository configPropertyRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public String getProperty(String key) {
        ConfigProperty property = getConfigProperty(key);
        return property != null ? property.getName() : null;
    }

    public String getProperty(String key, String defaultValue) {
        String value = getProperty(key);
        return value != null ? value : defaultValue;
    }

    /**
     * Get typed property value
     */
    @SuppressWarnings("unchecked")
    public <T> T getTypedProperty(String key, Class<T> type) {
        ConfigProperty property = getConfigProperty(key);
        if (property == null) {
            return null;
        }

        try {
            Object convertedValue = property.getType().convertValue(property.getValue());
            return (T) convertedValue;
        } catch (Exception e) {
            logger.error("Error converting property {} to type {}: {}", key, type.getSimpleName(), e.getMessage());
            return null;
        }
    }

    public ConfigProperty getConfigProperty(String key) {
        Optional<ConfigProperty> property = configPropertyRepository.findByNameAndIsActiveTrue(key);
        if (property.isPresent()) {
            return property.get();
        }
        return null;
    }

    public ConfigProperty saveProperty(ConfigProperty property) {
        validateProperty(property);
        
        // Set modification metadata
        property.setLastModified(new Date());
        if (property.getModifiedBy() == null) {
            property.setModifiedBy("system");
        }

        ConfigProperty saved = configPropertyRepository.save(property);
        
        // Publish property change event
        eventPublisher.publishEvent(new PropertyChangeEvent(this, saved));

        // Notify other nodes about the property change via Redis
        notifyDistributedCachePropertyChange(saved.getName(), saved.getValue());

        logger.info("Saved property: {} = {}",
                   property.getName(),
                   property.getIsEncrypted() ? "***ENCRYPTED***" : property.getValue());

        return saved;
    }

    public boolean updatePropertyValue(String key, String value, String modifiedBy) {
        Optional<ConfigProperty> existing = configPropertyRepository.findByName(key);
        
        if (existing.isPresent()) {
            ConfigProperty property = existing.get();
            validatePropertyValue(property, value);
            
            property.setValue(value);
            property.setLastModified(new Date());
            property.setModifiedBy(modifiedBy);
            
            saveProperty(property);
            return true;
        }
        
        return false;
    }

    public ConfigProperty createProperty(String key, String value, PropertyDataType dataType, String description) {
        if (configPropertyRepository.existsByName(key)) {
            throw new IllegalArgumentException("Property already exists: " + key);
        }

        ConfigProperty property = new ConfigProperty(key, value, dataType);
        property.setDescription(description);
        property.setModifiedBy("system");
        
        return saveProperty(property);
    }

    public boolean deleteProperty(String key) {
        Optional<ConfigProperty> property = configPropertyRepository.findByName(key);
        
        if (property.isPresent()) {
            configPropertyRepository.delete(property.get());
            
            // Publish property deletion event
            eventPublisher.publishEvent(new PropertyDeleteEvent(this, key));

            // Notify other nodes about the property deletion via Redis
            notifyDistributedCachePropertyChange(key, null);

            logger.info("Deleted property: {}", key);
            return true;
        }
        
        return false;
    }

    public void refreshProperties() {
        // Fire a dummy change event, that forces the DatabasePropertySource to refresh its cache
        ConfigProperty configProperty = new ConfigProperty("RefreshEvent", "RefreshEvent");
        eventPublisher.publishEvent(new PropertyChangeEvent(this, configProperty));
    }

    public List<ConfigProperty> getAllProperties() {
        return configPropertyRepository.findByIsActiveTrue();
    }

    public List<ConfigProperty> searchProperties(String searchTerm) {
        return configPropertyRepository.searchProperties(searchTerm, true);
    }

    public List<ConfigProperty> findByKeyPattern(String pattern) {
        return configPropertyRepository.findByNamePattern(pattern, true);
    }

    public List<ConfigProperty> findModifiedAfter(Date date) {
        return configPropertyRepository.findModifiedAfter(date, true);
    }

    public List<ConfigProperty> findByCriteria(PropertyDataType dataType, Boolean isEncrypted, Boolean requiresRestart) {
        return configPropertyRepository.findByCriteria(dataType, isEncrypted, requiresRestart, true);
    }

    private void validateProperty(ConfigProperty property) {
        if (property.getName() == null || property.getValue().trim().isEmpty()) {
            throw new IllegalArgumentException("Property key cannot be empty");
        }

        validatePropertyValue(property, property.getValue());
    }

    private void validatePropertyValue(ConfigProperty property, String value) {
        if (value == null || value.trim().isEmpty()) {
            return; // Allow empty values
        }

        // Validate against data type
        if (!property.getType().isValid(value)) {
            throw new IllegalArgumentException("Invalid value for data type " + property.getType().getDisplayName() + ": " + value);
        }
    }

    public Map<String, Object> getStatistics() {
        Object[] stats = configPropertyRepository.getPropertyStatistics();

        if(stats != null && stats.length >= 1) {
            stats = (Object[]) stats[0];
        }

        Map<String, Object> result = new HashMap<>();

        if (stats != null && stats.length >= 4) {
            result.put("total", stats[0]);
            result.put("active", stats[1]);
            result.put("encrypted", stats[2]);
            result.put("requiresRestart", stats[3]);
        }
        return result;
    }

    /**
     * Notify other nodes about property changes via distributed cache
     */
    private void notifyDistributedCachePropertyChange(String propertyKey, String newValue) {
        try {
            DistributedDatabasePropertySource distributedSource = DistributedDatabasePropertySource.getInstance();
            if (distributedSource != null) {
                if (newValue != null) {
                    // Property was updated
                    distributedSource.notifyPropertyChange(propertyKey, newValue);
                } else {
                    // Property was deleted
                    distributedSource.invalidateProperty(propertyKey);
                }
            }
        } catch (Exception e) {
            logger.error("Error notifying distributed cache about property change: {}", e.getMessage());
        }
    }

    /**
     * Refresh distributed cache across all nodes
     */
    public void refreshDistributedCache() {
        try {
            DistributedDatabasePropertySource distributedSource = DistributedDatabasePropertySource.getInstance();
            if (distributedSource != null) {
                distributedSource.refreshCacheAndNotify("Manual refresh requested");
                logger.info("Distributed cache refresh initiated");
            } else {
                logger.debug("Distributed property source not available, using local refresh");
                refreshProperties();
            }
        } catch (Exception e) {
            logger.error("Error refreshing distributed cache: {}", e.getMessage());
        }
    }

}
