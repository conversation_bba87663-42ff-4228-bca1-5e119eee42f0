package com.aphe.common.security.jwt;

import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.SSLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

@Component
public class TokenValidationUtil {

	@Value("${aphe.auth.tokenValidationURLLocal}")
	private String tokenValidationURL;

	@Value("${aphe.auth.tokenValidationPath}")
	private String tokenValidationPath;
	
	protected Logger logger = LoggerFactory.getLogger(getClass());


	public boolean validateTokenRemote(String token) throws Exception {
		try {
			
			if (PropertiesManager.isDev()) {
				SSLUtil.turnOffSslChecking();
			}
			
			RestTemplate restTemplate = new RestTemplate();
			restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));
			 
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);

			String resourceUrl = tokenValidationURL + tokenValidationPath;
			HttpEntity<String> validateToken = new HttpEntity<>(token, headers);
			
			
			ResponseEntity<String> response = restTemplate.exchange(resourceUrl, HttpMethod.PUT, validateToken, String.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return true;
			}
		} catch (Exception e) {
			logger.error("Error validating token remotely. " + e.getMessage(), e);
			return false;
		} finally {
			try {
				//SSLUtil.turnOnSslChecking();
			} catch (Exception e) {
				logger.error("Error turning on SSL cert checking", e);
			}
		}
		return false;
	}

}