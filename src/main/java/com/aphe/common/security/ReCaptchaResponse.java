package com.aphe.common.security;

import com.aphe.common.util.ArrayUtil;

import java.util.Date;
import java.util.List;

public class ReCaptchaResponse {

	public boolean success;

	public double score;

	public String action;

	public Date challenge_ts;

	public String hostname;

	public List<Long> error_codes;

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append("action=").append(action).append(" score=").append(score).append(" success=").append(success).append(" challenge_ts=").append(challenge_ts).append(" hostname=")
				.append(hostname);
		if(!success) {
			sb.append(" error_codes=").append(ArrayUtil.listToString(error_codes));
		}
		return sb.toString();
	}

}
