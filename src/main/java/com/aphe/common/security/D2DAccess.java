package com.aphe.common.security;

public class D2DAccess {

	//TODO: Should this be an enum, similar to relation type in D2DRelation.
	private String relType;
	private Long sourceDomainId;
	private Long sourceSubEntityId;
	private Long targetDomainId;
	private Long targetSubEntityId;

	public String getRelType() {
		return relType;
	}

	public void setRelType(String relType) {
		this.relType = relType;
	}

	public Long getSourceDomainId() {
		return sourceDomainId;
	}

	public void setSourceDomainId(Long sourceDomainId) {
		this.sourceDomainId = sourceDomainId;
	}

	public Long getSourceSubEntityId() {
		return sourceSubEntityId;
	}

	public void setSourceSubEntityId(Long sourceSubEntityId) {
		this.sourceSubEntityId = sourceSubEntityId;
	}

	public Long getTargetDomainId() {
		return targetDomainId;
	}

	public void setTargetDomainId(Long targetDomainId) {
		this.targetDomainId = targetDomainId;
	}

	public Long getTargetSubEntityId() {
		return targetSubEntityId;
	}

	public void setTargetSubEntityId(Long targetSubEntityId) {
		this.targetSubEntityId = targetSubEntityId;
	}
}
