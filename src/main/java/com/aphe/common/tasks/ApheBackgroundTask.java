package com.aphe.common.tasks;

import com.aphe.common.util.PropertiesManager;
import org.jetbrains.annotations.NotNull;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public abstract class ApheBackgroundTask {
    protected Logger logger = LoggerFactory.getLogger(ApheBackgroundTask.class);

    public static final String chunkPrefix = "chunk:";


    @Autowired
    Environment environment;;

    @Autowired
    ApheTaskStatusStore resultStore;

    @Autowired
    JobScheduler jobScheduler;

    protected String jobName;
    protected int chunkSize = 0;


    public abstract void startTask();
    public abstract List<String> getUnitsOfWork();
    public abstract ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork);
    public abstract List<String> getAdditionalEmailColumns();
    public abstract void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML);

    public String getNodeId () {
        String nodeId;
        try {
            nodeId = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            nodeId = "unknown-node";
        }
        return nodeId;
    }

    /**
     * Main entry point called by the actual tasks.
     */
    protected void executeTask() {
        String dateTime = new Date().toString().replace(" ", "-").replace(":", "-");
        String runId = jobName + ":run:" + UUID.randomUUID() + ":" + dateTime;

        boolean pauseTasks = isPauseTasks(jobName);
        if(pauseTasks) {
            onExecutionFailed(runId, "Skipping task because either this job or all jobs are paused. Please check the config properties to see if it is paused.");
            return;
        }

        try {
            String nodeId = getNodeId();
            String logContext = String.format("job_name=%s run_id=%s, node_id=%s", jobName, runId, nodeId);

            long startTime = System.currentTimeMillis();
            resultStore.setJobStartTime(jobName, runId, startTime);

            validateConfig();

            long unitsOfWorkStartTime = System.currentTimeMillis();
            List<String> unitsOfWork = getUnitsOfWork();
            long queryTime = System.currentTimeMillis() - unitsOfWorkStartTime;
            logger.info("{} - Retrieved work units. work_units={} time={}", logContext, unitsOfWork.size(), queryTime);

            if (unitsOfWork.isEmpty()) {
                logger.info("{} - No work to perform. Exiting.", logContext);
                onExecutionCompleted(runId);
                return;
            }

            int totalChunks = (unitsOfWork.size() + chunkSize - 1) / chunkSize;
            resultStore.setExpectedChunks(jobName, runId, totalChunks);
            logger.info("{} - Created chunks. chunks={} chunk_size={}", logContext, totalChunks, chunkSize);


            int chunkIndex = 0;
            for (int i = 0; i < unitsOfWork.size(); i += chunkSize) {
                // Create a proper serializable ArrayList instead of SubList
                List<String> chunk = new ArrayList<>(unitsOfWork.subList(i, Math.min(i + chunkSize, unitsOfWork.size())));
                chunkIndex++;
                logger.info("{} - Enqueueing chunk {}/{}. chunk_size={} ids={}", logContext, chunkIndex, totalChunks, chunk.size(), chunk);

                try {
                    final int chunkId = chunkIndex;
                    jobScheduler.enqueue(() -> executeChunk(runId, chunkId, chunk));
                    logger.debug("{} - Successfully enqueued chunk. chunk_id={}", logContext, chunkIndex);
                } catch (Exception e) {
                    logger.error("{} - FAILED to enqueue chunk. chunk_id={} messasge={}", logContext, chunkIndex, e.getMessage(), e);
                    throw e; // Re-throw to fail the orchestration
                }
            }

            logger.info("{} - Done enqueueing. work_units={} chunks={} chunk_size={}", logContext, unitsOfWork.size(), totalChunks, chunkSize);
        } catch (Exception e) {
            logger.error("{} - FAILED to execute task. message={}", jobName, e.getMessage(), e);
            onExecutionFailed(runId, e.getMessage());
        }
    }


    public void executeChunk(String runId, int chunkIndex, List<String> entityIds) {
        String chunkId = "chunk:" + chunkIndex;
        String nodeId = getNodeId();
        String logContext = String.format("job_name=%s - run_id=%s, chunk_id=%s, node_id=%s", jobName, runId, chunkId, nodeId);

        try {
            logger.info("{} - STARTING chunk processing. chunk_id={}  chunk_size={} ids={}", logContext, chunkId, entityIds.size(), entityIds);

            Map<String, Map<String, String>> jobStatusMap = new HashMap<>();

            long startTime = System.currentTimeMillis();

            for (String entityId : entityIds) {
                Map<String, String> values = new HashMap<>();
                jobStatusMap.put(entityId, values);
                values.put("nodeId", nodeId);
                ApheExecutionData workResult = null;
                try {
                    workResult = executeUnitOfWork(runId, chunkId, nodeId, entityId);
                }catch (Exception e) {
                    logger.error("{} - FAILED to execute work unit. id={} message={}", logContext, entityId, e.getMessage(), e);
                    workResult = new ApheExecutionData(ApheExecutionResult.FAILED, "Exception while executing work unit. " + e.getMessage());
                }
                values.put("result", workResult.result.name());
                values.put("message", workResult.message);
                values.putAll(workResult.additionalInfo);
                logger.debug("{} - finished work for {}", logContext, entityId);
                resultStore.saveJobStatus(jobName, runId, chunkId, entityId, nodeId, values);
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            logger.info("{} - COMPLETED chunk processing. chunk_id= time={}", logContext,duration);

            onChunkCompleted(runId, chunkId);

        } catch (Exception e) {
            onChunkCompleted(runId, chunkId);
            logger.error("{} - FAILED chunk processing. chunk_id={} message={}", logContext, chunkId, e.getMessage(), e);
            throw e; // Re-throw to ensure JobRunr marks this as failed
        }
    }

    public void onChunkCompleted(String runId, String chunkId) {
        String nodeId = getNodeId();
        String logContext = String.format("job_name=%s - run_id=%s, chunk_id=%s, node_id=%s", jobName, runId, chunkId, nodeId);

        logger.info("{} - Chunk completed", logContext);
        long completed = resultStore.incrementCompletedChunks(jobName, runId);
        long expected = resultStore.getExpectedChunks(jobName, runId);

        logger.info("{} - Job completion status. expected={} completed={}", logContext, expected, completed);

        if (completed == expected) {
            logger.info("{} - All chunks completed. Calling onExecutionCompleted()", logContext);
            jobScheduler.enqueue(() -> onExecutionCompleted(runId));
            logger.info("{} - Successfully enqueued onExecutionCompleted()", logContext);
        } else if(completed < expected){
            logger.debug("{} - Waiting for remaining chunks to complete", logContext);
        } else {
            logger.error("{} - More chunks completed than expected. expected={} completed={}", logContext, expected, completed);
            onExecutionFailed(runId, "More chunks completed than expected");
        }
    }

    public void onExecutionFailed(String runId, String message) {
        String nodeId = getNodeId();
        String logContext = String.format("job_name=%s - run_id=%s, node_id=%s", jobName, runId, nodeId);

        long endTime = System.currentTimeMillis();
        resultStore.setJobEndTime(jobName, runId, endTime);

        String emailHTML = getEmailReportFailed(logContext, runId, message);

        notifyJobCompleted(0,0,0,0, new HashMap<>(), getEmailSubject(), emailHTML);
    }

    public void onExecutionCompleted(String runId) {
        String nodeId = getNodeId();
        String logContext = String.format("job_name=%s - run_id=%s, node_id=%s", jobName, runId, nodeId);

        long endTime = System.currentTimeMillis();
        resultStore.setJobEndTime(jobName, runId, endTime);

        try {
            logger.info("{} - STARTING on completion", logContext);

            long startTime = System.currentTimeMillis();
            Map<String, Map<String, String>> jobStatusValues = resultStore.getAllStatuses(jobName, runId);
            long queryTime = System.currentTimeMillis() - startTime;

            logger.info("{} - Retrieved job statuses. work_units={} time={})", logContext, jobStatusValues.size(), queryTime);

            int toBeProcessed = jobStatusValues.size();
            int processed = 0;
            int errored = 0;
            int skipped = 0;
            int unknown = 0;

            Map<String, Map<String, String>> sortedJobStatusValues = jobStatusValues.entrySet().stream()
                    .sorted(
                            Comparator
                                    .comparing(
                                            (Map.Entry<String, Map<String, String>> e) -> e.getValue().get("chunkId"),
                                            Comparator.nullsFirst((a, b) -> compareMixed(a, b))
                                    )
                                    .thenComparing(
                                            e -> e.getValue().get("entityId"),
                                            Comparator.nullsFirst((a, b) -> compareMixed(a, b))
                                    )
                    )
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldVal, newVal) -> oldVal,
                            LinkedHashMap::new // preserves insertion (i.e. sort) order
                    ));

            for (Map.Entry<String, Map<String, String>> entry : sortedJobStatusValues.entrySet()) {
                Map<String, String> values = entry.getValue();
                String result = values.get("result");
                ApheExecutionResult status = ApheExecutionResult.   valueOf(result);
                switch (status) {
                    case SUCCESS:
                        processed++;
                        break;
                    case FAILED:
                        errored++;
                        break;
                    case SKIPPED:
                        skipped++;
                        break;
                    default:
                        unknown++;
                        break;
                }
            }
            logger.info("{} - Summary total={} processed={} skipped={} errored={} unknown={}", logContext, toBeProcessed, processed, skipped, errored, unknown);

            String emailHTML = getEmailReport(logContext, runId, toBeProcessed, processed, skipped, errored, sortedJobStatusValues);

            notifyJobCompleted(toBeProcessed, processed, skipped, errored, jobStatusValues, getEmailSubject(), emailHTML);

            logger.info("{} - Notification completed.", logContext);

        } catch (Exception e) {
            logger.error("{} - FAILED to generate summary report: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    private String getEmailReportFailed(String logContext, String runId, String message) {
        try {
            logger.info("{} - STARTING email report generation", logContext);

            String duration = getTimeDuration(resultStore.getJobStartTime(jobName, runId), resultStore.getJobEndTime(jobName, runId));

            StringBuilder sb = new StringBuilder();

            sb.append("<h2>" + jobName + " Summary</h2>");
            sb.append("Run Id = " + runId).append("<br/>");
            sb.append("Execution Time = " + duration).append("<br/>");

            sb.append("Message = " + message).append("<br/>");
            sb.append("<br/>");

            logger.info("{} - COMPLETED error email report generation", logContext);
            return sb.toString();

        } catch (Exception e) {
            logger.error("{} - FAILED to generate email report: {}", logContext, e.getMessage(), e);
            return null;
        }
    }



    private String getEmailReport(String logContext, String runId, int toBeProcessed, int processed, int skipped, int errored,
                                 Map<String, Map<String, String>> jobStatusValues) {
        try {
            logger.info("{} - STARTING email report generation", logContext);

            String duration = getTimeDuration(resultStore.getJobStartTime(jobName, runId), resultStore.getJobEndTime(jobName, runId));

            StringBuilder sb = new StringBuilder();

            // Add summary statistics
            sb.append("<h2>" + jobName + " Summary</h2>");
            sb.append("Run Id = " + runId).append("<br/>");
            sb.append("Execution Time = " + duration).append("<br/>");
            sb.append("Total Units of Work = " + toBeProcessed).append("<br/>");
            sb.append("Total Completed = " + processed).append("<br/>");
            sb.append("Total Skipped = " + skipped).append("<br/>");
            sb.append("Total Errored = " + errored).append("<br/>");
            sb.append("<br/>");

            if (jobStatusValues.keySet().size() > 0) {
                logger.debug("{} - Building detailed job status table with {} entries", logContext, jobStatusValues.size());

                sb.append("<h3>Detailed Results</h3>");
                sb.append("<table border='1' style='border-collapse: collapse;'>");
                sb.append("<tr style='background-color: #f2f2f2;'>");
                sb.append("<th>Unit Id</th>");
                sb.append("<th>Chunk Id</th>");
                sb.append("<th>Node Id</th>");
                sb.append("<th>Status</th>");
                sb.append("<th>Message</th>");

                // Add additional columns for each property
                List<String> additionalEmailColumns = getAdditionalEmailColumns();
                for (String column : additionalEmailColumns) {
                    sb.append("<th>").append(column).append("</th>");
                }
                sb.append("</tr>");

                for (String entityId : jobStatusValues.keySet()) {
                    Map<String, String> values = jobStatusValues.get(entityId);
                    sb.append("<tr>");

                    sb.append("<td>").append(entityId).append("</td>");
                    sb.append("<td>").append(safeGet(values, "chunkId")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "nodeId")).append("</td>");
                    String status = safeGet(values, "result");
                    String statusColor = getStatusColor(ApheExecutionResult.valueOf(status));
                    sb.append("<td style='background-color: ").append(statusColor).append("'>").append(status).append("</td>");
                    sb.append("<td>").append(safeGet(values, "message")).append("</td>");

                    // Add additional columns for each property
                    for (String column : additionalEmailColumns) {
                        sb.append("<td>").append(safeGet(values, column)).append("</td>");
                    }
                    sb.append("</tr>");
                }
                sb.append("</table>");
            } else {
                sb.append("<p>No detailed results available.</p>");
            }
            logger.info("{} - COMPLETED email report generation", logContext);
            return sb.toString();

        } catch (Exception e) {
            logger.error("{} - FAILED to generate email report: {}", logContext, e.getMessage(), e);
            return null;
        }
    }

    private @NotNull String getEmailSubject() {
        String subject = jobName + "-" + PropertiesManager.getAppMode() + " - Job Status Report";
        return subject;
    }

    private String safeGet(Map<String, String> map, String key) {
        String value = map.get(key);
        return value != null ? value : "N/A";
    }

    private String getStatusColor(ApheExecutionResult status) {
        switch (status) {
            case SUCCESS:
                return "#ccffcc"; // Light green for success
            case FAILED:
                return "#ffcccc"; // Light red for errors
            case SKIPPED:
                return "#ffffcc"; // Light yellow for skipped
            default:
                return "#ffffff"; // White for unknown
        }
    }


    public void validateConfig() {
        //Check jobName, chnunkSize are set.
        if (jobName == null || jobName.isEmpty()) {
            throw new RuntimeException("Job name is empty");
        }
        if(chunkSize <= 0) {
            throw new RuntimeException("Chunk size is invalid");
        }
    }


    public boolean isPauseTasks(String taskName) {
        //Check if all the tasks are paused or this specific task is paused.
        boolean pauseTasks = environment.getProperty("aphe.config.tasks.pause", Boolean.class, false);
        if(pauseTasks) {
            logger.info("Task " + taskName + " is paused because all tasks are paused because pause tasks config property is set to true.");
            return true;
        }

        pauseTasks = environment.getProperty("aphe.config.tasks." + taskName + ".pause", Boolean.class, false);
        if(pauseTasks) {
            logger.info("Task " + taskName + " is paused because this task's pause property is set to true.");
            return true;
        }

        return false;
    }

    int compareMixed(String a, String b) {
        if (a == null && b == null) return 0;
        if (a == null) return -1;
        if (b == null) return 1;

        String s1 = a.trim();
        String s2 = b.trim();

        //If the string starts with chunkId: , remove it before comparing.
        if (s1.startsWith(chunkPrefix)) {
            s1 = s1.substring(chunkPrefix.length());
        }
        if (s2.startsWith(chunkPrefix)) {
            s2 = s2.substring(chunkPrefix.length());
        }

        boolean n1 = isLong(s1);
        boolean n2 = isLong(s2);

        if (n1 && n2) {
            return Long.compare(Long.parseLong(s1), Long.parseLong(s2));
        }
        // If one or both are non-numeric, fall back to lexicographic compare
        return s1.compareTo(s2);
    }

    boolean isLong(String s) {
        try {
            Long.parseLong(s);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public String getTimeDuration(long startTime, long endTime) {
        long duration = endTime - startTime;

        //convert the duration to hours minutes, seconds and milliseconds. Do not show hours or minutes or seconds if they are 0.
        if(duration < 1000) {
            return String.format("%d ms", duration);
        }
        if(duration < 60000) {
            return String.format("%d sec, %d ms", TimeUnit.MILLISECONDS.toSeconds(duration),
                    duration - TimeUnit.SECONDS.toMillis(TimeUnit.MILLISECONDS.toSeconds(duration)));
        }
        if(duration < 3600000) {
            return String.format("%d min, %d sec, %d ms", TimeUnit.MILLISECONDS.toMinutes(duration),
                    TimeUnit.MILLISECONDS.toSeconds(duration) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(duration)),
                    duration - TimeUnit.SECONDS.toMillis(TimeUnit.MILLISECONDS.toSeconds(duration)));
        }
        return String.format("%d hr, %d min, %d sec, %d ms", TimeUnit.MILLISECONDS.toHours(duration),
                TimeUnit.MILLISECONDS.toMinutes(duration) - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(duration)),
                TimeUnit.MILLISECONDS.toSeconds(duration) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(duration)),
                duration - TimeUnit.SECONDS.toMillis(TimeUnit.MILLISECONDS.toSeconds(duration)));
    }
}
