package com.aphe.common.tasks;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Service
public class TaskStatusStore {

    private static final Logger logger = LoggerFactory.getLogger(TaskStatusStore.class);
    private final StringRedisTemplate redis;

    public TaskStatusStore(StringRedisTemplate redis) {
        this.redis = redis;
    }

    public String statusKey(String runId)     { return runId + ":status"; }
    public String expectedKey(String runId)   { return runId + ":expected"; }
    public String completedKey(String runId)  { return runId + ":completed"; }

    public void setExpectedChunks(String runId, int count) {
        String logContext = String.format("RunId: %ss", runId);

        try {
            logger.info("{} - Setting expected chunks to {}", logContext, count);

            redis.opsForValue().set(expectedKey(runId), String.valueOf(count));
            redis.opsForValue().set(completedKey(runId), "0");
            redis.expire(expectedKey(runId), Duration.ofDays(3));
            redis.expire(completedKey(runId), Duration.ofDays(3));
            redis.expire(statusKey(runId), Duration.ofDays(3));

            logger.debug("{} - Successfully set expected chunks and initialized counters", logContext);
        } catch (Exception e) {
            logger.error("{} - FAILED to set expected chunks: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public long incrementCompletedChunks(String runId) {
        String logContext = String.format("RunId: %s", runId);
        try {
            long completed = redis.opsForValue().increment(completedKey(runId));
            logger.info("{} - Incremented completed chunks to {}", logContext, completed);
            return completed;
        } catch (Exception e) {
            logger.error("{} - FAILED to increment completed chunks: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public long getExpectedChunks(String runId) {
        String logContext = String.format("RunId: %s", runId);

        try {
            String value = redis.opsForValue().get(expectedKey(runId));
            if (value == null) {
                logger.warn("{} - Expected chunks key not found in Redis", logContext);
                return 0;
            }
            long expected = Long.parseLong(value);
            logger.debug("{} - Retrieved expected chunks: {}", logContext, expected);
            return expected;
        } catch (Exception e) {
            logger.error("{} - FAILED to get expected chunks: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public void saveJobStatus(String runId, String chunkId, String entityId, Map<String, String> jobStatus) {
        String logContext = String.format("RunId: %s, chunkId: %s, enitityId: %s", runId, chunkId, entityId);
        try {
            logger.debug("{} - Saving job status with {} values", logContext, jobStatus.size());
            JsonObject jsonObject = new JsonObject();
            jobStatus.put("chunkId", chunkId);
            jobStatus.put("entityId", entityId);
            jobStatus.forEach(jsonObject::addProperty);
            String value = jsonObject.toString();
            redis.opsForHash().put(statusKey(runId), entityId, value);
            logger.debug("{} - Successfully saved job status: {}", logContext, jobStatus.get("result"));
        } catch (Exception e) {
            logger.error("{} - FAILED to save job status: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public Map<String, Map<String, String>> getAllStatuses(String runId) {
        String logContext = String.format("RunId: %s", runId);
        try {
            logger.info("{} - Retrieving all job statuses", logContext);

            long startTime = System.currentTimeMillis();
            Map<Object, Object> entries = redis.opsForHash().entries(statusKey(runId));
            long queryTime = System.currentTimeMillis() - startTime;

            logger.info("{} - Retrieved {} status entries from Redis (took {}ms)", logContext, entries.size(), queryTime);

            Map<String, Map<String, String>> result = new HashMap<>();
            int successCount = 0;
            int errorCount = 0;

            // Convert these entries to Map<Long, Map<String, String>> type
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                String key = (String) entry.getKey();
                String value = (String) entry.getValue();

                logger.info("{} - Status for entityId: {} is {}", logContext, key, value);
                try {
                    if (value.startsWith("{")) {
                        // It's a JSON string, parse it
                        JsonObject jsonObject = JsonParser.parseString(value).getAsJsonObject();
                        // Convert JsonObject to Map
                        Map<String, String> valueMap = new HashMap<>();
                        jsonObject.entrySet().forEach(e -> {
                                    logger.debug("{} - Entry key: {} value: {} isNull: {}", logContext, e.getKey(), e.getValue(), e.getValue().isJsonNull());
                                    String stringValue = safeGetAsString(e.getValue());
                                    valueMap.put(e.getKey(), stringValue);
                                }
                        );
                        result.put(key, valueMap);
                        successCount++;
                    } else {
                        throw new JsonSyntaxException("Value is not a valid JSON string: " + value);
                    }
                } catch (JsonSyntaxException e) {
                    logger.warn("{} - Invalid JSON format for entityId {}: {}", logContext, key, e.getMessage());
                    Map<String, String> valueMap = Map.of("result", "ERROR", "message", "Invalid JSON format");
                    result.put(key, valueMap);
                    errorCount++;
                }
            }
            logger.info("{} - Processed {} statuses: {} success, {} errors", logContext, entries.size(), successCount, errorCount);
            return result;
        } catch (Exception e) {
            logger.error("{} - FAILED to retrieve all statuses: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Safely extract string value from JsonElement, handling JsonNull
     */
    private String safeGetAsString(JsonElement element) {
        if (element == null || element.isJsonNull()) {
            return null;
        }
        return element.getAsString();
    }
}
