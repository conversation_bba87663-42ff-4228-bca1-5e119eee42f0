package com.aphe.common.tasks;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class GenericBackgroundTask {
    protected Logger logger = LoggerFactory.getLogger(GenericBackgroundTask.class);

    @Autowired
    Environment environment;;


    public boolean isPauseTasks(String taskName) {
        //Check if all the tasks are paused or this specific task is paused.

        boolean pauseTasks = environment.getProperty("aphe.config.tasks.pause", Boolean.class, false);
        if(pauseTasks) {
            logger.info("Task " + taskName + " is paused because all tasks are paused because pause tasks config property is set to true.");
            return true;
        }

        pauseTasks = environment.getProperty("aphe.config.tasks." + taskName + ".pause", Boolean.class, false);
        if(pauseTasks) {
            logger.info("Task " + taskName + " is paused because this task's pause property is set to true.");
            return true;
        }

        return false;
    }
}
