package com.aphe.common.util;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.iv.RandomIvGenerator;
import org.jasypt.salt.RandomSaltGenerator;
import org.jasypt.util.text.BasicTextEncryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Component
public class EncryptionUtil {

	private static Logger logger = LoggerFactory.getLogger(EncryptionUtil.class);

	private static final String DEFAULT_KEY_NAME = "jasypt.encryptor.password";

	//NOTE: DO NOT JUST CHANGE THE ALGO. Add a new ALGO.
	private static final String FILE_ENCRYPTION_ALG_1 = "AES/CTR/NoPadding";
	private static final String TEXT_ENCRYPTION_ALG_1 = "AES/CBC/PKCS5Padding";
	private static final String TEXT_ENCRYPTION_ALG_DEFAULT_305 = "PBEWithHMACSHA512AndAES_256";


	private static final int IV_LENGTH_BYTE = 16;
	private static final int SALT_LENGTH_BYTE = 16;
	private static final Charset UTF_8 = StandardCharsets.UTF_8;

	public static byte[] getRandomNonce(int numBytes) {
		byte[] nonce = new byte[numBytes];
		new SecureRandom().nextBytes(nonce);
		return nonce;
	}

	public static SecretKey getAESKey(int keysize) throws NoSuchAlgorithmException {
		KeyGenerator keyGen = KeyGenerator.getInstance("AES");
		keyGen.init(keysize, SecureRandom.getInstanceStrong());
		return keyGen.generateKey();
	}

	public static SecretKey getAESKeyFromPassword(char[] password, byte[] salt)  throws NoSuchAlgorithmException, InvalidKeySpecException {
		SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
		KeySpec spec = new PBEKeySpec(password, salt, 65536, 256);
		SecretKey secret = new SecretKeySpec(factory.generateSecret(spec).getEncoded(), "AES");
		return secret;
	}

	public static String hex(byte[] bytes) {
		StringBuilder result = new StringBuilder();
		for (byte b : bytes) {
			result.append(String.format("%02x", b));
		}
		return result.toString();
	}

	// print hex with block size split
	public static String hexWithBlockSize(byte[] bytes, int blockSize) {
		String hex = hex(bytes);
		// one hex = 2 chars
		blockSize = blockSize * 2;
		// better idea how to print this?
		List<String> result = new ArrayList<>();
		int index = 0;
		while (index < hex.length()) {
			result.add(hex.substring(index, Math.min(index + blockSize, hex.length())));
			index += blockSize;
		}
		return result.toString();
	}

	public String encryptWithDefault305(String strToEncrypt, String secret) {
		//Encrypt the string using PBEWithMD5AndDES
		if(PropertiesManager.isDev()) {
			//Do a poor man's encryption by incrementing the ascii value of each character by 1.
			StringBuffer sb = new StringBuffer();
			for(int i = 0; i < strToEncrypt.length(); i++) {
				sb.append((char)(strToEncrypt.charAt(i) + 1));
			}
			sb.append("---dev---");
			return sb.toString();
		}

		try {
			StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
			encryptor.setPassword(getDefaultKey(secret));
			encryptor.setAlgorithm("PBEWithHMACSHA512AndAES_256");
			encryptor.setIvGenerator(new RandomIvGenerator());
			encryptor.setSaltGenerator( new RandomSaltGenerator());
			encryptor.setKeyObtentionIterations(1000);
			return encryptor.encrypt(strToEncrypt);
		} catch (Exception ex) {
			logger.error("Error encrypting string with spring 3", ex);
		}
		return null;
	}

	public String decryptWithDefault305(String strToDecrypt) {
		try {
			return decryptWithDefault305(strToDecrypt, getDefaultKey(null));
		} catch (Exception e) {
			logger.error("error decrypting with spring 3", e);
		}
		return null;
	}

	public String decryptWithDefault305(String strToEncrypt, String secret) {
		//Encrypt the string using PBEWithMD5AndDES
		try {
			StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
			encryptor.setPassword(getDefaultKey(secret));
			encryptor.setAlgorithm("PBEWithHMACSHA512AndAES_256");
			encryptor.setIvGenerator(new RandomIvGenerator());
			encryptor.setSaltGenerator( new RandomSaltGenerator());
			encryptor.setKeyObtentionIterations(1000);
			return encryptor.decrypt(strToEncrypt);
		} catch (Exception ex) {
			logger.error("error decrypting with spring 3", ex);
		}
		return null;
	}

	public String encryptWithDefault200(String strToEncrypt, String secret) {
		//Encrypt the string using PBEWithMD5AndDES
		if(PropertiesManager.isDev()) {
			//Do a poor man's encryption by incrementing the ascii value of each character by 1.
			StringBuffer sb = new StringBuffer();
			for(int i = 0; i < strToEncrypt.length(); i++) {
				sb.append((char)(strToEncrypt.charAt(i) + 1));
			}
			sb.append("---dev---");
			return sb.toString();
		}

		try {
			BasicTextEncryptor encryptor = new BasicTextEncryptor();
			encryptor.setPassword(getDefaultKey(secret));
			return encryptor.encrypt(strToEncrypt);
		} catch (Exception ex) {
			logger.error("Error encrypting spring 2", ex);
		}
		return null;
	}

	public String decryptWithDefault200(String strToEncrypt, String secret) {
		//Encrypt the string using PBEWithMD5AndDES
		try {
			BasicTextEncryptor encryptor = new BasicTextEncryptor();
			encryptor.setPassword(getDefaultKey(secret));
			return encryptor.decrypt(strToEncrypt);
		} catch (Exception ex) {
			logger.error("Error decrypting spring 2", ex);
		}
		return null;
	}

	public String encryptAES(String strToEncrypt, String secret) {

		if(PropertiesManager.isDev()) {
			//Do a poor man's encryption by incrementing the ascii value of each character by 1.
			StringBuffer sb = new StringBuffer();
			for(int i = 0; i < strToEncrypt.length(); i++) {
				sb.append((char)(strToEncrypt.charAt(i) + 1));
			}
			sb.append("---dev---");
			return sb.toString();
		}


		byte[] salt = EncryptionUtil.getRandomNonce(SALT_LENGTH_BYTE);
		byte[] iv = EncryptionUtil.getRandomNonce(IV_LENGTH_BYTE);
		IvParameterSpec ivSpec = new IvParameterSpec(iv);
		try {
			SecretKey aesKeyFromPassword = EncryptionUtil.getAESKeyFromPassword(secret.toCharArray(), salt);
			byte[] raw = secret.getBytes(UTF_8);
			Cipher cipher = Cipher.getInstance(TEXT_ENCRYPTION_ALG_1);
			cipher.init(Cipher.ENCRYPT_MODE, aesKeyFromPassword, ivSpec);
			byte[] encrypted = cipher.doFinal(strToEncrypt.getBytes());
			byte[] cipherTextWithIvSalt = ByteBuffer.allocate(iv.length + salt.length + encrypted.length)
					.put(iv)
					.put(salt)
					.put(encrypted)
					.array();
			return Base64.getEncoder().encodeToString(cipherTextWithIvSalt);
		} catch (Exception ex) {
			logger.error("Error encrypting string with AES", ex);
		}
		return null;
	}

	public String decryptAES(String strToDecrypt, String secret) {

		if(PropertiesManager.isDev() && strToDecrypt.endsWith("---dev---")) {
			//decrypt the string by decrementing the ascii value of each character by 1.
			StringBuffer sb = new StringBuffer();
			for(int i = 0; i < strToDecrypt.length() - 9; i++) {
				sb.append((char)(strToDecrypt.charAt(i) - 1));
			}
			return sb.toString();
		}

		try {
			byte[] decode = Base64.getDecoder().decode(strToDecrypt.getBytes(UTF_8));
			ByteBuffer bb = ByteBuffer.wrap(decode);
			byte[] iv = new byte[IV_LENGTH_BYTE];
			bb.get(iv);
			byte[] salt = new byte[SALT_LENGTH_BYTE];
			bb.get(salt);
			byte[] cipherText = new byte[bb.remaining()];
			bb.get(cipherText);
			IvParameterSpec ivSpec = new IvParameterSpec(iv);

			SecretKey aesKeyFromPassword = EncryptionUtil.getAESKeyFromPassword(secret.toCharArray(), salt);
			Cipher cipher = Cipher.getInstance(TEXT_ENCRYPTION_ALG_1);
			cipher.init(Cipher.DECRYPT_MODE, aesKeyFromPassword, ivSpec);
			byte[] plainText = cipher.doFinal(cipherText);
			return new String(plainText, UTF_8);
		} catch (Exception ex) {
			logger.error("Error decrypting string with AES", ex);
		}
		return null;
	}


	public String getDefaultKey(String secret) {
		if (secret == null) {
			secret = System.getenv(DEFAULT_KEY_NAME);
		}
		if (secret == null) {
			secret = System.getProperty(DEFAULT_KEY_NAME);
		}
		if (secret == null)
			throw new RuntimeException("Encryption key not provided.");
		return secret;
	}

	public String encrypt(String strToEncrypt) {
		try {
			return encrypt(strToEncrypt, getDefaultKey(null));
		} catch (Exception e) {
			logger.error("error encrypting with default key", e);
		}
		return null;
	}

	public String encrypt(String strToEncrypt, String secret) {
		//All new encryption is going to be based on the new mode.
		return encryptAES(strToEncrypt, secret);
	}

	public String decrypt(String strToDecrypt) {
		try {
			return decrypt(strToDecrypt, getDefaultKey(null));
		} catch (Exception e) {
			logger.error("error decrypting with default key", e);
		}
		return null;
	}

	public String decrypt(String strToDecrypt, String secret) {
		if(strToDecrypt == null || strToDecrypt.length() == 0) {
			logger.error("Trying to decrypt an empty string");
			return strToDecrypt;
		}
		//Try decrypting with the latest one... If didn't work, use the old method and log it...
		String decryptedString = decryptAES(strToDecrypt, secret);
		if(decryptedString == null || decryptedString.length() == 0) {
			decryptedString = decryptWithDefault305(strToDecrypt, secret);
		}
		if(decryptedString == null || decryptedString.length() == 0) {
			decryptedString = decryptWithDefault200(strToDecrypt, secret);
			logger.error("Using an old method to decrypt value=" + strToDecrypt);
		}
		return decryptedString;
	}

	// public OutputStream getEncryptorStream(String transmissionFilePath) throws GeneralSecurityException, IOException, FileNotFoundException {
	// Key secretkey = KeyFactory.AES.keyFromPassword(getDefaultKey(null).toCharArray());
	// Encryptor encryptor = new Encryptor(secretkey, FILE_ENCRYPTION_ALG, 16);
	// return encryptor.wrapOutputStream(new FileOutputStream(transmissionFilePath));
	// }
	//
	// public InputStream getDecryptorStream(String transmissionFilePath) throws GeneralSecurityException, IOException, FileNotFoundException {
	// Key secretkey = KeyFactory.AES.keyFromPassword(getDefaultKey(null).toCharArray());
	// Encryptor encryptor = new Encryptor(secretkey, FILE_ENCRYPTION_ALG, 16);
	// return encryptor.wrapInputStream(new FileInputStream(transmissionFilePath));
	// }
	
	public String hashString(String input, String salt) {
		String combinedString = input + salt;
		//Hash the string SHA-512
		try {
			MessageDigest md = MessageDigest.getInstance("SHA-256");
			byte[] encodedHash = md.digest(combinedString.getBytes(StandardCharsets.UTF_8));
			BigInteger number = new BigInteger(1, encodedHash);
			StringBuilder hexString = new StringBuilder(number.toString(16));

			// Pad with leading zeros
			while (hexString.length() < 32) {
				hexString.insert(0, '0');
			}
			return hexString.toString();
		}catch (Exception e) {
			logger.error("error hashing... ", e);
		}
		return null;
	}
}

