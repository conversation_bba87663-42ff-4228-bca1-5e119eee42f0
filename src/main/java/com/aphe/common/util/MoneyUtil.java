package com.aphe.common.util;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class MoneyUtil {

    @NotNull
    public List<BigDecimal> distributeDiff(BigDecimal expectedTotal, List<BigDecimal> originalAmounts) {
        if(originalAmounts.size() == 0) {
            return new ArrayList<>();
        }

        BigDecimal lineSum = new BigDecimal("0.00");
        for (BigDecimal amount : originalAmounts) {
            lineSum = lineSum.add(amount);
        }
        int diff = expectedTotal.subtract(lineSum).multiply(new BigDecimal(100)).setScale(0).intValue();
        List<BigDecimal> adjustedAmounts = originalAmounts.stream().map(pTransactionLine -> new BigDecimal("0.00")).collect(Collectors.toList());

        final int lineDiff = diff/ originalAmounts.size();
        adjustedAmounts = adjustedAmounts.stream().map(adjustedAmount -> adjustedAmount.add(new BigDecimal(lineDiff).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP))).collect(Collectors.toList());


        final int remainder = diff % originalAmounts.size();
        int appliedDiff = 0;
        //loop through the list and apply the remainder to the first few lines
        BigDecimal oneCent = new BigDecimal("0.01");
        for(int i = 0; i < Math.abs(remainder); i++) {
            if(remainder > 0) {
                adjustedAmounts.set(i, adjustedAmounts.get(i).add(oneCent));
                appliedDiff++;
            } else {
                adjustedAmounts.set(i, adjustedAmounts.get(i).subtract(oneCent));
                appliedDiff--;
            }
            if (appliedDiff == remainder) {
                break;
            }
        }
        return adjustedAmounts;
    }

}
