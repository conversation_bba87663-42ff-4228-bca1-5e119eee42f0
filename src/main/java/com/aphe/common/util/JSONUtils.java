package com.aphe.common.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

@Component
public class JSONUtils {

	public Map<String, Object> jsonObjectToMap(JSONObject json) {
		return jsonObjectToMap(json, false);
	}

	public Map<String, Object> jsonObjectToMap(JSONObject json, boolean compact) throws JSONException {
		Map<String, Object> map = new HashMap<String, Object>();

		if (json != JSONObject.NULL) {
			Iterator<String> keysItr = json.keySet().iterator();
			while (keysItr.hasNext()) {
				String key = keysItr.next();
				Object value = json.get(key);

				if (value instanceof JSONArray) {
					value = jsonArrayToList((JSONArray) value, compact);
				} else if (value instanceof JSONObject) {
					value = jsonObjectToMap((JSONObject) value, compact);
				}
				if (value != null) {
					if (compact) {
						String stringValue = value.toString();
						if ("false".equalsIgnoreCase(stringValue) || StringUtil.isEmpty(stringValue) || "0.00".equalsIgnoreCase(stringValue) || "0.0".equalsIgnoreCase(stringValue)
								|| "0".equalsIgnoreCase(stringValue)) {
							continue;
						} else if (value instanceof Collection<?> && ((Collection) value).size() == 0) {
							continue;
						}
					}
					map.put(key, value);
				}
			}
		}
		return map;
	}

	private List<Object> jsonArrayToList(JSONArray array, boolean compact) {
		List<Object> list = new ArrayList<Object>();
		for (int i = 0; i < array.length(); i++) {
			Object value = array.get(i);
			if (value instanceof JSONArray) {
				value = jsonArrayToList((JSONArray) value, compact);
			}

			else if (value instanceof JSONObject) {
				value = jsonObjectToMap((JSONObject) value, compact);
			}
			list.add(value);
		}
		return list;
	}

	
	
	public JSONObject mapToJSONObject(Map<String, Object> map) {
		JSONObject retObj = new JSONObject();

		if (map != null) {
			Iterator<String> keysItr = map.keySet().iterator();
			while (keysItr.hasNext()) {
				String key = keysItr.next();
				Object value = map.get(key);

				if (value instanceof List) {
					value = listToJSONArray((List<Object>) value);
				} else if (value instanceof Map<?, ?>) {
					value = mapToJSONObject((Map<String, Object>) value);
				}
				retObj.put(key, value);
			}
		}
		return retObj;
	}

	private JSONArray listToJSONArray(List<Object> list) {
		JSONArray array = new JSONArray();

		for (int i = 0; i < list.size(); i++) {
			Object value = list.get(i);
			if (value instanceof Map<?, ?>) {
				value = mapToJSONObject((Map<String, Object>) value);
			}
			array.put(value);
		}
		return array;
	}

}