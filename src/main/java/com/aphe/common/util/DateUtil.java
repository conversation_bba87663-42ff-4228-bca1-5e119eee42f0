package com.aphe.common.util;

import net.objectlab.kit.datecalc.common.DateCalculator;
import net.objectlab.kit.datecalc.common.DefaultHolidayCalendar;
import net.objectlab.kit.datecalc.common.HolidayCalendar;
import net.objectlab.kit.datecalc.common.HolidayHandlerType;
import net.objectlab.kit.datecalc.joda.LocalDateKitCalculatorsFactory;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.joda.time.LocalDate;
import org.joda.time.Period;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * TODO : 1 - Write unit test cases for all these methods.
 * TODO : 2 - More comprehensive data driven testing where each method can have it own data set.
 * TODO : 3 - Then extend the data set to verify next/prev days and next/prev biz days.
 * 
 * <AUTHOR>
 * 
 */
public class DateUtil {

	private final static Set<LocalDate> HOLIDAYS = new HashSet<LocalDate>();
	private static DateCalculator<LocalDate> US_BIZ_CAL_FORWARD = null;
	private static DateCalculator<LocalDate> US_BIZ_CAL_BACKWARD = null;


	private static SimpleDateFormat DATE_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ");
	private static Date peakStartTime = null;
	private static Date peakEndTime = null;


	static {
		// read all holidays for next 10 and previous 10 years...
		// add them to holidays set.
		// construct calendar with 20 years range...
		// throw an exception if asked outside the range...
		HOLIDAYS.add(new LocalDate("2013-01-01"));
		HOLIDAYS.add(new LocalDate("2013-01-21"));
		HOLIDAYS.add(new LocalDate("2013-02-18"));
		HOLIDAYS.add(new LocalDate("2013-05-27"));
		HOLIDAYS.add(new LocalDate("2013-07-04"));
		HOLIDAYS.add(new LocalDate("2013-09-02"));
		HOLIDAYS.add(new LocalDate("2013-10-14"));
		HOLIDAYS.add(new LocalDate("2013-11-11"));
		HOLIDAYS.add(new LocalDate("2013-11-28"));
		HOLIDAYS.add(new LocalDate("2013-12-25"));

		final HolidayCalendar<LocalDate> calendar = new DefaultHolidayCalendar<LocalDate>(HOLIDAYS, new LocalDate("2006-01-01"), new LocalDate("2013-12-31"));

		// register the holidays, any calculator with name "UK"
		// asked from now on will receive an IMMUTABLE reference to this
		// calendar
		LocalDateKitCalculatorsFactory.getDefaultInstance().registerHolidays("US", calendar);

		US_BIZ_CAL_FORWARD = LocalDateKitCalculatorsFactory.getDefaultInstance().getDateCalculator("US", HolidayHandlerType.FORWARD);
		US_BIZ_CAL_BACKWARD = LocalDateKitCalculatorsFactory.getDefaultInstance().getDateCalculator("US", HolidayHandlerType.BACKWARD);


		try {
			peakStartTime = DATE_TIME_FORMAT.parse("2025-01-30T15:00:00PST");
			peakEndTime = DATE_TIME_FORMAT.parse("2025-02-01T12:00:00PST");
		} catch (ParseException e) {
		}
	}

	private DateUtil() {
	}

	public static String getEstDiffString(DateTime d1, DateTime d2) {
		Interval interval = new Interval(d1, d2);
		Period period = interval.toPeriod();
		return period.getYears() + " years, " + period.getMonths() + " months, " + period.getWeeks() + " weeks, " + period.getDays() + " days, " + period.getHours() + " hours, "
				+ period.getMinutes() + " minutes, " + period.getSeconds() + " seconds";
	}

	public static void setSystemDate(DateTime date) {
		String dateString = date.toString("MM-dd-yy");
		String cmd = "cmd /C date " + dateString;
		try {
			Runtime.getRuntime().exec(cmd);
			try {
				Thread.sleep(2000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} catch (IOException e1) {
			e1.printStackTrace();
		}
	}

	public static void setSystemDate(Date date) {
		setSystemDate(new DateTime(date));
	}

	public static DateTime getFirstDayOfYear(DateTime dt) {
		return new DateTime(dt.getYear(), 1, 1, 0, 0, 0, 0);
	}

	public static DateTime getFirstDayOfHalfYear(DateTime dt) {
		return new DateTime(dt.getYear(), (((dt.getMonthOfYear() - 1) / 6) * 6) + 1, 1, 0, 0, 0, 0);
	}

	public static DateTime getFirstDayOfQuarter(DateTime dt) {
		return new DateTime(dt.getYear(), (((dt.getMonthOfYear() - 1) / 3) * 3) + 1, 1, 0, 0, 0, 0);
	}

	public static DateTime getFirstDayOfMonth(DateTime dt) {
		return new DateTime(dt.getYear(), dt.getMonthOfYear(), 1, 0, 0, 0, 0);
	}

	public static DateTime getLastDayOfYear(DateTime dt) {
		return new DateTime(dt.getYear(), 12, 31, 0, 0, 0, 0);
	}

	public static DateTime getLastDayOfHalfYear(DateTime dt) {
		return getLastDayOfMonth(new DateTime(dt.getYear(), (((dt.getMonthOfYear() - 1) / 6) * 6) + 6, 1, 0, 0, 0, 0));
	}

	public static DateTime getLastDayOfQuarter(DateTime dt) {
		return getLastDayOfMonth(new DateTime(dt.getYear(), (((dt.getMonthOfYear() - 1) / 3) * 3) + 3, 1, 0, 0, 0, 0));
	}

	public static DateTime getLastDayOfMonth(DateTime dt) {
		int maxDay = dt.dayOfMonth().getMaximumValue();
		return new DateTime(dt.getYear(), dt.getMonthOfYear(), maxDay, 0, 0, 0, 0);
	}

	public static DateTime getFirstDayOfNextMonth(DateTime dt) {
		return getFirstDayOfMonth(dt.plusMonths(1));
	}

	public static DateTime getFirstDayOfNextQuarter(DateTime dt) {
		return getFirstDayOfMonth(dt.plusMonths(3));
	}

	public static DateTime getFirstDayOfNextHalfYear(DateTime dt) {
		return getFirstDayOfMonth(dt.plusMonths(6));
	}

	public static DateTime getFirstDayOfNextYear(DateTime dt) {
		return getFirstDayOfMonth(dt.plusMonths(12));
	}

	public static DateTime getFirstDayOfPreviousMonth(DateTime dt) {
		return getFirstDayOfMonth(dt.minusMonths(1));
	}

	public static DateTime getFirstDayOfPreviousQuarter(DateTime dt) {
		return getFirstDayOfMonth(dt.minusMonths(3));
	}

	public static DateTime getFirstDayOfPreviousHalfYear(DateTime dt) {
		return getFirstDayOfMonth(dt.minusMonths(6));
	}

	public static DateTime getFirstDayOfPreviousYear(DateTime dt) {
		return getFirstDayOfMonth(dt.minusMonths(12));
	}

	public static DateTime getLastDayOfNextMonth(DateTime dt) {
		return getLastDayOfMonth(dt.plusMonths(1));
	}

	public static DateTime getLastDayOfNextQuarter(DateTime dt) {
		return getLastDayOfMonth(dt.plusMonths(3));
	}

	public static DateTime getLastDayOfNextHalfYear(DateTime dt) {
		return getLastDayOfMonth(dt.plusMonths(6));
	}

	public static DateTime getLastDayOfNextYear(DateTime dt) {
		return getLastDayOfMonth(dt.plusMonths(12));
	}

	public static DateTime getLastDayOfPreviousMonth(DateTime dt) {
		return getLastDayOfMonth(dt.minusMonths(1));
	}

	public static DateTime getLastDayOfPreviousQuarter(DateTime dt) {
		return getLastDayOfMonth(dt.minusMonths(3));
	}

	public static DateTime getLastDayOfPreviousHalfYear(DateTime dt) {
		return getLastDayOfMonth(dt.minusMonths(6));
	}

	public static DateTime getLastDayOfPreviousYear(DateTime dt) {
		return getLastDayOfMonth(dt.minusMonths(12));
	}

	// TODO: do following two methods that access the calendars need to be synchronized?
	private static DateTime getNextBizDayForward(DateTime dt, int days) {
		US_BIZ_CAL_FORWARD.setStartDate(new LocalDate(dt));
		return US_BIZ_CAL_FORWARD.moveByBusinessDays(days).getCurrentBusinessDate().toDateTimeAtStartOfDay();
	}

	private static DateTime getNextBizDayBackward(DateTime dt, int days) {
		US_BIZ_CAL_BACKWARD.setStartDate(new LocalDate(dt));
		return US_BIZ_CAL_BACKWARD.moveByBusinessDays(days).getCurrentBusinessDate().toDateTimeAtStartOfDay();
	}

	public static DateTime getBizDay(DateTime dt) {
		return getBizDay(dt, 0);
	}

	public static DateTime getPreviousBizDay(DateTime dt) {
		return getNextBizDayBackward(dt, 0);
	}

	public static DateTime getBizDay(DateTime dt, int businessDays) {
		if (businessDays < 0) {
			return getNextBizDayBackward(dt, businessDays);
		} else {
			return getNextBizDayForward(dt, businessDays);
		}
	}

	public static DateTime getFirstBizDayOfMonth(DateTime dt) {
		return getBizDay(getFirstDayOfMonth(dt));
	}

	public static DateTime getLastBizDayOfMonth(DateTime dt) {
		return getPreviousBizDay(getLastDayOfMonth(dt));
	}

	public static DateTime getFirstBizDayOfQuarter(DateTime dt) {
		return getBizDay(getFirstDayOfQuarter(dt));
	}

	public static DateTime getLastBizDayOfQuarter(DateTime dt) {
		return getPreviousBizDay(getLastDayOfQuarter(dt));
	}

	public static DateTime getFirstBizDayOfHalfYear(DateTime dt) {
		return getBizDay(getFirstDayOfHalfYear(dt));
	}

	public static DateTime getLastBizDayOfHalfYear(DateTime dt) {
		return getPreviousBizDay(getLastDayOfHalfYear(dt));
	}

	public static DateTime getFirstBizDayOfYear(DateTime dt) {
		return getBizDay(getFirstDayOfYear(dt));
	}

	public static DateTime getLastBizDayOfYear(DateTime dt) {
		return getPreviousBizDay(getLastDayOfYear(dt));
	}

	public static Date truncateMinutes(Date dt) {
		return new DateTime(dt).minuteOfDay().roundFloorCopy().toDate();
	}

	public static Date truncateHours(Date dt) {
		return new DateTime(dt).hourOfDay().roundFloorCopy().toDate();
	}

	public static Date truncateMinutes(DateTime dt) {
		return dt.minuteOfDay().roundFloorCopy().toDate();
	}

	public static Date truncateHours(DateTime dt) {
		return dt.hourOfDay().roundFloorCopy().toDate();
	}

	public static Date addDays(Date dt, int days) {
		return new DateTime(dt).plusDays(days).toDate();
	}

	public static Date addHours(Date dt, int hours) {
		return new DateTime(dt).plusHours(hours).toDate();
	}

	public static Date addMinutes(Date dt, int minutes) {
		return new DateTime(dt).plusMinutes(minutes).toDate();
	}
	public static Date addSeconds(Date dt, int seconds) {
		return new DateTime(dt).plusSeconds(seconds).toDate();
	}


	public static Date addEndOfDay(Date dt) {
		return new DateTime(dt).plusHours(23).plusMinutes(59).plusSeconds(59).toDate();
	}

	public static Date addMonths(Date dt, int months) {
		return new DateTime(dt).plusMonths(months).toDate();
	}
	
	public static Date addYears(Date dt, int years) {
		return new DateTime(dt).plusYears(years).toDate();
	}



	public static boolean is1099PeakDay() {
		if(peakStartTime != null && peakEndTime != null) {
			Date now = new Date();
			if(now.after(peakStartTime) && now.before(peakEndTime)) {
				return true;
			}
		}
		return false;
	}



}
