package com.aphe.common.util;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JavaUtil {

	private static final String ALPHA_NUMERIC_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	private static final String NUMERIC_CHARS = "0123456789";
	private static final String ALPHA_NUMERIC_SPECIAL_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+";
	private static SecureRandom RANDOM = new SecureRandom();

	private JavaUtil() {
	}

	public static String getCallerStackTrace(int stackLength) {
		return getCallerStackTrace(new Throwable(), 2, stackLength);
	}

	public static String getLineSeparator() {
		return System.getProperty("line.separator");
	}

	private static String getCallerStackTrace(Throwable t, int fromStack, int stackLength) {
		boolean domainStackOnly = true;
		boolean stackPerLine = true;
		int stackLenghtToBeCovered = stackLength;

		StringBuilder builder = new StringBuilder(1000);
		StackTraceElement[] stackTrace = t.getStackTrace();
		for (int i = fromStack; i < stackTrace.length; i++) {
			StackTraceElement stackTraceElement = stackTrace[i];
			String className = stackTraceElement.getClassName();
			String methodName = stackTraceElement.getMethodName();
			int lineNumber = stackTraceElement.getLineNumber();

			if (domainStackOnly && className.startsWith("com.aphe")) {
				builder.append(className).append(".").append(methodName).append("(").append(lineNumber).append(")");
				if (stackPerLine) {
					builder.append(getLineSeparator());
				}
				stackLenghtToBeCovered--;
			}
			if (stackLenghtToBeCovered == 0) {
				break;
			}
		}

		return builder.toString();
	}

	public static String random(int len, String charString) {
		StringBuilder sb = new StringBuilder(len);
		for (int i = 0; i < len; i++) {
			sb.append(charString.charAt(RANDOM.nextInt(charString.length())));
		}
		return sb.toString();
	}

	public static String randomString(int len) {
		return random(len, ALPHA_NUMERIC_SPECIAL_CHARS);
	}

	public static String randomNumber(int len) {
		return random(len, NUMERIC_CHARS);
	}

	public static String randomText(int len) {
		return random(len, ALPHA_NUMERIC_CHARS);
	}

	/**
	 * Returns a new map with unflattend structure. Keys with a . in are converted to sub maps
	 * @param map
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<Object, Object> unflatten(Map<Object, Object> map) {
		Map<Object, Object> returnMap = new HashMap<Object, Object>(map);
		List<Object> keys = new ArrayList<>(returnMap.keySet());
		for (Object key : keys) {
			String stringKey = (String) key;
			if (stringKey.contains(".")) {
				Object value = returnMap.get(key);
				returnMap.remove(key);
				String firstPart = stringKey.substring(0, stringKey.indexOf("."));
				String rest = stringKey.substring(stringKey.indexOf(".") + 1);
				// add firstPart as a map to the main key to above map.
				Object innerObj = returnMap.get(firstPart);
				if (innerObj == null) {
					innerObj = new HashMap<Object, Object>();
					returnMap.put(firstPart, innerObj);
				} else {
					// throw an exception if innerMap is not a map or the inner
					// map already contains the property.
					if (!(innerObj instanceof Map<?, ?>)) {
						throw new RuntimeException("Parent key and child key name collission-----11111.");
					} else {
						Map<Object, Object> innerMap = ((Map<Object, Object>) innerObj);
						if (innerMap.get(rest) != null)
							throw new RuntimeException("Parent key and child key name collission.");
					}
				}
				Map<Object, Object> innerMap = (Map<Object, Object>) innerObj;
				innerMap.put(rest, value);
				// Flatten the inner map.
				innerObj = unflatten(innerMap);
			}
		}
		return returnMap;
	}
	
	
	public static boolean booleanValue(Boolean theBoolean){
		return theBoolean != null && theBoolean.booleanValue();
	}
}
