package com.aphe.common.error;

import java.util.HashMap;
import java.util.Map;

public enum ApheErrorCode {
    APP_100("Unknown error"),

    APP_200("Unknown error editing payer"),
    APP_300("Unknown error editing payee"),

    APP_400("Unknown error editing filing"),
    APP_401("You can not modify a filing that has been submitted for processing. If you need to modify a filing that been accepted by the IRS, you need to file corrections. Please check out our correction guide for more information."),

    //Partner access issues
    INT_1001("Invalid access token."), //Access token probably expired or revoked on the partner side.
    INT_1002("Not authorized."), // Access token is valid, but it doesn't have the required scope.

    //Partner connection issues like maintenance, outages (500s) etc. Mostly like will resolve after some time.
    INT_1010("Partner systems are unresponsive"), //
    INT_1011("Partner systems are under maintenance"), //
    INT_1012("Partner systems are throttling our requests"), //


    //Partner business validation errors. May be fixable by the users, are may be not, as they could be introduced by our layers.
    INT_1020("Error getting the data from the partner."), // Generic data processing error. Could be partner or us and is not really fixable by the user.


    AUTHN_1001("Bad Credentials"),
    AUTHN_1002("Account expired"),
    AUTHN_1003("Account locked out"),
    AUTHN_1004("Account credentials have expired"),
    AUTHN_1005("Email Address is not verified"),
    AUTHN_1006("Account has been disabled"),
    AUTHN_1007("Invalid recaptcha token"),
    AUTHN_1008("Invalid totp code"),


    AUTHZ_1001("Not authorized to perform the action"),

    PAYE_1001("Payee with filings can not be deleted."),
    PAYE_1002("Payee with TIN verification in progress can not be edited."),

    BULK_1001("Bulk operation failure."),

    BILL_1001("Payment authorization failed."),
    ;

    private static final Map<String, ApheErrorCode> nameIndex = new HashMap<String, ApheErrorCode>(ApheErrorCode.values().length);

    static {
        for (ApheErrorCode e : ApheErrorCode.values()) {
            nameIndex.put(e.name(), e);
        }
    }

    public static ApheErrorCode lookupByName(String name) {
        return nameIndex.get(name);
    }

    private String message;

    ApheErrorCode(String s) {
        this.setMessage(s);
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
