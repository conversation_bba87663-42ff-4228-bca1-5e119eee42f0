package com.aphe.common.error;

import org.hibernate.validator.constraints.ValidationErrors;

import java.util.HashMap;
import java.util.Map;



/**
 * Set of ValidationErrors grouped by some reference objectId as the key.
 * 
 * Used for returning validation errors on an array of of objects like a list of Filings 
 * 
 * <AUTHOR>
 *
 */
public class MappedValidationErros {

	private Map<String, ValidationErrors> errors;

	public MappedValidationErros() {
		setErrors(new HashMap<String, ValidationErrors>());
	}

	public Map<String, ValidationErrors> getErrors() {
		return errors;
	}

	public void setErrors(Map<String, ValidationErrors> errors) {
		this.errors = errors;
	}

	public void addErrors(String id, ValidationErrors validationErrors) {
		errors.put(id, validationErrors);
	}

}
