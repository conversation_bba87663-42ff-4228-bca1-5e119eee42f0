package com.aphe.common.vault;

import com.aphe.common.util.PropertiesManager;
import com.bettercloud.vault.Vault;
import com.bettercloud.vault.VaultConfig;
import com.bettercloud.vault.VaultException;
import com.bettercloud.vault.api.Auth;
import com.bettercloud.vault.json.JsonObject;
import com.bettercloud.vault.response.AuthResponse;
import com.bettercloud.vault.response.LogicalResponse;
import com.bettercloud.vault.rest.RestResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class VaultService {
    private static final Logger logger = LoggerFactory.getLogger(VaultService.class);

    // Configuration constants
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;
    private static final long TOKEN_CACHE_TTL_MINUTES = 30;
    private static final long SECRET_CACHE_TTL_MINUTES = 5;

    // Cache for tokens and secrets to avoid repeated Vault calls
    private static final ConcurrentHashMap<String, CachedToken> tokenCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, CachedSecret> secretCache = new ConcurrentHashMap<>();

    // Cache classes
    private static class CachedToken {
        final String token;
        final Instant expiryTime;

        CachedToken(String token, Duration ttl) {
            this.token = token;
            this.expiryTime = Instant.now().plus(ttl);
        }

        boolean isExpired() {
            return Instant.now().isAfter(expiryTime);
        }
    }

    private static class CachedSecret {
        final String value;
        final Instant expiryTime;

        CachedSecret(String value, Duration ttl) {
            this.value = value;
            this.expiryTime = Instant.now().plus(ttl);
        }

        boolean isExpired() {
            return Instant.now().isAfter(expiryTime);
        }
    }

    public static String getSecret(String appName, String env, String path, String keyName) {
        String secretPath = "secret/" + env + "/" + appName + "/" + path;
        String cacheKey = secretPath + ":" + keyName;

        // Check cache first
        CachedSecret cached = secretCache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            logger.debug("Retrieved property={} from path={} from cache for app={}, env={}", keyName, secretPath, appName, env);
            return cached.value;
        }

        return getSecretFromVault(appName, env, keyName, secretPath, cacheKey);
    }

    private static String getSecretFromVault(String appName, String env, String keyName, String secretPath, String cacheKey) {
        logger.info("Getting from Vault: property={} path={} app={} env={}", keyName, secretPath, appName, env);

        String vaultAddr = null;
        String vaultAuthentication = null;
        String vaultToken = null;
        String vaultRole = null;
        String vaultServiceAccountTokenFile = "/var/run/secrets/kubernetes.io/serviceaccount/token";

        boolean isDev = PropertiesManager.isDev();
        if (isDev) {
            vaultAddr = "https://vault.1099smartfile.com:18209";
            vaultAuthentication = "TOKEN";
            vaultToken = System.getProperty("spring.cloud.vault.token", "");
        } else {
            vaultAddr = "https://vault.1099smartfile.com:18209";
            vaultAuthentication = "KUBERNETES";
            vaultRole = env + "-app-role";
        }
        // Validate required properties
        validateVaultConfiguration(appName, env, vaultAddr, vaultAuthentication, vaultToken, vaultRole, vaultServiceAccountTokenFile);

        Exception lastException = null;
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                String secretValue = getSecretWithRetry(
                        secretPath, keyName, vaultAddr, vaultAuthentication,
                        vaultToken, vaultRole, vaultServiceAccountTokenFile, attempt
                );
                if (secretValue == null || secretValue.trim().isEmpty()) {
                    throw new VaultException("Empty or null secret retrieved from Vault");
                }

                // Cache the result
                secretCache.put(cacheKey, new CachedSecret(secretValue, Duration.ofMinutes(SECRET_CACHE_TTL_MINUTES)));

                logger.info("Successfully retrieved property={} path={} from Vault on attempt={}", keyName, secretPath, attempt);
                return secretValue;

            } catch (Exception e) {
                lastException = e;
                logger.warn("Attempt {} failed to get secret value from Vault: {}", attempt, e.getMessage());

                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt); // Exponential backoff
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted while retrying Vault access", ie);
                    }
                }
            }
        }

        // All attempts failed
        String errorMsg = String.format("Failed to retrieve secret value from Vault after %d attempts. Last error: %s", MAX_RETRY_ATTEMPTS, lastException.getMessage());
        logger.error(errorMsg, lastException);
        throw new RuntimeException(errorMsg, lastException);
    }


    private static void validateVaultConfiguration(String appName, String env, String vaultAddr, String vaultAuthMode,
                                                   String vaultToken, String vaultRole, String vaultServiceAccountTokenFile) {
        if (appName == null || appName.trim().isEmpty()) {
            throw new IllegalStateException("App name is required for Vault integration");
        }

        if (env == null || env.trim().isEmpty()) {
            throw new IllegalArgumentException("Environment cannot be null or empty");
        }

        if (vaultAddr == null || vaultAddr.trim().isEmpty()) {
            throw new IllegalArgumentException("Vault address cannot be null or empty");
        }

        if (vaultAuthMode == null || vaultAuthMode.trim().isEmpty()) {
            throw new IllegalStateException("Vault authentication mode is required for Vault integration");
        }

        // Validate authentication-specific requirements
        if ("token".equalsIgnoreCase(vaultAuthMode)) {
            if (vaultToken == null || vaultToken.trim().isEmpty()) {
                throw new IllegalStateException("Property 'spring.cloud.vault.token' is required for token authentication");
            }
        } else if ("kubernetes".equalsIgnoreCase(vaultAuthMode)) {
            if (vaultRole == null || vaultRole.trim().isEmpty()) {
                throw new IllegalStateException("Vault role is required for Kubernetes authentication");
            }
            if (vaultServiceAccountTokenFile == null || vaultServiceAccountTokenFile.trim().isEmpty()) {
                throw new IllegalStateException("Valut service account token file is required for Kubernetes authentication");
            }
        } else {
            throw new IllegalStateException("Unsupported authentication method: " + vaultAuthMode + ". Supported methods: token, kubernetes");
        }
    }


    /**
     * Core method to retrieve secrets from Vault with proper error handling
     */
    private static String getSecretWithRetry(String secretPath, String propName, String vaultAddr,
                                             String vaultAuthentication, String vaultToken, String vaultRole,
                                             String vaultServiceAccountTokenFile, int attemptNumber) throws Exception {

        logger.debug("Attempt {} - Configuring Vault client with address: {}", attemptNumber, vaultAddr);

        // Configure Vault with timeout settings
        VaultConfig config = new VaultConfig()
                .address(vaultAddr)
                .openTimeout(5) // 5 seconds
                .readTimeout(30) // 30 seconds
                .build();

        String token = getAuthToken(config, vaultAuthentication, vaultToken, vaultRole, vaultServiceAccountTokenFile);

        config.token(token).build();
        Vault vault = new Vault(config);

        logger.debug("Attempt {} - Reading secret from Vault path: {}", attemptNumber, secretPath);

        LogicalResponse response = vault.logical().read(secretPath);

        //check response status code, before attempting to read data, as this might give you better insight into what is happening.
        RestResponse restResponse = response.getRestResponse();
        if (restResponse.getStatus() != 200) {
            String error = new String(restResponse.getBody());
            throw new VaultException("Error reading secret from Vault. Status: " + restResponse.getStatus() + " Error: " + error);
        }

        if (response == null || response.getDataObject() == null) {
            throw new VaultException("No data found at Vault path: " + secretPath);
        }

        if (response.getDataObject().get(propName) == null) {
            throw new VaultException("Property '" + propName + "' not found in secret at path: " + secretPath);
        }

        JsonObject dataObject = response.getDataObject();
        String value = dataObject.get(propName).asString();
        if (value == null) {
            throw new VaultException("Property '" + propName + "' is null in secret at path: " + secretPath);
        }

        logger.debug("Attempt {} - Successfully retrieved {} from Vault", attemptNumber, propName);

        //Check expiry time and throw an exception if the token is expiring soon, so that the users can renew it, before it fully expires.
        checkTokenExpiryTime(config);

        return value;
    }

    private static void checkTokenExpiryTime(VaultConfig config) {
        //If app mode is dev, don't check token expiry time.
        if(!PropertiesManager.isDev()) {
            return;
        }

        LogicalResponse resp = null;
        try {
            VaultConfig sysCfg = new VaultConfig()
                    .address(config.getAddress())
                    .token(config.getToken())
                    .engineVersion(1)         // <- important
                    .build();
            Vault sysVault = new Vault(sysCfg);
            // Lookup this token’s details
            resp = sysVault.logical().read("auth/token/lookup-self");
        } catch (VaultException e) {
           logger.error("Error checking token expiry time", e);
        }

        Map<String, String> data = resp.getData();

        // TTL (seconds remaining)
        long ttlSeconds = Long.parseLong(data.get("ttl"));  // e.g., 86399

        // Whether it can be renewed
        boolean renewable = Boolean.parseBoolean(data.get("renewable"));

        // Fixed expire time (ISO-8601) appears for non-periodic tokens;
        // periodic tokens typically won’t include a fixed expire_time.
        String expireTime = data.get("expire_time");        // may be null

        // For periodic tokens, you may also see a "period" (e.g., "768h")
        String period = data.get("period");                 // may be null

        // Compute a soft “expiresAt” from TTL for reminders
        Instant expiresAt = Instant.now().plusSeconds(ttlSeconds);

        // Example threshold: notify when < 35 days left
        boolean notify = ttlSeconds < 10 * 24 * 3600;
        if (notify) {
            throw new RuntimeException("Vault token expiring soon. Renew asap." +
                    "vault token renew <new_token>" +
                    "vault token create \\\n" +
                    "  -policy=\"dev-app-policy\" \\\n" +
                    "  -period=2280h \\\n" +
                    "  -orphan");
        }
    }

    /**
     * Gets authentication token based on the configured method
     */
    private static String getAuthToken(VaultConfig config, String vaultAuthentication, String vaultToken,
                                       String vaultRole, String vaultServiceAccountTokenFile) throws Exception {

        if ("token".equalsIgnoreCase(vaultAuthentication)) {
            if (vaultToken == null || vaultToken.trim().isEmpty()) {
                throw new IllegalArgumentException("Vault token is required for token authentication");
            }
            logger.debug("Using provided Vault token authentication");
            return vaultToken;

        } else if ("kubernetes".equalsIgnoreCase(vaultAuthentication)) {
            logger.debug("Using Kubernetes authentication");
            return loginWithKubernetesAuth(config, vaultRole, vaultServiceAccountTokenFile);

        } else {
            throw new IllegalArgumentException("Unsupported Vault authentication method: " + vaultAuthentication +
                    ". Supported methods: token, kubernetes");
        }
    }

    /**
     * Performs Kubernetes authentication with Vault
     */
    private static String loginWithKubernetesAuth(VaultConfig config, String vaultRole, String vaultServiceAccountTokenFile) throws Exception {

        if (vaultRole == null || vaultRole.trim().isEmpty()) {
            throw new IllegalArgumentException("Vault role is required for Kubernetes authentication");
        }

        if (vaultServiceAccountTokenFile == null || vaultServiceAccountTokenFile.trim().isEmpty()) {
            throw new IllegalArgumentException("Service account token file path is required for Kubernetes authentication");
        }

        // Check cache first
        String cacheKey = vaultRole + ":" + vaultServiceAccountTokenFile;
        CachedToken cached = tokenCache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            logger.debug("Retrieved Kubernetes auth token from cache for role: {}", vaultRole);
            return cached.token;
        }

        logger.debug("Attempting Kubernetes authentication with role: {}", vaultRole);

        Vault vault = new Vault(config);
        Auth authClient = vault.auth();

        Path tokenPath = Paths.get(vaultServiceAccountTokenFile);
        logger.debug("Reading Kubernetes service account token from: {}", tokenPath);

        if (!Files.exists(tokenPath)) {
            throw new VaultException("Kubernetes service account token not found at: " + tokenPath);
        }

        String jwt = Files.readString(tokenPath).trim();
        if (jwt.isEmpty()) {
            throw new VaultException("Kubernetes service account token file is empty: " + tokenPath);
        }

        logger.debug("Successfully read Kubernetes service account token (length: {})", jwt.length());

        try {
            AuthResponse authResponse = authClient.loginByJwt("kubernetes", vaultRole, jwt);
            if (authResponse == null || authResponse.getAuthClientToken() == null) {
                throw new VaultException("Failed to authenticate with Vault using Kubernetes auth. No token in response.");
            }

            String token = authResponse.getAuthClientToken();

            // Cache the token
            tokenCache.put(cacheKey, new CachedToken(token, Duration.ofMinutes(TOKEN_CACHE_TTL_MINUTES)));

            logger.debug("Successfully authenticated with Vault using Kubernetes auth");
            return token;

        } catch (Exception e) {
            logger.error("Kubernetes authentication failed. Role: {}, Error: {}", vaultRole, e.getMessage(), e);
            throw new VaultException("Kubernetes authentication failed: " + e.getMessage());
        }
    }

    /**
     * Clears all cached tokens and secrets. Useful for testing or when credentials change.
     */
    public static void clearCache() {
        tokenCache.clear();
        secretCache.clear();
        logger.info("Cleared Vault token and secret caches");
    }

    /**
     * Gets cache statistics for monitoring purposes
     */
    public static String getCacheStats() {
        return String.format("Token cache: %d entries, Secret cache: %d entries",
                tokenCache.size(), secretCache.size());
    }
}