package com.aphe.common.mail;

import com.aphe.common.util.JSONUtils;
import com.aphe.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Component
@Service
@Transactional
public class MailService {

    private static Logger logger = LoggerFactory.getLogger(MailService.class);

    @Autowired
    MailRepository mailRepo;

    @Autowired
    private EmailSenderService emailSender;

    @Autowired
    JSONUtils jsonUtil;

    @Value("${aphe.sendInBlue.apiKey}")
    private String sendInBlueApiKey;

    @Value("${aphe.product.url}")
    private String productURL;

    @Value("${aphe.product.appName}")
    public String appName;

    @Value("${aphe.support.email}")
    public String supportEmail;

    @Value("${aphe.support.name}")
    public String supportName;

    @Value("${aphe.support.address.line1}")
    public String addressLine1;

    @Value("${aphe.support.address.city}")
    public String addressCity;

    @Value("${aphe.support.address.state}")
    public String addressState;

    @Value("${aphe.support.address.zip}")
    public String addressZip;

    @Value("${aphe.support.phone}")
    public String supportPhone;

    @Value("${aphe.support.hours}")
    public String supportHours;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Email createEmail(Email mail) {

        Map<String, Object> mailProperties = new HashMap<String, Object>();

        mailProperties.putAll(jsonUtil.jsonObjectToMap(mail.getParams()));
        addDefaultProperties(mailProperties);
        addUserProperties(mail.getMailTo().get(0), mailProperties);

        mail.setParams(jsonUtil.mapToJSONObject(mailProperties));

        if (StringUtil.isEmpty(mail.getFromAddress())) {
            mail.setFromName(supportName);
            mail.setFromAddress(supportEmail);
        }

        if (mail.getId() != null && mail.getId() > 0) {
            Email existingEntity = mailRepo.findById(mail.getId()).orElse(null);
            Email savedFiling = mailRepo.mergeAndSave(mail, existingEntity);
            return savedFiling;
        } else {
            return mailRepo.save(mail);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Async("applicationTaskExecutor")
    public void sendEmail(long mailId) {
        //generate random id for execution
        UUID uuid = java.util.UUID.randomUUID();
        String uuidString = uuid.toString();
        try {
            Email m = mailRepo.findById(mailId).orElse(null);
            if (m == null) {
                logger.error("Email not found with id=" + mailId);
                return;
            }
            if (m.getEmailStatus() != EmailStatus.Created) {
                logger.error("Email not in Created email_status=" + m.getEmailStatus());
                return;
            }

            try {
                m.setEmailStatus(EmailStatus.Sending);
                mailRepo.saveAndFlush(m);
//				updateEmailStatus(m.getId(), EmailStatus.Sending);
            } catch (Exception e) {
                logger.error("Error updating email status to Sending for id={}, email_status={}. Execution_id={}", m.getId(), m.getEmailStatus(), uuidString);
                return;
            }
            try {
                emailSender.sendEmail(m);
            } catch (Exception e) {
                logger.error("Error sending email id={}, message={} Execution_id={}", mailId, e.getMessage(), uuidString);
                logger.error(e.getMessage(), e);
                //Refresh the email object and commit the status.
                mailRepo.refresh(m);
                m.setEmailStatus(EmailStatus.Errored);
                mailRepo.saveAndFlush(m);
                return;
            }
            try {
                m.setEmailStatus(EmailStatus.Sent);
                mailRepo.saveAndFlush(m);
//                updateEmailStatus(m.getId(), EmailStatus.Sent);
            } catch (Exception e) {
                logger.error("Error updating email status to Sent for id={}, email_status={}. Execution_id={}", m.getId(), m.getEmailStatus(), uuidString);
                return;
            }
        } catch (Exception e) {
            logger.error("Error sending email id={}, message={} Execution_id={}", mailId, e.getMessage(), uuidString);
            logger.error(e.getMessage(), e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Email updateEmailStatus(long mailId, EmailStatus targetStatus) {
        Email existingEntity = mailRepo.findById(mailId).orElse(null);
        if (existingEntity != null) {
            existingEntity.setEmailStatus(targetStatus);
            mailRepo.save(existingEntity);
        }
        return existingEntity;
    }


    public List<Email> getQueuedEmails() {
        Page<Email> toBeSentMailsPage = mailRepo.findByEmailStatus(EmailStatus.Created, PageRequest.of(0, 10, Sort.by("id")));
        List<Email> toBeSentMails = toBeSentMailsPage.getContent();
        return toBeSentMails;
    }

    public List<Email> getErroredEmails() {
        Page<Email> erroredEmailsPage = mailRepo.findByEmailStatus(EmailStatus.Errored, PageRequest.of(0, 10, Sort.by("id")));
        List<Email> toBeSentMails = erroredEmailsPage.getContent();
        return toBeSentMails;
    }

    public List<EmailCount> getEmailCounts() {
        return mailRepo.emailCountsByStatus();
    }


    private void addUserProperties(String emailAddress, Map<String, Object> mailAttributes) {
        InternetAddress address;
        try {
            address = new InternetAddress(emailAddress);
            mailAttributes.put("FIRST_NAME", address.getPersonal());
            mailAttributes.put("LAST_NAME", address.getPersonal());
            mailAttributes.put("EMAIL", address.getAddress());
        } catch (AddressException e) {
        }
    }

    private void addDefaultProperties(Map<String, Object> mailAttributes) {

        String theProductURL = productURL;
        Object campaignParams = mailAttributes.get("CAMPAIGN_PARAMS");
        if (campaignParams instanceof Map && ((Map<?, ?>) campaignParams).size() > 0) {
            StringBuffer queryString = concatParams((HashMap<String, String>) campaignParams);
            theProductURL = theProductURL + "?" + queryString.toString();
        }
        mailAttributes.put("PRODUCT_URL_PARAMS", theProductURL);
        mailAttributes.put("PRODUCT_URL", productURL);
        mailAttributes.put("PRODUCT_NAME", appName);
        mailAttributes.put("FROM_NAME", supportName);
        mailAttributes.put("ADDRESS_LINE1", addressLine1);
        mailAttributes.put("CITY", addressCity);
        mailAttributes.put("STATE", addressState);
        mailAttributes.put("ZIP", addressZip);

        mailAttributes.put("SUPPORT_NAME", supportName);
        mailAttributes.put("SUPPORT_EMAIL", supportEmail);
        mailAttributes.put("SUPPORT_PHONE", supportPhone);
        mailAttributes.put("SUPPORT_HOURS", supportHours);

        mailAttributes.put("profile", activeProfile);
    }

    public StringBuffer concatParams(HashMap<String, String> params) {
        StringBuffer queryString = new StringBuffer();
        for (String key : params.keySet()) {
            String encodedValue = "";
            String encodedKey = "";
            try {
                encodedKey = URLEncoder.encode(key, "UTF-8");
                encodedValue = URLEncoder.encode(params.get(key), "UTF-8");
            } catch (UnsupportedEncodingException e) {
            }
            queryString.append(encodedKey).append("=").append(encodedValue).append("&");
        }
        if (queryString.length() > 0)
            queryString.deleteCharAt(queryString.length() - 1);
        return queryString;
    }

}
