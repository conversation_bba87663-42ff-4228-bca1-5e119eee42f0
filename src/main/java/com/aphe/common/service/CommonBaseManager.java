package com.aphe.common.service;

import com.aphe.common.graphql.PageMetadata;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.D2DAccess;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.hibernate.validator.constraints.ValidationErrors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.*;
import java.util.stream.Collectors;

public class CommonBaseManager {

	@Autowired
	protected Validator validator;

	protected Logger logger = LoggerFactory.getLogger(getClass());


	private ApheUserDetails getApheUserDetails() {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth != null && auth.isAuthenticated()) {
			Object principal = auth.getPrincipal();
			if(principal instanceof  ApheUserDetails) {
				return (ApheUserDetails) principal;
			}
		}
		return null;
	}

	public String getLoggedInUserName() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.getUsername();
		}
		return null;
	}

	public long getLoggedInUserId() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.getUserId();
		}
		return -1;
	}
	
	public String getLoggedInUserEmail() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.getUsername();
		}
		return null;
	}

	public boolean isAccountant() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			long parentId = userDetails.getParentDomainId();
			long currentDomainId = userDetails.getDomainId();
			String domainType = userDetails.getDomainType();
			if(parentId > 0 && parentId != currentDomainId) {
				return true;
			} else {
				return domainType.equalsIgnoreCase("A");
			}
		}
		return false;
	}

	public Long getCurrentDomainId() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			long domainId = userDetails.getDomainId();
			if (domainId > 0)
				return domainId;
		}
		return null;
	}
	
	public Long getParentDomainId() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			long parentDomainId = userDetails.getParentDomainId();
			if (parentDomainId > 0)
				return parentDomainId;
		}
		return null;
	}
	
	public Long getBillingDomainId() {
		Long parentDomainId = getParentDomainId();
		if (parentDomainId == null) {
			return getCurrentDomainId();
		} else {
			return parentDomainId;
		}
	}	

	public boolean hasRole(String s) {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.getAuthorities().contains(s);
		}
		return false;
	}
	
	public boolean isEmailAddressConfirmed() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.isEmailConfirmed();
		}
		return false;
	}
	
	
	public boolean isSystemUser() {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth != null && auth.isAuthenticated()) {
			if(auth.getAuthorities() != null) {
				for(GrantedAuthority role: auth.getAuthorities()) {
					if ("superadmin".equalsIgnoreCase(role.getAuthority())) {
						return true;
					}
				}
			}
		}
		return false;
	}

	public boolean isSuperAdmin() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.isSuperAdmin();
		}
		return false;
	}

	public String getSuperAdminLoginName() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.getSuperAdminLoginName();
		}
		return "";
	}
	
	public Collection<GrantedAuthority> getDoaminRoles() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.getAuthorities();
		}
		return Collections.emptyList();
	}

	public Collection<D2DAccess> getDomainRelations() {
		ApheUserDetails userDetails = getApheUserDetails();
		if(userDetails != null) {
			return userDetails.getDomainRelations();
		}
		return Collections.EMPTY_LIST;
	}


	protected boolean isDomainAccessible(Long domainId) {
		Long currentDomainId = getCurrentDomainId();
		Long parentDoaminId = getParentDomainId();
		boolean matchesCurrentDomain = currentDomainId != null && domainId != null && (currentDomainId == domainId || isSystemUser());
		boolean matchesParentDomain = parentDoaminId != null && domainId != null && (parentDoaminId == domainId || isSystemUser());
		return matchesCurrentDomain || matchesParentDomain;
	}

	public ValidationErrors convertConstraintViolations(Set<? extends ConstraintViolation<?>> violations) {
		Map<String, Set<String>> responseObj = new HashMap<String, Set<String>>();
		for (ConstraintViolation<?> violation : violations) {
			String propertyPath = violation.getPropertyPath().toString();
			Set<String> messages = responseObj.get(propertyPath);
			if (messages == null) {
				messages = new HashSet<String>();
				responseObj.put(propertyPath, messages);
			}
			messages.add(violation.getMessage());
		}
		// MapWrapper<String, Set<String>> mapWrapper = new MapWrapper<String, Set<String>>(responseObj);
		ValidationErrors errors = new ValidationErrors();
		errors.setMessages(responseObj);
		return errors;
	}

	public ValidationErrors getValidationErrors(Object dto) {
		Set<ConstraintViolation<Object>> constraintViolations = validator.validate(dto);
		return convertConstraintViolations(constraintViolations);
	}

	public final ValidationErrors buildError(String message) {
		ValidationErrors errors = new ValidationErrors();
		errors.addMessage("Message", message);
		return errors;
	}
	
	public PageMetadata buildPageMetadata(Page<? extends Object> page) {
		PageMetadata pageMetaData = new PageMetadata();
		pageMetaData.currentPage = page.getNumber();
		pageMetaData.totalPages = page.getTotalPages();
		pageMetaData.currentPageCount = page.getNumberOfElements();
		pageMetaData.totalCount = page.getTotalElements();
		pageMetaData.hasNextPage = page.hasNext();
		pageMetaData.hasPreviousPage = page.hasPrevious();
		return pageMetaData;
	}

	//Aphelion (dId=1, sourceId/payeeId=101) issued a 1099 to Indu Law Firm (dId=2, targetId,customerId=201)
	//Aphelion is the payer and Indu Law Firm is the Payee.
	//Once vendor portal access is established for Indu, Indu logs in to see all the payers/issuers of 1099....
	//So for Indu's domain (target domainId of a relation), find the sourceDOmainIds's sourceEntityIds for a given relation type.
	public List<Long> getDomainSourceEntityIds(String relationType) {
		Collection<D2DAccess> domainAccessList = getDomainRelations();
		//Traverse the domain relations of this domain, find the actual payeeId for which we want to look up filings by...
		List<Long> sourceEntityIds = domainAccessList.stream()
				.filter(d2d->d2d.getRelType().equalsIgnoreCase(relationType))
				.map(d2d->d2d.getSourceSubEntityId())
				.collect(Collectors.toList());
		return sourceEntityIds;
	}


}
