package com.aphe.common.model;

import com.aphe.common.beanutils.BeanUtil;
import jakarta.transaction.Transactional;
import jakarta.transaction.Transactional.TxType;
import org.springframework.stereotype.Component;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PostLoad;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import java.util.UUID;

@Component
public class BaseEntityUUIDListener {

	@PostLoad
	@Transactional(value = TxType.MANDATORY)
	public void addUUID(Object entity) {
		if (!(entity instanceof BaseEntity)) {
			return;
		}
		BaseEntity baseEntity = (BaseEntity) entity;
		String currentUUID = baseEntity.getGlobalId();
		if (currentUUID == null) {
			baseEntity.setGlobalId(UUID.randomUUID().toString());
			EntityManager entityManager = BeanUtil.getBean(EntityManager.class);
			entityManager.persist(baseEntity);
		}
	}

	@PrePersist
	@PreUpdate
	public void updateUUID(Object entity) {
		if (!(entity instanceof BaseEntity)) {
			return;
		}
		BaseEntity baseEntity = (BaseEntity) entity;
		String currentUUID = baseEntity.getGlobalId();
		if (currentUUID == null) {
			baseEntity.setGlobalId(UUID.randomUUID().toString());
		}
	}

}