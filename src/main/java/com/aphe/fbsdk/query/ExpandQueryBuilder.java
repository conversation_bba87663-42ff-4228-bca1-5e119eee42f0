package com.aphe.fbsdk.query;

import com.google.common.base.Joiner;

import java.util.ArrayList;
import java.util.List;

public class ExpandQueryBuilder implements QueryBuilder {

    private List<String> expandAttributes = new ArrayList<>();

    public ExpandQueryBuilder() {
    }

    public ExpandQueryBuilder(String attribute) {
        this.expandAttributes.add(attribute);
    }

    public ExpandQueryBuilder add(String attribute) {
        this.expandAttributes.add(attribute);
        return this;
    }

    @Override
    public String toString() {
        return "ExpandQueryBuilder{" +
                "attributes=" + expandAttributes +
                '}';
    }

    @Override
    public List<String[]> getQueryParams() {
        String key = "include[]";
        String value = Joiner.on(",").join(expandAttributes);
        String[] params = {key, value};
        ArrayList<String[]> list = new ArrayList<>();
        list.add(params);
        return list;
    }


}
