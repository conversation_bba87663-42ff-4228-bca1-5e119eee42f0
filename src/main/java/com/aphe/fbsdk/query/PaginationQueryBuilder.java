package com.aphe.fbsdk.query;

import java.util.ArrayList;
import java.util.List;

public class PaginationQueryBuilder implements QueryBuilder {

    private static final int MAX_PER_PAGE = 100;
    private static final int MIN_PAGE = 1;

    private int page;
    private int perPage;

    public PaginationQueryBuilder() {
        this.page = MIN_PAGE;
        this.perPage = MAX_PER_PAGE;
    }

    public PaginationQueryBuilder(int page, int perPage) {
        this.page = Math.max(page, MIN_PAGE);
        this.perPage = Math.min(perPage, MAX_PER_PAGE);
    }

    public PaginationQueryBuilder page(int page) {
        this.page = Math.max(page, MIN_PAGE);
        return this;
    }

    public PaginationQueryBuilder perPage(int perPage) {
        this.perPage = Math.min(perPage, MAX_PER_PAGE);
        return this;
    }

    @Override
    public String toString() {
        return "PaginationQueryBuilder{" +
                "page=" + page +
                ", perPage=" + perPage +
                '}';
    }

    @Override
    public List<String[]> getQueryParams() {
        ArrayList<String[]> list = new ArrayList<>();
        list.add(new String[]{"page", String.valueOf(page)});
        list.add(new String[]{"per_page", String.valueOf(perPage)});
        return list;
    }
}
