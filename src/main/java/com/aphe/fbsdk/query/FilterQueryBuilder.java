package com.aphe.fbsdk.query;

import com.aphe.fbsdk.query.filters.EqualsFilter;
import com.aphe.fbsdk.query.filters.Filter;
import com.aphe.fbsdk.query.filters.InFilter;
import com.aphe.fbsdk.query.filters.SearchFilter;
import com.aphe.fbsdk.util.Util;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class Filter<PERSON>ueryBuilder implements QueryBuilder {

    protected List<Filter> filters = new ArrayList<>();

    public FilterQueryBuilder addEquals(String field, String value) {
        this.filters.add(new EqualsFilter(field, value));
        return this;
    }

    public FilterQueryBuilder addBoolean(String field, boolean value) {
        this.filters.add(new EqualsFilter(field, String.valueOf(value)));
        return this;
    }

//    //Not tested.
//    public FilterQueryBuilder addDate(String field, LocalDate value) {
//        this.filters.add(new EqualsFilter(field, value.format(Util.getStandardDateFormatter())));
//        return this;
//    }

//    public FilterQueryBuilder addDateTime(String field, ZonedDateTime value) {
//        this.filters.add(new EqualsFilter(field, value.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)));
//        return this;
//    }

    public FilterQueryBuilder addIn(String field, List<String> values) {
        if (!"s".equals(field.substring(field.length() - 1))) {
            field = field + "s";
        }
        this.filters.add(new InFilter(field, values));
        return this;
    }

    public FilterQueryBuilder addBetween(String field, String minValue, String maxValue) {
        this.filters.add(new SearchFilter(field + "_min", String.valueOf(minValue)));
        this.filters.add(new SearchFilter(field + "_max", String.valueOf(maxValue)));
        return this;
    }

    public FilterQueryBuilder addBetween(String field, LocalDate minDate, LocalDate maxvalue) {
        String minDateValue = Util.LOCAL_DATE_FORMATTER.format(minDate);
        String maxDateValue = Util.LOCAL_DATE_FORMATTER.format(maxvalue);
        this.filters.add(new SearchFilter(field + "_min", String.valueOf(minDateValue)));
        this.filters.add(new SearchFilter(field + "_max", String.valueOf(maxDateValue)));
        return this;
    }

    @Override
    public List<String[]> getQueryParams() {
        ArrayList<String[]> list = new ArrayList<>();
        for (Filter filter : this.filters) {
            list.addAll(filter.getQueryParams());
        }
        return list;
    }
}
