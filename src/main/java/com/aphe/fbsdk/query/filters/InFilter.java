package com.aphe.fbsdk.query.filters;

import java.util.ArrayList;
import java.util.List;

public class InFilter implements Filter {
    String field;
    List<String> values;

    public InFilter(String field, List<String> values) {
        this.field = field;
        this.values = values;
    }

    @Override
    public List<String[]> getQueryParams() {
        ArrayList<String[]> list = new ArrayList<>();
        String key = "search[" + field + "][]";
        for (String value: this.values) {
            String[] params = {key, value};
            list.add(params);
        }
        return list;
    }
}