package com.aphe.fbsdk.query.filters;


import java.util.ArrayList;
import java.util.List;

public class SearchFilter implements Filter {
    String field;
    String value;

    public SearchFilter(String field, String value) {
        this.field = field;
        this.value = value;
    }

    @Override
    public List<String[]> getQueryParams() {
        String key = "search[" + field + "]";
        String[] params = {key, value};
        ArrayList<String[]> list = new ArrayList<>();
        list.add(params);
        return list;
    }
}


