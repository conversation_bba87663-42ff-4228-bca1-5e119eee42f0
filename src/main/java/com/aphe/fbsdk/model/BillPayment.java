package com.aphe.fbsdk.model;

import com.aphe.fbsdk.model.enums.PaymentType;
import com.aphe.fbsdk.model.enums.VisState;

import java.time.LocalDate;

public class BillPayment {

    public String id;
    public String billid;
    public Money amount;
    public String payment_type;
    public PaymentType getPaymentType() {
        return PaymentType.fromValue(payment_type);
    }

    public LocalDate paid_date;
    public String note;

    public boolean matched_with_expense;

    public String created_at;
    public String updated_at;
    public String vis_state;
    public VisState getVisState() {
        return VisState.fromValue(vis_state);
    }

}

