package com.aphe.fbsdk.model.enums;


//“unpaid”, “overdue”, “partial”, “paid”
public enum BillStatus {
    PAID("paid", "Paid"),
    UNPAID("unpaid", "Unpaid"),
    OVERDUE("overdue", "Overdue"),
    PARTIAL("partial", "Partial");

    private String value;
    private String displayName;

    BillStatus(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static BillStatus fromValue(String value) {
        for (BillStatus billStatus : BillStatus.values()) {
            if (billStatus.getValue().equalsIgnoreCase(value)) {
                return billStatus;
            }
        }
        return null;
    }
}