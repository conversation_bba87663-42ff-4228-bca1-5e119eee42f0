package com.aphe.fbsdk.model.enums;


//“unpaid”, “overdue”, “partial”, “paid”
public enum PaymentType {
    TWO_CHECKOUT("2Checkout", "2Checkout"),
    ACH("ACH", "ACH"),
    AMEX("Amex", "Amex"),
    BANK_TRANSFER("Bank Transfer", "Bank Transfer"),
    CASH("Cash", "Cash"),
    CHECK("Check", "Check"),
    CREDIT("Credit", "Credit"),
    CREDIT_CARD("Credit Card", "Credit Card"),
    DEBIT("Debit", "Debit"),
    DINERS("Diners", "Diners"),
    DISCOVER("Discover", "Discover"),
    EURO_CARD("Eurocard", "Eurocard"),
    JCB("JCB", "JCB"),
    MASTERCARD("MasterCard", "MasterCard"),
    NOVA("Nova", "Nova"),
    OTHER("Other", "Other"),
    PAYPAL("PayPal", "PayPal"),
    VISA("Visa", "Visa"),
    UNKNOWN("", "Unknown");

    private String value;
    private String displayName;

    PaymentType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static PaymentType fromValue(String value) {
        for (PaymentType billStatus : PaymentType.values()) {
            if (billStatus.getValue().equalsIgnoreCase(value)) {
                return billStatus;
            }
        }
        return UNKNOWN;
    }
}