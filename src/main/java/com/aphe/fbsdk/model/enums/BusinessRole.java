package com.aphe.fbsdk.model.enums;


//@Value("owner") OWNER,
//@Value("manager") MANAGER,
//@Value("business_accountant") BUSINESS_ACCOUNTANT,
//@Value("business_manager") BUSINESS_MANAGER,
//@Value("business_employee") BUSINESS_EMPLOYEE,
//@Value("business_partner") BUSINESS_PARTNER,
//@Value("systemless_owner") SYSTEMLESS_OWNER,
//@Value("systemless_manager") SYSTEMLESS_MANAGER,

public enum BusinessRole {
    OWNER("owner", "Owner"),
    MANAGER("manager", "manager"),
    BUSINESS_ACCOUNTANT("business_accountant", "business_accountant"),
    BUSINESS_MANAGER("business_manager", "business_manager"),
    BUSINESS_EMPLOYEE("business_employee", "business_employee"),
    BUSINESS_PARTNER("business_partner", "business_partner"),
    SYSTEMLESS_OWNER("systemless_owner", "Partial"),
    SYSTEMLESS_MANAGER("systemless_manager", "systemless_manager"),
    UNKNOWN("", "Unkoown");


    private String value;
    private String displayName;

    BusinessRole(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static BusinessRole fromValue(String value) {
        for (BusinessRole billStatus : BusinessRole.values()) {
            if (billStatus.getValue().equalsIgnoreCase(value)) {
                return billStatus;
            }
        }
        return UNKNOWN;
    }
}