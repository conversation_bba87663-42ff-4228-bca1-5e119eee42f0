package com.aphe.fbsdk.model.enums;


//@Value INTERNAL(0), // internal rather than client
//@Value OUTSTANDING(1), // has client, needs to be applied to an invoice
//@Value INVOICED(2), // has client, attached to an invoice
//@Value RECOUPED(4); // has client, attached to an invoice, and paid

public enum ExpenseStatus {
    INTERNAL("0", "Internal"),
    OUTSTANDING("1", "Outstanding"),
    INVOICED("2", "Invoiced"),
    RECOUPED("4", "Recouped");

    private String value;
    private String displayName;

    ExpenseStatus(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static ExpenseStatus fromValue(String value) {
        for (ExpenseStatus billStatus : ExpenseStatus.values()) {
            if (billStatus.getValue().equalsIgnoreCase(value)) {
                return billStatus;
            }
        }
        return null;
    }
}