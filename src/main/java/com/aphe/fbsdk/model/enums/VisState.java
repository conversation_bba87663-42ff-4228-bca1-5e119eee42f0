package com.aphe.fbsdk.model.enums;

//0 for active, 1 for deleted, 2 for archived
public enum VisState {
    ACTIVE("0", "Active"),
    DELETED("1", "Deleted"),
    ARCHIVED("2", "Archived");

    private String value;
    private String displayName;

    VisState(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static VisState fromValue(String value) {
        for (VisState billStatus : VisState.values()) {
            if (billStatus.getValue().equalsIgnoreCase(value)) {
                return billStatus;
            }
        }
        return null;
    }
}