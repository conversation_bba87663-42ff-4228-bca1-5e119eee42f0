package com.aphe.fbsdk.model;

import com.aphe.fbsdk.model.enums.ExpenseStatus;
import com.aphe.fbsdk.model.enums.VisState;

import java.time.LocalDate;

public class Expense {

    public String id;

    public LocalDate date;
    public String categoryid;
    public String status;
    public ExpenseStatus getExpenseStatus() {
        return ExpenseStatus.fromValue(status);
    }
    public String bank_name;
    public String notes;
    public Money amount;

    public boolean isduplicate;
    public boolean has_receipt;

    public String markup_percent;

    public String accountid;
    public String account_name;
    public String projectid;
    public String clientid;
    public String profileid;
    public String transactionid;
    public String invoiceid;
    public String vendor;
    public String staffid;
    public String expenseid;

    public String taxPercent1;
    public String taxName1;
    public Money taxAmount1;

    public String taxPercent2;
    public String taxName2;
    public Money taxAmount2;

    public boolean compounded_tax;

    public String ext_systemid;
    public String accounting_systemid;
    public String background_jobid;
    public String ext_invoiceid;

    public String created_at;
    public String updated;
    public String vis_state;
    public VisState getVisState() {
        return VisState.fromValue(vis_state);
    }


}

