package com.aphe.fbsdk.model;

import com.aphe.fbsdk.model.enums.VisState;

public class Vendor {

    public String vendorid;
    public String vendor_name;

    public String account_number;
    public String currency_code;
    public boolean is_1099;
    public String language;
    public String note;

//    public Money outstanding_balance;
//    public Money overdue_balance;

    public String phone;
    public String primary_contact_email;
    public String primary_contact_first_name;
    public String primary_contact_last_name;
    public String website;

    public String street;
    public String street2;
    public String city;
    public String province;
    public String country;
    public String postal_code;

    public String created_at;
    public String updated_at;
    public String vis_state;

    public VisState getVisState() {
        return VisState.fromValue(vis_state);
    }


}
