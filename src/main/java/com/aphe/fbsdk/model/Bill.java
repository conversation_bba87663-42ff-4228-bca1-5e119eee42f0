package com.aphe.fbsdk.model;

import com.aphe.fbsdk.model.enums.BillStatus;
import com.aphe.fbsdk.model.enums.VisState;

import java.time.LocalDate;
import java.util.List;

public class Bill {

    public String id;
    public Vendor vendor;

    public String status;

    public BillStatus getStatus() {
        return BillStatus.fromValue(status);
    }

    public String currency_code;
    public Money amount;
    public Money paid;
    public Money outstanding;
    public Money tax_amount;
    public Money total_amount;
    public String language;

    public LocalDate due_date;
    public LocalDate issue_date;
    public int due_offset_days;

    public String bill_number;
    public String overall_category;
    public String overall_description;

    public List<BillLine> lines;
    public List<BillPayment> bill_payments;

    public String created_at;
    public String updated_at;
    public String vis_state;

    public VisState getVisState() {
        return VisState.fromValue(vis_state);
    }


    public static class BillLine {
        public String categoryid;
        public ExpenseCategory category;
        public int list_index;
        public String description;
        public double quantity;
        public Money unit_cost;
        public Money amount;
        public Money total_amount;

        public String tax_name1;
        public String tax_name2;
//        public int tax_percent1;
//        public int tax_percent2;

        public String tax_authorityid1;
        public String tax_authorityid2;
//        public Money tax_amount1;
//        public Money tax_amount2;
    }


}

