package com.aphe.fbsdk.util;

import com.aphe.fbsdk.exceptions.FBAuthorizationException;
import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.query.ExpandQueryBuilder;
import com.aphe.fbsdk.query.PaginationQueryBuilder;
import com.aphe.fbsdk.query.QueryBuilder;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class FBEntityManager<T> {

    final static Logger logger = LogManager.getLogger(FBEntityManager.class);

    public HttpHeadersModel httpHeader;
    public String baseURL;
    public String accountId;

    protected abstract String getOnePath(String accountId, String id);

    protected abstract String getListPath(String accountId);

    protected abstract List<T> convertToList(ObjectMapper mapper, JSONObject json) throws FBException;

    protected abstract T convertToOne(ObjectMapper mapper, JSONObject json) throws FBException;

    public FBEntityManager(String baseURL, String accessToken, String accountId) {
        this.baseURL = baseURL;
        this.httpHeader = new HttpHeadersModel();
        this.httpHeader.authorization = "Bearer " + accessToken;
        this.accountId = accountId;
    }

    public T getOne(String id, ExpandQueryBuilder expandQueryBuilder) throws FBException {
        return getOneInternal(accountId, id, expandQueryBuilder);
    }

    public List<T> getList(List<QueryBuilder> builders) throws FBException {
        return getListInternal(builders);
    }

    public List<T> getListAll(List<QueryBuilder> queries) throws FBException {
        List<T> allEntities = new ArrayList<>();
        QueryBuilder paginationQuery = queries.stream().filter(queryBuilder -> queryBuilder instanceof PaginationQueryBuilder).findAny().orElse(null);
        if (paginationQuery != null) {
            throw new FBException("Including pagination query when calling getListALl is not allowed");
        }
        boolean hasMorePages = true;
        int page = 1;
        do {
            List<QueryBuilder> pageQueries = new ArrayList<>(queries);
            pageQueries.add(new PaginationQueryBuilder().page(page));
            List<T> pageEntities = getListInternal(pageQueries);
            if (pageEntities != null && pageEntities.size() > 0) {
                allEntities.addAll(pageEntities);
                page++;
            } else {
                hasMorePages = false;
            }
        } while (hasMorePages);
        return allEntities;
    }

    protected T getOneInternal(String accountId, String id, ExpandQueryBuilder expandQueryBuilder) throws FBException {
        try {
            String responseJSON;
            T entityModel = null;


            URI uri = getUri(Arrays.asList(expandQueryBuilder), getOnePath(accountId, id));

            ResponseEntity<String> httpResponse = null;
            try {
                httpResponse = APIHelper.get(uri.toString(), this.httpHeader);
            } catch (Exception e) {
                if(e instanceof FBException) {
                    throw (FBException) e;
                } else {
                    throw new FBException(e);
                }
            }
            ;
            if (httpResponse.getStatusCode() == HttpStatus.UNAUTHORIZED || httpResponse.getStatusCode() == HttpStatus.FORBIDDEN) {
                throw new FBAuthorizationException(httpResponse.getBody());
            } else if (httpResponse.getStatusCode() == HttpStatus.OK) {
                responseJSON = httpResponse.getBody();
                JSONObject httpResponseJSONObject = new JSONObject(responseJSON);
                JSONObject responseJSONObject = httpResponseJSONObject.getJSONObject("response");
                if(responseJSONObject.has("result")) {
                    JSONObject resultJSONObject = responseJSONObject.getJSONObject("result");
                    entityModel = convertToOne(resultJSONObject);
                } else {
                    entityModel = convertToOne(responseJSONObject);
                }

            } else {
                throw new FBException(httpResponse.getBody());
            }
            return entityModel;
        } catch (FBException ex) {
            throw ex;
        }
    }

    protected List<T> getListInternal(List<QueryBuilder> builders) throws FBException {
        List<T> allEntities = new ArrayList<>();

        List<QueryBuilder> internalQueries = new ArrayList<>(builders);

        QueryBuilder paginationQuery = internalQueries.stream().filter(queryBuilder -> queryBuilder instanceof PaginationQueryBuilder).findAny().orElse(null);
        if (paginationQuery == null) {
            internalQueries.add(new PaginationQueryBuilder());
        }

        List<T> pageEntities = null;
        ResponseEntity<String> httpResponse = null;
        try {

            URI uri = getUri(internalQueries, getListPath(accountId));

            httpResponse = APIHelper.get(uri.toString(), this.httpHeader);
        } catch (FBException e) {
            throw e;
        }
        if (httpResponse.getStatusCode() == HttpStatus.UNAUTHORIZED || httpResponse.getStatusCode() == HttpStatus.FORBIDDEN) {
            throw new FBAuthorizationException(httpResponse.getBody());
        } else if (httpResponse.getStatusCode() == HttpStatus.NO_CONTENT) {
        } else if (httpResponse.getStatusCode() == HttpStatus.OK) {
            String responseJSON = httpResponse.getBody();
            if (responseJSON != null && responseJSON.length() > 0) {

                JSONObject httpResponseJSONObject = new JSONObject(responseJSON);
                JSONObject responseJSONObject = httpResponseJSONObject.getJSONObject("response");
                JSONObject resultJSONObject = responseJSONObject.getJSONObject("result");
                pageEntities = convertToList(resultJSONObject);
                if (pageEntities != null && pageEntities.size() > 0) {
                    allEntities.addAll(pageEntities);
                }
            }
        } else {
            throw new FBException(httpResponse.getBody());
        }
        return allEntities;
    }

    private URI getUri(List<QueryBuilder> internalQueries, String path) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.baseURL).path(path);
        for(QueryBuilder queryBuilder : internalQueries) {
            if(queryBuilder == null) {
                continue;
            }
            List<String[]> queryParams = queryBuilder.getQueryParams();
            for(String[] queryParam : queryParams) {
                builder = builder.queryParam(queryParam[0], queryParam[1]);
            }
        }
        URI uri = builder.build().toUri();
        return uri;
    }

    protected List<T> convertToList(JSONObject json) throws FBException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.registerModule(new JavaTimeModule());
        return convertToList(mapper, json);
    }

    protected T convertToOne(JSONObject json) throws FBException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.registerModule(new JavaTimeModule());
        return convertToOne(mapper, json);
    }


}
