package com.aphe.fbsdk.util;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class Util {

    public static final ZoneId UTC_ZONE = ZoneId.of("UTC");

    public static final ZoneId PARTNER_LOCAL_ZONE = ZoneId.of("US/Eastern");

    public static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");


    public static final DateTimeFormatter LOCAL_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    public static DateTimeFormatter getStandardDateFormatter() {
        return DateTimeFormatter.ofPattern("YYYY-MM-DDThh:mm:ss");
    }

    public static ZonedDateTime getZonedDateTimeFromProjectNaiveUTC(String dateString) {
        return LocalDateTime.parse(dateString).atZone(UTC_ZONE);
    }

    public static ZonedDateTime getZonedDateTimeFromISO(String dateString) {
        return ZonedDateTime.parse(dateString).withZoneSameInstant(ZoneId.of("UTC"));
    }


}
