package com.aphe.fbsdk.util;

public class RestTemplateException extends RuntimeException {
    private RestTemplateError restTemplateError;

    public RestTemplateException(RestTemplateError restTemplateError) {
        super(restTemplateError.responseBody);
        this.restTemplateError = restTemplateError;
    }

    public RestTemplateError getRestTemplateError() {
        return restTemplateError;
    }

    public void setRestTemplateError(RestTemplateError restTemplateError) {
        this.restTemplateError = restTemplateError;
    }
}
