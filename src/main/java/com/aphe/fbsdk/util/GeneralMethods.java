package com.aphe.fbsdk.util;


import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Random;

public class GeneralMethods {

    public static String GenerateRandomString() throws Exception {
        try {
            String characters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+\":?><";
            int length = 20, charactersLength = characters.length();
            String randomString = "";
            Random r = new Random();
            for (int i = 0; i < length; i++) {
                randomString += characters.charAt((r.nextInt((charactersLength - 1) - 0) + 0));
            }
            return randomString;
        } catch (Exception ex) {
            throw ex;
        }
    }

    public static String Base64UrlDecode(String uri) throws Exception {
        try {
            // Getting decoder
            Base64.Decoder decoder = Base64.getUrlDecoder();
            return new String(decoder.decode(uri), StandardCharsets.UTF_8);
        } catch (Exception ex) {
            throw ex;
        }
    }

}