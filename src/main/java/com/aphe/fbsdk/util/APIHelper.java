package com.aphe.fbsdk.util;

import com.aphe.fbsdk.exceptions.FBAuthorizationException;
import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.exceptions.FBUserException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.DefaultUriBuilderFactory;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class APIHelper {


    private static ResponseEntity<String> getStringResponseEntity(String uri, String data, HttpHeadersModel httpHeaders, HttpMethod httpMethod) throws FBException {
        String resourceUrl = uri;
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));
        restTemplate.setErrorHandler(new RestTemplateErrorHandler());



        DefaultUriBuilderFactory factory = new DefaultUriBuilderFactory();
        factory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.VALUES_ONLY);
        restTemplate.setUriTemplateHandler(factory);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", httpHeaders.contentType);
        if (httpHeaders.authorization != null && !httpHeaders.authorization.isEmpty())
            headers.add("Authorization", httpHeaders.authorization);

        HttpEntity<String> entity = new HttpEntity<>(data, headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(resourceUrl, httpMethod, entity, String.class);
            return response;
        } catch (Exception e) {
            if (e instanceof RestTemplateException) {
                RestTemplateError restTemplateError = ((RestTemplateException) e).getRestTemplateError();
                if (restTemplateError != null) {
                    if (restTemplateError.httpStatusCode == 401 || restTemplateError.httpStatusCode == 403) {
                        throw new FBAuthorizationException(e.getMessage());
                    } else {
                        ObjectMapper objectMapper = new ObjectMapper();
                        try {
                            JSONObject httpResponseJSONObject = new JSONObject(restTemplateError.responseBody);
                            JSONObject responseJSONObject = httpResponseJSONObject.getJSONObject("response");
                            JSONArray errorsJSONArray = responseJSONObject.getJSONArray("errors");
                            List<FBError> errors =  Arrays.asList(objectMapper.readValue(errorsJSONArray.toString(), FBError[].class));
                            throw new FBUserException(errors);
                        } catch (JsonProcessingException ex) {
                        }
                    }
                }
            }
            throw new FBException(e);
        }
    }

    public static ResponseEntity<String> get(String uri, HttpHeadersModel httpHeaders) throws FBException {
        return getStringResponseEntity(uri, null, httpHeaders, HttpMethod.GET);
    }

    public static ResponseEntity<String> post(String uri, String data, HttpHeadersModel httpHeaders) throws FBException {
        return getStringResponseEntity(uri, data, httpHeaders, HttpMethod.POST);
    }

    public static ResponseEntity<String> put(String uri, String data, HttpHeadersModel httpHeaders) throws FBException {
        return getStringResponseEntity(uri, data, httpHeaders, HttpMethod.PUT);
    }

    public static ResponseEntity<String> delete(String uri, String data, HttpHeadersModel httpHeaders) throws FBException {
        return getStringResponseEntity(uri, data, httpHeaders, HttpMethod.DELETE);
    }

}