package com.aphe.fbsdk.util;

import org.springframework.http.HttpStatusCode;

public class RestTemplateError {
    public HttpStatusCode status;
    public int httpStatusCode;
    public String responseBody;

    public  RestTemplateError(HttpStatusCode httpStatus, int httpStatusCode, String responseBody) {
        this.status = httpStatus;
        this.httpStatusCode = httpStatusCode;
        this.responseBody = responseBody;
    }

}
