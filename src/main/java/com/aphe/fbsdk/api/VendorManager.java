package com.aphe.fbsdk.api;

import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.Vendor;
import com.aphe.fbsdk.util.FBEntityManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

public class VendorManager extends FBEntityManager<Vendor> {
    public VendorManager(String baseURL, String accessToken, String accountId) {
        super(baseURL, accessToken, accountId);
    }

    @Override
    protected String getOnePath(String accountId, String id) {
        return  "accounting/account/" + accountId +  "/bill_vendors/bill_vendors/" + id;
    }

    @Override
    protected String getListPath(String accountId) {
        return  "accounting/account/" + accountId +  "/bill_vendors/bill_vendors";
    }

    @Override
    protected List<Vendor> convertToList(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONArray jsonArray = json.getJSONArray("bill_vendors");
            return Arrays.asList(mapper.readValue(jsonArray.toString(), Vendor[].class));
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }

    @Override
    protected Vendor convertToOne(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONObject jsonObject = json.getJSONObject("bill_vendor");
            return mapper.readValue(jsonObject.toString(), Vendor.class);
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }
}
