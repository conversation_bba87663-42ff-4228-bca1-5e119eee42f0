package com.aphe.fbsdk.api;

import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.Identity;
import com.aphe.fbsdk.query.QueryBuilder;
import com.aphe.fbsdk.util.FBEntityManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONObject;

import java.util.List;

public class IdentityManager extends FBEntityManager<Identity> {

    public IdentityManager(String baseURL, String accessToken, String accountId) {
        super(baseURL, accessToken, accountId);
    }

    @Override
    public List<Identity> getList(List<QueryBuilder> builders) throws FBException {
        throw new RuntimeException("Get list not supported on company");
    }

    @Override
    protected String getOnePath(String accountId, String id) {
        return "auth/api/v1/users/me";
    }

    @Override
    protected String getListPath(String accountId) {
        throw new RuntimeException("Get list not supported on company");
    }

    @Override
    protected List<Identity> convertToList(ObjectMapper mapper, JSONObject json) throws FBException {
        return null;
    }

    @Override
    protected Identity convertToOne(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            return mapper.readValue(json.toString(), Identity.class);
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }
}
