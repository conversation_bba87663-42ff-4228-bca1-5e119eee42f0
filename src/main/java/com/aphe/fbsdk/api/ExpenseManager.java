package com.aphe.fbsdk.api;

import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.Expense;
import com.aphe.fbsdk.util.FBEntityManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

public class ExpenseManager extends FBEntityManager<Expense> {

    public ExpenseManager(String baseURL, String accessToken, String accountId) {
        super(baseURL, accessToken, accountId);
    }

    @Override
    protected String getOnePath(String accountId, String id) {
        return  "accounting/account/" + accountId +  "/expenses/expenses/" + id;
    }

    @Override
    protected String getListPath(String accountId) {
        return  "accounting/account/" + accountId +  "/expenses/expenses";
    }

    @Override
    protected List<Expense> convertToList(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONArray jsonArray = json.getJSONArray("expenses");
            return Arrays.asList(mapper.readValue(jsonArray.toString(), Expense[].class));
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }

    @Override
    protected Expense convertToOne(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONObject jsonObject = json.getJSONObject("expense");
            return mapper.readValue(jsonObject.toString(), Expense.class);
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }
}
