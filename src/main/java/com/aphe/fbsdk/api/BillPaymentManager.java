package com.aphe.fbsdk.api;

import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.BillPayment;
import com.aphe.fbsdk.util.FBEntityManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

public class BillPaymentManager extends FBEntityManager<BillPayment> {

    public BillPaymentManager(String baseURL, String accessToken, String accountId) {
        super(baseURL, accessToken, accountId);
    }

    @Override
    protected String getOnePath(String accountId, String id) {
        return  "accounting/account/" + accountId +  "/bill_payments/bill_payments/" + id;
    }

    @Override
    protected String getListPath(String accountId) {
        return  "accounting/account/" + accountId + "/bill_payments/bill_payments";
    }

    @Override
    protected List<BillPayment> convertToList(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONArray jsonArray = json.getJSONArray("bill_payments");
            return Arrays.asList(mapper.readValue(jsonArray.toString(), BillPayment[].class));
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }

    @Override
    protected BillPayment convertToOne(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONObject jsonObject = json.getJSONObject("bill_payment");
            return mapper.readValue(jsonObject.toString(), BillPayment.class);
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }
}
