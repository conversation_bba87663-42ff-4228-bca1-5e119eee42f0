package com.aphe.fbsdk.api;

import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.Bill;
import com.aphe.fbsdk.util.FBEntityManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

public class BillManager extends FBEntityManager<Bill> {

    public BillManager(String baseURL, String accessToken, String accountId) {
        super(baseURL, accessToken, accountId);
    }

    @Override
    protected String getOnePath(String accountId, String id) {
        return "accounting/account/" + accountId + "/bills/bills/" + id;
    }

    @Override
    protected String getListPath(String accountId) {
        return "accounting/account/" + accountId + "/bills/bills";
    }

    @Override
    protected List<Bill> convertToList(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONArray jsonArray = json.getJSONArray("bills");
            return Arrays.asList(mapper.readValue(jsonArray.toString(), Bill[].class));
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }

    @Override
    protected Bill convertToOne(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONObject jsonObject = json.getJSONObject("bill");
            return mapper.readValue(jsonObject.toString(), Bill.class);
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }
}
