package com.aphe.fbsdk.api;


import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.ExpenseCategory;
import com.aphe.fbsdk.util.FBEntityManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

public class ExpenseCategoryManager extends FBEntityManager<ExpenseCategory> {

    public ExpenseCategoryManager(String baseURL, String accessToken, String accountId) {
        super(baseURL, accessToken, accountId);
    }

    @Override
    protected String getOnePath(String accountId, String id) {
        return  "accounting/account/" + accountId +  "/expenses/categories/" + id;
    }

    @Override
    protected String getListPath(String accountId) {
        return  "accounting/account/" + accountId +  "/expenses/categories";
    }

    @Override
    protected List<ExpenseCategory> convertToList(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONArray jsonArray = json.getJSONArray("categories");
            return Arrays.asList(mapper.readValue(jsonArray.toString(), ExpenseCategory[].class));
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }

    @Override
    protected ExpenseCategory convertToOne(ObjectMapper mapper, JSONObject json) throws FBException {
        try {
            JSONObject jsonObject = json.getJSONObject("category");
            return mapper.readValue(jsonObject.toString(), ExpenseCategory.class);
        } catch (JsonProcessingException e) {
            throw new FBException(e.getMessage());
        }
    }

}
