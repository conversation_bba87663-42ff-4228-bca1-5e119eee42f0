package com.aphe.security;

import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.util.ArrayUtil;
import com.aphe.contractor.model.Filing;
import com.aphe.contractor.model.Payee;
import com.aphe.contractor.model.Payer;
import com.aphe.contractor.repo.FilingRepository;
import com.aphe.contractor.repo.PayeeRepository;
import com.aphe.contractor.repo.PayerRepository;
import com.aphe.customer.model.Customer;
import com.aphe.customer.model.repo.CustomerRepository;
import com.aphe.domain.repo.DomainRepository;
import com.aphe.partner.model.AccountingIntegration;
import com.aphe.partner.repo.AccountingIntegrationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;

/**
 * This is the main entry point into all access checking.
 * superadmin return true
 * ROLE_OWNER return (allow everything on the domain entities (exclude TNNTransmissions, TINMatch, EFSFilings etc)
 * ROLE_ADMIN return (allow everything on the domain entities (exclude TNNTransmissions, TINMatch, EFSFilings etc)
 * ROLE_TNN_PREPARE (allow CRUD on Payer, Payee, Filings (except for setting a filing for verified, submitted)
 * ROLE_TNN_VERIFY (allow reading reading of Payer, Payee, Filings)
 * ROLE_TNN_SUBMIT (allow reading reading of Payer, Payee, Filings and submit on filings)
 */

@Component
public class AphePermissionEvaluator implements PermissionEvaluator {

    private static final Logger logger = LoggerFactory.getLogger(AphePermissionEvaluator.class);

    private static List<AllowedOperation> allowedOperations = new ArrayList<>();

    @Autowired
    FilingRepository filingRepository;

    @Autowired
    PayeeRepository payeeRepository;

    @Autowired
    PayerRepository payerRepository;

    @Autowired
    DomainRepository domainRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    AccountingIntegrationRepository acctIntRepository;

    static {
        allowedOperations.add(new AllowedOperation(TargetObjectType.ACCOUNTANT, Permission.READ, LocalRole.ROLE_1099_PREPARE, LocalRole.ROLE_1099_SUBMIT));

        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAIN, Permission.CREATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAIN, Permission.READ, GlobalRole.ROLE_USER, LocalRole.ROLE_1099_PREPARE, LocalRole.ROLE_1099_SUBMIT));
        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAIN, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAIN, Permission.CHARGE, GlobalRole.ROLE_OWNER));

        allowedOperations.add(new AllowedOperation(TargetObjectType.PAYER, Permission.CREATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.PAYER, Permission.READ, LocalRole.ROLE_1099_PREPARE, LocalRole.ROLE_1099_SUBMIT));
        allowedOperations.add(new AllowedOperation(TargetObjectType.PAYER, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));

        allowedOperations.add(new AllowedOperation(TargetObjectType.PAYEE, Permission.CREATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.PAYEE, Permission.READ, LocalRole.ROLE_1099_PREPARE, LocalRole.ROLE_1099_SUBMIT));
        allowedOperations.add(new AllowedOperation(TargetObjectType.PAYEE, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.PAYEE, Permission.DELETE, LocalRole.ROLE_1099_PREPARE));

        allowedOperations.add(new AllowedOperation(TargetObjectType.FILING, Permission.CREATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.FILING, Permission.READ, LocalRole.ROLE_1099_PREPARE, LocalRole.ROLE_1099_SUBMIT));
        allowedOperations.add(new AllowedOperation(TargetObjectType.FILING, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.FILING, Permission.DELETE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.FILING, Permission.SUBMIT, LocalRole.ROLE_1099_SUBMIT));

        allowedOperations.add(new AllowedOperation(TargetObjectType.CUSTOMER, Permission.CREATE, GlobalRole.ROLE_ADMIN));
        allowedOperations.add(new AllowedOperation(TargetObjectType.CUSTOMER, Permission.READ, GlobalRole.ROLE_ADMIN));
        allowedOperations.add(new AllowedOperation(TargetObjectType.CUSTOMER, Permission.UPDATE, GlobalRole.ROLE_ADMIN));
        allowedOperations.add(new AllowedOperation(TargetObjectType.CUSTOMER, Permission.DELETE, GlobalRole.ROLE_ADMIN));

        allowedOperations.add(new AllowedOperation(TargetObjectType.WORKER, Permission.CREATE, GlobalRole.ROLE_ADMIN));
        allowedOperations.add(new AllowedOperation(TargetObjectType.WORKER, Permission.READ, GlobalRole.ROLE_ADMIN));
        allowedOperations.add(new AllowedOperation(TargetObjectType.WORKER, Permission.UPDATE, GlobalRole.ROLE_ADMIN));
        allowedOperations.add(new AllowedOperation(TargetObjectType.WORKER, Permission.DELETE, GlobalRole.ROLE_ADMIN));

    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {

        ApheUserDetails principal = (ApheUserDetails) authentication.getPrincipal();

//         logger.info("Checking access of user_id={} on domain_id={} with parent_domain_id={} for entity_type={} and entity_id={} for permission={}", principal.getUserId(), principal.getDomainId(), principal.getParentDomainId(), targetType, targetId, permission);

        TargetObjectType context = TargetObjectType.DOMAIN;
        boolean hasPermission = false;
        String reason = "";
        String targetDomain = "";
        AllowedOperation theOperation = null;
        List<String> requiredRoles = new ArrayList<>();

        //return true all the time for superadmin
        if (isSuperAdmin(authentication)) {
            hasPermission = true;
            reason = "SuperAdmin";
        } else {
            try {
                if (targetId instanceof Long) {
                    targetId = Long.toString((Long) targetId);
                }

                //Figure out the entity type and operation we are trying to do.
                TargetObjectType targetObject = TargetObjectType.valueOf(targetType);

                if (permission != null) {
                    theOperation = findOperation((String)permission);
                    if (theOperation != null) {
                        if (targetId instanceof List) {
                            switch (targetObject) {
                                case PAYEE: {
                                    targetDomain = getTargetDomainForPayees((List<String>) targetId);
                                    break;
                                }
                                case FILING: {
                                    targetDomain = getTargetDomainForFilings((List<String>) targetId);
                                    break;
                                }
                            }
                        } else if (targetId instanceof String) {
                            switch (targetObject) {
                                case PAYEE: {
                                    targetDomain = getTargetDomainForPayees(Arrays.asList((String) targetId));
                                    break;
                                }
                                case FILING: {
                                    targetDomain = getTargetDomainForFilings(Arrays.asList((String) targetId));
                                    break;
                                }
                                case PAYER: {
                                    targetDomain = getTargetDomainForPayer((String) targetId);
                                    break;
                                }
                                case ACCT_INTEGRATION: {
                                    targetDomain = getTargetDomainForAccountingIntegration((String) targetId);
                                    break;
                                }
                                case CUSTOMER: {
                                    targetDomain = getTargetDomainForCustomer((String) targetId);
                                    break;
                                }
                                case DOMAIN: {
                                    long longTargetId = Long.parseLong((String) targetId);
                                    targetDomain = Long.toString(longTargetId);
                                    break;
                                }
                            }
                        }

                        if (targetDomain.equalsIgnoreCase(Long.toString(principal.getDomainId()))) {
                            if (isOwnerOrAdmin(authentication)) {
                                reason = "OwnerOrAdmin";
                                hasPermission = true;
                            } else {
                                for (String requiredRole : theOperation.getAllowedRoles()) {
                                    if (hasRole(authentication, requiredRole)) {
                                        reason = "HasRequiredRole";
                                        hasPermission = true;
                                        break;
                                    }
                                }
                                if(!hasPermission) {
                                    reason = "HasNoRequiredRole";
                                }
                            }
                        } else {
                            reason = "InvalidTargetDomain";
                        }
                    } else {
                        reason = "NoAccessRulesFound";
                    }
                } else {
                    reason = "UnknownPermissionOrEntity";
                }
            } catch (Exception e) {
                logger.error("Error evaluating permissions", e);
                reason = e.getClass().getName();
            }
        }
        if(!hasPermission) {
            logger.info("Access check result={}, reason={} have_roles={} target_domain={} required_roles={}", (hasPermission ? "Granted" : "Denied"), reason, principal.getAuthorities(), targetDomain, (theOperation != null ? theOperation.getAllowedRoles() : ""));
        }
        return hasPermission;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        //For now do not support this type of permission evaluation...
        logger.error("Error evaluating permissions. role=UnsupportedEvaluation");
        return false;
    }

    private AllowedOperation findOperation(String permission) {
        String[] parts = permission.split("_");
        Permission permissionNeeded = Permission.valueOf(parts[0]);
        TargetObjectType targetObject = TargetObjectType.valueOf(parts[1]);

        AllowedOperation theOperation = null;
        for (AllowedOperation allowedOperation : allowedOperations) {
            if (allowedOperation.getTargetObjectType() == targetObject && allowedOperation.getPermission() == permissionNeeded) {
                theOperation = allowedOperation;
                break;
            }
        }
        return theOperation;
    }

    // Operation on a list of users, has to be in the context of domain.
    // If all users belong to a domian return that, otherwise return no domain.
    private String getTargetDomainForPayees(List<String> payeeIds) {
        String targetDomain = "-1";
        List<Long> listTargetIds = ArrayUtil.stringListToLongList(payeeIds);
        Set<String> domainIds = new HashSet<>();
        List<Payee> payees = payeeRepository.findByIdIn(listTargetIds);
        for (Payee p : payees) {
            domainIds.add(p.getPayer().getDomainId().toString());
        }
        if (domainIds.size() == 1) {
            targetDomain = domainIds.iterator().next();
        }
        return targetDomain;
    }

    private String getTargetDomainForFilings(List<String> filingIds) {
        String targetDomain = "-1";
        List<Long> listTargetIds = ArrayUtil.stringListToLongList(filingIds);
        Set<String> domainIds = new HashSet<>();
        List<Filing> filings = filingRepository.findByIdIn(listTargetIds);
        for (Filing f : filings) {
            domainIds.add(f.getPayer().getDomainId().toString());
        }
        if (domainIds.size() == 1) {
            targetDomain = domainIds.iterator().next();
        }
        return targetDomain;
    }

    private String getTargetDomainForCustomer(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        Customer entity = customerRepository.findById(longTargetId).orElse(null);
        if (entity != null) {
            targetDomain = entity.getDomainId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForAccountingIntegration(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        AccountingIntegration entity = acctIntRepository.findById(longTargetId).orElse(null);
        if (entity != null) {
            targetDomain = entity.getDomainId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForPayer(String payerId) {
        String targetDomain = "-1";
        Set<String> domainIds = new HashSet<>();
        Payer p = payerRepository.findById(Long.parseLong(payerId)).orElse(null);
        if(p != null) {
            domainIds.add(p.getDomainId().toString());
        }
        if (domainIds.size() == 1) {
            targetDomain = domainIds.iterator().next();
        }
        return targetDomain;
    }




    private boolean isOwnerOrAdmin(Authentication auth) {
        return isOwner(auth) || isAdmin(auth);
    }

    private boolean isAdmin(Authentication auth) {
        return hasRole(auth, GlobalRole.ROLE_ADMIN.name());
    }

    private boolean isOwner(Authentication auth) {
        return hasRole(auth, GlobalRole.ROLE_OWNER.name());
    }

    private boolean isSuperAdmin(Authentication auth) {
        return hasRole(auth, "superadmin");
    }

    private boolean hasRole(Authentication auth, String globalRole) {
        if (auth != null && auth.isAuthenticated()) {
            ApheUserDetails principal = (ApheUserDetails) auth.getPrincipal();
            for (GrantedAuthority authority : principal.getAuthorities()) {
                if (authority.getAuthority().equalsIgnoreCase(globalRole)) {
                    return true;
                }
            }
        }
        return false;
    }


}
