package com.aphe.auth.task;

import com.aphe.common.mail.*;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * A task that runs every couple of hours to see if there are any errored emails in the system.
 * Also sends a summary email every day to cover the email sent on the previous day. PST day.
 */
@Component
public class EmailQueueMonitoringTask {

    Logger logger = LoggerFactory.getLogger(EmailQueueMonitoringTask.class);

    @Value("${appName}")
    private String appName;

    @Autowired
    protected CommonMailManager commonMailManager;

    @Autowired
    MailService mailService;

    @Recurring(id = "authservice-email-monitoring-job", cron = "0 10 0/4 * * *")
    public void errorMonitoringTask() {
        //logger.info("Start of executing the taskName=ErrorEmailMonitoringTask status=Begin");
        monitorErroredEmails();
        //logger.info("Done executing the taskName=ErrorEmailMonitoringTask status=End");
    }

    private void monitorErroredEmails() {
        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        StringBuilder sb = new StringBuilder();
        int erroredCount = 0;
        boolean hasErrors = false;
        try {
            List<EmailCount> emailCounts = mailService.getEmailCounts();
            for (EmailCount emailCount : emailCounts) {
                if (emailCount.getEmailStatus() == EmailStatus.Errored) {
                    hasErrors = true;
                    erroredCount += emailCount.getTotal();
                }
            }
            if (erroredCount > 0) {
                hasErrors = true;
                logger.error("Found {} errored emails", erroredCount);
            }
        } catch (Exception e) {
            hasErrors = true;
            sb.append("Error getting email counts. Error=" + e.getMessage()).append("<br/><br/>");
        } finally {
            //Send the email if there is an error count or an exception getting data.
            if (hasErrors) {
                sb.append("Number of email in errored state = " + erroredCount).append("<br/>");
                sb.append("<br/>");
                String subject = "Attention Required: Errored emails in the system - " + appName;
                commonMailManager.sendSystemStatusEmail(subject, sb.toString());
            }
        }
        CronJobAuthenticationUtil.cleanAuthentication();
    }


    /**
     * Daily summary of emails counts by email status.
     */
    @Recurring(id = "authservice-email-summary-job", cron = "0 11 14 * * *")
    public void emailSummaryTask() {
        //logger.info("Start of executing the taskName=EmailSummmaryTask status=Begin");
        generateEmailSummaryEmail();
        //logger.info("Done executing the taskName=EmailSummmaryTask status=End");
    }

    private void generateEmailSummaryEmail() {
        CronJobAuthenticationUtil.configureAuthentication("superadmin");

        StringBuilder sb = new StringBuilder();
        List<EmailCount> emailCounts = new ArrayList<>();
        boolean hasErrors = false;
        try {
            emailCounts = mailService.getEmailCounts();
            if (emailCounts.size() < 0) {
                hasErrors = true;
                logger.error("Could not fetch email counts");
            }
        } catch (Exception e) {
            hasErrors = true;
            sb.append("Error executing the taskName=EmailSummmaryTask Error=" + e.getMessage()).append("<br/><br/>");
        } finally {
            //Send the email if there is an error count or an exception getting data.
            sb.append("Email summary for ").append(appName).append("<br/>");
            sb.append("<br/>");
            String subject = "Email summary - " + appName;
            if (hasErrors) {
                subject = "Attention Required: " + subject;
            }
            sb.append("<table><tr><th>Status</th><th>Count</th>")
                    .append("</tr>");
            for (EmailCount count : emailCounts) {
                sb.append("<tr>");
                sb.append("<td>").append(count.getEmailStatus()).append("</td>");
                sb.append("<td>").append(count.getTotal()).append("</td>");
                sb.append("</tr>");
            }
            sb.append("</table>");

            commonMailManager.sendSystemStatusEmail(subject, sb.toString());
        }
        CronJobAuthenticationUtil.cleanAuthentication();
    }

    @Recurring(id = "authservice-email-reattempt-job", interval = "PT4H")
    public void reAttemptFailedEmails() {
        processErroredEmails();
    }

    /**
     * Process errored emails
     * @return number of emails processed
     */
    private int processErroredEmails() {
        try {
            CronJobAuthenticationUtil.configureAuthentication("superadmin");
            List<Email> erroredEmails = mailService.getErroredEmails();
            if (erroredEmails.isEmpty()) {
                return 0;
            }
            int processedCount = 0;
            for (Email email : erroredEmails) {
                try {
                    mailService.updateEmailStatus(email.getId(), EmailStatus.Created);
                    processedCount++;
                } catch (Exception e) {
                    logger.error("Error processing errored email: {}", email.getId(), e);
                }
            }
            return processedCount;
        } catch (Exception e) {
            logger.error("Error in errored email processing", e);
            return 0;
        } finally {
            CronJobAuthenticationUtil.cleanAuthentication();
        }
    }
}