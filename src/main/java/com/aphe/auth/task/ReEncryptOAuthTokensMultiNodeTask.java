package com.aphe.auth.task;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.MailManager;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.OAuthTokenDTO;
import com.aphe.auth.sso.OAuthIntegrationConvertUtil;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ReEncryptOAuthTokensMultiNodeTask extends ApheBackgroundTask {

	Logger logger = LoggerFactory.getLogger(ReEncryptOAuthTokensMultiNodeTask.class);
	public static final String DOMAIN_ID = "domainId";
	public static final String DOMAIN_NAME = "domainName";
	public static final String PARTNER = "partner";

	@Autowired
	OAuthIntegrationMgr oAuthIntegrationMgr;

	@Autowired
	DomainRepository domainRepository;

	@Autowired
	OAuthIntegrationRepository integrationRepository;

	@Autowired
	AccountManager accountManager;

	@Autowired
	OAuthIntegrationConvertUtil convertUtil;

	@Autowired
	MailManager mailManager;

	@Override
	public void startTask() {
		jobName = "authservice-re-encrypt-oauth-tokens-job";
		chunkSize = 100;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		List<OAuthIntegration> allIntegrations = integrationRepository.findAll();
		return allIntegrations.stream().map(OAuthIntegration ::getId).map(String::valueOf).collect(Collectors.toList());
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
		String logContext = String.format("job_name=%s run_id=%s chunk_id=%s node_id=%s id=%s", jobName, runId, chunkId, nodeId, unitOfWork);
		try {
			//Load integration and domain.
			Long integrationId = Long.parseLong(unitOfWork);
			OAuthIntegration integration = integrationRepository.findById(integrationId).orElse(null);
			if(integration == null) {
				return new ApheExecutionData(ApheExecutionResult.SKIPPED, "Integration not found");
			}
			Domain domain = accountManager.getDomain(integration.getDomainId());
			if(domain == null) {
				return new ApheExecutionData(ApheExecutionResult.SKIPPED, "Domain not found");
			}

			OAuthIntegrationPartner partner = integration.getPartner();
			logContext = String.format("job_name=%s run_id=%s chunk_id=%s node_id=%s id=%s domain_id=%s partner=%s", jobName, runId, chunkId, nodeId, unitOfWork, domain.getId(), partner.name());

			logger.info("{} - STARTING processing.", logContext);

			CronJobAuthenticationUtil.configureAuthentication("superadmin", domain.getId(), domain.getDisplayName());
			logger.debug("{} - configured security for domain", logContext );


			OAuthTokenDTO dto = oAuthIntegrationMgr.getOAuthTokenDTO(domain.getId(), partner.name());

			if(dto != null) {
				try {
					logger.debug("{} integration is not null for partner={}", logContext, partner.name());

					OAuthIntegrationDTO dto1 = convertUtil.convertTokenDTOToIntegrationDTO(dto);
					logger.debug("{} loaded DTO for partner={}", logContext, partner.name());

					dto1.accountId = dto1.accountId + " ";
					dto1.accessToken = dto1.accessToken + " ";
					dto1.refreshToken = dto1.refreshToken + " ";
					OAuthIntegrationClientDTO updatedDTO = oAuthIntegrationMgr.addOrUpdateIntegration(dto1);
					logger.debug("{} saved successfully with spaces partner={}", logContext, partner.name());

					dto1.accountId = dto1.accountId.trim();
					dto1.accessToken = dto1.accessToken.trim();
					dto1.refreshToken = dto1.refreshToken.trim();
					updatedDTO = oAuthIntegrationMgr.addOrUpdateIntegration(dto1);
					logger.info("{} saved successfully after removing spaces partner={}", logContext, partner.name());

					Map<String, String> values = new HashMap<>();
					values.put(DOMAIN_ID, domain.getId().toString());
					values.put(DOMAIN_NAME, domain.getDisplayName());
					values.put(PARTNER, integration.getPartner().name());
					return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
				}catch (Exception e) {
					logger.error("{} - FAILED: Error re-encrypting tokens for partner={}", logContext, partner.name(), e);
					return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
				}
			} else {
				logger.info("{} - oauth integration not found", logContext);
				return new ApheExecutionData(ApheExecutionResult.SKIPPED, "OAuth integration not found");
			}
		}catch (Exception e) {
			logger.error("{} - FAILED: Error re-encrypting tokens message={}", logContext, e.getMessage(), e);
			return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
		}
    }

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of(DOMAIN_ID, DOMAIN_NAME, PARTNER);
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
		long emailStartTime = System.currentTimeMillis();
		mailManager.sendSystemStatusEmail(subject, emailHTML);
		long emailTime = System.currentTimeMillis() - emailStartTime;
		logger.info("Email sent in {}ms", emailTime);
	}
}
