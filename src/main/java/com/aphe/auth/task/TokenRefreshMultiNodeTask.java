package com.aphe.auth.task;


import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.MailManager;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TokenRefreshMultiNodeTask extends ApheBackgroundTask {

    private static final Logger logger = LoggerFactory.getLogger(TokenRefreshMultiNodeTask.class);
    public static final String DOMAIN_ID = "domainId";
    public static final String DOMAIN_NAME = "domainName";
    public static final String PARTNER = "partner";
    public static final String ACCOUNT_NAME = "accountName";
    public static final String ACCESS_TOKEN_EXPIRY_DATE = "accessTokenExpiryDate";
    public static final String REFRESH_TOKEN_EXPIRY_DATE = "refreshTokenExpiryDate";
    public static final String NEW_ACCESS_TOKEN_EXPIRY_DATE = "newAccessTokenExpiryDate";
    public static final String NEW_REFRESH_TOKEN_EXPIRY_DATE = "newRefreshTokenExpiryDate";

    @Autowired
    OAuthIntegrationRepository integrationRepository;

    @Autowired
    AccountManager accountManager;

    @Autowired
    OAuthIntegrationMgr oAuthIntegrationMgr;

    @Autowired
    MailManager mailManager;

    @Recurring(id = "authservice-oauth-token-refresh-job", cron = "25 12 11 * * *")
    public void startTask() {
        setupTask();
        executeTask();
    }

    public void setupTask() {
        jobName = "authservice-oauth-token-refresh-job";
        chunkSize = 10;
    }

    //A method to generate and return a list of 1000 ramdom 100 chars strings.
    public List<String> getUnitsOfWork() {
        Date expiryDate = DateUtils.addDays(new Date(), 10);
        List<OAuthIntegration> oAuthIntegrations = integrationRepository.findAllWithRefreshTokenDateBefore(expiryDate);
        List<String> integrationIds =
                oAuthIntegrations.stream().map(oAuthIntegration -> {
                    return oAuthIntegration.getId().toString();
                }).collect(Collectors.toList());
        return integrationIds;
    }


    //execute a unit of work
    public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWorkId) {

        String logContext = String.format("job_name=%s run_id=%s chunk_id=%s node_id=%s id=%s", jobName, runId, chunkId, nodeId, unitOfWorkId);
        try {
            logger.info("{} - STARTING processing.", logContext);

            long startTime = System.currentTimeMillis();
            Long integrationId = Long.parseLong(unitOfWorkId);
            OAuthIntegration integration = integrationRepository.findById(integrationId).orElse(null);
            long queryTime = System.currentTimeMillis() - startTime;

            if (integration == null) {
                logger.error("{} - FAILED to find OAuth integration", logContext);
                return new ApheExecutionData(ApheExecutionResult.SKIPPED, "OAuth integration not found");
            }
            logger.info("{} - Found {} OAuth integrations from database time={})", logContext, queryTime);

            // Add each integration to the map
            Map<String, String> values = new HashMap<>();
            values.put(DOMAIN_ID, integration.getDomainId().toString());
            values.put(DOMAIN_NAME, "??");
            values.put(PARTNER, integration.getPartner().name());
            values.put(ACCOUNT_NAME, integration.getAccountName());
            values.put(ACCESS_TOKEN_EXPIRY_DATE, String.valueOf(integration.getAccessTokenExpiryDate()));
            values.put(REFRESH_TOKEN_EXPIRY_DATE, String.valueOf(integration.getRefreshTokenExpiryDate()));
            logger.debug("{} - Prepared integration {} for domain {} partner {} account {}",
                    logContext, integration.getId(), integration.getDomainId(),
                    integration.getPartner().name(), integration.getAccountName());

            long domainId = Long.parseLong(values.get(DOMAIN_ID));
            logger.debug("{} - Configuring authentication for domain_id={}", logContext, domainId);

            CronJobAuthenticationUtil.configureAuthentication("superadmin", domainId, "Unknown domain name");

            long domainStartTime = System.currentTimeMillis();
            Domain domain = accountManager.getDomain(domainId);
            long domainQueryTime = System.currentTimeMillis() - domainStartTime;

            logger.debug("{} - Looked up domain. domain_id={} time={}", logContext, domainId, domainQueryTime);

            if (domain == null) {
                values.put(DOMAIN_NAME, "Domain not found");
                logger.warn("{} - Domain {} not found, skipping token refresh", logContext, domainId);
                return new ApheExecutionData(ApheExecutionResult.FAILED, "Domain not found");
            }

            values.put(DOMAIN_NAME, domain.getDisplayName());
            logger.debug("{} - Found domain domain_id={} domain_name={}", logContext, domainId, domain.getDisplayName());

            long refreshStartTime = System.currentTimeMillis();
            OAuthIntegrationClientDTO dto = refreshTokens(values);
            long refreshTime = System.currentTimeMillis() - refreshStartTime;

            logger.info("{} - Token refresh attempt completed. domain_id={} time={}", logContext, domainId, refreshTime);

            if (dto != null) {
                if (dto.connected) {
                    values.put(NEW_ACCESS_TOKEN_EXPIRY_DATE, String.valueOf(dto.accessTokenExpiryDate));
                    values.put(NEW_REFRESH_TOKEN_EXPIRY_DATE, String.valueOf(dto.refreshTokenExpiryDate));
                    logger.info("{} - SUCCESS: Tokens refreshed successfully. new_access_token_expiry={} new_refresh_token_expiry={}", logContext, dto.accessTokenExpiryDate, dto.refreshTokenExpiryDate);
                    return new ApheExecutionData(ApheExecutionResult.SUCCESS, null, values);
                } else {
                    logger.warn("{} - FAILED: Token refresh returned disconnected DTO", logContext);
                    return new ApheExecutionData(ApheExecutionResult.FAILED, "Token refresh returned disconnected DTO", values);
                }
            } else {
                logger.error("{} - FAILED: Token refresh returned null DTO", logContext);
                return new ApheExecutionData(ApheExecutionResult.FAILED, "Token refresh returned null DTO", values);
            }
        } catch (Exception e) {
            logger.error("{} - EXCEPTION during token refresh. message={}", logContext, e.getMessage(), e);
            return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
        } finally {
            try {
                CronJobAuthenticationUtil.cleanAuthentication();
                logger.debug("{} - Cleaned authentication context", logContext);
            } catch (Exception e) {
                logger.warn("{} - Error cleaning authentication context: {}", logContext, e.getMessage());
            }
        }
    }


    private OAuthIntegrationClientDTO refreshTokens(Map<String, String> values) throws Exception {
        String partner = values.get(PARTNER);
        String domainId = values.get(DOMAIN_ID);
        String accountName = values.get(ACCOUNT_NAME);
        String logContext = String.format("job_name=%s partner=%s domain_id=%s account_name=%s", jobName, partner, domainId, accountName);
        logger.debug("{} - STARTING hard refresh of tokens", logContext);
        try {
            long startTime = System.currentTimeMillis();
            OAuthIntegrationClientDTO dto = oAuthIntegrationMgr.hardRefreshTokens(partner);
            long refreshTime = System.currentTimeMillis() - startTime;
            if (dto != null) {
                logger.info("{} - Hard refresh completed. time={}, connected={}", logContext, refreshTime, dto.connected);
            } else {
                logger.warn("{} - Hard refresh completed, but failed. time={} connected={}", logContext, refreshTime, false);
            }
            return dto;
        } catch (Exception e) {
            logger.error("{} - EXCEPTION during hard refresh: {}", logContext, e.getMessage(), e);
            return null;
        }
    }

    public List<String> getAdditionalEmailColumns() {
        return List.of(DOMAIN_ID, PARTNER, DOMAIN_NAME, ACCOUNT_NAME, ACCESS_TOKEN_EXPIRY_DATE, REFRESH_TOKEN_EXPIRY_DATE, NEW_ACCESS_TOKEN_EXPIRY_DATE, NEW_REFRESH_TOKEN_EXPIRY_DATE);
    }

    public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
        logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
        long emailStartTime = System.currentTimeMillis();
        mailManager.sendSystemStatusEmail(subject, emailHTML);
        long emailTime = System.currentTimeMillis() - emailStartTime;
        logger.info("Email sent in {}ms", emailTime);
    }

}