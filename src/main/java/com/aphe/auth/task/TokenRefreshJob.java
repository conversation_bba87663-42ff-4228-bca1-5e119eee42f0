package com.aphe.auth.task;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class TokenRefreshJob {

    Logger logger = LoggerFactory.getLogger(TokenRefreshTask.class);

    @Autowired
    OAuthIntegrationMgr oAuthIntegrationMgr;

    @Autowired
    OAuthIntegrationRepository integrationRepository;

    @Autowired
    AccountManager accountManager;

    @Autowired
    TokenRefreshResultStore resultStore;

    @Autowired
    JobScheduler jobScheduler;


    public void refreshChunk(List<Long> integrationIds, String runId) {
        resultStore.setRunId(runId);

        Map<Long, Map<String, String>> integrationValues = new HashMap<>();
        List<OAuthIntegration> oAuthIntegrations  = integrationRepository.findByIdIn(integrationIds);
        //add each integration to the map
        for (OAuthIntegration integration : oAuthIntegrations) {
            Map<String, String> values = new HashMap<>();
            integrationValues.put(integration.getId(), values);
            values.put("domainId", integration.getDomainId());
            values.put("domainName", "??");
            values.put("partner", integration.getPartner().name());
            values.put("accountName", integration.getAccountName());
            values.put("accessTokenExpiryDate", String.valueOf(integration.getAccessTokenExpiryDate()));
            values.put("refreshTokenExpiryDate", String.valueOf(integration.getRefreshTokenExpiryDate()));
        }

        for (Long integrationId : integrationIds) {
            boolean success = false;
            String message = "";
            Map<String, String> values = integrationValues.get(integrationId);

            try {
                long domainId = Long.parseLong(values.get("domainId"));
                CronJobAuthenticationUtil.configureAuthentication("superadmin", domainId, "Unkown domain name");

                Domain domain = accountManager.getDomain(Long.toString(domainId));

                if (domain == null) {
                    values.put("domainName", "Domain not found");
                    values.put("refreshStatus", "Domain not found");
                    continue;
                } else {
                    values.put("domainName", domain.getDisplayName());
                }
                OAuthIntegrationClientDTO dto = refreshTokens(values);
                if (dto != null) {
                    if(dto.connected) {
                        values.put("refreshStatus", "Refreshed");
                        values.put("newAccessTokenExpiryDate", String.valueOf(dto.accessTokenExpiryDate));
                        values.put("newRefreshTokenExpiryDate", String.valueOf(dto.refreshTokenExpiryDate));
                        logger.info("Tokens have been refreshed successfully. taskName=TokenRefreshTask DomainId=" + domainId);
                    } else {
                        values.put("refreshStatus", "Errored. DTO is not connected");
                        logger.info("Failed to refresh token. Disconnected the integration. taskName=TokenRefreshTask DomainId=" + domainId);
                    }
                } else {
                    values.put("refreshStatus", "Errored. DTO is is null");
                    logger.error("Error refreshing token. Will attempt again. taskName=TokenRefreshTask DomainId=" + domainId);
                }
            } catch (Exception e) {
                values.put("refreshStatus", "Errored with an exception. " + e.getMessage());
                logger.error("Error refreshing token taskName=TokenRefreshTask DomainId=" + values.get("domainId") + " Error=" + e.getMessage(), e);
            } finally {
                CronJobAuthenticationUtil.cleanAuthentication();
            }
            resultStore.saveTokenStatus(integrationId, values);
        }

        long completed = resultStore.incrementCompletedChunks();
        if (completed == resultStore.getExpectedChunks()) {
            jobScheduler.enqueue(() -> TokenRefreshSummaryJob.sendSummaryReport());
        }
    }

    private OAuthIntegrationClientDTO refreshTokens(Map<String, String> values) throws Exception {
        OAuthIntegrationClientDTO dto = null;
        dto = oAuthIntegrationMgr.hardRefreshTokens(values.get("partner"));
        //wait for a few seconds to simulate the hard refresh
//        try {
//            Thread.sleep(5000); // Simulate a delay for the hard refresh
//        } catch (InterruptedException e) {
//            logger.error("Thread interrupted during sleep", e);
//        }
        return dto;
    }
}
