package com.aphe.auth.task.tokenrefresh;

import com.aphe.auth.service.MailManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.util.Map;
import java.util.UUID;

@Component
public class TokenRefreshSummaryJob {

    private static final Logger logger = LoggerFactory.getLogger(TokenRefreshSummaryJob.class);
    private static final String LOG_PREFIX = "TOKEN_REFRESH_SUMMARY";

    @Value("${appName}")
    private String appName;

    @Autowired
    private MailManager mailManager;

    @Autowired
    private TokenRefreshResultStore resultStore;

    private final String nodeId;

    public TokenRefreshSummaryJob() {
        String tempNodeId;
        try {
            tempNodeId = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            tempNodeId = "unknown-node-" + UUID.randomUUID().toString().substring(0, 8);
        }
        this.nodeId = tempNodeId;
        logger.info("{} - Initialized TokenRefreshSummaryJob on node: {}", LOG_PREFIX, nodeId);
    }

    public void sendSummaryReport(String runId) {
//        String runId = resultStore.getRunId();
        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, runId, nodeId);

        // Set MDC for correlation
        MDC.put("runId", runId);
        MDC.put("nodeId", nodeId);
        MDC.put("operation", "token-refresh-summary");

        try {
            logger.info("{} - STARTING summary report generation", logContext);

            long startTime = System.currentTimeMillis();
            Map<Long, Map<String, String>> integrationValues = resultStore.getAllStatuses(runId);
            long queryTime = System.currentTimeMillis() - startTime;

            int toBeProcessed = integrationValues.size();
            int processed = 0;
            int errored = 0;
            int skipped = 0;

            logger.info("{} - Retrieved {} integration statuses (took {}ms)", logContext, toBeProcessed, queryTime);

            for (Map.Entry<Long, Map<String, String>> entry : integrationValues.entrySet()) {
                Map<String, String> values = entry.getValue();
                String status = values.get("refreshStatus");

                if (status == null) {
                    logger.warn("{} - Integration {} has null refresh status", logContext, entry.getKey());
                    errored++;
                } else if (status.startsWith("Errored") || status.startsWith("FAILED")) {
                    errored++;
                } else if ("SKIPPED".equals(status)) {
                    skipped++;
                } else if ("Refreshed".equals(status) || "SUCCESS".equals(status)) {
                    processed++;
                } else {
                    logger.warn("{} - Integration {} has unknown status: {}", logContext, entry.getKey(), status);
                    errored++;
                }
            }

            logger.info("{} - Summary statistics: {} total, {} processed, {} skipped, {} errored",
                       logContext, toBeProcessed, processed, skipped, errored);

            // Send email report
            sendEmailReport(toBeProcessed, processed, skipped, errored, integrationValues, logContext);

            logger.info("{} - COMPLETED summary report generation", logContext);

        } catch (Exception e) {
            logger.error("{} - FAILED to generate summary report: {}", logContext, e.getMessage(), e);
            throw e;
        } finally {
            MDC.clear();
        }
    }


    private void sendEmailReport(int toBeProcessed, int processed, int skipped, int errored,
                                Map<Long, Map<String, String>> integrationValues, String logContext) {
        try {
            logger.info("{} - STARTING email report generation", logContext);

            String subject = "Token Refresh Task Status - " + appName;
            StringBuilder sb = new StringBuilder();

            // Add summary statistics
            sb.append("<h2>Token Refresh Summary</h2>");
            sb.append("Number of tokens to be processed = " + toBeProcessed).append("<br/>");
            sb.append("Number of tokens processed = " + processed).append("<br/>");
            sb.append("Number of tokens skipped = " + skipped).append("<br/>");
            sb.append("Number of tokens errored = " + errored).append("<br/>");
            sb.append("Processing node = " + nodeId).append("<br/>");
            sb.append("<br/>");

            if (integrationValues.keySet().size() > 0) {
                logger.debug("{} - Building detailed table with {} integrations", logContext, integrationValues.size());

                sb.append("<h3>Detailed Results</h3>");
                sb.append("<table border='1' style='border-collapse: collapse;'>");
                sb.append("<tr style='background-color: #f2f2f2;'>");
                sb.append("<th>Integration ID</th>");
                sb.append("<th>Domain ID</th>");
                sb.append("<th>Domain Name</th>");
                sb.append("<th>Partner Name</th>");
                sb.append("<th>Account Name</th>");
                sb.append("<th>Old Access Expiry</th>");
                sb.append("<th>Old Refresh Expiry</th>");
                sb.append("<th>Status Message</th>");
                sb.append("<th>New Access Expiry</th>");
                sb.append("<th>New Refresh Expiry</th>");
                sb.append("</tr>");

                for (Long integrationId : integrationValues.keySet()) {
                    Map<String, String> values = integrationValues.get(integrationId);
                    sb.append("<tr>");

                    sb.append("<td>").append(integrationId).append("</td>");
                    sb.append("<td>").append(safeGet(values, "domainId")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "domainName")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "partner")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "accountName")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "accessTokenExpiryDate")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "refreshTokenExpiryDate")).append("</td>");

                    String status = safeGet(values, "refreshStatus");
                    String statusColor = getStatusColor(status);
                    sb.append("<td style='background-color: ").append(statusColor).append("'>").append(status).append("</td>");

                    sb.append("<td>").append(safeGet(values, "newAccessTokenExpiryDate")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "newRefreshTokenExpiryDate")).append("</td>");

                    sb.append("</tr>");
                }
                sb.append("</table>");
            } else {
                sb.append("<p>No integration details available.</p>");
            }

            logger.info("{} - Sending email report with subject: {}", logContext, subject);

            long emailStartTime = System.currentTimeMillis();
            mailManager.sendSystemStatusEmail(subject, sb.toString());
            long emailTime = System.currentTimeMillis() - emailStartTime;

            logger.info("{} - Successfully sent email report (took {}ms)", logContext, emailTime);

        } catch (Exception e) {
            logger.error("{} - FAILED to send email report: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    private String safeGet(Map<String, String> map, String key) {
        String value = map.get(key);
        return value != null ? value : "N/A";
    }

    private String getStatusColor(String status) {
        if (status == null) return "#ffcccc"; // Light red for null
        if (status.startsWith("Refreshed") || status.equals("SUCCESS")) return "#ccffcc"; // Light green for success
        if (status.equals("SKIPPED")) return "#ffffcc"; // Light yellow for skipped
        if (status.startsWith("Errored") || status.startsWith("FAILED")) return "#ffcccc"; // Light red for errors
        return "#ffffff"; // White for unknown
    }
}
