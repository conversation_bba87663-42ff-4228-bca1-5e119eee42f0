package com.aphe.auth.task.tokenrefresh;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class TokenRefreshJob {

    private static final Logger logger = LoggerFactory.getLogger(TokenRefreshJob.class);
    private static final String LOG_PREFIX = "TOKEN_REFRESH_JOB";
    private String nodeId;

    @Autowired
    OAuthIntegrationMgr oAuthIntegrationMgr;

    @Autowired
    OAuthIntegrationRepository integrationRepository;

    @Autowired
    AccountManager accountManager;

    @Autowired
    TokenRefreshResultStore resultStore;

    @Autowired
    JobScheduler jobScheduler;

    @Autowired
    TokenRefreshSummaryJob summaryJob;

    public TokenRefreshJob() {
        try {
            this.nodeId = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            this.nodeId = "unknown-node-" + UUID.randomUUID().toString().substring(0, 8);
        }
        logger.info("{} - Initialized TokenRefreshJob on node: {}", LOG_PREFIX, nodeId);
    }

    public void refreshChunk(String runId, List<Long> integrationIds) {
        String chunkId = "chunk:" + UUID.randomUUID().toString().substring(0, 8);
        String logContext = String.format("%s - RunId: %s, ChunkId: %s, Node: %s", LOG_PREFIX, runId, chunkId, nodeId);

        // Set MDC for all log statements in this execution
        MDC.put("runId", runId);
        MDC.put("chunkId", chunkId);
        MDC.put("nodeId", nodeId);
        MDC.put("operation", "token-refresh-chunk");

        try {
            logger.info("{} - STARTING chunk processing with {} integration IDs: {}",
                       logContext, integrationIds.size(), integrationIds);

//            resultStore.setRunId(runId);
            logger.debug("{} - Set runId in result store", logContext);

            Map<Long, Map<String, String>> integrationValues = new HashMap<>();

            long startTime = System.currentTimeMillis();
            List<OAuthIntegration> oAuthIntegrations = integrationRepository.findByIdIn(integrationIds);
            long queryTime = System.currentTimeMillis() - startTime;

            logger.info("{} - Found {} OAuth integrations from database (query took {}ms)",
                       logContext, oAuthIntegrations.size(), queryTime);

            if (oAuthIntegrations.size() != integrationIds.size()) {
                logger.warn("{} - Mismatch: requested {} IDs but found {} integrations",
                           logContext, integrationIds.size(), oAuthIntegrations.size());
            }

            // Add each integration to the map
            for (OAuthIntegration integration : oAuthIntegrations) {
                Map<String, String> values = new HashMap<>();
                integrationValues.put(integration.getId(), values);
                values.put("domainId", integration.getDomainId());
                values.put("domainName", "??");
                values.put("partner", integration.getPartner().name());
                values.put("accountName", integration.getAccountName());
                values.put("accessTokenExpiryDate", String.valueOf(integration.getAccessTokenExpiryDate()));
                values.put("refreshTokenExpiryDate", String.valueOf(integration.getRefreshTokenExpiryDate()));

                logger.debug("{} - Prepared integration {} for domain {} partner {} account {}",
                           logContext, integration.getId(), integration.getDomainId(),
                           integration.getPartner().name(), integration.getAccountName());
            }

            int processedCount = 0;
            int successCount = 0;
            int errorCount = 0;

            for (Long integrationId : integrationIds) {
                processedCount++;
                Map<String, String> values = integrationValues.get(integrationId);

                if (values == null) {
                    logger.error("{} - No integration data found for ID {}, skipping", logContext, integrationId);
                    errorCount++;
                    continue;
                }

                String integrationContext = String.format("%s - Integration: %s (%d/%d)",
                                                         logContext, integrationId, processedCount, integrationIds.size());

                logger.info("{} - PROCESSING integration {} for domain {} partner {} account {}",
                           integrationContext, integrationId, values.get("domainId"),
                           values.get("partner"), values.get("accountName"));

                try {
                    long domainId = Long.parseLong(values.get("domainId"));
                    logger.debug("{} - Configuring authentication for domain {}", integrationContext, domainId);

                    CronJobAuthenticationUtil.configureAuthentication("superadmin", domainId, "Unknown domain name");

                    long domainStartTime = System.currentTimeMillis();
                    Domain domain = accountManager.getDomain(Long.toString(domainId));
                    long domainQueryTime = System.currentTimeMillis() - domainStartTime;

                    logger.debug("{} - Domain lookup took {}ms", integrationContext, domainQueryTime);

                    if (domain == null) {
                        values.put("domainName", "Domain not found");
                        values.put("refreshStatus", "Domain not found");
                        logger.warn("{} - Domain {} not found, skipping token refresh", integrationContext, domainId);
                        errorCount++;
                        continue;
                    } else {
                        values.put("domainName", domain.getDisplayName());
                        logger.debug("{} - Found domain: {}", integrationContext, domain.getDisplayName());
                    }

                    long refreshStartTime = System.currentTimeMillis();
                    OAuthIntegrationClientDTO dto = refreshTokens(values);
                    long refreshTime = System.currentTimeMillis() - refreshStartTime;

                    logger.info("{} - Token refresh attempt completed in {}ms", integrationContext, refreshTime);

                    if (dto != null) {
                        if (dto.connected) {
                            values.put("refreshStatus", "Refreshed");
                            values.put("newAccessTokenExpiryDate", String.valueOf(dto.accessTokenExpiryDate));
                            values.put("newRefreshTokenExpiryDate", String.valueOf(dto.refreshTokenExpiryDate));
                            logger.info("{} - SUCCESS: Tokens refreshed successfully. New access token expires: {}, refresh token expires: {}",
                                       integrationContext, dto.accessTokenExpiryDate, dto.refreshTokenExpiryDate);
                            successCount++;
                        } else {
                            values.put("refreshStatus", "Errored. DTO is not connected");
                            logger.warn("{} - FAILED: Token refresh returned disconnected DTO", integrationContext);
                            errorCount++;
                        }
                    } else {
                        values.put("refreshStatus", "Errored. DTO is null");
                        logger.error("{} - FAILED: Token refresh returned null DTO", integrationContext);
                        errorCount++;
                    }
                } catch (Exception e) {
                    values.put("refreshStatus", "Errored with an exception. " + e.getMessage());
                    logger.error("{} - EXCEPTION during token refresh: {}", integrationContext, e.getMessage(), e);
                    errorCount++;
                } finally {
                    try {
                        CronJobAuthenticationUtil.cleanAuthentication();
                        logger.debug("{} - Cleaned authentication context", integrationContext);
                    } catch (Exception e) {
                        logger.warn("{} - Error cleaning authentication context: {}", integrationContext, e.getMessage());
                    }
                }

                try {
                    resultStore.saveTokenStatus(runId, integrationId, values);
                    logger.debug("{} - Saved token status to result store", integrationContext);
                } catch (Exception e) {
                    logger.error("{} - FAILED to save token status: {}", integrationContext, e.getMessage(), e);
                }
            }

            logger.info("{} - COMPLETED chunk processing: {} total, {} success, {} errors",
                       logContext, processedCount, successCount, errorCount);

            long completed = resultStore.incrementCompletedChunks(runId);
            long expected = resultStore.getExpectedChunks(runId);

            logger.info("{} - Chunk completion status: {}/{} chunks completed", logContext, completed, expected);

            if (completed == expected) {
                logger.info("{} - All chunks completed, enqueueing summary report job", logContext);
                try {
                    jobScheduler.enqueue(() -> summaryJob.sendSummaryReport(runId));
                    logger.info("{} - Successfully enqueued summary report job", logContext);
                } catch (Exception e) {
                    logger.error("{} - FAILED to enqueue summary report job: {}", logContext, e.getMessage(), e);
                }
            } else {
                logger.debug("{} - Waiting for remaining chunks to complete", logContext);
            }

        } catch (Exception e) {
            logger.error("{} - FAILED chunk processing: {}", logContext, e.getMessage(), e);
            throw e; // Re-throw to ensure JobRunr marks this as failed
        } finally {
            // Clear MDC
            MDC.clear();
        }
    }

    private OAuthIntegrationClientDTO refreshTokens(Map<String, String> values) throws Exception {
        String partner = values.get("partner");
        String domainId = values.get("domainId");
        String accountName = values.get("accountName");

        String refreshContext = String.format("%s - RefreshTokens: Partner=%s, Domain=%s, Account=%s",
                                             LOG_PREFIX, partner, domainId, accountName);

        logger.debug("{} - STARTING hard refresh for partner {}", refreshContext, partner);

        try {
            long startTime = System.currentTimeMillis();
            OAuthIntegrationClientDTO dto = oAuthIntegrationMgr.hardRefreshTokens(partner);
            long refreshTime = System.currentTimeMillis() - startTime;

            if (dto != null) {
                logger.info("{} - Hard refresh completed in {}ms, connected: {}, accessTokenExpiry: {}, refreshTokenExpiry: {}",
                           refreshContext, refreshTime, dto.connected, dto.accessTokenExpiryDate, dto.refreshTokenExpiryDate);
            } else {
                logger.warn("{} - Hard refresh completed in {}ms but returned null DTO", refreshContext, refreshTime);
            }

            return dto;

        } catch (Exception e) {
            logger.error("{} - EXCEPTION during hard refresh: {}", refreshContext, e.getMessage(), e);
            throw e; // Re-throw to let caller handle
        }
    }
}
