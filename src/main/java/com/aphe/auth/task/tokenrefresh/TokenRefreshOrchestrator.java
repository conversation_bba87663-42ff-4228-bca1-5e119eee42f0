package com.aphe.auth.task.tokenrefresh;


import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import org.apache.commons.lang3.time.DateUtils;
import org.jobrunr.jobs.annotations.Recurring;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class TokenRefreshOrchestrator {

    private static final Logger logger = LoggerFactory.getLogger(TokenRefreshOrchestrator.class);
    private static final String LOG_PREFIX = "TOKEN_REFRESH_ORCHESTRATOR";

    @Autowired
    OAuthIntegrationRepository integrationRepository;

    @Autowired
    JobScheduler jobScheduler;

    @Autowired
    TokenRefreshJob tokenRefreshJob;

    @Autowired
    TokenRefreshResultStore resultStore;

    private int chunkSize = 5;
    private String nodeId;

    public TokenRefreshOrchestrator() {
        try {
            this.nodeId = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            this.nodeId = "unknown-node-" + UUID.randomUUID().toString().substring(0, 8);
        }
        logger.info("{} - Initialized TokenRefreshOrchestrator on node: {}", LOG_PREFIX, nodeId);
    }

    @Recurring(id = "authservice-token-refresh-orchestrator", cron = "25 12 11 * * *")
    public void scheduleTokenRefresh() {

        // Create a unique runId for this execution with a prefix and date time stamp.
        // This will be used to track the execution and all associated jobs.
        String runId = "refresh:run:" + UUID.randomUUID();

        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, runId, nodeId);

        // Set MDC for all log statements in this execution
        MDC.put("runId", runId);
        MDC.put("nodeId", nodeId);
        MDC.put("operation", "token-refresh-orchestrator");

        try {
            logger.info("{} - STARTING token refresh orchestration", logContext);

//            resultStore.setRunId(runId);
            logger.debug("{} - Set runId in result store", logContext);

            Date expiryDate = DateUtils.addDays(new Date(), 10);
            logger.info("{} - Looking for OAuth integrations expiring before: {}", logContext, expiryDate);

            long startTime = System.currentTimeMillis();
            List<OAuthIntegration> oAuthIntegrations = integrationRepository.findAllWithRefreshTokenDateBefore(expiryDate);
            long queryTime = System.currentTimeMillis() - startTime;

            logger.info("{} - Found {} OAuth integrations to refresh (query took {}ms)",
                       logContext, oAuthIntegrations.size(), queryTime);

            if (oAuthIntegrations.isEmpty()) {
                logger.info("{} - No OAuth integrations found for refresh, completing orchestration", logContext);
                resultStore.setExpectedChunks(runId, 0);
                return;
            }

            int totalChunks = (oAuthIntegrations.size() + chunkSize - 1) / chunkSize;
            resultStore.setExpectedChunks(runId, totalChunks);

            logger.info("{} - Will create {} chunks with chunk size {}", logContext, totalChunks, chunkSize);

            int chunkIndex = 0;
            for (int i = 0; i < oAuthIntegrations.size(); i += chunkSize) {
                List<Long> chunk = oAuthIntegrations.subList(i, Math.min(i + chunkSize, oAuthIntegrations.size()))
                        .stream().map(OAuthIntegration::getId).toList();

                chunkIndex++;
                logger.info("{} - Enqueueing chunk {}/{} with {} integration IDs: {}",
                           logContext, chunkIndex, totalChunks, chunk.size(), chunk);

                try {
                    jobScheduler.enqueue(() -> tokenRefreshJob.refreshChunk(runId, chunk));
                    logger.debug("{} - Successfully enqueued chunk {}", logContext, chunkIndex);
                } catch (Exception e) {
                    logger.error("{} - FAILED to enqueue chunk {}: {}", logContext, chunkIndex, e.getMessage(), e);
                    throw e; // Re-throw to fail the orchestration
                }
            }

            logger.info("{} - COMPLETED token refresh orchestration - enqueued {} chunks for {} integrations",
                       logContext, totalChunks, oAuthIntegrations.size());

        } catch (Exception e) {
            logger.error("{} - FAILED token refresh orchestration: {}", logContext, e.getMessage(), e);
            throw e; // Re-throw to ensure JobRunr marks this as failed
        } finally {
            // Clear MDC
            MDC.clear();
        }
    }
}