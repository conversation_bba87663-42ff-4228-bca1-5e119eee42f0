package com.aphe.auth.task;

import com.aphe.common.mail.Email;
import com.aphe.common.mail.EmailSenderService;
import com.aphe.common.mail.EmailStatus;
import com.aphe.common.mail.MailService;
import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Auto task to take submitted filings by the user and submit to the EFS. This run every minute or so??
 */

@Profile("test")
@Component
public class EmailQueueTask extends GenericBackgroundTask {

	@Autowired
    MailService mailService;
	
	@Autowired
	private EmailSenderService emailSender;

	Logger logger = LoggerFactory.getLogger(EmailQueueTask.class);

	@Scheduled(initialDelayString = "${aphe.email.emailQueueTask.initialDelay}", fixedDelayString = "${aphe.email.emailQueueTask.fixedDelay}")
	public void sendEmailsToFormRecepients() {

		boolean pauseTasks = isPauseTasks("authservice-old-email-job");
		if(pauseTasks) {
			return;
		}

//		logger.info("Start of executing the taskName=EmailQueueTask status=Begin");
		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		try {
			List<Email> mailQueue = mailService.getQueuedEmails();
			
			for(Email m : mailQueue) {
				try {
					mailService.updateEmailStatus(m.getId(), EmailStatus.Sending);
					emailSender.sendEmail(m);
					mailService.updateEmailStatus(m.getId(), EmailStatus.Sent);
				} catch (Exception e) {
					mailService.updateEmailStatus(m.getId(), EmailStatus.Errored);
					logger.error("Error sending email to form Recepients", e);
				}
			}
		} catch (Exception e) {
			logger.error("Attention Required: Error processing email requests: ", e);
		}

		CronJobAuthenticationUtil.cleanAuthentication();
//		logger.info("Done executing the taskName=EmailQueueTask status=End");
	}

}
