package com.aphe.auth.task.sample;

import com.aphe.auth.service.MailManager;
import com.aphe.common.tasks.TaskStatusStore;
import com.aphe.common.util.EncryptionUtil;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class SampleTaskWorker {

    private static final Logger logger = LoggerFactory.getLogger(SampleTaskWorker.class);
    private static final String LOG_PREFIX = "TOKEN_REFRESH_JOB";
    private String nodeId;


    @Autowired
    TaskStatusStore resultStore;

    @Autowired
    JobScheduler jobScheduler;

    @Autowired
    MailManager mailManager;


    public SampleTaskWorker() {
        try {
            this.nodeId = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            this.nodeId = "unknown-node-" + UUID.randomUUID().toString().substring(0, 8);
        }
        logger.info("{} - Initialized TokenRefreshJob on node: {}", LOG_PREFIX, nodeId);
    }

    public void doWork(String runId, int chunkIndex, List<String> entityIds) {
        String chunkId = "chunk:" + chunkIndex;
        String logContext = String.format("%s - RunId: %s, ChunkId: %s, Node: %s", LOG_PREFIX, runId, chunkId, nodeId);

        try {
            logger.info("{} - STARTING chunk processing with {} integration IDs: {}", logContext, entityIds.size(), entityIds);

            Map<String, Map<String, String>> jobStatusMap = new HashMap<>();

            long startTime = System.currentTimeMillis();

            for (String strToEncrypt : entityIds) {
                Map<String, String> values = new HashMap<>();
                jobStatusMap.put(strToEncrypt, values);
                values.put("encryptedValue-200", new EncryptionUtil().encryptWithDefault200(strToEncrypt));
                values.put("encryptedValue-305", new EncryptionUtil().encryptWithDefault305(strToEncrypt));
                values.put("encryptedValue-AES", new EncryptionUtil().decrypt(strToEncrypt));
                values.put("status", "SUCCESS");
                logger.debug("{} - finished encryption for {}", logContext, strToEncrypt);
                resultStore.saveJobStatus(runId, chunkId, strToEncrypt, values);
            }
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            logger.info("{} - COMPLETED chunk processing in {}ms", logContext,duration);

            onChunkCompleted(runId, chunkId);

        } catch (Exception e) {
            logger.error("{} - FAILED chunk processing: {}", logContext, e.getMessage(), e);
            throw e; // Re-throw to ensure JobRunr marks this as failed
        }
    }

    public void onChunkCompleted(String runId, String chunkId) {
        String logContext = String.format("%s - RunId: %s, ChunkId: %s, Node: %s", LOG_PREFIX, runId, chunkId, nodeId);
        logger.info("{} - Chunk {} completed", logContext, chunkId);
        resultStore.incrementCompletedChunks(runId);
        long completed = resultStore.incrementCompletedChunks(runId);
        long expected = resultStore.getExpectedChunks(runId);

        logger.info("{} - Chunk completion status: {}/{} chunks completed", logContext, completed, expected);

        if (completed == expected) {
            logger.info("{} - All chunks completed. Calling onExecutionCompleted()", logContext);
            jobScheduler.enqueue(() -> onExecutionCompleted(runId));
            logger.info("{} - Successfully enqueued onExecutionCompleted()", logContext);

        } else {
            logger.debug("{} - Waiting for remaining chunks to complete", logContext);
        }
    }

    public void onExecutionCompleted(String runId) {
        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, runId, nodeId);
        try {
            logger.info("{} - STARTING on completion", logContext);

            long startTime = System.currentTimeMillis();
            Map<String, Map<String, String>> jobStatusValues = resultStore.getAllStatuses(runId);
            long queryTime = System.currentTimeMillis() - startTime;

            int toBeProcessed = jobStatusValues.size();
            int processed = 0;
            int errored = 0;
            int skipped = 0;
            int unknown = 0;

            logger.info("{} - Retrieved {} job statuses (took {}ms)", logContext, toBeProcessed, queryTime);

            for (Map.Entry<String, Map<String, String>> entry : jobStatusValues.entrySet()) {
                Map<String, String> values = entry.getValue();
                String status = values.get("status");

                if (status == null) {
                    logger.warn("{} - Job for entityId={} has null status", logContext, entry.getKey());
                    unknown++;
                } else if (status.startsWith("Errored") || status.startsWith("FAIL")) {
                    errored++;
                } else if ("SKIPPED".equals(status)) {
                    skipped++;
                } else if ("SUCCESS".equals(status)) {
                    processed++;
                } else {
                    logger.warn("{} - Job for entityId={} {} has unknown status: {}", logContext, entry.getKey(), status);
                    unknown++;
                }
            }

            logger.info("{} - Summary statistics: {} total, {} processed, {} skipped, {} errored, {} unknown", logContext, toBeProcessed, processed, skipped, errored, unknown);

            // Send email report
            sendEmailReport(toBeProcessed, processed, skipped, errored, jobStatusValues, logContext);

            logger.info("{} - COMPLETED summary report generation", logContext);

        } catch (Exception e) {
            logger.error("{} - FAILED to generate summary report: {}", logContext, e.getMessage(), e);
            throw e;
        } finally {
            MDC.clear();
        }
    }


    private void sendEmailReport(int toBeProcessed, int processed, int skipped, int errored,
                                 Map<String, Map<String, String>> jobStatusValues, String logContext) {
        try {
            logger.info("{} - STARTING email report generation", logContext);

            String subject = "Sample Encryption Job Status";
            StringBuilder sb = new StringBuilder();

            // Add summary statistics
            sb.append("<h2>Sample Encryption Summary</h2>");
            sb.append("Strings to be encrypted = " + toBeProcessed).append("<br/>");
            sb.append("String ecncrypted = " + processed).append("<br/>");
            sb.append("Strings skipped = " + skipped).append("<br/>");
            sb.append("Strings errored = " + errored).append("<br/>");
            sb.append("<br/>");

            if (jobStatusValues.keySet().size() > 0) {
                logger.debug("{} - Building detailed table with {} strings", logContext, jobStatusValues.size());

                sb.append("<h3>Detailed Results</h3>");
                sb.append("<table border='1' style='border-collapse: collapse;'>");
                sb.append("<tr style='background-color: #f2f2f2;'>");
                sb.append("<th>String</th>");
                sb.append("<th>Chunk Id</th>");
                sb.append("<th>Processing Node</th>");
                sb.append("<th>200</th>");
                sb.append("<th>305</th>");
                sb.append("<th>AES</th>");
                sb.append("<th>Final Status</th>");
                sb.append("</tr>");

                for (String entityId : jobStatusValues.keySet()) {
                    Map<String, String> values = jobStatusValues.get(entityId);
                    sb.append("<tr>");

                    sb.append("<td>").append(entityId).append("</td>");
                    sb.append("<td>").append(safeGet(values, "chunkId")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "nodeId")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "encryptedValue-200")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "encryptedValue-305")).append("</td>");
                    sb.append("<td>").append(safeGet(values, "encryptedValue-AES")).append("</td>");


                    String status = safeGet(values, "status");
                    String statusColor = getStatusColor(status);
                    sb.append("<td style='background-color: ").append(statusColor).append("'>").append(status).append("</td>");

                    sb.append("</tr>");
                }
                sb.append("</table>");
            } else {
                sb.append("<p>No detailed results available.</p>");
            }

            logger.info("{} - Sending email report with subject: {}", logContext, subject);

            long emailStartTime = System.currentTimeMillis();
            mailManager.sendSystemStatusEmail(subject, sb.toString());
            long emailTime = System.currentTimeMillis() - emailStartTime;

            logger.info("{} - Successfully sent email report (took {}ms)", logContext, emailTime);

        } catch (Exception e) {
            logger.error("{} - FAILED to send email report: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    private String safeGet(Map<String, String> map, String key) {
        String value = map.get(key);
        return value != null ? value : "N/A";
    }

    private String getStatusColor(String status) {
        if (status == null) return "#ffcccc"; // Light red for null
        if (status.startsWith("Refreshed") || status.equals("SUCCESS")) return "#ccffcc"; // Light green for success
        if (status.equals("SKIPPED")) return "#ffffcc"; // Light yellow for skipped
        if (status.startsWith("Errored") || status.startsWith("FAILED")) return "#ffcccc"; // Light red for errors
        return "#ffffff"; // White for unknown
    }
}
