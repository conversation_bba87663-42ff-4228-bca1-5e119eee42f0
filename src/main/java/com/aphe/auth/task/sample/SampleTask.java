package com.aphe.auth.task.sample;


import com.aphe.common.tasks.GenericBackgroundTask;
import com.aphe.common.tasks.TaskStatusStore;
import org.jobrunr.jobs.annotations.Recurring;
import org.jobrunr.scheduling.JobScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thymeleaf.util.StringUtils;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class SampleTask extends GenericBackgroundTask {

    private static final Logger logger = LoggerFactory.getLogger(SampleTask.class);

    private static final String LOG_PREFIX = "SAMPLE_TASK";
    private int chunkSize = 3;
    private String nodeId;

    @Autowired
    TaskStatusStore resultStore;

    @Autowired
    JobScheduler jobScheduler;

    @Autowired
    SampleTaskWorker sampleTaskWorker;


    /**
     * This is a sample task where the work is distributed to multiple nodes. The purpose of this main job is to
     * 1. Identify the work by either queritying a database or something.
     * 2. break the work into executable small chunks.
     * 3. Enqueue the chunks for execution.
     * 4. Keep track of the number of chunks created and store them in redis cache with a unique jobId and runId.
     * 5. Implements onChunkCompleted() method that gets called by each chunk job when it completes. This method will be executed on the node where the chunk job is executed.
     * 6. The onChunkCompleted() method will check if all the chunks are completed. If yes,
     * 7.It will call the onExecutionCompleted() method, which will be executed on the node where the chunk job is executed.
     * 8. The onExecutionCompleted() method will do any final work like updating the database, sending notifications, etc.
     */

    public SampleTask() {
        try {
            this.nodeId = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            this.nodeId = "unknown-node-" + UUID.randomUUID().toString().substring(0, 8);
        }
        logger.info("{} - Initialized SampleTask on node: {}", LOG_PREFIX, nodeId);
    }

    @Recurring(id = "authservice-sample-task", cron = "25 12 11 * * *")
    public void scheduleTokenRefresh() {
        // Create a unique runId for this execution with a prefix and date time stamp.
        // This will be used to track the execution and all associated jobs.
        String dateTime = new Date().toString().replace(" ", "-").replace(":", "-");
        String runId = "sample-task:run:" + UUID.randomUUID() + ":" + dateTime;
        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, runId, nodeId);

        try {
            logger.info("{} - STARTING sample-task", logContext);
            logger.info("{} - Looking for Strings to encrypt", logContext);

            long startTime = System.currentTimeMillis();
            List<String> randomStrings = generateRandomStrings();
            long queryTime = System.currentTimeMillis() - startTime;

            logger.info("{} - got {} strings to encrypt in {}ms)", logContext, randomStrings.size(), queryTime);

            if (randomStrings.isEmpty()) {
                logger.info("{} - No strings to encrypt. Exiting.", logContext);
                resultStore.setExpectedChunks(runId, 0);
                return;
            }

            int totalChunks = (randomStrings.size() + chunkSize - 1) / chunkSize;
            resultStore.setExpectedChunks(runId, totalChunks);

            logger.info("{} - Will create {} chunks with chunk size {}", logContext, totalChunks, chunkSize);

            int chunkIndex = 0;
            for (int i = 0; i < randomStrings.size(); i += chunkSize) {
                List<String> chunk = randomStrings.subList(i, Math.min(i + chunkSize, randomStrings.size()));
                chunkIndex++;
                logger.info("{} - Enqueueing chunk {}/{} with {} integration IDs: {}", logContext, chunkIndex, totalChunks, chunk.size(), chunk);

                try {
                    final int chunkId = chunkIndex;
                    jobScheduler.enqueue(() -> sampleTaskWorker.doWork(runId, chunkId, chunk));
                    logger.debug("{} - Successfully enqueued chunk {}", logContext, chunkIndex);
                } catch (Exception e) {
                    logger.error("{} - FAILED to enqueue chunk {}: {}", logContext, chunkIndex, e.getMessage(), e);
                    throw e; // Re-throw to fail the orchestration
                }
            }

            logger.info("{} - COMPLETED token refresh orchestration - enqueued {} chunks for {} integrations",
                       logContext, totalChunks, randomStrings.size());

        } catch (Exception e) {
            logger.error("{} - FAILED token refresh orchestration: {}", logContext, e.getMessage(), e);
            throw e; // Re-throw to ensure JobRunr marks this as failed
        }
    }


    //A method to generate and return a list of 1000 ramdom 100 chars strings.
    public List<String> generateRandomStrings() {
        List<String> randomStrings = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            String randomString = StringUtils.randomAlphanumeric(100);
            randomStrings.add(randomString);
        }
        return randomStrings;
    }
}