package com.aphe.auth.task;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class TokenRefreshResultStore {

    private static final Logger logger = LoggerFactory.getLogger(TokenRefreshResultStore.class);
    private static final String LOG_PREFIX = "TOKEN_REFRESH_RESULT_STORE";

    private final StringRedisTemplate redis;
    private final ThreadLocal<String> runId = new ThreadLocal<>();
    private final String nodeId;

    public TokenRefreshResultStore(StringRedisTemplate redis) {
        this.redis = redis;
        try {
            this.nodeId = InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            this.nodeId = "unknown-node-" + UUID.randomUUID().toString().substring(0, 8);
        }
        logger.info("{} - Initialized TokenRefreshResultStore on node: {}", LOG_PREFIX, nodeId);
    }

    public void setRunId(String id) {
        this.runId.set(id);
    }

    public String getRunId() {
        return this.runId.get();
    }

    public String statusKey()     { return getRunId() + ":status"; }
    public String expectedKey()   { return getRunId() + ":expected"; }
    public String completedKey()  { return getRunId() + ":completed"; }

    public void setExpectedChunks(int count) {
        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, getRunId(), nodeId);

        try {
            logger.info("{} - Setting expected chunks to {}", logContext, count);

            redis.opsForValue().set(expectedKey(), String.valueOf(count));
            redis.opsForValue().set(completedKey(), "0");
            redis.expire(expectedKey(), Duration.ofDays(3));
            redis.expire(completedKey(), Duration.ofDays(3));
            redis.expire(statusKey(), Duration.ofDays(3));

            logger.debug("{} - Successfully set expected chunks and initialized counters", logContext);
        } catch (Exception e) {
            logger.error("{} - FAILED to set expected chunks: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public long incrementCompletedChunks() {
        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, getRunId(), nodeId);

        try {
            long completed = redis.opsForValue().increment(completedKey());
            logger.info("{} - Incremented completed chunks to {}", logContext, completed);
            return completed;
        } catch (Exception e) {
            logger.error("{} - FAILED to increment completed chunks: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public long getExpectedChunks() {
        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, getRunId(), nodeId);

        try {
            String value = redis.opsForValue().get(expectedKey());
            if (value == null) {
                logger.warn("{} - Expected chunks key not found in Redis", logContext);
                return 0;
            }
            long expected = Long.parseLong(value);
            logger.debug("{} - Retrieved expected chunks: {}", logContext, expected);
            return expected;
        } catch (Exception e) {
            logger.error("{} - FAILED to get expected chunks: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public void saveTokenStatus(Long tokenId, Map<String, String> tokenValues) {
        String logContext = String.format("%s - RunId: %s, Node: %s, TokenId: %s",
                                         LOG_PREFIX, getRunId(), nodeId, tokenId);

        try {
            logger.debug("{} - Saving token status with {} values", logContext, tokenValues.size());

            JsonObject jsonObject = new JsonObject();
            tokenValues.forEach(jsonObject::addProperty);
            String value = jsonObject.toString();

            redis.opsForHash().put(statusKey(), tokenId.toString(), value);

            logger.debug("{} - Successfully saved token status: {}", logContext, tokenValues.get("refreshStatus"));
        } catch (Exception e) {
            logger.error("{} - FAILED to save token status: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }

    public Map<Long, Map<String, String>> getAllStatuses() {
        String logContext = String.format("%s - RunId: %s, Node: %s", LOG_PREFIX, getRunId(), nodeId);

        try {
            logger.info("{} - Retrieving all token statuses", logContext);

            long startTime = System.currentTimeMillis();
            Map<Object, Object> entries = redis.opsForHash().entries(statusKey());
            long queryTime = System.currentTimeMillis() - startTime;

            logger.info("{} - Retrieved {} status entries from Redis (took {}ms)",
                       logContext, entries.size(), queryTime);

            Map<Long, Map<String, String>> result = new HashMap<>();
            int successCount = 0;
            int errorCount = 0;

            // Convert these entries to Map<Long, Map<String, String>> type
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                String key = (String) entry.getKey();
                String value = (String) entry.getValue();

                try {
                    if (value.startsWith("{")) {
                        // It's a JSON string, parse it
                        JsonObject jsonObject = JsonParser.parseString(value).getAsJsonObject();
                        // Convert JsonObject to Map
                        Map<String, String> valueMap = new HashMap<>();
                        jsonObject.entrySet().forEach(e ->
                                valueMap.put(e.getKey(), e.getValue().getAsString())
                        );
                        result.put(Long.parseLong(key), valueMap);
                        successCount++;
                    } else {
                        throw new JsonSyntaxException("Value is not a valid JSON string: " + value);
                    }
                } catch (JsonSyntaxException e) {
                    logger.warn("{} - Invalid JSON format for token {}: {}", logContext, key, e.getMessage());
                    Map<String, String> valueMap = Map.of("refreshStatus", "ERROR", "message", "Invalid JSON format");
                    result.put(Long.parseLong(key), valueMap);
                    errorCount++;
                }
            }

            logger.info("{} - Processed {} statuses: {} success, {} errors",
                       logContext, entries.size(), successCount, errorCount);

            return result;

        } catch (Exception e) {
            logger.error("{} - FAILED to retrieve all statuses: {}", logContext, e.getMessage(), e);
            throw e;
        }
    }
}
