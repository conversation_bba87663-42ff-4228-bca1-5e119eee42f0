package com.aphe.auth.task;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.OAuthTokenDTO;
import com.aphe.auth.sso.OAuthIntegrationConvertUtil;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class ReEncryptOAuthTokensTask {

	Logger logger = LoggerFactory.getLogger(ReEncryptOAuthTokensTask.class);

	@Autowired
	OAuthIntegrationMgr oAuthIntegrationMgr;

	@Autowired
	DomainRepository domainRepository;

	@Autowired
	OAuthIntegrationRepository integrationRepository;

	@Autowired
	AccountManager accountManager;

	@Autowired
	OAuthIntegrationConvertUtil convertUtil;

	@Scheduled(cron = "0 25 23 4 11 *")
	public void executeTask() {
		//logger.info("Start of executing the taskName=ReEncryptTask status=Begin");

		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		Iterable<Domain> allDomains = domainRepository.findAll();
		for (Domain domain : allDomains) {
			try {
				if(domain != null){
					logger.info("Working on re-encrypting oAuth values taskName=ReEncryptTask DomainId=" + domain.getId());
					CronJobAuthenticationUtil.configureAuthentication("superadmin", domain.getId(), domain.getDisplayName());
					logger.info("Configured security auth taskName=ReEncryptTask DomainId=" + domain.getId());
					for(OAuthIntegrationPartner partner : OAuthIntegrationPartner.values()) {
						logger.info("Working on partner=" + partner.name() + " taskName=ReEncryptTask DomainId=" + domain.getId());
						OAuthTokenDTO dto = oAuthIntegrationMgr.getOAuthTokenDTO(Long.toString(domain.getId()), partner.name());
						if(dto != null) {
							logger.info("Integration is not null for partner=" + partner.name() + " taskName=ReEncryptTask DomainId=" + domain.getId());
							OAuthIntegrationDTO dto1 = convertUtil.convertTokenDTOToIntegrationDTO(dto);

							logger.info("Loaded DTO for partner=" + partner.name() + " taskName=ReEncryptTask DomainId=" + domain.getId());

							dto1.accountId = dto1.accountId + " ";
							dto1.accessToken = dto1.accessToken + " ";
							dto1.refreshToken = dto1.refreshToken + " ";

							OAuthIntegrationClientDTO updatedDTO = oAuthIntegrationMgr.addOrUpdateIntegration(dto1);
							logger.info("Saved successfully with spaces partner=" + partner.name() + " taskName=ReEncryptTask DomainId=" + domain.getId());

							dto1.accountId = dto1.accountId.trim();
							dto1.accessToken = dto1.accessToken.trim();
							dto1.refreshToken = dto1.refreshToken.trim();

							updatedDTO = oAuthIntegrationMgr.addOrUpdateIntegration(dto1);

							logger.info("Saved successfully after removing spaces partner=" + partner.name() + " taskName=ReEncryptTask DomainId=" + domain.getId());
						}
					}
				}

			} catch (Exception e) {
				logger.error("Error re-encrypting  taskName=ReEncryptTask DomainId=" + domain.getId());
			} finally {
				CronJobAuthenticationUtil.cleanAuthentication();
			}
		}

		CronJobAuthenticationUtil.cleanAuthentication();
		//logger.info("Done executing the taskName=ReEncryptTask status=End");
	}

}
