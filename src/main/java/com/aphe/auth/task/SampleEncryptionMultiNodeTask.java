package com.aphe.auth.task;


import com.aphe.auth.service.MailManager;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.EncryptionUtil;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thymeleaf.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class SampleEncryptionMultiNodeTask extends ApheBackgroundTask {

    private static final Logger logger = LoggerFactory.getLogger(SampleEncryptionMultiNodeTask.class);

    @Autowired
    MailManager mailManager;


    @Recurring(id = "authservice-sample-encryption-task", cron = "25 12 11 * * *")
    public void startTask() {
        setupTask();
        executeTask();
    }

    public void setupTask() {
        jobName = "sample-encryption-task";
        chunkSize = 21;
    }

    //A method to generate and return a list of 1000 ramdom 100 chars strings.
    public List<String> getUnitsOfWork() {
        List<String> randomStrings = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            String randomString = StringUtils.randomAlphanumeric(20);
            randomStrings.add(randomString);
        }
        return randomStrings;
    }

    //execute a unit of work
    public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWorkId) {
        String logContext = String.format("job_name=%s run_id=%s chunk_id=%s node_id=%s id=%s", jobName, runId, chunkId, nodeId, unitOfWorkId);
        String strToEncrypt = unitOfWorkId;
        try {
            String s = new EncryptionUtil().encryptWithDefault200(strToEncrypt);
            String s1 = new EncryptionUtil().encryptWithDefault305(strToEncrypt);
            String encrypt = new EncryptionUtil().encrypt(strToEncrypt);
            ApheExecutionData resultData = new ApheExecutionData();
            resultData.result = ApheExecutionResult.SUCCESS;
            resultData.additionalInfo.put("encryptedValue-200", s);
            resultData.additionalInfo.put("encryptedValue-305", s1);
            resultData.additionalInfo.put("encryptedValue-AES", encrypt);
            Thread.sleep(100);
            return resultData;
        }catch (Exception e) {
            ApheExecutionData result = new ApheExecutionData();
            result.result = ApheExecutionResult.FAILED;
            result.message = e.getMessage();
            return result;
        }
    }

    public List<String> getAdditionalEmailColumns() {
        return List.of("encryptedValue-200", "encryptedValue-305", "encryptedValue-AES");
    }

    public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
        logger.info("Job completed. toBeProcessed={}, processed={}, skipped={}, errored={}", toBeProcessed, processed, skipped, errored);
        long emailStartTime = System.currentTimeMillis();
        mailManager.sendSystemStatusEmail(subject, emailHTML);
        long emailTime = System.currentTimeMillis() - emailStartTime;
        logger.info("Email sent in {}ms", emailTime);
    }
}