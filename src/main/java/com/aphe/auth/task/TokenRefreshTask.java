package com.aphe.auth.task;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.MailManager;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TokenRefreshTask {

	Logger logger = LoggerFactory.getLogger(TokenRefreshTask.class);

	@Autowired
	OAuthIntegrationMgr oAuthIntegrationMgr;

	@Autowired
	OAuthIntegrationRepository integrationRepository;

	@Autowired
	AccountManager accountManager;

	@Autowired
	protected MailManager mailManager;

	@Value("${appName}")
	private String appName;

//	@Scheduled(cron = "25 12 11 * * *")
	public void executeTask() {
		//logger.info("Start of executing the taskName=TokenRefreshTask status=Begin");

		CronJobAuthenticationUtil.configureAuthentication("superadmin");

		StringBuilder sb = new StringBuilder();
		int toBeProcessed = 0;
		int processed = 0;
		int errored = 0;
		int skipped = 0;

		Map<Long, Map<String, String>> integrationValues = new HashMap<>();
		try {
			// Get all the accounts that have token expiring in next 10 days...
			Date expiryDate = DateUtils.addDays(new Date(), 10);
			List<OAuthIntegration> expiringTokens = integrationRepository.findAllWithRefreshTokenDateBefore(expiryDate);

			//add each integration to the map
			for (OAuthIntegration integration : expiringTokens) {
				Map<String, String> values = new HashMap<>();
				integrationValues.put(integration.getId(), values);
				values.put("domainId", integration.getDomainId());
				values.put("domainName", "??");
				values.put("partner", integration.getPartner().name());
				values.put("accountName", integration.getAccountName());
				values.put("accessTokenExpiryDate", String.valueOf(integration.getAccessTokenExpiryDate()));
				values.put("refreshTokenExpiryDate", String.valueOf(integration.getRefreshTokenExpiryDate()));
			}

			toBeProcessed = expiringTokens.size();
			skipped = toBeProcessed;

			for (OAuthIntegration integration : expiringTokens) {
				Map<String, String> values = integrationValues.get(integration.getId());

				try {
					long domainId = Long.parseLong(integration.getDomainId());

					CronJobAuthenticationUtil.configureAuthentication("superadmin", domainId, "Unkown domain name");

					Domain domain = accountManager.getDomain(integration.getDomainId());

					if (domain == null) {
						values.put("domainName", "Domain not found");
						values.put("refreshStatus", "Domain not found");
						continue;
					} else {
						values.put("domainName", domain.getDisplayName());
					}

					OAuthIntegrationClientDTO dto = oAuthIntegrationMgr.hardRefreshTokens(integration.getPartner().toString());
					if (dto != null) {
						if(dto.connected) {
							processed++;
							skipped--;
							values.put("refreshStatus", "Refreshed");
							values.put("newAccessTokenExpiryDate", String.valueOf(dto.accessTokenExpiryDate));
							values.put("newRefreshTokenExpiryDate", String.valueOf(dto.refreshTokenExpiryDate));
							logger.info("Tokens have been refreshed successfully. taskName=TokenRefreshTask DomainId=" + integration.getDomainId());
						} else {
							values.put("refreshStatus", "Errored. DTO is not connected");
							errored++;
							logger.info("Failed to refresh token. Disconnected the integration. taskName=TokenRefreshTask DomainId=" + integration.getDomainId());
						}
					} else {
						values.put("refreshStatus", "Errored. DTO is is null");
						errored++;
						logger.error("Error refreshing token. Will attempt again. taskName=TokenRefreshTask DomainId=" + integration.getDomainId());
					}
				} catch (Exception e) {
					values.put("refreshStatus", "Errored with an exception. " + e.getMessage());
					errored++;
					logger.error("Error refreshing token taskName=TokenRefreshTask DomainId=" + integration.getDomainId() + " Error=" + e.getMessage(), e);
				} finally {
					CronJobAuthenticationUtil.cleanAuthentication();
				}
			}
		} catch (Exception e) {
			sb.append("Error executing the taskName=TokenRefreshTask Error=" + e.getMessage()).append("<br/><br/>");
		}finally {
			//Send the email.
			String subject = "Token Refresh Task Status - " + appName;

			sb.append("Nubmer of tokens to be processed= " + toBeProcessed).append("<br/>");
			sb.append("Nubmer of tokens processed = " + processed).append("<br/>");
			sb.append("Nubmer of tokens skipped = " + skipped).append("<br/>");
			sb.append("Nubmer of tokens errored = " + errored).append("<br/>");
			sb.append("<br/>");

			if(integrationValues.keySet().size() > 0) {

				sb.append("<table><tr><th>Domain Id</th><th>Domain Name</th><th>Partner Name</th>")
						.append("<th>Account Name</th>")
						.append("<th>Old Access Expiry</th>")
						.append("<th>Old Refresh Expiry</th>")
						.append("<th>Status Message</th>")
						.append("<th>New Access Expiry</th>")
						.append("<th>New Refresh Expiry</th>")
						.append("</tr>");

				for (Long integrationId : integrationValues.keySet()) {
					Map<String, String> values = integrationValues.get(integrationId);
					sb.append("<tr>");

					sb.append("<td>").append(values.get("domainId")).append("</td>");
					sb.append("<td>").append(values.get("domainName")).append("</td>");
					sb.append("<td>").append(values.get("partner")).append("</td>");
					sb.append("<td>").append(values.get("accountName")).append("</td>");
					sb.append("<td>").append(values.get("accessTokenExpiryDate")).append("</td>");
					sb.append("<td>").append(values.get("refreshTokenExpiryDate")).append("</td>");

					sb.append("<td>").append(values.get("refreshStatus")).append("</td>");
					sb.append("<td>").append(values.get("newAccessTokenExpiryDate")).append("</td>");
					sb.append("<td>").append(values.get("newRefreshTokenExpiryDate")).append("</td>");

					sb.append("</tr>");
				}
				sb.append("</table>");

			}

			mailManager.sendSystemStatusEmail(subject, sb.toString());
			CronJobAuthenticationUtil.cleanAuthentication();
			//logger.info("Done executing the taskName=TokenRefreshTask status=End");
		}

	}
}
