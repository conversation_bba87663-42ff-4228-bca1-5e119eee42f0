package com.aphe.auth.task;

import com.aphe.common.mail.Email;
import com.aphe.common.mail.MailService;
import com.aphe.common.tasks.ApheBackgroundTask;
import com.aphe.common.tasks.ApheExecutionData;
import com.aphe.common.tasks.ApheExecutionResult;
import com.aphe.common.util.CronJobAuthenticationUtil;
import org.jobrunr.jobs.annotations.Recurring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Creates recurring job to process email queue.
 */

@Profile({"dev", "qa", "sandbox", "prod"})
@Component
public class EmailQueueMultiNodeTask extends ApheBackgroundTask {

	private static final Logger logger = LoggerFactory.getLogger(EmailQueueMultiNodeTask.class);

	@Autowired
	private MailService mailService;

	@Recurring(id = "authservice-email-job", interval = "PT30S")
	@Override
	public void startTask() {
		jobName = "authservice-email-job";
		chunkSize = 10;
		executeTask();
	}

	@Override
	public List<String> getUnitsOfWork() {
		CronJobAuthenticationUtil.configureAuthentication("superadmin");
		List<Email> queue = mailService.getQueuedEmails();
		List<String> mailIds = queue.stream().map(Email::getId).map(String::valueOf).collect(Collectors.toList());
		return mailIds;
	}

	@Override
	public ApheExecutionData executeUnitOfWork(String runId, String chunkId, String nodeId, String unitOfWork) {
		String logContext = String.format("job_name=%s run_id=%s chunk_id=%s node_id=%s id=%s", jobName, runId, chunkId, nodeId, unitOfWork);
		try {
			logger.info("{} - STARTING processing.", logContext);
			Long mailId = Long.parseLong(unitOfWork);
			mailService.sendEmail(mailId);
			return new ApheExecutionData(ApheExecutionResult.SUCCESS, null);
		}catch (Exception e) {
			logger.error("{} - FAILED processing. message={}", logContext, e.getMessage(), e);
			return new ApheExecutionData(ApheExecutionResult.FAILED, e.getMessage());
		}
	}

	@Override
	public List<String> getAdditionalEmailColumns() {
		return List.of();
	}

	@Override
	public void notifyJobCompleted(int toBeProcessed, int processed, int skipped, int errored, Map<String, Map<String, String>> workStatus, String subject, String emailHTML) {
		return;
	}
}
