package com.aphe.auth.task;

import com.aphe.common.tasks.GenericBackgroundTask;
import org.jobrunr.jobs.annotations.Recurring;
import org.jobrunr.scheduling.BackgroundJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * Creates recurring job to process email queue.
 */

@Profile({"dev", "qa", "sandbox", "prod"})
@Component
public class EmailQueueBackgroundTask extends GenericBackgroundTask {

	@Autowired
    EmailQueueDispatcher emailQueueDispatcher;

	Logger logger = LoggerFactory.getLogger(EmailQueueBackgroundTask.class);

	@Recurring(id = "authservice-email-job", interval = "PT30S")
//	@Scheduled(initialDelayString = "${aphe.email.emailQueueTask.initialDelay}", fixedDelayString = "${aphe.email.emailQueueTask.fixedDelay}")
	public void sendEmails() throws InterruptedException {
		long startTime = System.currentTimeMillis();  // Record the start time
//		logger.info("Started sending emails at: " + startTime);

		boolean pauseTasks = isPauseTasks("authservice-email-job");
		if(pauseTasks) {
			return;
		}

		BackgroundJob.enqueue(() -> emailQueueDispatcher.dispatchEmails());
//		CronJobAuthenticationUtil.configureAuthentication("superadmin");
//		try {
//			List<Email> mailQueue = mailService.getQueuedEmails();
//			BackgroundJob.enqueue(mailQueue.stream(), (mail) -> mailService.sendEmail(mail.getId()));
//		} catch (Exception e) {
//			logger.error("Attention Required: Error processing email requests: ", e);
//		} finally {
////			logger.info("The domain service email job has finished...");
//		}
		long endTime = System.currentTimeMillis();  // Record the end time
//		logger.info("Finished sending emails at: " + endTime);
		long duration = endTime - startTime;  // Calculate the duration
		logger.info("authservice-email-job completed in: " + duration + " milliseconds");
	}

}
