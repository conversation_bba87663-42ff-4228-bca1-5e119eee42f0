package com.aphe.auth;

import com.aphe.auth.security.FailedAttemptsManager;
import com.aphe.common.util.PropertiesManager;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import io.sentry.Sentry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.HashMap;

@SpringBootApplication
@EnableJpaRepositories("com.aphe.*")
@ComponentScan("com.aphe.*")
@EntityScan("com.aphe.*")
@EnableScheduling
@PropertySource("classpath:git.properties")
@EnableEncryptableProperties
@EnableAsync
public class Application extends SpringBootServletInitializer {

	private static final Logger logger = LoggerFactory.getLogger(Application.class);

	@Autowired
	Environment env;

	@Value("${app}")
	private String app;

	@Value("${appName}")
	private String appName;

	@Value("${spring.profiles.active:}")
	private String activeProfiles;

	@Value("${aphe.sentry.dsn}")
	private String sentryDSN;

	@Value("${aphe.sentry.stacktrace.app.packages}")
	private String sentryPackages;

	@Value("${git.commit.id.describe}")
	private String gitCommitId;
	
	@Value("${aphe.auth.maxUserFailedAttempts}")
	private int MAX_USER_ATTEMPTS;
	
	@Value("${aphe.auth.maxIPFailedAttempts}")
	private int MAX_IP_ATTEMPTS;
	
	@Value("${aphe.auth.resetIPFailedAttempts}")
	private int RESET_IP_ATTEMPTS;
	
	@Value("${aphe.auth.decrementIPFailedAttempts}")
	private int DECREMENT_IP_ATTEMPTS;

	@Value("${aphe.auth.userLockoutPeriod}")
	private int USER_LOCKOUT_PERIOD;

	@Value("${aphe.auth.ipLockoutPeriod}")
	private int IP_LOCKOUT_PERIOD;

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(Application.class);
	}

	@Bean
	public CommandLineRunner demo() {

		return (args) -> {
			
			if(!PropertiesManager.isDev()) {
				String sentryURL = getSentryURL();
				Sentry.init(sentryURL);	
			}
			
			logger.info("************* APP NAME           *** " + appName);
			logger.info("************* APP MODE           *** " + PropertiesManager.getAppMode());
			logger.info("************* ACTIVE PROFILES    *** " + activeProfiles);


			logger.info("************* DB Config      *** " + env.getProperty("aphe.dbProperty"));

			logger.error("This is a sentry test for auth service... please ignore");
		};
	}

	public String getSentryURL() {
		HashMap<String, String> sentryParams = new HashMap<>();
		sentryParams.put("stacktrace.app.packages", sentryPackages);
		sentryParams.put("environment", activeProfiles);
		sentryParams.put("release", gitCommitId);

		StringBuffer buffer = new StringBuffer();
		buffer.append(sentryDSN).append("?");
		for (String s : sentryParams.keySet()) {
			buffer.append(s).append("=").append(sentryParams.get(s)).append("&");
		}
		buffer.setLength(buffer.length() - 1);
		return buffer.toString();
	}
	
    @Bean FailedAttemptsManager failedAttemptsManager() {
        return new FailedAttemptsManager(MAX_USER_ATTEMPTS, MAX_IP_ATTEMPTS, RESET_IP_ATTEMPTS, DECREMENT_IP_ATTEMPTS, USER_LOCKOUT_PERIOD, IP_LOCKOUT_PERIOD);
    }
}