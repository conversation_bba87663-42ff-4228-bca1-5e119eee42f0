package com.aphe.auth.rest;

import com.aphe.auth.service.ApheSecurityManager;
import com.aphe.auth.service.dto.MFAConfigDTO;
import com.aphe.auth.service.dto.MFADTO;
import com.aphe.auth.service.mfa.MFAManager;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.jwt.JwtUtil;
import io.swagger.v3.oas.annotations.Hidden;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


/**
 * UNAUTHENTICATED SPACE, but checks for jwt token and allows MFA operations.
 */
@RestController
@RequestMapping(path = "/rs/api/accounts/mfa/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Hidden
public class MFAController {

    private static final Logger logger = LoggerFactory.getLogger(MFAController.class);

    @Autowired
    protected ApheSecurityManager apheSecurityManager;

    @Autowired
    JwtUtil jwtUtil;

    @Autowired
    protected MFAManager mfaManager;


    @GetMapping
    public MFAConfigDTO mfaConfig(HttpServletRequest request, HttpServletResponse response) throws ApheException {

        ApheUserDetails apheUserDetails = jwtUtil.getUserDetails(request);

        if (apheUserDetails == null) {
            throw new ApheForbiddenException();
        }

        return mfaManager.getMFAConfig(apheUserDetails.getUserId());

    }


    @PutMapping(path = "enableMFA/")
    public ResponseEntity<?> enableMFA(HttpServletRequest request, HttpServletResponse response, @RequestBody MFADTO mfaDTO) throws Exception {

        ApheUserDetails apheUserDetails = jwtUtil.getUserDetails(request);

        if (apheUserDetails == null) {
            throw new ApheForbiddenException();
        }

        if (apheUserDetails.getUsername().equalsIgnoreCase(mfaDTO.loginName)) {
            try {
                boolean b = mfaManager.enableAndVerifyMFA(mfaDTO.loginName, mfaDTO.code, mfaDTO.deviceFingerprint, mfaDTO.deviceName, mfaDTO.trustDevice);

                if (b == true) {
                    //If true, enable MFA on the token too.
                    String token = jwtUtil.getToken(request);
                    apheSecurityManager.setMFASuccessOnToken(token, request, response);
                }
                return ResponseEntity.ok().build();
            } catch (Exception e) {
                String message = "Error enabling MFA.";
                logger.error(message, e);
                throw e;
            }
        } else {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping(path = "verifyMFA/")
    public ResponseEntity<?> verifyMFA(HttpServletRequest request, HttpServletResponse response, @RequestBody MFADTO mfaDTO) throws Exception {
        return enableMFA(request, response, mfaDTO);
    }


    @PutMapping(path = "skipMFA/")
    public ResponseEntity<?> skipMFA(HttpServletRequest request, HttpServletResponse response, @RequestBody MFADTO mfaDTO) throws Exception {

        ApheUserDetails apheUserDetails = jwtUtil.getUserDetails(request);

        if (apheUserDetails == null) {
            throw new ApheForbiddenException();
        }

        if (apheUserDetails.getUsername().equalsIgnoreCase(mfaDTO.loginName)) {
            try {
                boolean canSkipMFA = mfaManager.canSkipMFA(apheUserDetails.getUserId());
                if (canSkipMFA) {
                    //If true, increment the skip counter on the user.
                    mfaManager.incrementSkipCounter(apheUserDetails.getUserId());
                    String token = jwtUtil.getToken(request);
                    apheSecurityManager.setMFANotRequiredOnToken(token, request, response);
                    return ResponseEntity.ok().build();
                } else {
                    return ResponseEntity.badRequest().build();
                }
            } catch (Exception e) {
                String message = "Error enabling MFA.";
                logger.error(message, e);
                throw e;
            }
        } else {
            return ResponseEntity.badRequest().build();
        }
    }



}
