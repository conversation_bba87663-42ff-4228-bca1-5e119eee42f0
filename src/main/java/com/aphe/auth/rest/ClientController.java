package com.aphe.auth.rest;

import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.AccountManagerTxn;
import com.aphe.auth.service.dto.CreateDomainDTO;
import com.aphe.auth.service.dto.DomainDTO;
import io.swagger.v3.oas.annotations.Hidden;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping(path = "/rs/api/clients/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Hidden
public class ClientController {

	private static Logger logger = LoggerFactory.getLogger(ClientController.class);

	@Autowired
	private AccountManager acctMgr;

	@Autowired
	private AccountManagerTxn acctMgrTxn;

	/**
	 * Adds a client under the authenticated session. Authenticated session must be an accountant user.
	 */
	@PostMapping
	public DomainDTO addClient(HttpServletRequest request, HttpServletResponse response, @RequestBody CreateDomainDTO domainDTO) throws Exception {
		try {
			String parentDomainID = acctMgr.getParentDomainId();
			return acctMgr.addClient(parentDomainID, domainDTO, request, response);
		} catch (Exception e) {
			logger.error("Error adding client. " + e.getMessage(), e);
			throw e;
		}
	}

	@GetMapping
	public List<DomainDTO> getClients() throws Exception {
		try {
			return acctMgrTxn.getClients(true);
		} catch (Exception e) {
			logger.error("Error getting clients. " + e.getMessage(), e);
			throw e;
		}
	}

}
