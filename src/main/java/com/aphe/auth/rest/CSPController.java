package com.aphe.auth.rest;

import io.swagger.v3.oas.annotations.Hidden;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * UNAUTHENTICATED SPACE.....
 */
@RestController
@RequestMapping(path = "/rs/api/cspreport/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Hidden
public class CSPController {

    private static final Logger logger = LoggerFactory.getLogger(CSPController.class);

    @PostMapping(consumes = "application/csp-report")
    public ResponseEntity<?> reportCSP(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            String s = IOUtils.toString(request.getReader());
            logger.error("CSP report: {}", s);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error creating account. " + e.getMessage(), e);
            throw e;
        }
    }
}
