package com.aphe.auth.rest;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.core.repo.UserRepository;
import com.aphe.auth.service.*;
import com.aphe.auth.service.d2d.D2DInvitationManagerTxn;
import com.aphe.auth.service.d2d.dto.D2DInvitationDTO;
import com.aphe.auth.service.dto.*;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.error.exceptions.ApheNotFoundException;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.jwt.JwtUtil;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


/**
 * UNAUTHENTICATED SPACE.....
 */
@RestController
@RequestMapping(path = "/rs/api/accounts/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Hidden
public class AccountController {

    private static final Logger logger = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private AccountManager acctMgr;

    @Autowired
    private UserManager userMgr;

    @Autowired
    private UserManagerTxn userMgrTxn;

    @Autowired
    private DomainAccessManager domainAccessManager;

    @Autowired
    private D2DInvitationManagerTxn d2DInvitationManagerTxn;

    @Autowired
    private MailManager mailManager;

    @Autowired
    JwtUtil jwtUtil;

    @Autowired
    UserRepository userRepository;

    @Autowired
    DomainRepository domainRepository;


    @PostMapping
    public UserDTO createAccount(HttpServletRequest request, HttpServletResponse response, @RequestBody CreateAccountDTO accountDTO) throws Exception {
        try {
            UserDTO dto = acctMgr.createAccount(accountDTO, request, response);
            return dto;
        } catch (Exception e) {
            logger.error("Error creating account. " + e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping(path = "users/")
    public UserDTO createUser(HttpServletRequest request, HttpServletResponse response, @RequestBody CreateUserDTO createUserDTO) throws Exception {
        try {
            UserDTO dto = userMgr.createUser(createUserDTO, request, response);
            return dto;
        } catch (Exception e) {
            logger.error("Error creating account. " + e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping(path = "contactus/")
    public ResponseEntity<?> contact(@RequestBody ContactDTO contatDTO) throws Exception {
        try {
            mailManager.sendContactUsEmail(contatDTO);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error creating account. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping(path = "emailconfirmationcode/")
    public ResponseEntity<?> sendEmailConfirmationLink(@RequestBody EmailConfirmationDTO emailConfirmationDTO) throws Exception {
        try {
            userMgrTxn.sendEmailConfirmationLink(emailConfirmationDTO.loginName);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error sending email confirmation link. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping(path = "confirmemail/")
    public ResponseEntity<?> confirmEmail(@RequestBody EmailConfirmationDTO emailConfirmationDTO) throws Exception {
        try {
            userMgrTxn.confirmEmail(emailConfirmationDTO);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error confirming email. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping(path = "passwordresetcode/")
    public ResponseEntity<?> sendPasswordResetLink(@RequestBody PasswordResetDTO passwordResetDTO) throws Exception {
        try {
            userMgrTxn.sendPasswordResetLink(passwordResetDTO.loginName);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error sending password reset link. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping(path = "resetpassword/")
    public ResponseEntity<?> resetPassword(@RequestBody PasswordResetDTO passwordResetDTO) throws Exception {
        return changeOrResetPassword(passwordResetDTO);
    }

    @PutMapping(path = "changepassword/")
    public ResponseEntity<?> changePassword(@RequestBody PasswordResetDTO passwordResetDTO) throws Exception {
        return changeOrResetPassword(passwordResetDTO);
    }

    private ResponseEntity<?> changeOrResetPassword(PasswordResetDTO passwordResetDTO) throws Exception {
        try {
            userMgrTxn.changePassword(passwordResetDTO);
            return ResponseEntity.ok().build();
        } catch (ApheDataValidationException e) {
            logger.error("Error changing/resetting password. " + e.getValidationMessages(), e);
            throw e;
        } catch (Exception e) {
            logger.error("Error changing/resetting password. " + e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping(path = "invitations/{invitationCode}/")
    @Hidden
    public InvitationDTO getInvitation(@PathVariable("invitationCode") String invitationCode) throws Exception {
        try {
            InvitationDTO dto = domainAccessManager.getInvitationByCode(invitationCode);
            if (dto == null) {
                throw new ApheNotFoundException();
            }
            return dto;
        } catch (Exception e) {
            logger.error("Error creating a new user and accepting invite. " + e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping(path = "domaininvitations/{invitationCode}/")
    @Hidden
    public D2DInvitationDTO getDomainInvitation(@PathVariable("invitationCode") String invitationCode) throws Exception {
        try {
            D2DInvitationDTO dto = d2DInvitationManagerTxn.getInvitationByCode(invitationCode);
            if (dto == null) {
                throw new ApheNotFoundException();
            }
            return dto;
        } catch (Exception e) {
            logger.error("Error creating a new user and accepting invite. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping(path = "tokens/")
    public ResponseEntity<?> validateToken(HttpServletRequest request, HttpServletResponse response, @RequestBody String token) throws Exception {
        try {
            if (token != null) {
                boolean validToken = acctMgr.validateToken(request, response, token);
                if (!validToken) {
                    return ResponseEntity.badRequest().build();
                    //throw new ApheDataValidationException("token", "Invalid token");
                }
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.badRequest().build();
//                throw new ApheDataValidationException("token", "Invalid token");
            }
        } catch (Exception e) {
            logger.error("Error validating token. " + e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping(path = "currentsession/")
    @Hidden
    public SessionDTO getSession(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            SessionDTO sessionDTO = getCurrentSession(request);
            return sessionDTO;
        } catch (ApheForbiddenException e) {
            logger.warn("Error getting current session. Forbidden exception. " + e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("Error getting current session. " + e.getMessage(), e);
            throw e;
        }
    }


    public SessionDTO getCurrentSession(HttpServletRequest request) throws ApheForbiddenException {
        ApheUserDetails apheUserDetails = jwtUtil.getUserDetails(request);

        if (apheUserDetails == null) {
            logger.warn("Could not get user details from token.");
            throw new ApheForbiddenException();
        }

        long currentDomainId = apheUserDetails.getDomainId();
        long parentDomainId = apheUserDetails.getParentDomainId();
        long userId = apheUserDetails.getUserId();

        Domain currentDomain = domainRepository.findById(currentDomainId).orElse(null);
        Domain parentDomain = domainRepository.findById(parentDomainId).orElse(null);
        User u = userRepository.findById(userId).orElse(null);

        SessionDTO dto = new SessionDTO();
        dto.userId = u.getId();
        dto.userGlobalId = u.getGlobalId().toString();
        dto.loginName = u.getLogin().getLoginName();
        dto.domainId = currentDomainId;
        if(currentDomain != null) {
            dto.domainGlobalId = currentDomain.getGlobalId().toString();
            dto.domainName = currentDomain != null ? currentDomain.getName() : "";
        }
        if(parentDomain != null) {
            dto.parentDomainGlobalId = parentDomain.getGlobalId().toString();
            dto.parentDomainName = parentDomain != null ? parentDomain.getName() : "";
        }
        dto.parentDomainId = parentDomainId;
        dto.firstName = u.getFirstName();
        dto.lastName = u.getLastName();
        dto.email = u.getEmail();
        dto.isSuperAdmin = apheUserDetails.isSuperAdmin();
        dto.isEmailConfirmed = u.getLogin().isEmailConfirmed();
        dto.domainType = currentDomain != null ? currentDomain.getDomainType() : null;
        dto.ssoPartner = u.getLogin().getSsoPartner() != null ? u.getLogin().getSsoPartner().toString() : "";

        return dto;

    }

}
