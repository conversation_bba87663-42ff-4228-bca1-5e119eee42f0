package com.aphe.auth.rest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/rs/api/log/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
public class LoggingController {

	private static Logger logger = LoggerFactory.getLogger(LoggingController.class);

	@GetMapping
	public String log() {
		logger.trace("This is a TRACE level message");
		logger.debug("This is a DEBUG level message");
		logger.info("This is an INFO level message");
		logger.warn("This is a WARN level message");
		logger.error("This is an ERROR level message");
		return "See the log for details";
	}

}
