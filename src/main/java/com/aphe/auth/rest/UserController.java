package com.aphe.auth.rest;

import com.aphe.auth.service.UserManagerTxn;
import com.aphe.auth.service.dto.PersonalInfoDTO;
import io.swagger.v3.oas.annotations.Hidden;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping(path = "/rs/api/users/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Hidden
public class UserController {

    private static Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserManagerTxn userMgrTxn;

    @PutMapping(path = "updatepersonalinfo/")
    public ResponseEntity<?> updatePersonalInfo(@RequestBody PersonalInfoDTO personalInfoDTO) throws Exception {
        try {
            userMgrTxn.updateUser(personalInfoDTO);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error sending password reset link. " + e.getMessage(), e);
            throw e;
        }
}


    @GetMapping
    @Hidden
    public List<com.aphe.common.auth.UserDTO> getUsersAndDomains(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            List<com.aphe.common.auth.UserDTO> users = userMgrTxn.getUsersAndDomains();
            return users;
        } catch (Exception e) {
            logger.error("Error getting domain users. " + e.getMessage(), e);
            throw e;
        }
    }

}
