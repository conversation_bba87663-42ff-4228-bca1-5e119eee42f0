package com.aphe.auth.rest;

import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.UserManager;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(path = "/rs/api/sessions/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Auth")
public class SessionController {

    private static Logger logger = LoggerFactory.getLogger(SessionController.class);

    @Autowired
    private AccountManager acctMgr;

    @Autowired
    private UserManager userMgr;


    @PostMapping(path = "tokens/{domainId}/")
    public ResponseEntity<?> generateTokenForDomain(HttpServletRequest request, HttpServletResponse response, @PathVariable("domainId") long domainId) throws Exception {
        try {
            String token = acctMgr.generateTokenForDomain(domainId, request, response);
            if (token != null) {
                return ResponseEntity.ok().build();
            } else {
                throw new Exception("Error generating the token. Null token was returned.");
            }
        } catch (Exception e) {
            logger.error("Error generating token for domain. " + e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping(path = "logins/{loginName}/")
    public ResponseEntity<?> generateTokenForLogin(HttpServletRequest request, HttpServletResponse response, @PathVariable("loginName") String loginName) throws Exception {
        try {
            String token = acctMgr.generateTokenForLogin(loginName, request, response);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error generating token for domain. " + e.getMessage(), e);
            throw e;
        }
    }

}
