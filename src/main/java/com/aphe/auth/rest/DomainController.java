package com.aphe.auth.rest;

import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.AccountManagerTxn;
import com.aphe.auth.service.UserManagerTxn;
import com.aphe.auth.service.dto.*;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(path = "/rs/api/domains/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Auth")
public class DomainController {

    private static Logger logger = LoggerFactory.getLogger(DomainController.class);

    @Autowired
    private AccountManager acctMgr;

    @Autowired
    private AccountManagerTxn accountManagerTxn;

    @Autowired
    private UserManagerTxn userMgrTxn;

    @Autowired
    private OAuthIntegrationMgr integrationMgr;

    @GetMapping
    public List<DomainDTO> getDomains() throws Exception {
        try {
            return acctMgr.getDomains(false);
        } catch (Exception e) {
            logger.error("Error getting domains. " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Adds a domain under the authenticated user.
     */
    @PostMapping
    @Hidden
    public DomainDTO addDomain(@RequestBody CreateDomainDTO domainDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            DomainDTO d = acctMgr.addDomain(domainDTO, request, response);
            return d;
        } catch (Exception e) {
            logger.error("Error adding domain. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping
    @Hidden
    public DomainDTO updateDomain(@RequestBody UpdateDomainDTO domainDTO) throws Exception {
        try {
            DomainDTO d = acctMgr.updateDomainDoNotPropagate(domainDTO);
            return d;
        } catch (Exception e) {
            logger.error("Error updating domain. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping(path = "users/")
    @Hidden
    public Map<Long, Collection<UserDTO>> getDomainUsers(HttpServletRequest request, HttpServletResponse response, @RequestBody List<Long> domainIds) throws Exception {
        try {
            Map<Long, Collection<UserDTO>> users = userMgrTxn.getUsers(domainIds);
            return users;
        } catch (Exception e) {
            logger.error("Error getting domain users. " + e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping(path = "accountants/")
    @Hidden
    public Map<Long, Collection<String>> getDomainAccountants(HttpServletRequest request, HttpServletResponse response, @RequestBody List<Long> domainIds) throws Exception {
        try {
            Map<Long, Collection<String>> users = accountManagerTxn.getAccountants(domainIds);
            return users;
        } catch (Exception e) {
            logger.error("Error getting domain accountants. " + e.getMessage(), e);
            throw e;
        }
    }

    @GetMapping(path = "oauth/{partner}/")
    @Hidden
    public OAuthIntegrationDTO getOAuthAccessToken(@PathVariable(name = "partner") String partner) throws Exception {
        try {
            integrationMgr.refreshTokensIfRequired(partner);
            String domainId = integrationMgr.getCurrentDoaminId();
            return integrationMgr.getAccountingIntegrationForDomainService(domainId, partner);
        } catch (Exception e) {
            logger.error("Error getting oAuthAccessToken " + e.getMessage(), e);
            throw e;
        }
    }

}
