package com.aphe.auth.sso;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.List;

public abstract class OAuthConnectionUtil {

    final static Logger logger = LogManager.getLogger(OAuthConnectionUtil.class);

    protected boolean isAccessTokenExpiringDefault(OAuthIntegrationClientDTO dto) {
        try {
            if (DateUtils.addMinutes(new Date(), 10).after(dto.accessTokenExpiryDate)) {
                return true;
            }
        } catch (Exception exception) {
            logger.error("Error figuring out token expiry time");
        }
        return false;
    }

    public abstract boolean revokeToken(OAuthIntegrationDTO dto);

    public abstract OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException;

    public abstract boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto);

    public abstract OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException;

    public abstract String getAccountName(OAuthIntegrationDTO dto);

    public abstract List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto);

}
