package com.aphe.auth.sso.zoho;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.auth.sso.OAuthConnectionUtil;
import com.aphe.auth.sso.TokenRevokedException;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.StringUtil;
import com.google.api.client.googleapis.auth.oauth2.GoogleRefreshTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.zoho.books.api.OrganizationsApi;
import com.zoho.books.exception.BooksException;
import com.zoho.books.model.Organization;
import com.zoho.books.model.OrganizationList;
import com.zoho.books.service.ZohoBooks;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

@Component
public class ZohoConnectUtil extends OAuthConnectionUtil {

	@Value("${aphe.zoho.clientId}")
	private String clientId;

	@Value("${aphe.zoho.clientSecret}")
	private String clientSecret;

	@Value("${aphe.zoho.revokeTokenURL}")
	private String revokeTokenURL;

	@Value("${aphe.zoho.tokenServerURL}")
	private String tokenServerURL;

	@Value("${aphe.xero.authServerURL}")
	private String authServerURL;

	@Value("${aphe.zoho.redirectPath}")
	private String redirectPath;

	final static Logger logger = LogManager.getLogger(ZohoConnectUtil.class);

	public boolean revokeToken(OAuthIntegrationDTO dto) {
		try {
			RestTemplate restTemplate = new RestTemplate();
			restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			String resourceUrl = revokeTokenURL + "?token="+dto.refreshToken;
			ResponseEntity<String> response = restTemplate.exchange(resourceUrl, HttpMethod.POST, null, String.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return true;
			}
		} catch (Exception e1) {
			logger.error("Error revoking token", e1);
			return false;
		}
		return true;
	}

	@Override
	public OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
		OAuthIntegrationDTO updatedDTO = null;
		String realmId = dto.accountId;
		String accessToken = dto.accessToken;

		boolean isAccessTokenValid = true;

		ZohoBooks service = new ZohoBooks();
		service.initialize(accessToken, null);
		OrganizationsApi organizationsApi = service.getOrganizationsApi();
		try {
			OrganizationList orgList = organizationsApi.getOrganizations();
			if(orgList != null && orgList.size() > 0) {
				Organization org = orgList.get(0);
			}
		} catch (Exception e) {
			if(e instanceof BooksException) {
				if(((BooksException) e).getCode() == 57) {
					isAccessTokenValid = false;
				}
			}
		}

		if (!isAccessTokenValid) {
			updatedDTO = refreshTokens(dto);
			//Try with the new token...
			try {
				ZohoBooks newZohoService = new ZohoBooks();
				newZohoService.initialize(updatedDTO.accessToken, null);
				OrganizationsApi newOrgApi = newZohoService.getOrganizationsApi();
				OrganizationList orgList = newOrgApi.getOrganizations();
				if(orgList != null && orgList.size() > 0) {
					Organization org = orgList.get(0);
				}
			} catch (Exception e) {
				if(e instanceof BooksException) {
					if(((BooksException) e).getCode() == 57) {
							throw new TokenRevokedException();
					}
				}
			}
		}
		return updatedDTO;
	}

	public boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto) {
		return isAccessTokenExpiringDefault(dto);
	}

	public OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
		try {
			GoogleRefreshTokenRequest refreshTokenRequest = new GoogleRefreshTokenRequest(
					new NetHttpTransport(), GsonFactory.getDefaultInstance(),
					dto.refreshToken, clientId, clientSecret);
			refreshTokenRequest.setGrantType("refresh_token");
			refreshTokenRequest.setTokenServerUrl(new GenericUrl(tokenServerURL));
			Map<String, Object> customParams =  new HashMap<>();
			customParams.put("redirect_uri", authServerURL + redirectPath);
			refreshTokenRequest.setUnknownKeys(customParams);

			GoogleTokenResponse tokenResponse = refreshTokenRequest.execute();
            if(tokenResponse.get("error") == null) {
                dto.accessToken = tokenResponse.getAccessToken();
                dto.accessTokenExpiresIn = tokenResponse.getExpiresInSeconds();
                dto.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.accessTokenExpiresIn);
                dto.refreshToken = tokenResponse.getRefreshToken() != null ? tokenResponse.getRefreshToken() : dto.refreshToken;
                dto.refreshTokenExpiresIn = ZohoConnectionController.DEFAULT_REFRESH_EXPIRY;
                dto.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.refreshTokenExpiresIn);
                dto.updatedDate = new Date();
                return dto;
            } else {
                throw new TokenRevokedException();
            }
        } catch (IOException e) {
			logger.error(e.getMessage());
			throw new TokenRevokedException();
		}
	}

	public String getAccountName(OAuthIntegrationDTO dto) {

		ZohoBooks service = new ZohoBooks();
		service.initialize(dto.accessToken, null);
		OrganizationsApi organizationsApi = service.getOrganizationsApi();
		try {
			OrganizationList orgList = organizationsApi.getOrganizations();
			if(orgList != null && orgList.size() > 0) {
				for(Organization org : orgList) {
					if(org.getOrganizationId().equalsIgnoreCase(dto.accountId)) {
						String bizName = org.getName();
						if(StringUtil.isEmpty(bizName)) {
							bizName = "No Business Name";
						}
						return bizName;
					}
				}
			}
		} catch (Exception e) {
			//TODO: Figure out the right exception code for the token revoked or token expired scenario
//				if (e.statusCode == 401 ) {
//					throw new TokenRevokedException();
//				}
		}
		return null;
	}

	public List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto) {
		List<PartnerAccountDTO> partnerAccountDTOS = new ArrayList<>();
		ZohoBooks service = new ZohoBooks();
		service.initialize(dto.accessToken, null);
		OrganizationsApi organizationsApi = service.getOrganizationsApi();
		try {
			OrganizationList orgList = organizationsApi.getOrganizations();
			if(orgList != null && orgList.size() > 0) {
				for(Organization org : orgList) {
					String bizName = org.getName();
					if(StringUtil.isEmpty(bizName)) {
						bizName = "No Business Name";
					}
					partnerAccountDTOS.add(new PartnerAccountDTO(org.getOrganizationId(), bizName));

				}
			}
		} catch (Exception e) {
			logger.error("Error getting parner account from Zoho", e);
		}
		return partnerAccountDTOS;
	}

}
