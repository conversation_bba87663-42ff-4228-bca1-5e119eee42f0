package com.aphe.auth.sso.zoho;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.sso.OAuthConnectionController;
import com.aphe.auth.sso.OAuthConnectionException;
import com.aphe.common.util.ArrayUtil;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.DataStoreFactory;
import com.google.api.client.util.store.MemoryDataStoreFactory;
import com.zoho.books.api.OrganizationsApi;
import com.zoho.books.exception.BooksException;
import com.zoho.books.model.Organization;
import com.zoho.books.model.OrganizationList;
import com.zoho.books.service.ZohoBooks;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Controller
public class ZohoConnectionController extends OAuthConnectionController {

    final static Logger logger = LogManager.getLogger(ZohoConnectionController.class);

    @Value("${aphe.zoho.clientId}")
    private String clientId;

    @Value("${aphe.zoho.clientSecret}")
    private String clientSecret;

    @Value("${aphe.zoho.redirectPath}")
    private String redirectPath;

    @Value("${aphe.zoho.tokenServerURL}")
    private String tokenServerURL;

    @Value("${aphe.zoho.authServerURL}")
    private String authServerURL;

    @Value("${aphe.zoho.baseURL}")
    private String baseURL;


    private static ArrayList<String> accountingScopes = new ArrayList<String>();

    static {
//        String s = "contacts,settings,estimates,invoices,customerpayments,creditnotes,projects,expenses,salesorder,purchaseorder,bills,debitnotes,vendorpayments,banking,accountants";
        String s = "contacts,settings,estimates,invoices,customerpayments,creditnotes,projects,expenses,bills,debitnotes,vendorpayments,banking,accountants";
        List<String> entities = ArrayUtil.stringToStringList(s);
        for (String entity : entities) {
            accountingScopes.add("ZohoBooks." + entity + ".READ");
            accountingScopes.add("ZohoBooks." + entity + ".Create");
            accountingScopes.add("ZohoBooks." + entity + ".UPDATE");
        }
    }

    public ZohoConnectionController() {
        super();
    }

    @Override
    public SSOPartner getSSOPartner() {
        return null;
    }

    @Override
    public OAuthIntegrationPartner getOAuthPartner() {
        return OAuthIntegrationPartner.ZohoBooks;
    }

    @RequestMapping("/access/connectToZoho")
    protected void connectToZohoBooks(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String authorizationURL = null;

        OAuthConnectionException oAuthConnectionException = null;

        try {
            //Validations
            checkUserLoggedIn();
            checkDomainSelected();

            //Build oAuth Redirect URL
            String redirectURL = new StringBuffer(getRedirectURL(request, redirectPath)).toString();
            String stateString = buildStateString(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(), GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    accountingScopes)
                    .setDataStoreFactory(DATA_STORE_FACTORY)
                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .build();

            authorizationURL = codeFlow.newAuthorizationUrl()
                    .setClientId(clientId)
                    .setState(stateString)
                    .setRedirectUri(redirectURL).build();
            authorizationURL = authorizationURL + "&prompt=consent";


        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            processConnectionRequest(request, response, oAuthConnectionException, authorizationURL);
        }
    }

    @RequestMapping("/access/zohoCallback")
    protected void zohoCallback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        OAuthConnectionException oAuthConnectionException = null;

        try {
            checkUserLoggedIn();
            checkDomainSelected();
            validateCSRFToken(request);
            validateOAuthErrorCode(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(),
                    GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    accountingScopes)
                    .setDataStoreFactory(DATA_STORE_FACTORY)
                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .setTokenServerUrl(new GenericUrl(tokenServerURL))
                    .build();

            String authCode = request.getParameter("code");
            String redirectURL = getRedirectURL(request, redirectPath);

            GoogleTokenResponse tokenResponse = codeFlow.newTokenRequest(authCode).setRedirectUri(redirectURL).execute();
            String accessToken = (String) tokenResponse.getAccessToken();


            ZohoBooks service = new ZohoBooks();
            service.initialize(accessToken, null);
            OrganizationsApi organizationsApi = service.getOrganizationsApi();
            OrganizationList orgList = organizationsApi.getOrganizations();
            List<Organization> orgs = new ArrayList<>(orgList);

            handleOrgsSize(orgs);

            String accountId = null;
            String domainName = null;

            if (orgList.size() == 1) {
                Organization org = orgList.get(0);
                accountId = org.getOrganizationId();
                domainName = org.getName();
            }

            String loggedInDomainId = getCurrentDoaminId();
            saveOAuthConnectionFromTokenResponse(Long.parseLong(loggedInDomainId), tokenResponse, accountId, domainName);
        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else if (e instanceof BooksException) {
                oAuthConnectionException = buildOAuthException((BooksException) e);
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            processCallbackResponse(request, response, oAuthConnectionException);
        }
    }

    @NotNull
    private OAuthConnectionException buildOAuthException(BooksException e) {
        boolean isAuthError = false;
        String errorMessage = isAuthError ? "Authorization error when connecting to " + getOAuthPartner().getCompany() + "." : "System error when connecting to " + getOAuthPartner().getCompany() + ".";
        OAuthConnectionException.ERROR_TYPE errorType = OAuthConnectionException.ERROR_TYPE.SYSTEM_ERROR;
        return new OAuthConnectionException(errorType, errorMessage);
    }

    private void saveOAuthConnectionFromTokenResponse(long domainId, GoogleTokenResponse tokenResponse, String realmId, String accountName) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        String refreshToken = tokenResponse.getRefreshToken();
        Long accessTokenExpiresInSeconds = tokenResponse.getExpiresInSeconds();
        Long refreshTokenExpiresInSeconds = DEFAULT_REFRESH_EXPIRY; //180 days
        saveOAuthConnection(domainId, accessToken, accessTokenExpiresInSeconds, refreshToken, refreshTokenExpiresInSeconds, realmId, null, accountName);
    }

}