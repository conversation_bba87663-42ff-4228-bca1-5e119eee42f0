package com.aphe.auth.sso.bqecore;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.sso.OAuthConnectionController;
import com.aphe.auth.sso.OAuthConnectionException;
import com.aphe.bqecoresdk.exceptions.BQECoreAuthorizationException;
import com.aphe.bqecoresdk.exceptions.BQECoreException;
import com.aphe.bqecoresdk.exceptions.BQECoreUserException;
import com.aphe.bqecoresdk.model.Company;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.DataStoreFactory;
import com.google.api.client.util.store.MemoryDataStoreFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Controller
public class BQECOREConnectionController extends OAuthConnectionController {

    final static Logger logger = LogManager.getLogger(BQECOREConnectionController.class);

    public static long DEFAULT_REFRESH_EXPIRY = 59 * 24 * 60 * 60L;

    @Value("${aphe.bqecore.clientId}")
    private String clientId;

    @Value("${aphe.bqecore.clientSecret}")
    private String clientSecret;

    @Value("${aphe.bqecore.redirectPath}")
    private String redirectPath;

    @Value("${aphe.bqecore.tokenServerURL}")
    private String tokenServerURL;

    @Value("${aphe.bqecore.authServerURL}")
    private String authServerURL;

    @Autowired
    BQECOREUtil bqecoreUtil;

    private static ArrayList<String> accountingScopes = new ArrayList<String>();

    static {
        accountingScopes.add("offline_access");
        accountingScopes.add("readwrite:core");
    }


    public BQECOREConnectionController() {
        super();
    }

    @Override
    public SSOPartner getSSOPartner() {
        return null;
    }

    @Override
    public OAuthIntegrationPartner getOAuthPartner() {
        return OAuthIntegrationPartner.BQECORE;
    }

    @RequestMapping("/access/connectToBQECORE")
    protected void connectTOBQECORE(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String authorizationURL = null;

        OAuthConnectionException oAuthConnectionException = null;

        try {
            //Validations
            checkUserLoggedIn();
            checkDomainSelected();

            //Build oAuth Redirect URL
            String redirectURL = new StringBuffer(getRedirectURL(request, redirectPath)).toString();
            String stateString = buildStateString(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(), GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    accountingScopes)
                    .setDataStoreFactory(DATA_STORE_FACTORY)
                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .build();

            authorizationURL = codeFlow.newAuthorizationUrl()
                    .setClientId(clientId)
                    .setState(stateString)
                    .setRedirectUri(redirectURL).build();

        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            processConnectionRequest(request, response, oAuthConnectionException, authorizationURL);
        }
    }

    @RequestMapping("/access/bqecoreCallback")
    protected void bqecoreCallback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        OAuthConnectionException oAuthConnectionException = null;

        try {
            checkUserLoggedIn();
            checkDomainSelected();
            validateCSRFToken(request);
            validateOAuthErrorCode(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(),
                    GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    accountingScopes)
                    .setDataStoreFactory(DATA_STORE_FACTORY)
                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .setTokenServerUrl(new GenericUrl(tokenServerURL))
                    .build();

            String authCode = request.getParameter("code");
            String redirectURL = getRedirectURL(request, redirectPath);

            GoogleTokenResponse tokenResponse = codeFlow.newTokenRequest(authCode).setRedirectUri(redirectURL).execute();
            String accessToken = (String) tokenResponse.getAccessToken();
            String baseURL = (String) tokenResponse.get("endpoint");


            List<Company> businessList = bqecoreUtil.getBusinesses(baseURL, accessToken);

            handleOrgsSize(businessList);

            String accountId = null;
            String domainName = null;
            if (businessList != null && businessList.size() == 1) {
                Company org = businessList.get(0);
                accountId = org.id;
                domainName = org.name;
            }

            Long loggedInDomainId = getCurrentDoaminId();
            saveOAuthConnectionFromTokenResponse(loggedInDomainId, tokenResponse, accountId, domainName, baseURL);

        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else if (e instanceof BQECoreAuthorizationException) {
                oAuthConnectionException = buildOAuthException((BQECoreException) e);
            } else if (e instanceof BQECoreUserException) {
                oAuthConnectionException = buildOAuthException((BQECoreUserException) e);
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            processCallbackResponse(request, response, oAuthConnectionException);
        }

    }

    @NotNull
    private OAuthConnectionException buildOAuthException(BQECoreException e) {
        boolean isAuthError = e instanceof BQECoreAuthorizationException;
        boolean isSystemError = e instanceof BQECoreUserException;
        String errorMessage = isAuthError ? "Authorization error when connecting to " + getOAuthPartner().getCompany() + "." : "System error when connecting to " + getOAuthPartner().getCompany() + ".";
        OAuthConnectionException.ERROR_TYPE errorType = OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR;
        if (isAuthError || isSystemError) {
            errorType = isAuthError ? OAuthConnectionException.ERROR_TYPE.USER_ERROR : OAuthConnectionException.ERROR_TYPE.SYSTEM_ERROR;
        }
        return new OAuthConnectionException(errorType, errorMessage);
    }


    private void saveOAuthConnectionFromTokenResponse(long domainId, GoogleTokenResponse tokenResponse, String realmId, String accountName, String baseURL) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        String refreshToken = tokenResponse.getRefreshToken();
        Long accessTokenExpiresInSeconds = tokenResponse.getExpiresInSeconds();
        Long refreshTokenExpiresInSeconds;
        try {
            refreshTokenExpiresInSeconds = tokenResponse.get("refresh_token_expires_in") != null ? Long.parseLong(tokenResponse.get("refresh_token_expires_in").toString()) : DEFAULT_REFRESH_EXPIRY;
        }catch (Exception e){
            logger.error("Error while parsing refresh_token_expires_in", e);
            refreshTokenExpiresInSeconds = DEFAULT_REFRESH_EXPIRY;
        }
        //Using xeroConnectionId for baseURL for BQECore integrations.
        saveOAuthConnection(domainId, accessToken, accessTokenExpiresInSeconds, refreshToken, refreshTokenExpiresInSeconds, realmId, baseURL, accountName);
    }

}