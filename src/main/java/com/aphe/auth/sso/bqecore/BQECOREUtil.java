package com.aphe.auth.sso.bqecore;

import com.aphe.bqecoresdk.api.CompanyManager;
import com.aphe.bqecoresdk.model.Company;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class BQECOREUtil {

    public List<Company> getBusinesses(String baseURL, String accessToken) throws Exception {
        List<Company> allBusinesses = new ArrayList<>();
        CompanyManager companyManager = new CompanyManager(baseURL, accessToken);
        Company companyModel = companyManager.getOne("");
        allBusinesses.add(companyModel);
        return allBusinesses;
    }


}
