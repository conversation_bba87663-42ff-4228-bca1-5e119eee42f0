package com.aphe.auth.sso.bqecore;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.auth.sso.OAuthConnectionUtil;
import com.aphe.auth.sso.TokenRevokedException;
import com.aphe.bqecoresdk.exceptions.BQECoreAuthorizationException;
import com.aphe.bqecoresdk.model.Company;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.StringUtil;
import com.google.api.client.googleapis.auth.oauth2.GoogleRefreshTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

@Component
public class BQECOREConnectUtil extends OAuthConnectionUtil {

    @Value("${aphe.bqecore.clientId}")
    private String clientId;

    @Value("${aphe.bqecore.clientSecret}")
    private String clientSecret;

    @Value("${aphe.bqecore.revokeTokenURL}")
    private String revokeTokenURL;

    @Value("${aphe.bqecore.tokenServerURL}")
    private String tokenServerURL;

    @Value("${aphe.bqecore.authServerURL}")
    private String authServerURL;

    @Value("${aphe.bqecore.redirectPath}")
    private String redirectPath;

    @Autowired
    private BQECOREUtil bqecoreUtil;

    final static Logger logger = LogManager.getLogger(BQECOREConnectUtil.class);

    public boolean revokeToken(OAuthIntegrationDTO dto) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            String resourceUrl = revokeTokenURL;
            MultiValueMap<String, String> formValues = new LinkedMultiValueMap();
            formValues.add("client_id", clientId);
            formValues.add("client_secret", clientSecret);
            formValues.add("token", dto.refreshToken);
            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formValues, headers);
            ResponseEntity<String> response = restTemplate.exchange(resourceUrl, HttpMethod.POST, entity, String.class);
            if (response.getStatusCode() == HttpStatus.OK) {
                return true;
            } else {
                logger.error("Error revoking token", response);
                return false;
            }
        } catch (Exception e1) {
            logger.error("Error revoking token", e1);
            return false;
        }
    }

    @Override
    public OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
        OAuthIntegrationDTO updatedDTO = null;
        boolean isAccessTokenValid = true;
        try {
            List<Company> orgList = bqecoreUtil.getBusinesses(dto.xeroConnectionId, dto.accessToken);
            if (orgList != null && orgList.size() > 0) {
                Company org = orgList.get(0);
            }
        } catch (Exception e) {
            if (e instanceof BQECoreAuthorizationException) {
                isAccessTokenValid = false;
            }
        }

        if (!isAccessTokenValid) {
            updatedDTO = refreshTokens(dto);
            try {
                List<Company> orgList = bqecoreUtil.getBusinesses(dto.xeroConnectionId, dto.accessToken);
                if (orgList != null && orgList.size() > 0) {
                    Company org = orgList.get(0);
                }
            } catch (Exception e) {
                if (e instanceof BQECoreAuthorizationException) {
                    throw new TokenRevokedException();
                }
            }
        }
        return updatedDTO;
    }

    public boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto) {
        return isAccessTokenExpiringDefault(dto);
    }

    public OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
        try {
            GoogleRefreshTokenRequest refreshTokenRequest = new GoogleRefreshTokenRequest(
                    new NetHttpTransport(), GsonFactory.getDefaultInstance(),
                    dto.refreshToken, clientId, clientSecret);
            refreshTokenRequest.setGrantType("refresh_token");
            refreshTokenRequest.setTokenServerUrl(new GenericUrl(tokenServerURL));
            Map<String, Object> customParams = new HashMap<>();
            customParams.put("redirect_uri", authServerURL + redirectPath);
            refreshTokenRequest.setUnknownKeys(customParams);

            GoogleTokenResponse tokenResponse = refreshTokenRequest.execute();
            dto.accessToken = tokenResponse.getAccessToken();
            dto.accessTokenExpiresIn = tokenResponse.getExpiresInSeconds();
            dto.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.accessTokenExpiresIn);
            dto.refreshToken = tokenResponse.getRefreshToken();
            dto.refreshTokenExpiresIn = BQECOREConnectionController.DEFAULT_REFRESH_EXPIRY;
            dto.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.refreshTokenExpiresIn);
            dto.updatedDate = new Date();
            return dto;
        } catch (IOException e) {
            logger.error("Error refreshing token", e);
            throw new TokenRevokedException();
        }
    }

    public String getAccountName(OAuthIntegrationDTO dto) {
        try {
            List<Company> companies = bqecoreUtil.getBusinesses(dto.xeroConnectionId, dto.accessToken);
            for(Company c : companies) {
                if(c.id.equalsIgnoreCase(dto.accountId)) {
                    String bizName = c.name;
                    if (StringUtil.isEmpty(bizName)) {
                        bizName = "No Business Name";
                    }
                    return bizName;
                }
            }
        } catch (Exception e) {
            logger.error("Error getting account name", e);
        }
        return null;
    }

    public List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto) {
        List<PartnerAccountDTO> partnerAccountDTOS = new ArrayList<>();
        try {
            List<Company> companies = bqecoreUtil.getBusinesses(dto.xeroConnectionId, dto.accessToken);
            for(Company c : companies) {
                String bizName = c.name;
                if (StringUtil.isEmpty(bizName)) {
                    bizName = "No Business Name";
                }
                partnerAccountDTOS.add(new PartnerAccountDTO(c.id, bizName));
            }
        } catch (Exception e) {
            logger.error("Error getting partner accounts", e);
        }
        return partnerAccountDTOS;
    }

}
