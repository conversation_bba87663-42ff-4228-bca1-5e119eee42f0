package com.aphe.auth.sso.bdc;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.sso.ConnectionStatusDTO;
import com.aphe.auth.sso.OAuthConnectionController;
import com.aphe.auth.sso.OAuthConnectionException;
import com.aphe.bdcsdk.api.OrganizationRefManager;
import com.aphe.bdcsdk.api.SessionManager;
import com.aphe.bdcsdk.exceptions.BDCAuthorizationException;
import com.aphe.bdcsdk.exceptions.BDCException;
import com.aphe.bdcsdk.model.BDCError;
import com.aphe.bdcsdk.model.OrganizationRef;
import com.aphe.bdcsdk.model.Session;
import com.aphe.common.util.PartnerUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.web.util.HtmlUtils.htmlUnescape;

@RestController
public class BDCConnectionController extends OAuthConnectionController {

    final static Logger logger = LogManager.getLogger(BDCConnectionController.class);

    public static final String TOKEN_DELIMITER = "###1099SF###";
    public static final long ACCESS_TOKEN_EXPIRES_IN_SECONDS = 30 * 60L;

    @Value("${aphe.bdc.devKey}")
    private String bdcDevKey;

    @Value("${aphe.bdc.baseURL}")
    private String baseURL;

    @Autowired
    PartnerUtil partnerUtil;


    public BDCConnectionController() {
        super();
    }

    @Override
    public SSOPartner getSSOPartner() {
        return null;
    }

    @Override
    public OAuthIntegrationPartner getOAuthPartner() {
        return OAuthIntegrationPartner.Bill;
    }


    /**
     * Types of errors...
     * 1. Known error
     *  - Correctable (user_error)
     *   - Hey you did so and so. You should do so and so to fix this error.
     *  - Non-correctable (system_error)
     *   - Hey you did so and so, but something is down. Here is little bit additional context. Just try again later or contact us.
     * 2. Unknown error (unexpected_error)
     *  -- Hey something unexpected happened. Please try again later. (By the way, we logged something on our side with this unique id)
     *  -- Try again after some time or contact us.
     */

    /**
     * There is no oAuth with Bill.com. UI will ask for user's BDC username and password, which it posted to this end point so that we can store
     * it in the backend.
     * We will use this username, password along with our devKey to get the sessionId to be used as accessToken.
     * Username and password will serve as refreshToken.
     */
    @PutMapping(path = "/access/bdcCallback")
    protected ConnectionStatusDTO bdcCallBack(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        OAuthConnectionException oAuthConnectionException = null;
        //Get username, password
        String bdcUsername = request.getParameter("bdcUsername");
        String bdcPassword = request.getParameter("bdcPassword");

        try {
            checkUserLoggedIn();
            checkDomainSelected();

            OrganizationRefManager organizationRefManager = new OrganizationRefManager(baseURL, bdcDevKey, bdcUsername, bdcPassword);
            List<OrganizationRef> organizationRefs = organizationRefManager.getList(new ArrayList<>());

            handleOrgsSize(organizationRefs);

            String accessToken = "InvalidSessionId";
            String accountId = null;
            String accountName = null;

            if (organizationRefs.size() == 1) {
                accountId = organizationRefs.get(0).orgId;
                accountName = organizationRefs.get(0).orgName;
                SessionManager sessionManager = new SessionManager(baseURL, bdcDevKey, bdcUsername, bdcPassword, accountId);
                Session session = sessionManager.getOne(null);
                accessToken = session.sessionId;
            }

            Long loggedInDomainId = getCurrentDoaminId();
            saveOAuthConnectionFromTokenResponse(loggedInDomainId, bdcUsername, bdcPassword, accessToken, accountId, accountName);

        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else if (e instanceof BDCAuthorizationException) {
                oAuthConnectionException = buildOAuthException((BDCException) e);
            } else if (e instanceof BDCException) {
                oAuthConnectionException = buildOAuthException((BDCException) e);
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            if(oAuthConnectionException == null) {
                return new ConnectionStatusDTO("Success", null, null);
            } else {
                String errorMessage = buildCallbackError(oAuthConnectionException);
                return new ConnectionStatusDTO("Fail", oAuthConnectionException.getErrorType().toString(),  errorMessage);
            }
        }
    }

    @NotNull
    private OAuthConnectionException buildOAuthException(BDCException e) {
        boolean isAuthError = e instanceof BDCAuthorizationException;
        BDCError error = e.getError();
        String errorMessage = isAuthError ? "Authorization error when connecting to " + getOAuthPartner().getCompany() + "." : "System error when connecting to " + getOAuthPartner().getCompany() + ".";
        OAuthConnectionException.ERROR_TYPE errorType = OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR;
        if (error != null) {
            errorType = isAuthError ? OAuthConnectionException.ERROR_TYPE.USER_ERROR : OAuthConnectionException.ERROR_TYPE.SYSTEM_ERROR;
            String encoded = error.error_message;
            String result = htmlUnescape(encoded);
            errorMessage = result.replaceAll("\"", "'");
        }
        return new OAuthConnectionException(errorType, errorMessage);
    }

    private void saveOAuthConnectionFromTokenResponse(long domainId, String bdcUserName, String bdcPassword, String sessionId, String realmId, String accountName) throws Exception {
        String refreshToken = bdcUserName + TOKEN_DELIMITER + bdcPassword;
        long refreshTokenExpiresInSeconds = DEFAULT_REFRESH_EXPIRY;
        saveOAuthConnection(domainId, sessionId, ACCESS_TOKEN_EXPIRES_IN_SECONDS, refreshToken, refreshTokenExpiresInSeconds, realmId, null, accountName);
    }
}