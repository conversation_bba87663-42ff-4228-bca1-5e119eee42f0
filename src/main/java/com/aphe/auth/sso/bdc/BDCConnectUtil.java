package com.aphe.auth.sso.bdc;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.auth.sso.OAuthConnectionUtil;
import com.aphe.auth.sso.TokenRevokedException;
import com.aphe.bdcsdk.api.CurrentTimeManager;
import com.aphe.bdcsdk.api.OrganizationRefManager;
import com.aphe.bdcsdk.api.SessionManager;
import com.aphe.bdcsdk.exceptions.BDCAuthorizationException;
import com.aphe.bdcsdk.exceptions.BDCException;
import com.aphe.bdcsdk.model.CurrentTime;
import com.aphe.bdcsdk.model.OrganizationRef;
import com.aphe.bdcsdk.model.Session;
import com.aphe.common.util.StringUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.aphe.auth.sso.bdc.BDCConnectionController.ACCESS_TOKEN_EXPIRES_IN_SECONDS;
import static com.aphe.auth.sso.bdc.BDCConnectionController.TOKEN_DELIMITER;

@Component
public class BDCConnectUtil extends OAuthConnectionUtil {

    @Value("${aphe.bdc.devKey}")
    private String bdcDevKey;

    @Value("${aphe.bdc.baseURL}")
    private String baseURL;

    private final static Logger logger = LogManager.getLogger(BDCConnectUtil.class);

    public boolean revokeToken(OAuthIntegrationDTO dto) {
        return true;
    }

    @Override
    public OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
        OAuthIntegrationDTO updatedDTO = null;
        boolean isAccessTokenValid = true;
        try {
            isAccessTokenValid = isAccessTokenValid(dto);
        } catch (Exception e) {
            if (e instanceof TokenRevokedException) {
                isAccessTokenValid = false;
            }
        }
        if (!isAccessTokenValid) {
            updatedDTO = refreshTokens(dto);
            isAccessTokenValid = isAccessTokenValid(dto);
        }
        return updatedDTO;
    }

    private boolean isAccessTokenValid(OAuthIntegrationDTO dto) throws TokenRevokedException {
        boolean isAccessTokenValid = false;
        CurrentTimeManager currentTimeManager = new CurrentTimeManager(baseURL, bdcDevKey, dto.accessToken);
        try {
            CurrentTime currentTime = currentTimeManager.getOne("aaa");
            isAccessTokenValid = true;
        } catch (BDCException e) {
            if (e instanceof BDCAuthorizationException) {
                throw new TokenRevokedException();
            }
        }
        return isAccessTokenValid;
    }

    public boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto) {
        return isAccessTokenExpiringDefault(dto);
    }

    public OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
        String refreshToken = dto.refreshToken;
        String[] splitRefreshToken = refreshToken.split(TOKEN_DELIMITER, 2);
        try {
            SessionManager sessionManager = new SessionManager(baseURL, bdcDevKey, splitRefreshToken[0], splitRefreshToken[1], dto.accountId);
            Session session = sessionManager.getOne(dto.accountId);
            if (session != null) {
                dto.accessToken = session.sessionId;
                dto.accessTokenExpiresIn = ACCESS_TOKEN_EXPIRES_IN_SECONDS;
                dto.refreshTokenExpiresIn = BDCConnectionController.DEFAULT_REFRESH_EXPIRY;
                dto.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.accessTokenExpiresIn);
                dto.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.refreshTokenExpiresIn);
                dto.updatedDate = new Date();
            }
        } catch (BDCException e) {
            logger.error("Error refreshing access token", e);
            if (e instanceof BDCAuthorizationException) {
                throw new TokenRevokedException();
            }
        }
        return dto;
    }

    public String getAccountName(OAuthIntegrationDTO dto) {
        return null;
    }

    public List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto) {
        List<PartnerAccountDTO> partnerAccountDTOS = new ArrayList<>();

        String refreshToken = dto.refreshToken;
        String[] splitRefreshToken = refreshToken.split(TOKEN_DELIMITER, 2);
        String userName = splitRefreshToken[0];
        String password = splitRefreshToken[1];

        OrganizationRefManager organizationManager = new OrganizationRefManager(baseURL, bdcDevKey, userName, password);
        try {
            List<OrganizationRef> orgList = organizationManager.getList(new ArrayList<>());
            if (orgList != null && orgList.size() > 0) {
                for (OrganizationRef org : orgList) {
                    String bizName = org.orgName;
                    if (StringUtil.isEmpty(bizName)) {
                        bizName = "No Business Name";
                    }
                    partnerAccountDTOS.add(new PartnerAccountDTO(org.orgId, bizName));
                }
            }
        } catch (BDCException e) {
            logger.error("Error getting org list", e);
        }
        return partnerAccountDTOS;
    }

}
