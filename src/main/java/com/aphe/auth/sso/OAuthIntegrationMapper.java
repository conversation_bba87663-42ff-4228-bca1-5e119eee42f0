package com.aphe.auth.sso;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthTokenDTO;
import org.mapstruct.Mapper;

import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface OAuthIntegrationMapper {

	OAuthIntegrationClientDTO toClientDTO(OAuthIntegration acctInt);

	OAuthIntegrationDTO toDTO(OAuthIntegration acctInt);

	@Mapping(target = "connectedDate", ignore = true)
	@Mapping(target = "updatedDate", ignore = true)
	@Mapping(target = "refreshTokenExpiryDate", ignore = true)
	@Mapping(target = "accessTokenExpiryDate", ignore = true)
	OAuthTokenDTO toTokenDTO(OAuthIntegration acctInt);

	@Mapping(target = "connectedDate", ignore = true)
	@Mapping(target = "updatedDate", ignore = true)
	@Mapping(target = "refreshTokenExpiryDate", ignore = true)
	@Mapping(target = "accessTokenExpiryDate", ignore = true)
	OAuthIntegrationDTO toIntegrationDTOFromTokenDTO(OAuthTokenDTO tokenDTO);

	OAuthIntegration toEntity(OAuthIntegrationDTO dto);

}
