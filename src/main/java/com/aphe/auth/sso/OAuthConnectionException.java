package com.aphe.auth.sso;

public class OAuthConnectionException extends Exception {
    public static enum ERROR_TYPE {
        USER_ERROR,
        SYSTEM_ERROR,
        UNEXPECTED_ERROR
    }

    private ERROR_TYPE errorType;
    private String errorMessage;

    public OAuthConnectionException(ERROR_TYPE errorType, String errorMessage) {
        this.errorType = errorType;
        this.errorMessage = errorMessage;
    }

    public ERROR_TYPE getErrorType() {
        return errorType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
