package com.aphe.auth.sso.qbo;

import com.aphe.common.util.PropertiesManager;
import com.intuit.oauth2.client.OAuth2PlatformClient;
import com.intuit.oauth2.config.Environment;
import com.intuit.oauth2.config.OAuth2Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
public class QBOOAuth2PlatformClientFactory {

	private boolean initialzied = false;

	@Value("${aphe.intuit.clientId}")
	private String clientId;

	@Value("${aphe.intuit.clientSecret}")
	private String clientSecret;

	private OAuth2PlatformClient client;
	private OAuth2Config oauth2Config;

	@PostConstruct
	public void init() {
		boolean isProd = PropertiesManager.isProd();
		if (isProd) {
			initialize();
		}
//		else {
//			oauth2Config = new OAuth2Config.OAuth2ConfigBuilder(clientId, clientSecret).callDiscoveryAPI(Environment.SANDBOX).buildConfig();
//		}


	}

	private void initialize() {
		oauth2Config = new OAuth2Config.OAuth2ConfigBuilder(clientId, clientSecret).callDiscoveryAPI(Environment.PRODUCTION).buildConfig();
		client = new OAuth2PlatformClient(oauth2Config);
		initialzied = true;
	}

	public OAuth2PlatformClient getOAuth2PlatformClient() {
		if(!initialzied) {
			initialize();
		}
		return client;
	}

	public OAuth2Config getOAuth2Config() {
		if(!initialzied) {
			initialize();
		}
		return oauth2Config;
	}

}
