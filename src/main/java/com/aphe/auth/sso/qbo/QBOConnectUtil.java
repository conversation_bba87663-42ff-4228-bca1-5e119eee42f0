package com.aphe.auth.sso.qbo;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.auth.sso.OAuthConnectionUtil;
import com.aphe.auth.sso.TokenRevokedException;
import com.intuit.ipp.data.CompanyInfo;
import com.intuit.ipp.data.Error;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.exception.InvalidTokenException;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import com.intuit.oauth2.client.OAuth2PlatformClient;
import com.intuit.oauth2.config.OAuth2Config;
import com.intuit.oauth2.data.BearerTokenResponse;
import com.intuit.oauth2.data.PlatformResponse;
import com.intuit.oauth2.exception.ConnectionException;
import com.intuit.oauth2.exception.OAuthException;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Transactional
public class QBOConnectUtil extends OAuthConnectionUtil {

	final static Logger logger = LogManager.getLogger(QBOConnectUtil.class);

	@Autowired
	QBOOAuth2PlatformClientFactory factory;

	@Autowired
	public QBOServiceHelper helper;

	public boolean revokeToken(OAuthIntegrationDTO dto) {
		OAuth2Config oauth2Config = factory.getOAuth2Config();
		OAuth2PlatformClient client = new OAuth2PlatformClient(oauth2Config);
		try {
			PlatformResponse response = client.revokeToken(dto.refreshToken);
			if (response.getStatus().equalsIgnoreCase("SUCCESS")) {
				return true;
			} else {
				System.err.println("Error revoking token : " + response.getErrorCode() + "  " + response.getErrorMessage());
			}
		} catch (ConnectionException e) {
			logger.error("ConnectionException while revoking token", e);
		}
		return false;
	}

	/**
	 * Returns true or false based on access token response.
	 */
	public OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
		OAuthIntegrationDTO updatedDTO = null;
		String realmId = dto.accountId;
		String accessToken = dto.accessToken;

		boolean isAccessTokenValid = true;

		try {
			DataService service;
			service = helper.getDataService(realmId, accessToken);
			String sql = "select * from companyinfo";
			QueryResult queryResult = service.executeQuery(sql);
			if (!queryResult.getEntities().isEmpty() && queryResult.getEntities().size() > 0) {
				CompanyInfo companyInfo = (CompanyInfo) queryResult.getEntities().get(0);
				String companyName = companyInfo.getCompanyName();
			}
		} catch (FMSException e) {
			isAccessTokenValid = false;
			List<Error> errors = e.getErrorList();
			if (errors != null) {
				for (Error err : errors) {
					String message = err.getMessage();
					boolean is401 = message != null && message.contains("statusCode=401");
					boolean isRevoked = "Token revoked".equalsIgnoreCase(err.getDetail());
					logger.info("Is revoked check -----", err.getDetail() + "  --- " + err.getMessage());
					if (is401 && isRevoked) {
						logger.info("Token is not valid anymore because it is revoked. domainId=" + dto.domainId);
						throw new TokenRevokedException();
					}
				}
			}
		}

		if (!isAccessTokenValid) {
			updatedDTO = refreshTokens(dto);
		}
		return updatedDTO;
	}

	public boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto) {
		return isAccessTokenExpiringDefault(dto);
	}

	public String getAccountName(OAuthIntegrationDTO dto) {
		DataService service;
		try {
			service = helper.getDataService(dto.accountId, dto.accessToken);
			String sql = "select * from companyinfo";
			QueryResult queryResult = service.executeQuery(sql);
			if (!queryResult.getEntities().isEmpty() && queryResult.getEntities().size() > 0) {
				CompanyInfo companyInfo = (CompanyInfo) queryResult.getEntities().get(0);
				String companyName = companyInfo.getCompanyName();
				return companyName;
			}
		} catch (InvalidTokenException e) {
			logger.error("Invalid token", e);
		} catch (FMSException e) {
			logger.error("FMS exception", e);
		}
		return null;
	}

	public OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
		OAuth2PlatformClient client = factory.getOAuth2PlatformClient();
		try {
			BearerTokenResponse bearerTokenResponse = client.refreshToken(dto.refreshToken);
			dto.accessToken = bearerTokenResponse.getAccessToken();
			dto.accessTokenExpiresIn = bearerTokenResponse.getExpiresIn();
			dto.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.accessTokenExpiresIn);
			dto.refreshToken = bearerTokenResponse.getRefreshToken();
			dto.refreshTokenExpiresIn = bearerTokenResponse.getXRefreshTokenExpiresIn();
			dto.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.refreshTokenExpiresIn);
			dto.updatedDate = new Date();
			return dto;
		} catch (OAuthException e1) {
			logger.error("Error while calling bearer token :: " + e1.getMessage());
			throw new TokenRevokedException();
		}
	}

	public List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto) {
		return new ArrayList<>();
	}
}
