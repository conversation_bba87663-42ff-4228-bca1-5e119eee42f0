package com.aphe.auth.sso.qbo;

import com.intuit.ipp.core.Context;
import com.intuit.ipp.core.IEntity;
import com.intuit.ipp.core.ServiceType;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.exception.InvalidTokenException;
import com.intuit.ipp.security.OAuth2Authorizer;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import com.intuit.ipp.services.ReportService;
import com.intuit.ipp.util.Config;
import com.intuit.oauth2.client.OAuth2PlatformClient;
import com.intuit.oauth2.data.BearerTokenResponse;
import com.intuit.oauth2.exception.OAuthException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QBOServiceHelper {

	@Autowired
	QBOOAuth2PlatformClientFactory factory;

	@Value("${aphe.intuit.baseURL}")
	private String baseURL;

	public DataService getDataService(String realmId, String accessToken) throws FMSException {

		String url = baseURL + "/v3/company";

		Config.setProperty(Config.BASE_URL_QBO, url);
		OAuth2Authorizer oauth = new OAuth2Authorizer(accessToken);
		Context context = new Context(oauth, ServiceType.QBO, realmId);

		return new DataService(context);
	}

	public ReportService getReportService(String realmId, String accessToken) throws FMSException {

		String url = baseURL + "/v3/company";

		Config.setProperty(Config.BASE_URL_QBO, url);
		OAuth2Authorizer oauth = new OAuth2Authorizer(accessToken);
		Context context = new Context(oauth, ServiceType.QBO, realmId);

		return new ReportService(context);
	}

	public List<? extends IEntity> queryData(String realmId, String accessToken, String refreshToken, String sql) {

		if (StringUtils.isEmpty(realmId)) {
			// logger.error("Relam id is null ");
		}
		try {
			// get DataService
			DataService service = getDataService(realmId, accessToken);
			// get data
			QueryResult queryResult = service.executeQuery(sql);
			return queryResult.getEntities();
		}
		/*
		 * Handle 401 status code - If a 401 response is received, refresh tokens should be used to get a new access token, and the API call should be tried again.
		 */
		catch (InvalidTokenException e) {
			// logger.error("Error while calling executeQuery :: " + e.getMessage());

			// refresh tokens
			// logger.info("received 401 during companyinfo call, refreshing tokens now");
			OAuth2PlatformClient client = factory.getOAuth2PlatformClient();
			try {
				BearerTokenResponse bearerTokenResponse = client.refreshToken(refreshToken);
				accessToken = bearerTokenResponse.getAccessToken();
				refreshToken = bearerTokenResponse.getRefreshToken();

				// call company info again using new tokens
				// logger.info("calling companyinfo using new tokens");
				DataService service = getDataService(realmId, accessToken);

				// get data
				QueryResult queryResult = service.executeQuery(sql);
				return queryResult.getEntities();

			} catch (OAuthException e1) {
				// logger.error("Error while calling bearer token :: " + e.getMessage());

			} catch (FMSException e1) {
				// logger.error("Error while calling company currency :: " + e.getMessage());
			}
		} catch (FMSException e) {

		}
		return null;
	}
}
