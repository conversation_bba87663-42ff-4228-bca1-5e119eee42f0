package com.aphe.auth.sso.qbo;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.auth.service.dto.UserDTO;
import com.aphe.auth.sso.OAuthConnectionController;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.CookieStorage;
import com.aphe.common.util.PartnerUtil;
import com.aphe.common.util.StringUtil;
import com.intuit.ipp.data.CompanyInfo;
import com.intuit.ipp.data.EntitlementsResponse;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import com.intuit.oauth2.client.OAuth2PlatformClient;
import com.intuit.oauth2.config.OAuth2Config;
import com.intuit.oauth2.config.Scope;
import com.intuit.oauth2.data.BearerTokenResponse;
import com.intuit.oauth2.data.UserInfoResponse;
import com.intuit.oauth2.exception.InvalidRequestException;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
public class QBOConnectionController extends OAuthConnectionController {

    final static Logger logger = LogManager.getLogger(QBOConnectionController.class);

    @Autowired
    QBOOAuth2PlatformClientFactory factory;

    @Value("${aphe.intuit.redirectPath}")
    private String redirectPath;

    @Autowired
    PartnerUtil partnerUtil;

    @Autowired
    CookieStorage cookieStore;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    public QBOServiceHelper helper;

    public QBOConnectionController() {
        super();
    }


    @Override
    public SSOPartner getSSOPartner() {
        return SSOPartner.Intuit;
    }

    @Override
    public OAuthIntegrationPartner getOAuthPartner() {
        return OAuthIntegrationPartner.QBO;
    }

    @RequestMapping("/access/signUpWithIntuit")
    protected void signUpWithIntuit(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        signInOrSignUp(request, response, true);
    }

    @RequestMapping("/access/getAppWithQBO")
    protected void getAppIntuit(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        signInOrSignUp(request, response, true);
    }

    @RequestMapping("/access/signInWithIntuit")
    protected void signInWithIntuit(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        signInOrSignUp(request, response, false);
    }

    private void signInOrSignUp(HttpServletRequest request, HttpServletResponse response, boolean getApp) throws IOException {
        String successURL = request.getParameter("onSuccess");
        String failureURL = request.getParameter("onFailure");
        failureURL = failureURL == null ? successURL : failureURL;
        cookieStore.saveItem(response, "intuit_onSuccess", successURL);
        cookieStore.saveItem(response, "intuit_onFailure", failureURL);

        String redirectURL = new StringBuffer(new PartnerUtil().getRedirectURL(request, redirectPath)).toString();

        List<Scope> scopes = new ArrayList<Scope>();
        scopes.add(Scope.OpenIdAll);
        if (getApp) {
            scopes.add(Scope.Accounting);
        }

        OAuth2Config oauth2Config = factory.getOAuth2Config();
        String csrf = oauth2Config.generateCSRFToken();

        saveCsrfTokenToRedis(csrf);

        JSONObject stateObject = new JSONObject();
        stateObject.put("csrfToken", csrf);
        stateObject.put("ssoFlow", Boolean.TRUE.booleanValue());
        stateObject.put("oAuthFlow", getApp);
        String stateString = URLEncoder.encode(stateObject.toString(), "UTF-8");

        try {
            response.sendRedirect(oauth2Config.prepareUrl(scopes, redirectURL, stateString));
        } catch (InvalidRequestException e) {
            logger.error("Error redirecting to Intuit SSO", e);
        }
    }


    @RequestMapping("/access/connectToQuickBooks")
    protected void connectToQuickbooks(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String errorCode = request.getParameter("error");
        String systemErrorMessage = null;

        String successURL = request.getParameter("onSuccess");
        String failureURL = request.getParameter("onFailure");
        failureURL = failureURL == null ? successURL : failureURL;
        cookieStore.saveItem(response, "intuit_onSuccess", successURL);
        cookieStore.saveItem(response, "intuit_onFailure", failureURL);

        String isNewAccount = request.getParameter("newAccount");
        String isClientAccount = request.getParameter("client");

        Boolean isNewAccountBoolean = new Boolean(isNewAccount);
        Boolean isClientAccountBoolean = new Boolean(isClientAccount);

        List<NameValuePair> currentParams = getQueryParams(successURL);
        JSONObject paramsObject = new JSONObject();
        NameValuePair paramsQueryParam = currentParams.stream().filter(e -> e.getName().equals("params")).findFirst().orElse(null);
        if (paramsQueryParam != null) {
            paramsObject = new JSONObject(URLDecoder.decode(paramsQueryParam.getValue(), "UTF-8"));
        }

        try {
            boolean isLoggedIn = isLoggedIn();

            if (!isLoggedIn) {
                errorCode = "system_error";
                systemErrorMessage = "Please login and select an account that you want to connect to.";
            } else {
                String redirectURL = new StringBuffer(new PartnerUtil().getRedirectURL(request, redirectPath)).toString();

                if(isClientAccountBoolean.booleanValue()) {
                    long parentDomainId = userManager.getParentDoaminId();
                    if(parentDomainId > 0) {
                        String loginName = userManager.getLoggedInUserName();
                        apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, parentDomainId, request, response);
                    } else {
                        throw new ApheException("Can not connect a client from non accountant account.");
                    }
                } else {
                    if (isNewAccountBoolean.booleanValue() && getCurrentDoaminId() != null) {
                        String loginName = userManager.getLoggedInUserName();
                        apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, -1, request, response);
                    }
                }

                List<Scope> scopes = new ArrayList<Scope>();
                scopes.add(Scope.Accounting);

                OAuth2Config oauth2Config = factory.getOAuth2Config();
                String csrf = oauth2Config.generateCSRFToken();
                saveCsrfTokenToRedis(csrf);

                JSONObject stateObject = new JSONObject();
                stateObject.put("csrfToken", csrf);
                stateObject.put("newAccount", isNewAccountBoolean.booleanValue());
                stateObject.put("client", isClientAccountBoolean.booleanValue());
                stateObject.put("oAuthFlow", Boolean.TRUE.booleanValue());
                stateObject.put("ssoFlow", Boolean.FALSE.booleanValue());
                String stateString = URLEncoder.encode(stateObject.toString(), "UTF-8");

                response.sendRedirect(oauth2Config.prepareUrl(scopes, redirectURL, stateString));
            }
        } catch (Throwable e) {
            logger.error("Error while attempting QBO OAuth Connection", e);
        } finally {
            String errorMessage = null;
            if (errorCode != null) {
                errorMessage = "An error occured while connecting to your QuickBooks Online account.";
                if ("system_error".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " " + systemErrorMessage;
                }
                paramsObject.put("errorMessage", errorMessage);
                paramsObject.put("errorCode", errorCode);
                String paramsString = paramsObject.toString();
                List<NameValuePair> newParams = currentParams.stream().filter(m -> !(m.getName().equalsIgnoreCase("params"))).collect(Collectors.toList());
                newParams.add(new BasicNameValuePair("params", paramsString));
                String newURL = updateQueryString(failureURL, newParams);
                response.sendRedirect(newURL);
            }
        }
    }

    /**
     * Call back gets called in 3 scenarios.
     * <p>
     * 1. SSO: (No association with a realm) - with just OpenId scope - accessToken good openId scope info
     * 2. GetApp: (With openId and realmId) - with OpenId Scope and what ever other scopes we ask for -- accessToken good for all of them.
     * 3. OAuth: (With realmId) - with accounting scope of what ever we ask for
     * <p>
     * <p>
     * If an existing user is present in out system with the same email address...
     * 1. Leave that account alone. -- Go with this for now
     * 2. Tell them they need to login with non-sso mode to access them.
     * 3. Ask for the password of standalone account and link them.
     * 3a. If the user has 3 standalone acounts, present 3, let the user pick one to map to this
     * SSO relamId and only move that standalone account under this sso login.
     * 3b. When they login next time, with a completely different relamId, present other two realmIds
     * 3c. User can always create a new account and not link to any existing ones.
     * include them in the realm picker
     * or tell them they need to login with out SSO to access these companies.
     */
    @RequestMapping("/access/qbCallback")
    protected void qbCallBack(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String onSuccessParam = cookieStore.get(request, "intuit_onSuccess");
        String onFailureParam = cookieStore.get(request, "intuit_onFailure");

        String successURL = StringUtil.isNotEmpty(onSuccessParam) ? onSuccessParam : partnerUtil.getRedirectURL(request, "/");
        String failureURL = StringUtil.isNotEmpty(onFailureParam) ? onFailureParam : partnerUtil.getRedirectURL(request, "/");

        List<NameValuePair> currentParams = getQueryParams(successURL);
        JSONObject paramsObject = new JSONObject();
        NameValuePair paramsQueryParam = currentParams.stream().filter(e -> e.getName().equals("params")).findFirst().orElse(null);
        if (paramsQueryParam != null) {
            paramsObject = new JSONObject(URLDecoder.decode(paramsQueryParam.getValue(), "UTF-8"));
        }

        String authCode = request.getParameter("code");
        String state = request.getParameter("state");
        String realmId = request.getParameter("realmId");

        String stateString = URLDecoder.decode(state, "UTF-8");
        JSONObject stateJSON = new JSONObject(stateString);
        String stateCSRF = (String) stateJSON.get("csrfToken");
        boolean ssoFlow =  false;
        boolean oAuthFlow = false;
        boolean clientAccount = false;
        boolean newAccount = false;
        if(stateJSON.has("client")) {
            clientAccount = ((Boolean) stateJSON.get("client")).booleanValue();
        }
        if(stateJSON.has("newAccount")) {
            newAccount = ((Boolean) stateJSON.get("newAccount")).booleanValue();
        }
        if(stateJSON.has("ssoFlow")) {
            ssoFlow = ((Boolean) stateJSON.get("ssoFlow")).booleanValue();
        }
        if(stateJSON.has("oAuthFlow")) {
            oAuthFlow = ((Boolean) stateJSON.get("oAuthFlow")).booleanValue();
        }


        if (StringUtil.isEmpty(realmId) || "null".equalsIgnoreCase(realmId)) {
            realmId = null;
        }

        String errorCode = request.getParameter("error");
        String systemErrorMessage = null;

        String redirectURL = partnerUtil.getRedirectURL(request, redirectPath);
        try {
            boolean exists = isCsrfTokenValid(stateCSRF);
            if (exists) {
                if (errorCode == null) {
                    OAuth2PlatformClient client = factory.getOAuth2PlatformClient();

                    BearerTokenResponse bearerTokenResponse = client.retrieveBearerTokens(authCode, redirectURL);

                    String idToken = bearerTokenResponse.getIdToken();
                    String accessToken = bearerTokenResponse.getAccessToken();

                    if(ssoFlow) {
                        if(idToken != null) {
                            UserDTO userDTO = null;
                            try {
                                userDTO = createOrUpdateUser(client, idToken, accessToken, request, response);
                            }catch (ApheDataValidationException e) {
                                errorCode = "system_error";
                                systemErrorMessage = "Your account has been disabled. Please contact us to enable your account.";
                            } catch (Exception e) {
                                errorCode = "system_error";
                                systemErrorMessage = "An error occured while connecting to your Xero account.";
                            }
                        } else {
                            errorCode = "system_error";
                            systemErrorMessage = "Could not get identity details from Intuit.";
                        }
                    }
                    if(oAuthFlow) {
                        if(ssoFlow) {
                            String loginName = userManager.getLoggedInUserName();
                            apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, -99, request, response);
                        }

                        if(ssoFlow || clientAccount || newAccount) {
                            if(accessToken != null) {
                                if(clientAccount) {
                                    long parentDomainId = userManager.getParentDoaminId();
                                    if(parentDomainId > 0) {
                                        long userId = userManager.getLoggedInUserId();
                                        UserDTO userDTO = userManager.getUser(Long.toString(userId));
                                        DomainDTO domainDTO = createOrUpdateClientDomain(userDTO, realmId, parentDomainId, bearerTokenResponse, request, response);
                                    } else {
                                        errorCode = "system_error";
                                        systemErrorMessage = "You are trying to a client to an non accountant account.";
                                    }
                                } else {
                                    Long loggedInDomainId = getCurrentDoaminId();
                                    if (loggedInDomainId != null) {
                                        errorCode = "system_error";
                                        systemErrorMessage = "You are trying to add a new account while logged into another domain. Please add new account from add account page.";
                                    } else {
                                        try {
                                            long userId = userManager.getLoggedInUserId();
                                            UserDTO userDTO = userManager.getUser(Long.toString(userId));
                                            DomainDTO domainDTO = createOrUpdateDomain(userDTO, realmId, bearerTokenResponse, request, response);
                                        } catch (Exception e) {
                                            errorCode = "system_error";
                                            systemErrorMessage = "System error while creating an account. Please retry again or contact us.";
                                        }
                                    }
                                }
                            } else {
                                errorCode = "system_error";
                                systemErrorMessage = "You have not authorized a new account to be added.";
                            }
                        } else {
                            //This is the oAuthConnect Flow. if there are connections. present as success, if not show an error message to the user and keep as disconnected.
                            if(accessToken != null) {
                                if(isLoggedIn()) {
                                    Long loggedInDomainId = getCurrentDoaminId();
                                    try {
                                        String domainName = getAccountName(realmId, bearerTokenResponse.getAccessToken());
                                        saveOAuthConnectionFromTokenResponse(loggedInDomainId, bearerTokenResponse, realmId, domainName);
                                    } catch (Exception e) {
                                        errorCode = "system_error";
                                        systemErrorMessage = "System error while trying to connect. Please retry again or contact us.";
                                    }
                                } else {
                                    errorCode = "system_error";
                                    systemErrorMessage = "You are not logged into any 1099SmartFile account. Please select an account before try to connect to QuickBooks.";
                                }
                            } else {
                                errorCode = "system_error";
                                systemErrorMessage = "You have not authorized any accounts to be connected.";
                            }
                        }
                    }
                } else {
                    systemErrorMessage = "Error authorizing the connection " + errorCode;
                }
            } else {
                errorCode = "system_error";
                systemErrorMessage = "CSRF token mismatch.";
            }
        } catch (Throwable e) {
            errorCode = "system_error";
            systemErrorMessage = e.getMessage();
            logger.error("Error processing QBCallBack", e);
        } finally {
            String errorMessage = null;
            if (errorCode != null) {
                paramsObject.put("ssoStatus", "Fail");

                if (ssoFlow) {
                    errorMessage = "An error occurred while logging in using your Intuit account.";
                } else {
                    errorMessage = "An error occurred while connecting to your QuickBooks Online account.";
                }
                if ("access_denied".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " Access was denied.";
                } else if ("invalid_scope".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " An invalid scope was specified in connection.";
                } else if ("system_error".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " " + systemErrorMessage;
                }

                logger.error("Error handling qbCallBack errorMessage={} systemErrorMessage={}", errorMessage, systemErrorMessage);

                paramsObject.put("errorMessage", errorMessage);
                paramsObject.put("errorCode", errorCode);
                String paramsString = paramsObject.toString();
                List<NameValuePair> newParams = currentParams.stream().filter(m -> !(m.getName().equalsIgnoreCase("params"))).collect(Collectors.toList());
                newParams.add(new BasicNameValuePair("params", paramsString));
                String newURL = updateQueryString(failureURL, newParams);
                if(ssoFlow || newAccount) {
                    response.sendRedirect(newURL);
                } else {
                    sendOAuthPopupResponse(response, "Fail", errorCode, errorMessage);
                }
            } else {
                paramsObject.put("ssoStatus", "Success");
                String paramsString = paramsObject.toString();
                List<NameValuePair> newParams = currentParams.stream().filter(m -> !(m.getName().equalsIgnoreCase("params"))).collect(Collectors.toList());
                newParams.add(new BasicNameValuePair("params", paramsString));
                String newURL = updateQueryString(successURL, newParams);
                if(ssoFlow || newAccount) {
                    response.sendRedirect(newURL);
                } else {
                    sendOAuthPopupResponse(response, "Success", null, null);
                }
            }
        }
    }

    private void saveOAuthConnectionFromTokenResponse(long domainId, BearerTokenResponse tokenResponse, String realmId, String accountName) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        String refreshToken = tokenResponse.getRefreshToken();
        Long accessTokenExpiresInSeconds = tokenResponse.getExpiresIn();
        Long refreshTokenExpiresInSeconds = tokenResponse.getXRefreshTokenExpiresIn();
        saveOAuthConnection(domainId, accessToken, accessTokenExpiresInSeconds, refreshToken, refreshTokenExpiresInSeconds, realmId, null, accountName);
    }

    /**
     * If passed idToken is valid, creates or updates the user and sets the auth context.
     */
    private UserDTO createOrUpdateUser(OAuth2PlatformClient client, String idToken, String accessToken, HttpServletRequest request, HttpServletResponse response) throws Exception {
            boolean validIdToken = client.validateIDToken(idToken);

            if (validIdToken) {
                JSONObject parsedIdToken = jwtUtil.parseTokenToJSON(idToken, null);
                String loginId = parsedIdToken.getString("sub");
                UserInfoResponse userInfo = client.getUserInfo(accessToken);

                String cleanFirstName = StringUtil.cleanTOAscii(userInfo.getGivenName());
                String cleanLastName = StringUtil.cleanTOAscii(userInfo.getFamilyName());

                String firstName = StringUtil.isNotEmpty(cleanFirstName) ? cleanFirstName : "NoFirstName";
                String lastName = StringUtil.isNotEmpty(cleanLastName) ? cleanLastName : "NoLastName";
                String email = userInfo.getEmail();
                boolean emailVerified = userInfo.isEmailVerified();

                return createUserAndSetContext(loginId, firstName, lastName, email, emailVerified, request, response);
            } else {
                throw new Exception("Invalid idToken");
            }
    }


    private DomainDTO createOrUpdateDomain(UserDTO userDTO, String realmId, BearerTokenResponse bearerTokenResponse, HttpServletRequest request, HttpServletResponse response) {
        try {
            String domainName = getAccountName(realmId, bearerTokenResponse.getAccessToken());
            boolean isAccountant = false; //isAccountant(realmId, bearerTokenResponse.getAccessToken());
            DomainDTO theDomain = createDomainAndSetContext(userDTO, realmId, domainName, isAccountant, false, -1, request, response);
            try {
                saveOAuthConnectionFromTokenResponse(theDomain.id, bearerTokenResponse, realmId, domainName);
            } catch (Exception e) {
                logger.error("Error oAuthIntegration during adding a new domain");
            }
            return theDomain;
        } catch (Exception e) {
            logger.error("Error saving/updating sso user", e);
            return null;
        }
    }

    private DomainDTO createOrUpdateClientDomain(
            UserDTO userDTO,
            String realmId,
            long parentDomainId,
            BearerTokenResponse bearerTokenResponse, HttpServletRequest request, HttpServletResponse response) {
        try {
            String domainName = getAccountName(realmId, bearerTokenResponse.getAccessToken());
            DomainDTO theDomain = createDomainAndSetContext(
                    userDTO, realmId, domainName, false, true, parentDomainId, request, response);
            try {
                saveOAuthConnectionFromTokenResponse(theDomain.id, bearerTokenResponse, realmId, domainName);
            } catch (Exception e) {
                logger.error("Error oAuthIntegration during adding a new domain");
            }
            return theDomain;
        } catch (Exception e) {
            logger.error("Error saving/updating sso user", e);
            return null;
        }
    }


    private String getAccountName(String realmId, String accessToken) {
        DataService service;
        try {
            service = helper.getDataService(realmId, accessToken);
            String sql = "select * from companyinfo";
            QueryResult queryResult = service.executeQuery(sql);
            if (!queryResult.getEntities().isEmpty() && queryResult.getEntities().size() > 0) {
                CompanyInfo companyInfo = (CompanyInfo) queryResult.getEntities().get(0);
                String companyName = companyInfo.getCompanyName();
                return companyName;
            }
        } catch (FMSException e) {
            logger.error("Error getting accountName in qbCallBack", e);
        }
        return null;
    }


    private boolean isAccountant(String realmId, String accessToken) {
        DataService service;
        boolean isAccountant = false;
        try {
            service = helper.getDataService(realmId, accessToken);
            EntitlementsResponse response = service.getEntitlements();
            if (response != null) {
                List<EntitlementsResponse.Entitlement> entitlementList = response.getEntitlement();
                for(EntitlementsResponse.Entitlement entitlement : entitlementList) {
                    if("Accountant Menu".equalsIgnoreCase(entitlement.getName())){
                        logger.info("Entitlements Response -> : " + entitlement.getName() + " == " + entitlement.getTerm() );
                        isAccountant = "On".equalsIgnoreCase(entitlement.getTerm());
                        break;
                    }
                }
            }
        } catch (FMSException e) {
            logger.error("Error getting accountName in qbCallBack", e);
        }
        return isAccountant;
    }

}