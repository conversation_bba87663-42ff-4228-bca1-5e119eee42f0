package com.aphe.auth.sso;

import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.service.BaseManager;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.OAuthTokenDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import org.hibernate.validator.constraints.ValidationErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional
public class OAuthIntegrationMgr extends BaseManager {

	@Autowired
	OAuthIntegrationRepository integrationRepository;

	@Autowired
	OAuthIntegrationConvertUtil convertUtil;

	@Autowired
	OAuthConnectionUtilFactory oAuthConnFactory;

//	@Transactional
//	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_INTEGRATION')")
//	public List<OAuthIntegrationClientDTO> getAccountingIntegrationDTOs(String domainId) {
//		List<OAuthIntegration> integrations = integrationRepository.findByDomainId(domainId);
//		List<OAuthIntegrationClientDTO> dtos = new ArrayList<OAuthIntegrationClientDTO>();
//		for (OAuthIntegration integration : integrations) {
//			dtos.add(convertUtil.convertAccountingIntegrationToClientDTO(integration));
//		}
//		return dtos;
//	}

	@Transactional
//	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_INTEGRATION')")
	//TODO: should we protect this?
	// There is a need to read this info as part of creating a new domain when connecting to QBO to if there is an existing domain for
	// connecting realmId.
	public OAuthIntegrationClientDTO getAccountingIntegrationDTO(String domainId, String partner) {
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);
		if (currentIntegration != null) {
			return convertUtil.convertAccountingIntegrationToClientDTO(currentIntegration);
		}
		return null;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public OAuthTokenDTO getOAuthTokenDTO(String domainId, String partner) {
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);
		if (currentIntegration != null) {
			return convertUtil.convertAccountingIntegrationToTokenDTO(currentIntegration);
		}
		return null;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public OAuthTokenDTO getOAuthTokenDTOById(String domainId, String integrationId) {
		OAuthIntegration currentIntegration = integrationRepository.findById(Long.parseLong(integrationId)).orElse(null);
		if (currentIntegration != null) {
			return convertUtil.convertAccountingIntegrationToTokenDTO(currentIntegration);
		}
		return null;
	}

	@Transactional
//	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_INTEGRATION')")
	public List<PartnerAccountDTO> getOAuthAccounts(String domainId, String partner) {
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);
		if (currentIntegration != null) {
			OAuthConnectionUtil connectionUtil = oAuthConnFactory.getOAuthConnectionUtil(currentIntegration.getPartner());
			OAuthIntegrationDTO dto = convertUtil.convertAccountingIntegrationToDTO(currentIntegration);
			return connectionUtil.getPartnerAccounts(dto);
		}
		return null;
	}

	@Transactional
	@PreAuthorize("hasPermission(#dto.domainId, 'DOMAIN', 'UPDATE_INTEGRATION')")
	public OAuthIntegrationClientDTO addOrUpdateIntegration(OAuthIntegrationDTO dto) throws Exception {

		OAuthConnectionUtil connectionUtil = oAuthConnFactory.getOAuthConnectionUtil(dto.partner);
		ValidationErrors errors = getValidationErrors(dto);

		if (errors.getMessages().size() > 0) {
			logger.error("Validation Errors", errors.toString());
			throw new ApheDataValidationException(errors, "Invalid input");
		}

//		if(dto.accountId != null) {
//			try {
//				String accountName = connectionUtil.getAccountName(dto);
//				if(accountName != null) {
//					dto.accountName = accountName;
//				}
//			}catch (RuntimeException e) {
//			}
//		}


		OAuthIntegration entity = convertUtil.convertAccountingIntegrationDTOToEntity(dto);

		if (dto.id != null && dto.id > 0) {
			OAuthIntegration existingEntity = integrationRepository.findById(dto.id).orElse(null);
			if (existingEntity == null) {
				throw new Exception("Entity not found");
			} else if (existingEntity.getDomainId() == null || !existingEntity.getDomainId().equalsIgnoreCase(entity.getDomainId())) {
				throw new Exception("Invalid domainId. New domainId doesn't match old domainId.");
			} else if (existingEntity.getPartner() != null && existingEntity.getPartner() != entity.getPartner()) {
				throw new Exception("Invalid partner. New partner doesn't match old partner");
			} else {
				existingEntity.setAccountName(entity.getAccountName());
				existingEntity.setAccountId(entity.getAccountId());
				existingEntity.setXeroConnectionId(entity.getXeroConnectionId());
				OAuthIntegration savedAccountingIntegration = integrationRepository.mergeAndSave(entity, existingEntity);
				return convertUtil.convertAccountingIntegrationToClientDTO(savedAccountingIntegration);

			}
		} else {
			OAuthIntegration currentIntegration = getAccountingIntegration(dto.domainId, dto.partner.toString());
			if (currentIntegration != null) {
				throw new Exception("Existing integration exists. Update existing integration or delete it and recreate the integration");
			}
			OAuthIntegration savedEntity = integrationRepository.save(entity);
			return convertUtil.convertAccountingIntegrationToClientDTO(savedEntity);
		}
	}

	/**
	 * Called when the user goes to integration page. Could be called when ever use logins too.
	 * The purpose of this method is to make sure token are ready to be used.
	 * @throws Exception
	 */
//	@Transactional
//	public void hardRefreshTokens() throws Exception {
//		String domainId = getCurrentDoaminId();
//		List<OAuthIntegration> integrations = integrationRepository.findByDomainId(domainId);
//		for (OAuthIntegration currentIntegration : integrations) {
//			hardRefreshToken(currentIntegration);
//		}
//	}

	/**
	 * Refreshes the tokens. To be used by the auto task when refresh tokens are about to expire.
	 */
	@Transactional
	public OAuthIntegrationClientDTO hardRefreshTokens(String partner) throws Exception {
		String domainId = getCurrentDoaminId();
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);
		if(currentIntegration != null) {
			return hardRefreshToken(currentIntegration);
		}
		return null;
	}

	private OAuthIntegrationClientDTO hardRefreshToken(OAuthIntegration currentIntegration) throws Exception {
		OAuthConnectionUtil connectionUtil = oAuthConnFactory.getOAuthConnectionUtil(currentIntegration.getPartner());
		OAuthIntegrationDTO dto = convertUtil.convertAccountingIntegrationToDTO(currentIntegration);
		OAuthIntegrationDTO updatedDTO = null;
		OAuthIntegrationClientDTO updatedIntegration = null;
		try {
			//TODO: We should think about whether refreshing the tokens anyways, even if accountId is null. For FB, Zoho, the same token is good for multiple accountIds.
			if(currentIntegration.isConnected() && currentIntegration.getAccountId() != null) {
				updatedDTO = connectionUtil.hardRefreshTokens(dto);
				if (updatedDTO != null) {
					updatedIntegration = addOrUpdateIntegration(updatedDTO);
					logger.info("refreshed tokens successfully.");
				}
			}
		} catch (TokenRevokedException e) {
			currentIntegration.setConnected(false);
			integrationRepository.save(currentIntegration);
			updatedIntegration = convertUtil.convertAccountingIntegrationToClientDTO(currentIntegration);
			logger.info("disconnecting this accounting integration");
		}
		return updatedIntegration;
	}


	@Transactional
	public OAuthIntegrationClientDTO refreshTokens(String partner) throws Exception {
		String domainId = getCurrentDoaminId();
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);

		if (currentIntegration != null) {
			OAuthConnectionUtil connectionUtil = oAuthConnFactory.getOAuthConnectionUtil(currentIntegration.getPartner());
			OAuthIntegrationDTO dto = convertUtil.convertAccountingIntegrationToDTO(currentIntegration);
			dto = connectionUtil.refreshTokens(dto);
			if (dto != null) {
				OAuthIntegrationClientDTO newIntegration = addOrUpdateIntegration(dto);
				return newIntegration;
			}
		}

		return null;
	}

	@Transactional
	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'UPDATE_INTEGRATION')")
	public boolean disconnect(String domainId, String partner) throws Exception {
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);

		if (currentIntegration != null) {
			OAuthConnectionUtil connectionUtil = oAuthConnFactory.getOAuthConnectionUtil(currentIntegration.getPartner());
			OAuthIntegrationDTO dto = convertUtil.convertAccountingIntegrationToDTO(currentIntegration);
			boolean revoked = connectionUtil.revokeToken(dto);
			if(revoked) {
				currentIntegration.setConnected(false);
			} else {
				logger.error("Error revoking the token. Make sure token is revoked");
			}
			integrationRepository.save(currentIntegration);
			return true;
		}
		return false;
	}

	@Transactional
	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'UPDATE_INTEGRATION')")
	public boolean connectOAuthAccount(String domainId, String partner, String accountId, String accountName) throws Exception {
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);
		if (currentIntegration != null && currentIntegration.isConnected()) {
			OAuthConnectionUtil connectionUtil = oAuthConnFactory.getOAuthConnectionUtil(currentIntegration.getPartner());
			currentIntegration.setAccountId(accountId);
			currentIntegration.setAccountName(accountName);

			if(currentIntegration.getPartner() == OAuthIntegrationPartner.Bill) {
				OAuthIntegrationDTO dto = convertUtil.convertAccountingIntegrationToDTO(currentIntegration);
				OAuthIntegrationDTO updateDTO = connectionUtil.refreshTokens(dto);
				currentIntegration.setAccessToken(updateDTO.accessToken);
				currentIntegration.setAccessTokenExpiresIn(updateDTO.accessTokenExpiresIn);
			}

			integrationRepository.save(currentIntegration);
			return true;
		}
		return false;
	}

	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public boolean updateOAuthToken(String domainId, String partner, OAuthTokenDTO oAuthTokenDTO) throws Exception {
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);

		OAuthIntegrationDTO newDto = convertUtil.convertTokenDTOToIntegrationDTO(oAuthTokenDTO);
		newDto.domainId = domainId;
		newDto.partner = OAuthIntegrationPartner.getFromName(partner);
		if(oAuthTokenDTO.accessToken == null) {
			//Disconnect case
			newDto.connected = false;
			newDto.accessToken= "disconnected";
			newDto.refreshToken= "disconnected";
		} else {
			newDto.connected = true;
		}
		if (currentIntegration != null) {
			newDto.id = currentIntegration.getId();
		}
		OAuthIntegrationClientDTO updatedDTO = addOrUpdateIntegration(newDto);

		return true;
	}

	/**
	 * If access token has expired, it will refresh the token, save it to database and return the updated DTO object.
	 * Returns null, if the refreshing fails either because of network issues or because of refresh tokens have expired.
	 * It will/should delete the current integration if the refresh token is alos expired, which requires a new connection to be setup.
	 * 
	 * @param partner
	 * @return
	 * @throws Exception
	 */
	@Transactional
	public OAuthIntegrationClientDTO refreshTokensIfRequired(String partner) throws Exception {
		String domainId = getCurrentDoaminId();
		OAuthIntegrationClientDTO currentIntegration = getAccountingIntegrationDTO(domainId, partner);
		if (currentIntegration != null) {
			OAuthConnectionUtil connectionUtil = oAuthConnFactory.getOAuthConnectionUtil(currentIntegration.partner);

			if (connectionUtil.isAccessTokenExpiring(currentIntegration)) {
				OAuthIntegrationClientDTO dto = refreshTokens(partner);
				return dto;
			} else {
				return currentIntegration;
			}
		}
		return null;
	}

	private OAuthIntegration getAccountingIntegration(String domainId, String partner) {
		OAuthIntegration retVal = null;
		OAuthIntegrationPartner partnerFromName = OAuthIntegrationPartner.getFromName(partner);
		if(partnerFromName != null ) {
			List<OAuthIntegration> integrations = integrationRepository.findByDomainIdAndPartner(domainId, partnerFromName);
			for (OAuthIntegration integration : integrations) {
				if (integration.getPartner() == partnerFromName) {
					retVal = integration;
					break;
				}
			}
		}
		return retVal;
	}


	/**
	 * A special API to be used from DomainController so that we are able to pass access token to domain service.
	 * @param domainId
	 * @param partner
	 * @return
	 */
	@Transactional
	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_INTEGRATION')")
	public OAuthIntegrationDTO getAccountingIntegrationForDomainService(String domainId, String partner) {
		OAuthIntegration currentIntegration = getAccountingIntegration(domainId, partner);
		if (currentIntegration != null) {
			return convertUtil.convertAccountingIntegrationToDTO(currentIntegration);
		}
		return null;
	}


	public boolean hasAccountingIntegration(Long domainId) {
		List<OAuthIntegration> integrations = integrationRepository.findByDomainIdAndPartner(Long.toString(domainId), OAuthIntegrationPartner.Xero);
		for (OAuthIntegration integration : integrations) {
			if (integration.isConnected()) {
				return true;
			}
		}
		return false;
	}
}
