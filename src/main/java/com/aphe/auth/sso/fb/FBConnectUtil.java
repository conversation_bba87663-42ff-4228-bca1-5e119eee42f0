package com.aphe.auth.sso.fb;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.auth.sso.OAuthConnectionUtil;
import com.aphe.auth.sso.TokenRevokedException;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.StringUtil;
import com.aphe.fbsdk.api.IdentityManager;
import com.aphe.fbsdk.exceptions.FBAuthorizationException;
import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.Business;
import com.aphe.fbsdk.model.BusinessMembership;
import com.aphe.fbsdk.model.Identity;
import com.google.api.client.googleapis.auth.oauth2.GoogleRefreshTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;

@Component
public class FBConnectUtil extends OAuthConnectionUtil {

	@Value("${aphe.freshbooks.clientId}")
	private String clientId;

	@Value("${aphe.freshbooks.clientSecret}")
	private String clientSecret;

	@Value("${aphe.freshbooks.redirectPath}")
	private String redirectPath;

	@Value("${aphe.auth.tokenValidationURL}")
	private String authHost;

	@Value("${aphe.freshbooks.revokeTokenURL}")
	private String revokeTokenURL;

	@Value("${aphe.freshbooks.tokenServerURL}")
	private String tokenServerURL;

	@Value("${aphe.freshbooks.authServerURL}")
	private String authServerURL;

	@Value("${aphe.freshbooks.baseURL}")
	private String baseURL;

	final static Logger logger = LogManager.getLogger(FBConnectUtil.class);

	DecimalFormat df = new DecimalFormat("0.00");

	public boolean revokeToken(OAuthIntegrationDTO dto) {
		try {
			RestTemplate restTemplate = new RestTemplate();
			restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			String resourceUrl = revokeTokenURL;
			RevokeTokenDTO revokeTokenDTO  = new RevokeTokenDTO(clientId, clientSecret, dto.refreshToken);
			HttpEntity<RevokeTokenDTO> entity = new HttpEntity<>(revokeTokenDTO, headers);
			ResponseEntity<String> response = restTemplate.exchange(resourceUrl, HttpMethod.POST, entity, String.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return true;
			} else {
				logger.error("Error revoking token", response);
				return false;
			}
		} catch (Exception e1) {
			logger.error("Error revoking token", e1);
			return false;
		}
	}

	@Override
	public OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
		OAuthIntegrationDTO updatedDTO = null;
		boolean isAccessTokenValid = true;
		try {
			isAccessTokenValid = isAccessTokenValid(dto);
		}catch (Exception e) {
			if(e instanceof TokenRevokedException) {
				isAccessTokenValid = false;
			}
		}

		if (!isAccessTokenValid) {
			updatedDTO = refreshTokens(dto);
			isAccessTokenValid = isAccessTokenValid(dto);
		}
		return updatedDTO;
	}

	private boolean isAccessTokenValid(OAuthIntegrationDTO dto) throws TokenRevokedException {
		boolean isAccessTokenValid = false;

		IdentityManager identityManager = new IdentityManager(baseURL, dto.accessToken, dto.accountId);
		try {
			Identity currentUser = identityManager.getOne(null, null);
			List<BusinessMembership> businessMemberships = currentUser.business_memberships;
			if (businessMemberships != null && businessMemberships.size() > 0) {
				businessMemberships.get(0);
			}
			isAccessTokenValid = true;
		} catch (FBException e) {
			if (e instanceof FBAuthorizationException) {
				throw new TokenRevokedException();
			}
		}
		return isAccessTokenValid;
	}

	public boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto) {
		return isAccessTokenExpiringDefault(dto);
	}

	public OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
		try {
			GoogleRefreshTokenRequest refreshTokenRequest = new GoogleRefreshTokenRequest(
					new NetHttpTransport(), GsonFactory.getDefaultInstance(),
					dto.refreshToken, clientId, clientSecret);
			refreshTokenRequest.setGrantType("refresh_token");
			refreshTokenRequest.setTokenServerUrl(new GenericUrl(tokenServerURL));
			Map<String, Object> customParams =  new HashMap<>();
			customParams.put("redirect_uri", authServerURL + redirectPath);
			refreshTokenRequest.setUnknownKeys(customParams);

			GoogleTokenResponse tokenResponse = refreshTokenRequest.execute();
			if(tokenResponse.get("error") == null) {
				dto.accessToken = tokenResponse.getAccessToken();
				dto.accessTokenExpiresIn = tokenResponse.getExpiresInSeconds();
				dto.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.accessTokenExpiresIn);
				dto.refreshToken = tokenResponse.getRefreshToken() != null ? tokenResponse.getRefreshToken() : dto.refreshToken;
				dto.refreshTokenExpiresIn = FBConnectionController.DEFAULT_REFRESH_EXPIRY;
				dto.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.refreshTokenExpiresIn);
				dto.updatedDate = new Date();
				return dto;
			} else {
				throw new TokenRevokedException();
			}
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new TokenRevokedException();
		}
	}

	public String getAccountName(OAuthIntegrationDTO dto) {

		IdentityManager identityManager = new IdentityManager(baseURL, dto.accessToken, dto.accountId);
		try {
			Identity currentUser = identityManager.getOne(null, null);
			List<BusinessMembership> businessMemberships = currentUser.business_memberships;
			if (businessMemberships != null && businessMemberships.size() > 0) {
				for(BusinessMembership membership : businessMemberships) {
					Business business = membership.business;
					if(business.account_id.equalsIgnoreCase(dto.accountId)) {
						String bizName = business.name;
						if(StringUtil.isEmpty(bizName)) {
							bizName = "No Business Name";
						}
						return bizName;
					}
				}
			}
		} catch (FBException e) {
			logger.error("Error getting account name from FreshBooks", e);
		}
		return null;
	}

	public List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto) {
		List<PartnerAccountDTO> partnerAccountDTOS = new ArrayList<>();
		IdentityManager identityManager = new IdentityManager(baseURL, dto.accessToken, dto.accountId);
		try {
			Identity currentUser = identityManager.getOne(null, null);
			List<BusinessMembership> businessMemberships = currentUser.business_memberships;
			if (businessMemberships != null && businessMemberships.size() > 0) {
				for(BusinessMembership membership : businessMemberships) {
					Business business = membership.business;
					String bizName = business.name;
					if(StringUtil.isEmpty(bizName)) {
						bizName = "No Business Name";
					}
					partnerAccountDTOS.add(new PartnerAccountDTO(business.account_id, bizName));
				}
			}
		} catch (FBException e) {
			logger.error("Error getting partner accounts from FreshBooks", e);
		}
		return partnerAccountDTOS;
	}

}
