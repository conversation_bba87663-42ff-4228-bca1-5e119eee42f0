package com.aphe.auth.sso.fb;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.sso.OAuthConnectionController;
import com.aphe.auth.sso.OAuthConnectionException;
import com.aphe.common.util.StringUtil;
import com.aphe.fbsdk.api.IdentityManager;
import com.aphe.fbsdk.exceptions.FBException;
import com.aphe.fbsdk.model.Business;
import com.aphe.fbsdk.model.BusinessMembership;
import com.aphe.fbsdk.model.Identity;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.DataStoreFactory;
import com.google.api.client.util.store.MemoryDataStoreFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Controller
public class FBConnectionController extends OAuthConnectionController {

    final static Logger logger = LogManager.getLogger(FBConnectionController.class);

    public static long DEFAULT_REFRESH_EXPIRY = 59 * 24 * 60 * 60L;

    @Value("${aphe.freshbooks.clientId}")
    private String clientId;

    @Value("${aphe.freshbooks.clientSecret}")
    private String clientSecret;

    @Value("${aphe.freshbooks.tokenServerURL}")
    private String tokenServerURL;

    @Value("${aphe.freshbooks.authServerURL}")
    private String authServerURL;

    @Value("${aphe.freshbooks.redirectPath}")
    private String redirectPath;

    @Value("${aphe.freshbooks.baseURL}")
    private String baseURL;

    public FBConnectionController() {
        super();
    }

    @Override
    public SSOPartner getSSOPartner() {
        return null;
    }

    @Override
    public OAuthIntegrationPartner getOAuthPartner() {
        return OAuthIntegrationPartner.FreshBooks;
    }

    /**
     * User this end point for connecting a 1099SmartFile account to QB.
     * For this we should have an authenticated session and a company (domainId) selected.
     */
    @RequestMapping("/access/connectToFB")
    protected void connectToFreshBooks(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String authorizationURL = null;

        OAuthConnectionException oAuthConnectionException = null;

        try {
            //Validations
            checkUserLoggedIn();
            checkDomainSelected();

            //Build oAuth Redirect URL
            String redirectURL = new StringBuffer(getRedirectURL(request, redirectPath)).toString();
            //No csrf token for FeshBooks client.
            //String stateString = buildStateString(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(), GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    Arrays.asList(" "))
                    .setDataStoreFactory(DATA_STORE_FACTORY)
//                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .build();

            authorizationURL = codeFlow.newAuthorizationUrl()
                    .setClientId(clientId)
//                    .setState(stateString)
                    .setRedirectUri(redirectURL).build();
            authorizationURL = authorizationURL;// + "&prompt=consent";

        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            processConnectionRequest(request, response, oAuthConnectionException, authorizationURL);
        }
    }

    @RequestMapping("/access/fbCallback")
    protected void fbCallBack(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        OAuthConnectionException oAuthConnectionException = null;

        try {
            checkUserLoggedIn();
            checkDomainSelected();
            //No csrf token for FeshBooks client.
            //validateCSRFToken(request);
            validateOAuthErrorCode(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(),
                    GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    Arrays.asList(" "))
                    .setDataStoreFactory(DATA_STORE_FACTORY)
                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .setTokenServerUrl(new GenericUrl(tokenServerURL))
                    .build();


            String authCode = request.getParameter("code");
            String redirectURL = getRedirectURL(request, redirectPath);

            GoogleTokenResponse tokenResponse = codeFlow.newTokenRequest(authCode).setRedirectUri(redirectURL).execute();
            String accessToken = (String) tokenResponse.getAccessToken();

            IdentityManager identityManager = new IdentityManager(baseURL, accessToken, null);
            Identity currentUser = identityManager.getOne(null, null);
            String userEmail = currentUser.email;
            List<BusinessMembership> businesses = currentUser.business_memberships;

            handleOrgsSize(businesses);

            String accountId = null;
            String domainName = null;
            if (businesses.size() == 1) {
                BusinessMembership business = businesses.get(0);
                Business fbBusiness = business.business;
                accountId = fbBusiness.account_id;
                domainName = fbBusiness.name;
                if (StringUtil.isEmpty(domainName)) {
                    domainName = "MyBusiness";
                }
            }

            String loggedInDomainId = getCurrentDoaminId();
            saveOAuthConnectionFromTokenResponse(Long.parseLong(loggedInDomainId), tokenResponse, accountId, domainName);

        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else if (e instanceof FBException) {
                oAuthConnectionException = buildOAuthException((FBException) e);
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            processCallbackResponse(request, response, oAuthConnectionException);
        }
    }

    @NotNull
    private OAuthConnectionException buildOAuthException(FBException e) {
        boolean isAuthError = false;
        String errorMessage = isAuthError ? "Authorization error when connecting to " + getOAuthPartner().getCompany() + "." : "System error when connecting to " + getOAuthPartner().getCompany() + ".";
        OAuthConnectionException.ERROR_TYPE errorType = OAuthConnectionException.ERROR_TYPE.SYSTEM_ERROR;
        return new OAuthConnectionException(errorType, errorMessage);
    }

    private void saveOAuthConnectionFromTokenResponse(long domainId, GoogleTokenResponse tokenResponse, String realmId, String accountName) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        String refreshToken = tokenResponse.getRefreshToken();
        Long accessTokenExpiresInSeconds = tokenResponse.getExpiresInSeconds();
        Long refreshTokenExpiresInSeconds = DEFAULT_REFRESH_EXPIRY;
        saveOAuthConnection(domainId, accessToken, accessTokenExpiresInSeconds, refreshToken, refreshTokenExpiresInSeconds, realmId, null, accountName);
    }

}