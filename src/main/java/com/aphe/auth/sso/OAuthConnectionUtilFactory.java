package com.aphe.auth.sso;

import com.aphe.auth.sso.bdc.BDCConnectUtil;
import com.aphe.auth.sso.bqecore.BQECOREConnectUtil;
import com.aphe.auth.sso.fb.FBConnectUtil;
import com.aphe.auth.sso.qbo.QBOConnectUtil;
import com.aphe.auth.sso.wave.WaveConnectUtil;
import com.aphe.auth.sso.xero.XeroConnectUtil;
import com.aphe.auth.sso.zoho.ZohoConnectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aphe.auth.model.core.OAuthIntegrationPartner;

@Component
public class OAuthConnectionUtilFactory {

	@Autowired
    QBOConnectUtil qboConnectUtil;

	@Autowired
	XeroConnectUtil xeroConnectUtil;

	@Autowired
	FBConnectUtil fbConnectUtil;

	@Autowired
	ZohoConnectUtil zohoConnectUtil;

	@Autowired
	WaveConnectUtil waveConnectUtil;

	@Autowired
	BQECOREConnectUtil bqecoreConnectUtil;

	@Autowired
	BDCConnectUtil bdcConnectUtil;

	public OAuthConnectionUtil getOAuthConnectionUtil(OAuthIntegrationPartner partner) {
		if (OAuthIntegrationPartner.QBO == partner) {
			return qboConnectUtil;
		} else if (OAuthIntegrationPartner.Xero == partner) {
			return xeroConnectUtil;
		} else if (OAuthIntegrationPartner.FreshBooks == partner) {
			return fbConnectUtil;
		} else if (OAuthIntegrationPartner.ZohoBooks == partner) {
			return zohoConnectUtil;
		} else if (OAuthIntegrationPartner.Wave == partner) {
			return waveConnectUtil;
		} else if (OAuthIntegrationPartner.BQECORE == partner) {
			return bqecoreConnectUtil;
		} else if (OAuthIntegrationPartner.Bill == partner) {
			return bdcConnectUtil;
		}
		return null;
	}

}
