package com.aphe.auth.sso;

import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.OAuthTokenDTO;
import com.aphe.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;
import java.util.Date;

@Component
public class OAuthIntegrationConvertUtil {

	@Autowired
	OAuthIntegrationMapper mapper;

	public OAuthIntegration convertAccountingIntegrationDTOToEntity(OAuthIntegrationDTO dto) {
		return mapper.toEntity(dto);
	}

	public OAuthIntegration convertClientDTOToEntity(OAuthIntegrationDTO dto) {
		return mapper.toEntity(dto);
	}

	public OAuthIntegrationClientDTO convertAccountingIntegrationToClientDTO(OAuthIntegration entity) {
		return mapper.toClientDTO(entity);
	}

	public OAuthTokenDTO convertAccountingIntegrationToTokenDTO(OAuthIntegration entity) {
		OAuthTokenDTO oAuthTokenDTO = mapper.toTokenDTO(entity);
		oAuthTokenDTO.connectedDate = entity.getConnectedDate().toInstant().atOffset(ZoneOffset.UTC);
		oAuthTokenDTO.updatedDate = entity.getUpdatedDate().toInstant().atOffset(ZoneOffset.UTC);
		oAuthTokenDTO.refreshTokenExpiryDate = entity.getRefreshTokenExpiryDate().toInstant().atOffset(ZoneOffset.UTC);
		oAuthTokenDTO.accessTokenExpiryDate = entity.getAccessTokenExpiryDate().toInstant().atOffset(ZoneOffset.UTC);
		return oAuthTokenDTO;
	}

	public OAuthIntegrationDTO convertTokenDTOToIntegrationDTO(OAuthTokenDTO tokenDTO) {
		OAuthIntegrationDTO oAuthIntegrationDTO = mapper.toIntegrationDTOFromTokenDTO(tokenDTO);
		Date defaultDate = DateUtil.addDays(new Date(), -365);
		if(tokenDTO.connectedDate != null) {
			oAuthIntegrationDTO.connectedDate =  new Date(tokenDTO.connectedDate.atZoneSameInstant(ZoneOffset.UTC).toInstant().toEpochMilli());
		} else {
			oAuthIntegrationDTO.connectedDate = defaultDate;
		}

		if(tokenDTO.updatedDate != null) {
			oAuthIntegrationDTO.updatedDate =  new Date(tokenDTO.updatedDate.atZoneSameInstant(ZoneOffset.UTC).toInstant().toEpochMilli());
		} else {
			oAuthIntegrationDTO.updatedDate = defaultDate;
		}

		if(tokenDTO.refreshTokenExpiryDate != null) {
			oAuthIntegrationDTO.refreshTokenExpiryDate =  new Date(tokenDTO.refreshTokenExpiryDate.atZoneSameInstant(ZoneOffset.UTC).toInstant().toEpochMilli());
		} else {
			oAuthIntegrationDTO.refreshTokenExpiryDate = defaultDate;
		}

		if(tokenDTO.accessTokenExpiryDate != null) {
			oAuthIntegrationDTO.accessTokenExpiryDate =  new Date(tokenDTO.accessTokenExpiryDate.atZoneSameInstant(ZoneOffset.UTC).toInstant().toEpochMilli());
		} else {
			oAuthIntegrationDTO.accessTokenExpiryDate = defaultDate;
		}
		return oAuthIntegrationDTO;
	}


	public OAuthIntegrationDTO convertAccountingIntegrationToDTO(OAuthIntegration entity) {
		return mapper.toDTO(entity);
	}

}
