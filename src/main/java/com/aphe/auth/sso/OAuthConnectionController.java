package com.aphe.auth.sso;

import com.aphe.auth.model.core.Login;
import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.model.core.repo.LoginRepository;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.ApheSecurityManager;
import com.aphe.auth.service.ApheUserDetailsService;
import com.aphe.auth.service.UserManager;
import com.aphe.auth.service.dto.*;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.util.CookieStorage;
import com.aphe.common.util.JavaUtil;
import com.aphe.common.util.StringUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public abstract class OAuthConnectionController {

    private final static Logger logger = LogManager.getLogger(OAuthConnectionController.class);

    public static long DEFAULT_REFRESH_EXPIRY = 180L * 24 * 60 * 60L;

    @Autowired
    protected OAuthIntegrationMgr oAuthIntegrationMgr;

    @Autowired
    protected ApheUserDetailsService userDetailsService;

    @Autowired
    protected ApheSecurityManager apheSecurityManager;

    @Autowired
    protected AccountManager accountManager;

    @Autowired
    protected UserManager userManager;

    @Autowired
    LoginRepository loginRepo;

    @Autowired
    CookieStorage cookieStore;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    public abstract SSOPartner getSSOPartner();

    public abstract OAuthIntegrationPartner getOAuthPartner();

    private ApheUserDetails getApheUserDetails() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated()) {
            Object principal = auth.getPrincipal();
            if (principal instanceof ApheUserDetails) {
                return (ApheUserDetails) principal;
            }
        }
        return null;
    }


    protected boolean isLoggedIn() {
        ApheUserDetails userDetails = getApheUserDetails();
        if (userDetails != null) {
            return true;
        }
        return false;
    }

    protected Long getCurrentDoaminId() {
        ApheUserDetails userDetails = getApheUserDetails();
        if (userDetails != null) {
            long domainId = userDetails.getDomainId();
            if (domainId > 0)
                return domainId;
        }
        return null;
    }


    protected List<NameValuePair> getQueryParams(String urlString) {
        URI uri;
        try {
            uri = new URI(urlString);
            final List<NameValuePair> params = URLEncodedUtils.parse(uri.getQuery(), StandardCharsets.UTF_8);
            return params;
        } catch (URISyntaxException e) {
            logger.error("Error parsing query parameters", e);
        }
        return new ArrayList<>();
    }

    protected String updateQueryString(String urlString, List<NameValuePair> queryParams) {
        URI oldUri;
        try {
            oldUri = new URI(urlString);
            //			String newQueryString = URLEncodedUtils.format(queryParams, StandardCharsets.UTF_8);
            StringBuffer newQueryString = new StringBuffer();
            for (NameValuePair pair : queryParams) {
                newQueryString.append(pair.getName()).append("=").append(pair.getValue()).append("&");
            }
            URI newURI = new URI(oldUri.getScheme(), oldUri.getAuthority(), oldUri.getPath(), newQueryString.toString(), oldUri.getFragment());
            return newURI.toString();
        } catch (URISyntaxException e) {
            logger.error("Error parsing query parameters", e);
        }
        return null;
    }

    protected String getNewURL(String urlString, String newPath, List<NameValuePair> newQueryParams) {
        URI oldUri;
        try {
            oldUri = new URI(urlString);
            StringBuffer newQueryString = new StringBuffer();
            for (NameValuePair pair : newQueryParams) {
                newQueryString.append(pair.getName()).append("=").append(pair.getValue()).append("&");
            }
            URI newURI = new URI(oldUri.getScheme(), oldUri.getAuthority(), newPath, newQueryString.toString(), oldUri.getFragment());
            return newURI.toString();
        } catch (URISyntaxException e) {
            logger.error("Error parsing query parameters", e);
        }
        return null;
    }

    protected void saveOAuthConnection(long domainId, String accessToken, long accessTokenExpiresInSeconds, String refreshToken, long refreshTokenExpiresInSeconds, String partnerAccountId,
                                       String xeroConnectionId, String accountName) throws Exception, IOException {

        OAuthIntegrationPartner partner = getOAuthPartner();

        OAuthIntegrationClientDTO oAuthIntegrationClientDTO = oAuthIntegrationMgr.getAccountingIntegrationDTO(domainId, partner.toString());

        OAuthIntegrationDTO currentIntegration = new OAuthIntegrationDTO();
        if (oAuthIntegrationClientDTO == null) {
            currentIntegration.connectedDate = new Date();
            currentIntegration.domainId = getCurrentDoaminId();
            currentIntegration.partner = partner;
        } else {
            currentIntegration.id = oAuthIntegrationClientDTO.id;
            currentIntegration.domainId = oAuthIntegrationClientDTO.domainId;
            currentIntegration.partner = oAuthIntegrationClientDTO.partner;
            currentIntegration.connectedDate = oAuthIntegrationClientDTO.connectedDate;
        }

        currentIntegration.accountId = partnerAccountId;
        currentIntegration.accountName = accountName;
        currentIntegration.accessToken = accessToken;
        currentIntegration.accessTokenExpiresIn = accessTokenExpiresInSeconds;
        currentIntegration.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) currentIntegration.accessTokenExpiresIn);
        currentIntegration.refreshToken = refreshToken;
        currentIntegration.refreshTokenExpiresIn = refreshTokenExpiresInSeconds;
        currentIntegration.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) currentIntegration.refreshTokenExpiresIn);
        currentIntegration.updatedDate = new Date();
        currentIntegration.xeroConnectionId = xeroConnectionId;
        currentIntegration.connected = true;

        oAuthIntegrationMgr.addOrUpdateIntegration(currentIntegration);
    }


    protected UserDTO createUserAndSetContext(String loginId, String firstName, String lastName, String email, boolean emailVerified, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException {
        //Find login by openId;
        Login login = null;
        try {
            login = loginRepo.findByLoginName(loginId);
        } catch (UsernameNotFoundException e) {
            //Ignore this error.
        }
        UserDTO retVal = null;
        if (login == null) {
            //Sign up scenario Construct a payload to create account.
            CreateSSOUser ssoUser = buildSSOUser(-1, loginId, firstName, lastName, email, emailVerified);

            //Call the create SSO account.. that is more smart about sending emails and all.
            retVal = userManager.createSSOUser(ssoUser);
            apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginId, -1, request, response);
        } else {
            //If this is an existing user, make sure this user is not disabled.
            boolean enabled = JavaUtil.booleanValue(login.isEnabled());
            if(!enabled) {
                throw new ApheDataValidationException();
            }

            apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginId, -1, request, response);
            long userId = userManager.getLoggedInUserId();
            retVal = userManager.getUser(Long.toString(userId));

            //If FN, LN, EMAIL changed.. update our systems.
            boolean b = !firstName.equalsIgnoreCase(retVal.firstName);
            boolean b1 = !lastName.equalsIgnoreCase(retVal.lastName);
            boolean b2 = !email.equalsIgnoreCase(retVal.email);
            if (b || b1 || b2) {
                //Somethings has changed.. let's update the user.
                CreateSSOUser ssoUser = buildSSOUser(login.getUser().getId(), loginId, firstName, lastName, email, emailVerified);
                retVal = userManager.updateSSOUser(ssoUser);
            }
        }
        return retVal;
    }

    @NotNull
    private CreateSSOUser buildSSOUser(long userId, String loginId, String firstName, String lastName, String email, boolean emailVerified) {
        CreateSSOUser ssoUser = new CreateSSOUser();
        ssoUser.id = userId;
        ssoUser.email = email;
        ssoUser.isEmailConfirmed = emailVerified;
        ssoUser.firstName = firstName;
        ssoUser.lastName = lastName;
        ssoUser.loginName = loginId;
        ssoUser.ssoPartner = getSSOPartner();
        return ssoUser;
    }

    protected DomainDTO createDomainAndSetContext(
            UserDTO userDTO,
            String accountId, String domainName,
            boolean isAccountant,
            boolean isClientAccount,
            long parentDomainId,
            HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException, ApheException {

        //See if any of this user domains are connected to this realm
        DomainDTO theDomain = null;
        List<DomainDTO> domains = isClientAccount ? accountManager.getClients(true) : accountManager.getDomains(true);

        //Sort by the active flag first.
        domains.sort((d1, d2) -> {
            return Boolean.compare(d2.isActive, d1.isActive);
        });

        for (DomainDTO dto : domains) {
            OAuthIntegrationClientDTO intDTO = oAuthIntegrationMgr.getAccountingIntegrationDTO(dto.id, getOAuthPartner().toString());
            if (intDTO != null && intDTO.accountId != null && intDTO.accountId.equalsIgnoreCase(accountId)) {
                theDomain = dto;
                break;
            }
        }

        if (theDomain == null) {
            //Connecting a new domain.
            CreateDomainDTO createDomainDTO = new CreateDomainDTO();
            createDomainDTO.domainType = (!isClientAccount && isAccountant) ? DomainType.A : DomainType.B;
            createDomainDTO.firstName = userDTO.firstName;
            createDomainDTO.lastName = userDTO.lastName;
            createDomainDTO.name = domainName;
            if (isClientAccount) {
                theDomain = accountManager.addClient(parentDomainId, createDomainDTO, request, response);
            } else {
                theDomain = accountManager.addDomain(createDomainDTO, request, response);
            }

        } else {
            //If client make the accountClientRelation active. If not make the domain active.
            if (isClientAccount) {
                if (!theDomain.isActive()) {
                    accountManager.toggleClientActiveStatus(parentDomainId, theDomain.id, true);
                }
            } else {
                if (!theDomain.isActive()) {
                    accountManager.toggleDomainActiveStatus(theDomain.id, true);
                }
            }
            //Reconnecting account again... update the auth context.
            apheSecurityManager.authenticateUserAndSetSessionAndCookies(userDTO.loginName, theDomain.id, request, response);

        }
        return theDomain;
    }

    /**
     * Looks through all the domain this user has access to, the clients this user has access to, find the one that is connected to this partnerId.
     *
     * @param accountId
     * @return
     */
    protected long getDomainIdByPartnerId(String accountId, String loginName, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException, ApheException {
        DomainDTO theDomain = null;
        List<DomainDTO> domains = accountManager.getDomains(true);
        theDomain = findDomainByPartnerId(domains, accountId);

        boolean isClientAccount = false;
        DomainDTO parentDomain = null;
        if (theDomain == null) {
            //See any of the accessible clients is connected to this domain.. for this to happen, switch the context to that parent domain first,
            //get accessible clients by this user and return that client domainId.
            for (DomainDTO dto : domains) {
                if (dto.domainType != DomainType.A) {
                    continue;
                }

                if (dto.domainType == DomainType.A) {
                    apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, dto.id, request, response);
                    List<DomainDTO> clientDomains = accountManager.getClients(true);
                    theDomain = findDomainByPartnerId(clientDomains, accountId);
                }
                if (theDomain != null) {
                    isClientAccount = true;
                    parentDomain = dto;
                    break;
                } else {
                    //We haven't found a client in this accountant. So, let's log out of that.
                    apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, -1L, request, response);
                }
            }
        }

        if (theDomain != null) {
            //If client make the accountClientRelation active. If not make the domain active.
            if (isClientAccount) {
                if (!theDomain.isActive()) {
                    accountManager.toggleClientActiveStatus(parentDomain.id, theDomain.id, true);
                }
            } else {
                if (!theDomain.isActive()) {
                    accountManager.toggleDomainActiveStatus(theDomain.id, true);
                }
            }
            return theDomain.id;
        }
        return -1;
    }

    private DomainDTO findDomainByPartnerId(List<DomainDTO> domains, String accountId) {
        DomainDTO theDomain = null;
        domains.sort((d1, d2) -> {
            return Boolean.compare(d2.isActive, d1.isActive);
        });
        for (DomainDTO dto : domains) {
            OAuthIntegrationClientDTO intDTO = oAuthIntegrationMgr.getAccountingIntegrationDTO(dto.id, getOAuthPartner().toString());
            if (intDTO != null && intDTO.connected == true && intDTO.accountId != null && intDTO.accountId.equalsIgnoreCase(accountId)) {
                theDomain = dto;
                break;
            }
        }
        return theDomain;
    }

    protected void processConnectionRequest(HttpServletRequest request, HttpServletResponse response, OAuthConnectionException oAuthConnectionException, String authorizationURL) throws IOException {

        String successURL = request.getParameter("onSuccess");
        String failureURL = request.getParameter("onFailure");
        failureURL = failureURL == null ? successURL : failureURL;
        cookieStore.saveItem(response, "oAuth_onSuccess", successURL);
        cookieStore.saveItem(response, "oAuth_onFailure", failureURL);

        if (oAuthConnectionException != null) {
            List<NameValuePair> currentParams = getQueryParams(successURL);
            JSONObject paramsObject = new JSONObject();
            NameValuePair paramsQueryParam = currentParams.stream().filter(e -> e.getName().equals("params")).findFirst().orElse(null);
            if (paramsQueryParam != null) {
                paramsObject = new JSONObject(URLDecoder.decode(paramsQueryParam.getValue(), "UTF-8"));
            }

            String errorMessage = null;
            if (oAuthConnectionException.getErrorType() == OAuthConnectionException.ERROR_TYPE.USER_ERROR) {
                errorMessage = "An error occurred while connecting to your " + getOAuthPartner().getCompany() + " account. " + getOAuthPartner().getCompany() + ": " + oAuthConnectionException.getErrorMessage();
            } else if (oAuthConnectionException.getErrorType() == OAuthConnectionException.ERROR_TYPE.SYSTEM_ERROR) {
                errorMessage = "An error occurred while connecting to your " + getOAuthPartner().getCompany() + " account. " + getOAuthPartner().getCompany() + ": " + oAuthConnectionException.getErrorMessage() + " Please try again later.";
            } else if (oAuthConnectionException.getErrorType() == OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR) {
                errorMessage = "An unexpected error occurred when connecting to your " + getOAuthPartner().getCompany() + " account. Please try again later or contact us";
            }

            logger.error("Error handling connectionRequest errorMessage={} systemErrorMessage={}", errorMessage, oAuthConnectionException.getErrorMessage());

            paramsObject.put("ssoStatus", "Fail");
            paramsObject.put("errorMessage", errorMessage);
            paramsObject.put("errorCode", oAuthConnectionException.getErrorType());
            String paramsString = paramsObject.toString();
            redirect(response, failureURL, currentParams, paramsObject);
        } else {
            response.sendRedirect(authorizationURL);
        }
    }


    protected void processCallbackResponse(HttpServletRequest request, HttpServletResponse response, OAuthConnectionException oAuthConnectionException) throws IOException {

        String onSuccessParam = request.getParameter("onSuccess");
        String onFailureParam = request.getParameter("onFailure");

        onSuccessParam = onSuccessParam == null ? cookieStore.get(request, "oAuth_onSuccess") : onSuccessParam;
        onFailureParam = onFailureParam == null ? cookieStore.get(request, "oAuth_onFailure") : onFailureParam;

        String successURL = StringUtil.isNotEmpty(onSuccessParam) ? onSuccessParam : getRedirectURL(request, "/");
        String failureURL = StringUtil.isNotEmpty(onFailureParam) ? onFailureParam : getRedirectURL(request, "/");

        List<NameValuePair> allParams = getQueryParams(successURL);
        JSONObject paramsObject = new JSONObject();
        NameValuePair paramsQueryParam = allParams.stream().filter(e -> e.getName().equals("params")).findFirst().orElse(null);
        if (paramsQueryParam != null) {
            paramsObject = new JSONObject(URLDecoder.decode(paramsQueryParam.getValue(), "UTF-8"));
        }

        if (oAuthConnectionException != null) {
            String errorMessage = buildCallbackError(oAuthConnectionException);
            logger.error("Error handling connectionCallBack errorMessage={} systemErrorMessage={}", errorMessage, oAuthConnectionException.getErrorMessage());
//            paramsObject.put("ssoStatus", "Fail");
//            paramsObject.put("errorMessage", errorMessage);
//            paramsObject.put("errorCode", oAuthConnectionException.getErrorType().toString());
            sendOAuthPopupResponse(response, "Fail", oAuthConnectionException.getErrorType().toString(), errorMessage);
        } else {
//            paramsObject.put("ssoStatus", "Success");
//            paramsObject.remove("errorMessage");
//            paramsObject.remove("errorCode");
            sendOAuthPopupResponse(response, "Success", null, null);
        }
    }

    protected String buildCallbackError(OAuthConnectionException oAuthConnectionException) {
        String errorMessage = null;
        if (oAuthConnectionException.getErrorType() == OAuthConnectionException.ERROR_TYPE.USER_ERROR) {
            errorMessage = "An error occurred while connecting to your " + getOAuthPartner().getCompany() + " account. Response from " + getOAuthPartner().getCompany() + ": " + oAuthConnectionException.getErrorMessage();
        } else if (oAuthConnectionException.getErrorType() == OAuthConnectionException.ERROR_TYPE.SYSTEM_ERROR) {
            errorMessage = "An error occurred while connecting to your " + getOAuthPartner().getCompany() + " account. Please try again later. Response from " + getOAuthPartner().getCompany() + ": " + oAuthConnectionException.getErrorMessage();
        } else if (oAuthConnectionException.getErrorType() == OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR) {
            errorMessage = "An unexpected error occurred when connecting to your " + getOAuthPartner().getCompany() + " account. Please try again later or contact us";
        } else {
            errorMessage = oAuthConnectionException.getErrorMessage();
        }
        return errorMessage;
    }

    protected void sendOAuthPopupResponse(HttpServletResponse response, String oAuthStatus, String errorCode, String errorMessage) throws IOException {
        String html = """
        <!DOCTYPE html>
        <html>
        <head><title>OAuth Callback</title></head>
        <body>
        <script>
          const status = {
            connectionStatus:'%s',
            errorCode:'%s',
            errorMessage: '%s'
          }
          window.opener.postMessage(status, '*');
//          window.close();
        </script>
        <p>Completing OAuth...</p>
        </body>
        </html>
        """.formatted(oAuthStatus, errorCode, errorMessage);

        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(html);
    }


    public final String HTTPS_PROTO = "https";

    public String getRedirectURL(HttpServletRequest request, String path) {
        // Assume default https scheme.
        String url = request.getRequestURL().toString();
        String requestBaseURL = url.substring(request.getScheme().length(), url.length() - request.getRequestURI().length()) + request.getContextPath();
        String redirectURL = new StringBuffer().append(HTTPS_PROTO).append(requestBaseURL).append(path).toString();
        return redirectURL;
    }


    private void redirect(HttpServletResponse response, String redirectURL, List<NameValuePair> allParams, JSONObject paramsObject) throws IOException {
        String paramsString = paramsObject.toString();
        List<NameValuePair> newParams = allParams.stream().filter(m -> !(m.getName().equalsIgnoreCase("params"))).collect(Collectors.toList());
        newParams.add(new BasicNameValuePair("params", paramsString));
        String newURL = updateQueryString(redirectURL, newParams);
        response.sendRedirect(newURL);
    }

    protected void checkUserLoggedIn() throws OAuthConnectionException {
        boolean isLoggedIn = isLoggedIn();
        if (!isLoggedIn) {
            String systemErrorMessage = "You are not logged into a 1099SmartFile account. Please login and select a 1099SmartFile account before connecting to " + getOAuthPartner().getCompany() + ".";
            throw new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.USER_ERROR, systemErrorMessage);
        }
    }

    protected void checkDomainSelected() throws OAuthConnectionException {
        Long loggedInDomainId = getCurrentDoaminId();
        if (loggedInDomainId == null) {
            String systemErrorMessage = "You are not logged into a 1099SmartFile account. Please select a 1099SmartFile account before connecting to " + getOAuthPartner().getCompany() + ".";
            throw new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.USER_ERROR, systemErrorMessage);
        }
    }

    protected void handleOrgsSize(List<? extends Object> orgs) throws OAuthConnectionException {
        if (orgs == null || orgs.size() == 0){
            String systemErrorMessage = "There are no " + getOAuthPartner().getOrgsRef() + " associated with this " + getOAuthPartner().getCompany() +  " login.";
            throw new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.USER_ERROR, systemErrorMessage);
        }
    }

    protected void validateCSRFToken(HttpServletRequest request) throws OAuthConnectionException {
        String state = request.getParameter("state");
        String stateCSRF = null;
        try {
            String stateString = URLDecoder.decode(state, "UTF-8");
            JSONObject stateJSON = new JSONObject(stateString);
            stateCSRF = (String) stateJSON.get("csrfToken");
        } catch (Exception e) {
        }

        boolean isValid = isCsrfTokenValid(stateCSRF);
        if (!isValid) {
            throw new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, "Invalid CSRF Token");
        }
    }

    protected void validateOAuthErrorCode(HttpServletRequest request) throws OAuthConnectionException {
        String errorCode = request.getParameter("error");
        if (errorCode != null) {
            if ("access_denied".equals(errorCode)) {
                throw new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.USER_ERROR, "Access was denied.");
            } else {
                throw new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, errorCode);
            }

        }
    }


    protected String buildStateString(HttpServletRequest request) throws UnsupportedEncodingException {
        String csrf = UUID.randomUUID().toString();
        saveCsrfTokenToRedis(csrf);
        JSONObject stateObject = new JSONObject();
        stateObject.put("csrfToken", csrf);
        String stateString = URLEncoder.encode(stateObject.toString(), "UTF-8");
        return stateString;
    }

    protected void saveCsrfTokenToRedis(String csrf) {
        String redisKey = "oauth:csrf:" + csrf;
        redisTemplate.opsForValue().set(redisKey, "valid", Duration.ofMinutes(5));
    }

    protected boolean isCsrfTokenValid(String csrf) {
        //check for null
        if (csrf == null || csrf.isEmpty()) {
            return false;
        }
        String redisKey = "oauth:csrf:" + csrf;
        Object value = redisTemplate.opsForValue().get(redisKey);
        if (value != null) {
            return true;
        }
        return false;
    }
}
