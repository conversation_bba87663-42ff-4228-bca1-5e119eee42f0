package com.aphe.auth.sso.wave;

import com.aphe.wavesdk.WaveClient;
import com.aphe.wavesdk.WaveResponse;
import com.aphe.wavesdk.model.Business;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Component
public class WaveUtil {

    public static final String BIZ_QUERY = "queries/wave/businesses.graphql";

    @Autowired
    WaveClient waveClient;

    public List<Business> getBusinesses(String baseURL, String accessToken) throws Exception {
        List<Business> allBusinesses = new ArrayList<>();
        String queryString = getQueryString(BIZ_QUERY);

        int page = 1;
        int pageSize = 100;
        boolean hasMorePages = false;
        do {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("page", page);
            jsonObject.put("pageSize", pageSize);
            String variablesString = jsonObject.toString();

            WaveResponse waveResponse = waveClient.getWaveResponse(baseURL, accessToken, queryString, variablesString);
            List<Business> businessList = waveResponse.getData().getBusinesses();
            allBusinesses.addAll(businessList);
            hasMorePages = waveResponse.getData().hasMoreBusinesses();
            page++;
        } while (hasMorePages);
        return allBusinesses;
    }


    @NotNull
    private String getQueryString(String queryFileName) throws IOException {
        String queryString = "";
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream(queryFileName);
        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line);
                sb.append('\n');
            }
            queryString = sb.toString();
        }
        return queryString;
    }


}
