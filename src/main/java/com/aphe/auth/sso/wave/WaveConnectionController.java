package com.aphe.auth.sso.wave;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.sso.OAuthConnectionController;
import com.aphe.auth.sso.OAuthConnectionException;
import com.aphe.wavesdk.WaveException;
import com.aphe.wavesdk.model.Business;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.DataStoreFactory;
import com.google.api.client.util.store.MemoryDataStoreFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Controller
public class WaveConnectionController extends OAuthConnectionController {

    final static Logger logger = LogManager.getLogger(WaveConnectionController.class);

    @Value("${aphe.wave.clientId}")
    private String clientId;

    @Value("${aphe.wave.clientSecret}")
    private String clientSecret;

    @Value("${aphe.wave.redirectPath}")
    private String redirectPath;

    @Value("${aphe.wave.tokenServerURL}")
    private String tokenServerURL;

    @Value("${aphe.wave.authServerURL}")
    private String authServerURL;

    @Value("${aphe.wave.baseURL}")
    private String baseURL;

    @Autowired
    WaveUtil waveUtil;

    private static ArrayList<String> accountingScopes = new ArrayList<String>();

    static {
        accountingScopes.add("vendor:*");
        accountingScopes.add("transaction:*");
        accountingScopes.add("invoice:*");
        accountingScopes.add("business:*");
        accountingScopes.add("customer:*");
        accountingScopes.add("account:*");
    }

    public WaveConnectionController() {
        super();
    }


    @Override
    public SSOPartner getSSOPartner() {
        return null;
    }

    @Override
    public OAuthIntegrationPartner getOAuthPartner() {
        return OAuthIntegrationPartner.Wave;
    }

    @RequestMapping("/access/connectToWave")
    protected void connectToWave(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String authorizationURL = null;

        OAuthConnectionException oAuthConnectionException = null;

        try {
            //Validations
            checkUserLoggedIn();
            checkDomainSelected();

            String redirectURL = new StringBuffer(getRedirectURL(request, redirectPath)).toString();
            String stateString = buildStateString(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(), GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    accountingScopes)
                    .setDataStoreFactory(DATA_STORE_FACTORY)
                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .build();

            authorizationURL = codeFlow.newAuthorizationUrl()
                    .setClientId(clientId)
                    .setState(stateString)
                    .setRedirectUri(redirectURL).build();


        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            processConnectionRequest(request, response, oAuthConnectionException, authorizationURL);
        }

    }

    @RequestMapping("/access/waveCallback")
    protected void waveCallback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        OAuthConnectionException oAuthConnectionException = null;

        try {
            checkUserLoggedIn();
            checkDomainSelected();
            validateCSRFToken(request);
            validateOAuthErrorCode(request);

            DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
            GoogleAuthorizationCodeFlow codeFlow = new GoogleAuthorizationCodeFlow.Builder(
                    new NetHttpTransport(),
                    GsonFactory.getDefaultInstance(),
                    clientId, clientSecret,
                    accountingScopes)
                    .setDataStoreFactory(DATA_STORE_FACTORY)
                    .setAccessType("offline")
                    .setAuthorizationServerEncodedUrl(authServerURL)
                    .setTokenServerUrl(new GenericUrl(tokenServerURL))
                    .build();

            String authCode = request.getParameter("code");
            String redirectURL = getRedirectURL(request, redirectPath);

            GoogleTokenResponse tokenResponse = codeFlow.newTokenRequest(authCode).setRedirectUri(redirectURL).execute();
            String accessToken = (String) tokenResponse.getAccessToken();

            List<Business> businessList = waveUtil.getBusinesses(baseURL, accessToken);

            handleOrgsSize(businessList);

            String accountId = null;
            String domainName = null;
            if (businessList != null && businessList.size() == 1) {
                Business org = businessList.get(0);
                accountId = org.getId();
                domainName = org.getName();
            }

            String loggedInDomainId = getCurrentDoaminId();
            saveOAuthConnectionFromTokenResponse(Long.parseLong(loggedInDomainId), tokenResponse, accountId, domainName);

        } catch (Throwable e) {
            if (e instanceof OAuthConnectionException) {
                oAuthConnectionException = (OAuthConnectionException) e;
            } else if (e instanceof WaveException) {
                oAuthConnectionException = buildOAuthException((WaveException) e);
            } else {
                oAuthConnectionException = new OAuthConnectionException(OAuthConnectionException.ERROR_TYPE.UNEXPECTED_ERROR, e.getMessage());
            }
        } finally {
            String errorMessage = null;
            processCallbackResponse(request, response, oAuthConnectionException);
        }
    }

    @NotNull
    private OAuthConnectionException buildOAuthException(WaveException e) {
        boolean isAuthError = false;
        String errorMessage = isAuthError ? "Authorization error when connecting to " + getOAuthPartner().getCompany() + "." : "System error when connecting to " + getOAuthPartner().getCompany() + ".";
        OAuthConnectionException.ERROR_TYPE errorType = OAuthConnectionException.ERROR_TYPE.SYSTEM_ERROR;
        return new OAuthConnectionException(errorType, errorMessage);
    }


    private void saveOAuthConnectionFromTokenResponse(long domainId, GoogleTokenResponse tokenResponse, String realmId, String accountName) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        String refreshToken = tokenResponse.getRefreshToken();
        Long accessTokenExpiresInSeconds = tokenResponse.getExpiresInSeconds();
        Long refreshTokenExpiresInSeconds = DEFAULT_REFRESH_EXPIRY;
        saveOAuthConnection(domainId, accessToken, accessTokenExpiresInSeconds, refreshToken, refreshTokenExpiresInSeconds, realmId, null, accountName);
    }

}