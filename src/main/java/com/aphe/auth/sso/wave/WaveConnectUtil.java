package com.aphe.auth.sso.wave;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.auth.sso.OAuthConnectionUtil;
import com.aphe.auth.sso.TokenRevokedException;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.StringUtil;
import com.aphe.wavesdk.WaveAuthenticationException;
import com.aphe.wavesdk.model.Business;
import com.google.api.client.googleapis.auth.oauth2.GoogleRefreshTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

@Component
public class WaveConnectUtil extends OAuthConnectionUtil {

    @Value("${aphe.wave.clientId}")
    private String clientId;

    @Value("${aphe.wave.clientSecret}")
    private String clientSecret;

    @Value("${aphe.wave.revokeTokenURL}")
    private String revokeTokenURL;

    @Value("${aphe.wave.tokenServerURL}")
    private String tokenServerURL;

    @Value("${aphe.wave.authServerURL}")
    private String authServerURL;

    @Value("${aphe.wave.redirectPath}")
    private String redirectPath;

    @Value("${aphe.wave.baseURL}")
    private String baseURL;

    @Autowired
    private WaveUtil waveUtil;

    final static Logger logger = LogManager.getLogger(WaveConnectUtil.class);

    public boolean revokeToken(OAuthIntegrationDTO dto) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            String resourceUrl = revokeTokenURL;
            MultiValueMap<String, String> formValues = new LinkedMultiValueMap();
            formValues.add("client_id", clientId);
            formValues.add("client_secret", clientSecret);
            formValues.add("token", dto.refreshToken);
            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formValues, headers);
            ResponseEntity<String> response = restTemplate.exchange(resourceUrl, HttpMethod.POST, entity, String.class);
            if (response.getStatusCode() == HttpStatus.OK) {
                return true;
            } else {
                logger.error("Error revoking token", response);
                return false;
            }
        } catch (Exception e1) {
            logger.error("Error revoking token", e1);
            return false;
        }
    }

    @Override
    public OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
        OAuthIntegrationDTO updatedDTO = null;
        boolean isAccessTokenValid = true;
        try {
            List<Business> orgList = waveUtil.getBusinesses(baseURL, dto.accessToken);
            if (orgList != null && orgList.size() > 0) {
                Business org = orgList.get(0);
            }
        } catch (Exception e) {
            if (e instanceof WaveAuthenticationException) {
                isAccessTokenValid = false;
            }
        }

        if (!isAccessTokenValid) {
            updatedDTO = refreshTokens(dto);
            try {
                List<Business> orgList = waveUtil.getBusinesses(baseURL, dto.accessToken);
                if (orgList != null && orgList.size() > 0) {
                    Business org = orgList.get(0);
                }
            } catch (Exception e) {
                if (e instanceof WaveAuthenticationException) {
                    throw new TokenRevokedException();
                }
            }
        }
        return updatedDTO;
    }

    public boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto) {
        return isAccessTokenExpiringDefault(dto);
    }

    public OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
        try {
            GoogleRefreshTokenRequest refreshTokenRequest = new GoogleRefreshTokenRequest(
                    new NetHttpTransport(), GsonFactory.getDefaultInstance(),
                    dto.refreshToken, clientId, clientSecret);
            refreshTokenRequest.setGrantType("refresh_token");
            refreshTokenRequest.setTokenServerUrl(new GenericUrl(tokenServerURL));
            Map<String, Object> customParams = new HashMap<>();
            customParams.put("redirect_uri", authServerURL + redirectPath);
            refreshTokenRequest.setUnknownKeys(customParams);

            GoogleTokenResponse tokenResponse = refreshTokenRequest.execute();
            dto.accessToken = tokenResponse.getAccessToken();
            dto.accessTokenExpiresIn = tokenResponse.getExpiresInSeconds();
            dto.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.accessTokenExpiresIn);
            dto.refreshToken = tokenResponse.getRefreshToken();
            dto.refreshTokenExpiresIn = WaveConnectionController.DEFAULT_REFRESH_EXPIRY;
            dto.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.refreshTokenExpiresIn);
            dto.updatedDate = new Date();
            return dto;
        } catch (IOException e) {
            logger.error("Error refreshing token", e);
            throw new TokenRevokedException();
        }
    }

    public String getAccountName(OAuthIntegrationDTO dto) {
        try {
            List<Business> orgList = waveUtil.getBusinesses(baseURL, dto.accessToken);
            if (orgList != null && orgList.size() > 0) {
                for (Business org : orgList) {
                    if (org.getId().equalsIgnoreCase(dto.accountId)) {
                        String bizName = org.getName();
                        if (StringUtil.isEmpty(bizName)) {
                            bizName = "No Business Name";
                        }
                        return bizName;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error getting account name", e);
        }
        return null;
    }

    public List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto) {
        List<PartnerAccountDTO> partnerAccountDTOS = new ArrayList<>();
        try {
            List<Business> businessList = waveUtil.getBusinesses(baseURL, dto.accessToken);
            if (businessList != null && businessList.size() > 0) {
                for (Business org : businessList) {
                    String bizName = org.getName();
                    if (StringUtil.isEmpty(bizName)) {
                        bizName = "No Business Name";
                    }
                    partnerAccountDTOS.add(new PartnerAccountDTO(org.getId(), bizName));
                }
            }
        } catch (Exception e) {
            logger.error("Error getting partner accounts", e);
        }
        return partnerAccountDTOS;
    }

}
