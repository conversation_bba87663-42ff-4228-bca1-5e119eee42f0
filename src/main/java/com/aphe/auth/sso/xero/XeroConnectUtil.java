package com.aphe.auth.sso.xero;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthIntegrationDTO;
import com.aphe.auth.service.dto.PartnerAccountDTO;
import com.aphe.auth.sso.OAuthConnectionUtil;
import com.aphe.auth.sso.TokenRevokedException;
import com.aphe.common.util.StringUtil;
import com.google.api.client.auth.oauth2.RefreshTokenRequest;
import com.google.api.client.auth.oauth2.TokenResponse;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.http.BasicAuthentication;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpRequestFactory;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.xero.api.ApiClient;
import com.xero.api.XeroForbiddenException;
import com.xero.api.XeroUnauthorizedException;
import com.xero.api.client.AccountingApi;
import com.xero.api.client.IdentityApi;
import com.xero.models.accounting.Organisation;
import com.xero.models.accounting.Organisations;
import com.xero.models.identity.Connection;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Component
public class XeroConnectUtil extends OAuthConnectionUtil {

	@Value("${aphe.xero.clientId}")
	private String clientId;

	@Value("${aphe.xero.clientSecret}")
	private String clientSecret;

	@Value("${aphe.xero.tokenServerURL}")
	private String tokenServerURL;

	final static Logger logger = LogManager.getLogger(XeroConnectUtil.class);

	DecimalFormat df = new DecimalFormat("0.00");

	public boolean revokeToken(OAuthIntegrationDTO dto) {

		if(dto.xeroConnectionId == null) {
			//We don't have the xero connection id for wahtever reason, so we can't revoke the token anyways. So, we'll just return true implying that the token is revoked.
			return true;
		}

		HttpTransport httpTransport = new NetHttpTransport();
		JsonFactory jsonFactory = GsonFactory.getDefaultInstance();
		GoogleCredential credential = new GoogleCredential.Builder().setTransport(httpTransport).setJsonFactory(jsonFactory).setClientSecrets(clientId, clientSecret).build();
		String accessToken = dto.accessToken;
		String refreshToken = dto.refreshToken;
		Long accessTokenExpiresInSeconds = dto.accessTokenExpiresIn;
		credential.setAccessToken(accessToken);
		credential.setRefreshToken(refreshToken);
		credential.setExpiresInSeconds(accessTokenExpiresInSeconds);

		HttpTransport transport = new NetHttpTransport();
		HttpRequestFactory requestFactory = transport.createRequestFactory(credential);

		ApiClient defaultClient = new ApiClient("https://api.xero.com", null, null, null, requestFactory);
		IdentityApi idApi = new IdentityApi(defaultClient);
		try {
			idApi.deleteConnection(accessToken, UUID.fromString(dto.xeroConnectionId));
		} catch (Exception e1) {
			logger.error("Error revoking token. message=" + e1.getMessage(), e1);
			return false;
		}
		return true;

	}

	@Override
	public OAuthIntegrationDTO hardRefreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {

		OAuthIntegrationDTO updatedDTO = null;
		String realmId = dto.accountId;
		String accessToken = dto.accessToken;

		boolean isAccessTokenValid = true;

		ApiClient defaultClient = new ApiClient();
		AccountingApi accountingApi = AccountingApi.getInstance(defaultClient);
		try {
			Organisations orgs = accountingApi.getOrganisations(accessToken, realmId);
			List<Organisation> organisations = orgs.getOrganisations();
			if (organisations != null && organisations.size() > 0) {
				organisations.get(0);
			}
		} catch (Exception e) {
			isAccessTokenValid = false;
			if (e instanceof XeroForbiddenException) {
				throw new TokenRevokedException();
			}
		}

		if (!isAccessTokenValid) {
			updatedDTO = refreshTokens(dto);
			
			//Try with the new token...
			try {
				Organisations orgs = accountingApi.getOrganisations(updatedDTO.accessToken, realmId);
				List<Organisation> organisations = orgs.getOrganisations();
				if (organisations != null && organisations.size() > 0) {
					organisations.get(0);
				}
			} catch (Exception e) {
				if (e instanceof XeroForbiddenException || e instanceof XeroUnauthorizedException) {
					throw new TokenRevokedException();
				}
			}
		}
		return updatedDTO;

	}

	public boolean isTokenValid(OAuthIntegrationDTO dto) {
		String companyName = getAccountName(dto);
		if (companyName != null)
			return true;
		return false;
	}

	public boolean isAccessTokenExpiring(OAuthIntegrationClientDTO dto) {
		return isAccessTokenExpiringDefault(dto);
	}

	public OAuthIntegrationDTO refreshTokens(OAuthIntegrationDTO dto) throws TokenRevokedException {
		try {
			TokenResponse tokenResponse = new RefreshTokenRequest(new NetHttpTransport(), GsonFactory.getDefaultInstance(), new GenericUrl(tokenServerURL), dto.refreshToken)
					.setClientAuthentication(new BasicAuthentication(this.clientId, this.clientSecret)).execute();

			dto.accessToken = tokenResponse.getAccessToken();
			dto.accessTokenExpiresIn = tokenResponse.getExpiresInSeconds();
			dto.accessTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.accessTokenExpiresIn);
			dto.refreshToken = tokenResponse.getRefreshToken();
			dto.refreshTokenExpiresIn = XeroConnectionController.DEFAULT_REFRESH_EXPIRY;
			dto.refreshTokenExpiryDate = DateUtils.addSeconds(new Date(), (int) dto.refreshTokenExpiresIn);
			dto.updatedDate = new Date();
			return dto;
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw new TokenRevokedException();
		}
	}

	public String getAccountName(OAuthIntegrationDTO dto) {
		ApiClient defaultClient = new ApiClient();
		AccountingApi accountingApi = AccountingApi.getInstance(defaultClient);
		try {
			Organisations orgs = accountingApi.getOrganisations(dto.accessToken, dto.accountId);
			List<Organisation> organisations = orgs.getOrganisations();
			if (organisations != null && organisations.size() > 0) {
				for(Organisation org : organisations) {
					if(org.getOrganisationID().toString().equalsIgnoreCase(dto.accountId)) {
						String bizName = org.getLegalName();
						if(StringUtil.isEmpty(bizName)) {
							bizName = "No Business Name";
						}
						return bizName;
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error getting account name from Xero", e);
		}
		return null;
	}


	public List<PartnerAccountDTO> getPartnerAccounts(OAuthIntegrationDTO dto) {
		List<PartnerAccountDTO> partnerAccountDTOS = new ArrayList<>();

		ApiClient defaultClient = new ApiClient("https://api.xero.com", null, null, null, null);
		IdentityApi identityApi = IdentityApi.getInstance(defaultClient);
		try {
			List<Connection> connections = identityApi.getConnections(dto.accessToken, null);
			if (connections != null && connections.size() > 0) {
				for(Connection connection : connections) {
					String accountId = connection.getTenantId().toString();
					String bizName = connection.getTenantName();
					if(StringUtil.isEmpty(bizName)) {
						bizName = "No Business Name";
					}
					partnerAccountDTOS.add(new PartnerAccountDTO(accountId, bizName));
				}
			}
		} catch (Exception e) {
			logger.error("Error getting partner account from Xero", e);
		}
		return partnerAccountDTOS;
	}


}
