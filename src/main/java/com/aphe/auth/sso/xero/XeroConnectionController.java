package com.aphe.auth.sso.xero;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.auth.model.core.SSOPartner;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.auth.service.dto.UserDTO;
import com.aphe.auth.sso.OAuthConnectionController;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.CookieStorage;
import com.aphe.common.util.PartnerUtil;
import com.aphe.common.util.StringUtil;
import com.google.api.client.auth.oauth2.AuthorizationCodeFlow;
import com.google.api.client.auth.oauth2.BearerToken;
import com.google.api.client.auth.oauth2.ClientParametersAuthentication;
import com.google.api.client.auth.oauth2.TokenResponse;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpRequestFactory;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.DataStoreFactory;
import com.google.api.client.util.store.MemoryDataStoreFactory;
import com.xero.api.ApiClient;
import com.xero.api.client.IdentityApi;
import com.xero.models.identity.Connection;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

@Controller
public class XeroConnectionController extends OAuthConnectionController {

    final static Logger logger = LogManager.getLogger(XeroConnectionController.class);

    public static long DEFAULT_REFRESH_EXPIRY = 59 * 24 * 60 * 60L;

    @Value("${aphe.xero.clientId}")
    private String clientId;

    @Value("${aphe.xero.clientSecret}")
    private String clientSecret;

    @Value("${aphe.xero.redirectPath}")
    private String redirectPath;

    @Value("${aphe.xero.tokenServerURL}")
    private String tokenServerURL;

    @Value("${aphe.xero.authServerURL}")
    private String authServerURL;

    @Value("${aphe.xero.baseURL}")
    private String baseURL;

    private static ArrayList<String> idScopes = new ArrayList<String>();
    private static ArrayList<String> accounintingScopes = new ArrayList<String>();

    static {
        idScopes.add("openid");
        idScopes.add("email");
        idScopes.add("profile");
        idScopes.add("offline_access");

        accounintingScopes.add("offline_access");
//        accounintingScopes.add("accounting.transactions");
        accounintingScopes.add("accounting.reports.read");
//        accounintingScopes.add("accounting.reports.tenninetynine.read");
        accounintingScopes.add("accounting.settings");
        accounintingScopes.add("accounting.contacts");
//        accounintingScopes.add("accounting.journals.read");
//        accounintingScopes.add("accounting.attachments");
    }

    @Autowired
    PartnerUtil partnerUtil;

    @Autowired
    CookieStorage cookieStore;

    @Autowired
    private JwtUtil jwtUtil;

    final NetHttpTransport HTTP_TRANSPORT = new NetHttpTransport();
    final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();

    final String secretState = "secret" + new Random().nextInt(999_999);

    public XeroConnectionController() {
        super();
    }

    @Override
    public SSOPartner getSSOPartner() {
        return SSOPartner.Xero;
    }

    @Override
    public OAuthIntegrationPartner getOAuthPartner() {
        return OAuthIntegrationPartner.Xero;
    }

    @RequestMapping("/access/signUpWithXero")
    protected void signUpWithXero(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        signInOrSignUp(request, response, true);
    }

    @RequestMapping("/access/getAppWithXero")
    protected void signInWithIntuit(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        signInOrSignUp(request, response, true);
    }

    @RequestMapping("/access/signInWithXero")
    protected void signInWithXero(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        signInOrSignUp(request, response, false);
    }

    private void signInOrSignUp(HttpServletRequest request, HttpServletResponse response, boolean getApp) throws IOException {
        String successURL = request.getParameter("onSuccess");
        String failureURL = request.getParameter("onFailure");
        String xeroTenantId = request.getParameter("tenantId");
        cookieStore.saveItem(response, "xero_onSuccess", successURL);
        cookieStore.saveItem(response, "xero_onFailure", failureURL);
        cookieStore.saveItem(response, "xeroTenantId", xeroTenantId);

        String redirectURL = new StringBuffer(new PartnerUtil().getRedirectURL(request, redirectPath)).toString();

        ArrayList<String> scopeList = new ArrayList<String>(idScopes);
        if(getApp) {
            scopeList.addAll(accounintingScopes);
        }

        String csrf = UUID.randomUUID().toString();
        saveCsrfTokenToRedis(csrf);

        JSONObject stateObject = new JSONObject();
        stateObject.put("csrfToken", csrf);
        stateObject.put("ssoFlow", Boolean.TRUE.booleanValue());
        stateObject.put("oAuthFlow", getApp);
        String stateString = URLEncoder.encode(stateObject.toString(), "UTF-8");


        DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
        AuthorizationCodeFlow flow = new AuthorizationCodeFlow.Builder(BearerToken.authorizationHeaderAccessMethod(), HTTP_TRANSPORT, JSON_FACTORY, new GenericUrl(tokenServerURL),
                new ClientParametersAuthentication(clientId, clientSecret), clientId, authServerURL).setScopes(scopeList).setDataStoreFactory(DATA_STORE_FACTORY).build();

        String authorizationURL = flow.newAuthorizationUrl().setClientId(clientId).setScopes(scopeList).setState(stateString).setRedirectUri(redirectURL).build();

        response.sendRedirect(authorizationURL);
    }

    @RequestMapping("/access/connectToXero")
    protected void connectToXero(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String errorCode = request.getParameter("error");
        String systemErrorMessage = null;

        String successURL = request.getParameter("onSuccess");
        String failureURL = request.getParameter("onFailure");
        failureURL = failureURL == null ? successURL : failureURL;
        cookieStore.saveItem(response, "xero_onSuccess", successURL);
        cookieStore.saveItem(response, "xero_onFailure", failureURL);

        String isNewAccount = request.getParameter("newAccount");
        String isClientAccount = request.getParameter("client");

        Boolean isNewAccountBoolean = new Boolean(isNewAccount);
        Boolean isClientAccountBoolean = new Boolean(isClientAccount);

        List<NameValuePair> currentParams = getQueryParams(successURL);
        JSONObject paramsObject = new JSONObject();
        NameValuePair paramsQueryParam = currentParams.stream().filter(e -> e.getName().equals("params")).findFirst().orElse(null);
        if (paramsQueryParam != null) {
            paramsObject = new JSONObject(URLDecoder.decode(paramsQueryParam.getValue(), "UTF-8"));
        }

        try {
            boolean isLoggedIn = isLoggedIn();

            if (!isLoggedIn) {
                errorCode = "system_error";
                systemErrorMessage = "Please login and select an account that you want to connect to.";
            } else {
                String redirectURL = new StringBuffer(new PartnerUtil().getRedirectURL(request, redirectPath)).toString();

                if(isClientAccountBoolean.booleanValue()) {
                    long parentDomainId = userManager.getParentDoaminId();
                    if(parentDomainId > 0) {
                        String loginName = userManager.getLoggedInUserName();
                        apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, parentDomainId, request, response);
                    } else {
                        throw new ApheException("Can not connect a client from non accountant account.");
                    }
                } else {
                    if (isNewAccountBoolean.booleanValue() && getCurrentDoaminId() != null) {
                        String loginName = userManager.getLoggedInUserName();
                        apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, -99, request, response);
                    }
                }

                ArrayList<String> scopeList = new ArrayList<String>(accounintingScopes);

                String csrf = UUID.randomUUID().toString();
                saveCsrfTokenToRedis(csrf);
                JSONObject stateObject = new JSONObject();
                stateObject.put("csrfToken", csrf);
                stateObject.put("newAccount", isNewAccountBoolean.booleanValue());
                stateObject.put("client", isClientAccountBoolean.booleanValue());
                stateObject.put("oAuthFlow", Boolean.TRUE.booleanValue());
                stateObject.put("ssoFlow", Boolean.FALSE.booleanValue());
                String stateString = URLEncoder.encode(stateObject.toString(), "UTF-8");


                DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();
                AuthorizationCodeFlow flow = new AuthorizationCodeFlow.Builder(BearerToken.authorizationHeaderAccessMethod(), HTTP_TRANSPORT, JSON_FACTORY,
                        new GenericUrl(tokenServerURL), new ClientParametersAuthentication(clientId, clientSecret), clientId, authServerURL).setScopes(scopeList)
                        .setDataStoreFactory(DATA_STORE_FACTORY).build();

                String authorizationURL = flow.newAuthorizationUrl().setClientId(clientId).setScopes(scopeList).setState(stateString).setRedirectUri(redirectURL).build();

                response.sendRedirect(authorizationURL);
            }

        } catch (Exception e) {
            logger.error("Error while attempting Xero OAuth Connection", e);
        } finally {
            String errorMessage = null;
            if (errorCode != null) {
                errorMessage = "An error occured while connecting to your Xero account.";
                if ("system_error".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " " + systemErrorMessage;
                }
                paramsObject.put("errorMessage", errorMessage);
                paramsObject.put("errorCode", errorCode);
                String paramsString = paramsObject.toString();
                List<NameValuePair> newParams = currentParams.stream().filter(m -> !(m.getName().equalsIgnoreCase("params"))).collect(Collectors.toList());
                newParams.add(new BasicNameValuePair("params", paramsString));
                String newURL = updateQueryString(successURL, newParams);
                response.sendRedirect(newURL);
            }
        }
    }

    /**
     * Refer QBConnectionController documentation.
     */
    @RequestMapping("/access/xeroCallback")
    protected void xeroCallback(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String onSuccessParam = cookieStore.get(request, "xero_onSuccess");
        String onFailureParam = cookieStore.get(request, "xero_onFailure");
        String xeroTenantId = cookieStore.get(request, "xeroTenantId");

        String successURL = StringUtil.isNotEmpty(onSuccessParam) ? onSuccessParam : partnerUtil.getRedirectURL(request, "/");
        String failureURL = StringUtil.isNotEmpty(onFailureParam) ? onFailureParam : partnerUtil.getRedirectURL(request, "/");

        List<NameValuePair> currentParams = getQueryParams(successURL);
        JSONObject paramsObject = new JSONObject();
        NameValuePair paramsQueryParam = currentParams.stream().filter(e -> e.getName().equals("params")).findFirst().orElse(null);
        if (paramsQueryParam != null) {
            paramsObject = new JSONObject(URLDecoder.decode(paramsQueryParam.getValue(), "UTF-8"));
        }

        String authCode = request.getParameter("code");
        String state = request.getParameter("state");
        String realmId = request.getParameter("realmId");

        String stateString = URLDecoder.decode(state, "UTF-8");
        JSONObject stateJSON = new JSONObject(stateString);
        String stateCSRF = (String) stateJSON.get("csrfToken");
        boolean ssoFlow =  false;
        boolean oAuthFlow = false;
        boolean clientAccount = false;
        boolean newAccount = false;
        if(stateJSON.has("client")) {
            clientAccount = ((Boolean) stateJSON.get("client")).booleanValue();
        }
        if(stateJSON.has("newAccount")) {
            newAccount = ((Boolean) stateJSON.get("newAccount")).booleanValue();
        }
        if(stateJSON.has("ssoFlow")) {
            ssoFlow = ((Boolean) stateJSON.get("ssoFlow")).booleanValue();
        }
        if(stateJSON.has("oAuthFlow")) {
            oAuthFlow = ((Boolean) stateJSON.get("oAuthFlow")).booleanValue();
        }

        if (StringUtil.isEmpty(realmId) || "null".equalsIgnoreCase(realmId)) {
            realmId = null;
        }

        //TODO: Figure out how to detect xero authorization errors.
        String errorCode = request.getParameter("error");
        String systemErrorMessage = null;

        String redirectURL = new StringBuffer(new PartnerUtil().getRedirectURL(request, redirectPath)).toString();

        try {
            boolean exists = isCsrfTokenValid(stateCSRF);
            if (exists) {
                if (errorCode == null) {
                    DataStoreFactory DATA_STORE_FACTORY = new MemoryDataStoreFactory();

                    ArrayList<String> scopeList = new ArrayList<String>(idScopes);
                    scopeList.addAll(accounintingScopes);
                    AuthorizationCodeFlow flow = new AuthorizationCodeFlow.Builder(BearerToken.authorizationHeaderAccessMethod(), HTTP_TRANSPORT, JSON_FACTORY,
                            new GenericUrl(tokenServerURL), new ClientParametersAuthentication(clientId, clientSecret), clientId, authServerURL).setScopes(scopeList)
                            .setDataStoreFactory(DATA_STORE_FACTORY).build();

                    TokenResponse tokenResponse = flow.newTokenRequest(authCode).setRedirectUri(redirectURL).execute();
                    String idToken = (String) tokenResponse.get("id_token");

                    if(ssoFlow) {
                        if(idToken != null) {

                            UserDTO userDTO = null;
                            try {
                                userDTO = createOrUpdateUser(idToken, request, response);
                            }catch (ApheDataValidationException e) {
                                errorCode = "system_error";
                                systemErrorMessage = "Your account has been disabled. Please contact us to enable your account.";
                            } catch (Exception e) {
                                errorCode = "system_error";
                                systemErrorMessage = "An error occured while connecting to your Xero account.";
                            }
                            if (userDTO == null) {
                                return;
                            }

                            if(StringUtil.isNotEmpty(xeroTenantId)) {
                                long domainId = getDomainIdByPartnerId(xeroTenantId, userDTO.loginName, request, response);
                                if(domainId > 0) {
                                    apheSecurityManager.authenticateUserAndSetSessionAndCookies(userDTO.loginName, domainId, request, response);
                                } else {
                                    //Instead of throwing an error, redirect to get app and trigger an oAuth flow for this tenantId.
                                    List<NameValuePair> newParams = new ArrayList<>();
                                    newParams.add(new BasicNameValuePair("tenantId", xeroTenantId));
                                    String newURL = getNewURL(successURL, "/connectxero", newParams);
                                    response.sendRedirect(newURL);
                                }
                            }
                        } else {
                            errorCode = "system_error";
                            systemErrorMessage = "Could not get identity details from Xero.";
                        }
                    }
                    if(oAuthFlow) {
                        if(ssoFlow) {
                            //Reset the current logged in domain from because of the default domain selection behavior.
                            String loginName = userManager.getLoggedInUserName();
                            apheSecurityManager.authenticateUserAndSetSessionAndCookies(loginName, -99, request, response);
                        }

                        Connection theConnection = getLatestConnection(tokenResponse);

                        if(ssoFlow || clientAccount || newAccount) {
                            if(theConnection != null) {
                                String domainName = theConnection.getTenantName();
                                String accountId = theConnection.getTenantId().toString();
                                String xeroConnectionId = theConnection.getId().toString();
                                if(clientAccount) {
                                    long parentDomainId = userManager.getParentDoaminId();
                                    if(parentDomainId > 0) {
                                        long userId = userManager.getLoggedInUserId();
                                        UserDTO userDTO = userManager.getUser(Long.toString(userId));
                                        DomainDTO domainDTO = createOrUpdateClientDomain(userDTO, accountId, domainName, xeroConnectionId, tokenResponse, parentDomainId, request, response);
                                    } else {
                                        errorCode = "system_error";
                                        systemErrorMessage = "You are trying to add a client to a non-accountant account.";
                                    }
                                } else {
                                    String loggedInDomainId = getCurrentDoaminId();
                                    if (loggedInDomainId != null) {
                                        errorCode = "system_error";
                                        systemErrorMessage = "You are trying to add a new account while logged into another account. Please add new account from add account page.";
                                    } else {
                                        try {
                                            long userId = userManager.getLoggedInUserId();
                                            UserDTO userDTO = userManager.getUser(Long.toString(userId));
                                            DomainDTO domainDTO = createOrUpdateDomain(userDTO, accountId, domainName, xeroConnectionId, tokenResponse, request, response);
                                        } catch (Exception e) {
                                            errorCode = "system_error";
                                            systemErrorMessage = "System error while creating an account. Please retry again or contact us.";
                                        }
                                    }
                                }
                            } else {
                                errorCode = "system_error";
                                systemErrorMessage = "You have not authorized a new organization to be added.";
                            }
                        } else {
                            //This is the oAuthConnect Flow. if there are connections. present as success, if not show an error message to the user and keep as disconnected.
                            boolean hasConnections = false;
                            if(theConnection != null) {
                                hasConnections = true;
                            } else {
                                List<Connection> allConnections = getAllConnections(tokenResponse);
                                if(allConnections.size() > 0) {
                                    hasConnections = true;
                                }
                                if(allConnections != null && allConnections.size() == 1) {
                                    theConnection = allConnections.get(0);
                                }

                            }
                            if(hasConnections) {
                                String domainName = null;
                                String accountId = null;
                                String xeroConnectionId = null;
                                if(theConnection != null) {
                                    domainName = theConnection.getTenantName();
                                    accountId = theConnection.getTenantId().toString();
                                    xeroConnectionId = theConnection.getId().toString();
                                }
                                if (isLoggedIn()) {
                                    String loggedInDomainId = getCurrentDoaminId();
                                    try {
                                        saveOAuthConnectionFromTokenResponse(Long.parseLong(loggedInDomainId), tokenResponse, accountId, xeroConnectionId, domainName);
                                    } catch (Exception e) {
                                        errorCode = "system_error";
                                        systemErrorMessage = "System error while trying to connect. Please retry again or contact us.";
                                    }
                                } else {
                                    errorCode = "system_error";
                                    systemErrorMessage = "You are not logged into any 1099SmartFile account. Please select an account before connecting to Xero.";
                                }
                            } else {
                                errorCode = "system_error";
                                systemErrorMessage = "You have not authorized any organizations to be connected.";
                            }
                        }
                    }
                } else {
//                    errorCode = errorCode;
//                    systemErrorMessage = "Error authorizing the connection " + errorCode;
                }
            } else {
                errorCode = "system_error";
                systemErrorMessage = "CSRF token mismatch.";
            }
        } catch (Exception e) {
            errorCode = "system_error";
            systemErrorMessage = "Unexpected system error.";
            logger.error("Error processing XeroCallBack", e);
        } finally {
            String errorMessage = null;
            if (errorCode != null) {
                paramsObject.put("ssoStatus", "Fail");
                errorMessage = "An error occurred while connecting to your Xero organization.";
                if ("access_denied".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " Access was denied.";
                } else if ("invalid_scope".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " An invalid scope was specified in connection.";
                } else if ("system_error".equalsIgnoreCase(errorCode)) {
                    errorMessage = errorMessage + " " + systemErrorMessage;
                }
                paramsObject.put("errorMessage", errorMessage);
                paramsObject.put("errorCode", errorCode);
                String paramsString = paramsObject.toString();
                List<NameValuePair> newParams = currentParams.stream().filter(m -> !(m.getName().equalsIgnoreCase("params"))).collect(Collectors.toList());
                newParams.add(new BasicNameValuePair("params", paramsString));
                String newURL = updateQueryString(successURL, newParams);
                if(ssoFlow || newAccount) {
                    response.sendRedirect(newURL);
                } else {
                    sendOAuthPopupResponse(response, "Fail", errorCode, errorMessage);
                }
            } else {
                paramsObject.put("ssoStatus", "Success");
                paramsObject.remove("errorMessage");
                paramsObject.remove("errorCode");
               String paramsString = paramsObject.toString();
                List<NameValuePair> newParams = currentParams.stream().filter(m -> !(m.getName().equalsIgnoreCase("params"))).collect(Collectors.toList());
                newParams.add(new BasicNameValuePair("params", paramsString));
                String newURL = updateQueryString(successURL, newParams);
                if(ssoFlow || newAccount) {
                    response.sendRedirect(newURL);
                } else {
                    sendOAuthPopupResponse(response, "Success", null, null);
                }
            }
        }

    }


    private void saveOAuthConnectionFromTokenResponse(long domainId, TokenResponse tokenResponse, String realmId, String xeroConnectionId, String accountName) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        String refreshToken = tokenResponse.getRefreshToken();
        Long accessTokenExpiresInSeconds = tokenResponse.getExpiresInSeconds();
        Long refreshTokenExpiresInSeconds = DEFAULT_REFRESH_EXPIRY;
        saveOAuthConnection(domainId, accessToken, accessTokenExpiresInSeconds, refreshToken, refreshTokenExpiresInSeconds, realmId, xeroConnectionId, accountName);
    }

    private Connection getLatestConnection(TokenResponse tokenResponse) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        JSONObject accessTokenJSON = jwtUtil.parseTokenToJSON(accessToken);
        String authEventId = accessTokenJSON.getString("authentication_event_id");
        List<Connection> connections = getConnections(accessToken, authEventId);
        if (connections.size() == 1) {
            return connections.get(0);
        }
        return null;
    }

    private List<Connection> getAllConnections(TokenResponse tokenResponse) throws Exception, IOException {
        String accessToken = tokenResponse.getAccessToken();
        List<Connection> connections = getConnections(accessToken, null);
        if (connections.size() == 0) {
            return null;
        } else {
            return connections;
        }
    }

    private List<Connection> getConnections(String accessToken, String authEventId) throws IOException {
        HttpTransport httpTransport = new NetHttpTransport();
        JsonFactory jsonFactory = GsonFactory.getDefaultInstance();
        GoogleCredential credential = new GoogleCredential.Builder().setTransport(httpTransport).setJsonFactory(jsonFactory).setClientSecrets(clientId, clientSecret).build();

        HttpTransport transport = new NetHttpTransport();
        HttpRequestFactory requestFactory = transport.createRequestFactory(credential);

        // Init IdentityApi client
        ApiClient defaultClient = new ApiClient(baseURL, null, null, null, requestFactory);
        IdentityApi idApi = new IdentityApi(defaultClient);
        List<Connection> connections = idApi.getConnections(accessToken, authEventId != null ? UUID.fromString(authEventId) : null);
        return connections;
    }

    /**
     * If passed idToken is valid, creates or updates the user and sets the auth context.
     */
    private UserDTO createOrUpdateUser(String idToken, HttpServletRequest request, HttpServletResponse response) throws Exception {
            //TODO: validate this token first.
            JSONObject parsedIdToken = jwtUtil.parseTokenToJSON(idToken, null);

            String loginId = parsedIdToken.getString("sub");
            String given_name = parsedIdToken.getString("given_name");
            String family_name = parsedIdToken.getString("family_name");

            String firstName = given_name != null ? given_name : "NoFirstName";
            String lastName = family_name != null ? family_name : "NoLastName";
            String email = parsedIdToken.getString("email");
            boolean emailVerified = true;

            return createUserAndSetContext(loginId, firstName, lastName, email, emailVerified, request, response);
    }

    private DomainDTO createOrUpdateDomain(UserDTO userDTO, String realmId, String accountName, String xeroConnectionId, TokenResponse tokenResponse, HttpServletRequest request, HttpServletResponse response) {
        try {
            DomainDTO theDomain = createDomainAndSetContext(userDTO, realmId, accountName, false, false, -1, request, response);
            try {
                saveOAuthConnectionFromTokenResponse(theDomain.id, tokenResponse, realmId, xeroConnectionId, accountName);
            } catch (Exception e) {
                logger.error("Error oAuthIntegration during adding a new domain", e);
            }
            return theDomain;
        } catch (Exception e) {
            logger.error("Error saving/updating sso user", e);
            return null;
        }
    }

    private DomainDTO createOrUpdateClientDomain(UserDTO userDTO, String realmId, String accountName, String xeroConnectionId, TokenResponse tokenResponse, long parentDomainId, HttpServletRequest request, HttpServletResponse response) {
        try {
            DomainDTO theDomain = createDomainAndSetContext(userDTO, realmId, accountName, false, true, parentDomainId, request, response);
            try {
                saveOAuthConnectionFromTokenResponse(theDomain.id, tokenResponse, realmId, xeroConnectionId, accountName);
            } catch (Exception e) {
                logger.error("Error oAuthIntegration during adding a new domain");
            }
            return theDomain;
        } catch (Exception e) {
            logger.error("Error saving/updating sso user", e);
            return null;
        }
    }

}