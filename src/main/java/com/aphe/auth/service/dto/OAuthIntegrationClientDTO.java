package com.aphe.auth.service.dto;

import com.aphe.auth.model.core.OAuthIntegrationPartner;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

public class OAuthIntegrationClientDTO {

	public Long id;

	@Min(1)
	public String domainId;

	@NotNull
	public OAuthIntegrationPartner partner;

	public String accountId;

	public String accountName;

	public long accessTokenExpiresIn;

	public Date accessTokenExpiryDate;

	public long refreshTokenExpiresIn;
	public Date refreshTokenExpiryDate;
	public Date connectedDate;
	public Date updatedDate;

	public String xeroConnectionId;
	public String xeroIdToken;
	public String xeroTenantType;

	public boolean connected;

}
