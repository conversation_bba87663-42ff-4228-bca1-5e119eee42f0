package com.aphe.auth.service.dto;

import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class CreateDomainDTO {

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String firstName;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String lastName;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String name;

	@NotNull
	public DomainType domainType;

	public Boolean isTestAccount = false;

}
