package com.aphe.auth.service.dto;

import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.NotNull;
import java.time.OffsetDateTime;

public class OAuthTokenDTO {

	public String id;

	public String partner;

	public String domainId;

	@NotNull
	@TrimLength(min = 1, max = 5000)
	public String accountId;

	@NotNull
	@TrimLength(min = 1, max = 5000)
	public String accountName;

	@NotNull
	@TrimLength(min = 1, max = 5000)
	public String accessToken;

	public long accessTokenExpiresIn;

	public OffsetDateTime accessTokenExpiryDate;

	@NotNull
	@TrimLength(min = 1, max = 5000)
	public String refreshToken;

	public long refreshTokenExpiresIn;
	public OffsetDateTime refreshTokenExpiryDate;
	public OffsetDateTime connectedDate;
	public OffsetDateTime updatedDate;

	public String xeroConnectionId;
	public String xeroIdToken;
	public String xeroTenantType;
}
