package com.aphe.auth.service.dto;

import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

public class CreateInvitationDTO {

    @TrimLength(min = 1, max = 100)
    @NotBlank
    public String firstName;

    @TrimLength(min = 1, max = 100)
    @NotBlank
    public String lastName;

    @TrimLength(min = 2, max = 100)
    @NotBlank
    @Email
    public String email;

    @NotNull
    public String domainId;

    @NotNull
    public GlobalRole globalRole;

    public List<LocalRole> localRoles;

    public List<CreateClientAccessInvitationDTO> clientAccess;

}
