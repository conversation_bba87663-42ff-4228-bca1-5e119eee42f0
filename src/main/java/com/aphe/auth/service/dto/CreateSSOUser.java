package com.aphe.auth.service.dto;

import com.aphe.auth.model.core.SSOPartner;
import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public class CreateSSOUser {

	public long id;

	@TrimLength(min = 1, max = 100)
	@NotBlank
	public String firstName;

	@TrimLength(min = 1, max = 100)
	@NotBlank
	public String lastName;

	@TrimLength(min = 2, max = 100)
	@NotBlank
	@Email
	public String email;

	public String loginName;
	
	public boolean isEmailConfirmed = true;
	
	public SSOPartner ssoPartner;

}
