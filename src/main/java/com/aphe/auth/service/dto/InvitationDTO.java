package com.aphe.auth.service.dto;

import com.aphe.auth.model.u2d.InvitationStatus;

import java.util.Date;

public class InvitationDTO {

	public long id;

	public String firstName;

	public String lastName;

	public String email;

	public InvitationStatus status;

	public Date createdDate;

	public Date acceptedDate;

	public DomainAccessDTO domainAccess;

	public boolean existingUser;

	public String domainName;
	
}
