package com.aphe.auth.service.dto;

import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * DTO to update domain acess. Change global and local roles, active and inactive the entire access.
 */

public class UpdateDomainAccessDTO {

    @NotNull
    public String id = "-1";

    @NotNull
    public GlobalRole globalRole;

    public List<LocalRole> localRoles;

    public boolean isActive;

    public List<UpdateClientAccessDTO> clientAccess;

}
