package com.aphe.auth.service.dto;

import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class CreateDomainRemoteDTO {

	public long id;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String name;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String firstName;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String lastName;

	@NotNull
	public DomainType domainType;
	
	public String emailAddress;

	public Boolean isTestAccount;

	public Boolean isActive;

}
