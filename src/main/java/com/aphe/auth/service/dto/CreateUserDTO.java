package com.aphe.auth.service.dto;

import com.aphe.auth.model.core.Login;
import io.swagger.v3.oas.annotations.Hidden;
import org.hibernate.validator.constraints.CheckUnique;
import org.hibernate.validator.constraints.FieldMatch;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@FieldMatch.List({ @FieldMatch(first = "password", second = "confirmPassword", message = "The password fields must match") })
@CheckUnique(entityClass = Login.class, entityPropertyNames = { "loginName" }, propertyNames = { "email" }, message = "Email address is already in use")
public class CreateUserDTO {

	@TrimLength(min = 1, max = 100)
	@NotBlank
	public String firstName;

	@TrimLength(min = 1, max = 100)
	@NotBlank
	public String lastName;

	@TrimLength(min = 2, max = 100)
	@NotBlank
	@Email
	private String email;

	@Length(min = 8, max = 72)
	@Pattern(regexp = "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[`~!@#$%^&*()\\-+=_{}])[A-Za-z\\d`~!@#$%^&*()\\-+=_{}]{8,72}$",
			message = "Must be between 8 to 72 characters, Must have one upercase, one lowercase, one number and one special character")
	@NotBlank
	public String password;


	public String loginName;

	public String confirmPassword;

	public String recaptchaToken;

	@Hidden
	public String ipAddress;

	@Hidden
	public String code;


	//Getter & Setter needed for unique account validation.
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
}
