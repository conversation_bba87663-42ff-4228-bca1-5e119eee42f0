package com.aphe.auth.service.dto;

import java.util.List;

public class UserDomainDTO {

	public long id;

	public String globalId;

	public String name;
	
	public DomainType domainType;

	public boolean isActive;
	public boolean isTestAccount;

	public List<OAuthIntegrationClientDTO> oAuthIntegrations;


	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean active) {
		isActive = active;
	}

	public boolean isTestAccount() {
		return isTestAccount;
	}

	public void setTestAccount(boolean testAccount) {
		isTestAccount = testAccount;
	}
}
