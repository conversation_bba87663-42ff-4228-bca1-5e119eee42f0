package com.aphe.auth.service.dto;

import com.aphe.auth.model.core.OAuthIntegrationPartner;
import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

public class OAuthIntegrationDTO {

	public Long id;

	@Min(1)
	public String domainId;

	@NotNull
	public OAuthIntegrationPartner partner;

	public String accountId;

	public String accountName;

	@NotNull
	@TrimLength(min = 1, max = 5000)
	public String accessToken;

	public long accessTokenExpiresIn;

	public Date accessTokenExpiryDate;

	@NotNull
	@TrimLength(min = 1, max = 5000)
	public String refreshToken;

	public long refreshTokenExpiresIn;
	public Date refreshTokenExpiryDate;
	public Date connectedDate;
	public Date updatedDate;

	public String xeroConnectionId;
	public String xeroIdToken;
	public String xeroTenantType;

	public boolean connected;

}
