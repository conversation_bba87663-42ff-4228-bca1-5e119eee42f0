package com.aphe.auth.service.dto;

import com.aphe.auth.insights.InsightDTO;
import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

public class DomainDTO {

	public long id;

	public String globalId;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String name;
	
	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String firstName;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String lastName;
	
	@NotNull
	public DomainType domainType;

	public long accountantId;

	public List<InsightDTO> insights;

	public boolean isActive;

	public boolean isTestAccount;

	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean active) {
		isActive = active;
	}

	public boolean isTestAccount() {
		return isTestAccount;
	}

	public void setTestAccount(boolean testAccount) {
		isTestAccount = testAccount;
	}
}
