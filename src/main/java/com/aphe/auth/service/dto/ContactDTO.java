package com.aphe.auth.service.dto;

import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public class ContactDTO {

	@TrimLength(min = 1, max = 100)
	@NotBlank
	public String firstName;

	@TrimLength(min = 1, max = 100)
	@NotBlank
	public String lastName;

	@TrimLength(min = 2, max = 100)
	@NotBlank
	@Email
	public String emailAddress;
	
	public String phoneNumber;

	public String accountName;

	public String accountId;

	@TrimLength(min = 2, max = 5000)
	@NotBlank
	public String subject;

	@TrimLength(min = 2, max = 5000)
	@NotBlank
	public String message;

}
