package com.aphe.auth.service.dto;

import org.hibernate.validator.constraints.FieldMatch;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@FieldMatch.List({ @FieldMatch(first = "password", second = "confirmPassword", message = "The password fields must match") })
public class PasswordResetDTO {

	@NotBlank
	public String loginName;

	/**
	 * Populate this field if you don't know your original password and have obtained a password reset code.
	 */
	public String passwordResetCode;

	/**
	 * Populate this field if you know your original password.
	 */
	public String originalPassword;

	@Length(min = 8, max = 72)
	@Pattern(regexp = "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[`~!@#$%^&*()\\-+=_{}])[A-Za-z\\d`~!@#$%^&*()\\-+=_{}]{8,72}$",
		message = "Must be between 8 to 72 characters, Must have one uppercase, one lowercase, one number and one special character")
	@NotBlank
	public String password;

	@NotBlank
	public String confirmPassword;
}
