package com.aphe.auth.service.dto;

import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.NotBlank;

/**
 * DTO to update doamin entity. Only name can be changed for now.
 * <AUTHOR>
 *
 */

public class UpdateDomainDTO {

	public String id;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String name;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String firstName;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	public String lastName;

	public Boolean isTestAccount;


}
