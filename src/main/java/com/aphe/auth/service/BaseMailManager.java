package com.aphe.auth.service;

import com.aphe.auth.model.core.User;
import com.aphe.common.util.StringUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
public class BaseMailManager {

    @Value("${aphe.product.url}")
    protected String productURL;

    @Value("${aphe.product.appName}")
    protected String productName;

    @Value("${aphe.product.signInPath}")
    protected String signInPath;



    @NotNull
    protected String buildFullName(String firstName, String lastName) {
        return new StringBuffer(StringUtil.isNotEmpty(firstName) ? firstName : "")
                .append(" ")
                .append(StringUtil.isNotEmpty(lastName) ? lastName : "")
                .toString().trim();
    }

    protected void addUserProperties(User u, Map<String, Object> mailAttributes) {

        String signInURL = productURL + signInPath;
        mailAttributes.put("LOGIN_URL", signInURL);


        // Set User specific properties.
        mailAttributes.put("FIRST_NAME", u.getFirstName());
        mailAttributes.put("LAST_NAME", u.getLastName());
        mailAttributes.put("EMAIL", u.getEmail());
    }


}
