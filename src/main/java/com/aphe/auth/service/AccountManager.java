package com.aphe.auth.service;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.User;
import com.aphe.auth.service.dto.*;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import java.util.List;

/**
 * This service class is used for orchestrate. Useful when an transaction needs to be committed before we take another action.
 * 
 * <AUTHOR>
 *
 */
@Service
@Component
public class AccountManager extends BaseManager {

	//TODO: Move this to a model, where accountManagerTxn is the primary thing that will be used by others.
	// this accountmanager should AccountManagerOrchestraor sort of thing.

	private static Logger logger = LoggerFactory.getLogger(AccountManager.class);

	@Autowired
	protected AccountManagerTxn acctMgr;

	@Autowired
	protected UserManagerTxn userManagerTxn;

	@Autowired
	protected AuthMapper authMapper;

	@Autowired
	protected SandboxManager sandboxManager;

	@Autowired
	protected RequestUtil requestUtil;

	@Autowired
	protected ApheSecurityManager apheSecurityManager;

	/**
	 * Will create an account with email address and will generate and send a email confirmation code Email needs to be confirmed. Also creates the right type of domain with right
	 * access roles for the user who is creating the domain.
	 * 
	 * @param
	 * @return
	 */
	public UserDTO createAccount(CreateAccountDTO accountDTO, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException {

		// Create a user and login
		accountDTO.ipAddress = requestUtil.getIPAddress(request);

		CreateUserDTO createUserDTO = authMapper.createAccountDTOToCreateUserDTO(accountDTO);


		User createdUser = userManagerTxn.createUser(createUserDTO);

		if (createdUser != null) {
			userManagerTxn.sendEmailConfirmationLink(createdUser.getLogin().getLoginName());
			apheSecurityManager.authenticateUserAndSetSession(createdUser, -1);
		}

		DomainDTO createdDomain = null;

		try {
			// Create a domain and setup right roles.
			CreateDomainDTO dto = new CreateDomainDTO();
			dto.name = accountDTO.domainName;
			dto.firstName = accountDTO.firstName;
			dto.lastName = accountDTO.lastName;
			dto.domainType = accountDTO.domainType;
			dto.isTestAccount = accountDTO.isTestAccount;
			createdDomain = addDomain(dto, createdUser, request, response);
			if (PropertiesManager.isSandbox()) {
				try {
					sandboxManager.seedAccount(createdDomain, request, response);
				} catch (ApheForbiddenException e) {
					logger.error("Error seeding sandbox account", e);
				}
			}
		} catch (Exception e) {
			logger.error("Error creating a domain when signing up", e);
		}
		UserDTO retVal = authMapper.userToUserDto(createdUser);

		return retVal;
	}

	public DomainDTO addDomain(CreateDomainDTO dto, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException {
		User u = getLoggedInUser();
		return addDomain(dto, u, request, response);
	}

	public DomainDTO addDomain(CreateDomainDTO dto, User u, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException {

		Domain d = acctMgr.addDomain(dto);
		String token = apheSecurityManager.authenticateUserAndSetSession(u, d.getId());
		if (u.getLogin().isEmailConfirmed()) {
			apheSecurityManager.setTokenInCookies(token, request, response);
		}
		DomainDTO createdDomain = acctMgr.getDomainDTO(d.getId());
		return createdDomain;

	}

	/**
	 * Adds a client and switches the security context to that client domain for downstream services and the clients (by setting cookies)
	 * 
	 * @throws ApheDataValidationException
	 */
	public DomainDTO addClient(Long accountantDomainId, CreateDomainDTO dto, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException, ApheException {
		Domain d = acctMgr.addClient(accountantDomainId, dto);
		try {
			String token = apheSecurityManager.authenticateUserAndSetSessionAndCookies(getLoggedInUserName(), d.getId(), request, response);
			DomainDTO createdDomain = acctMgr.getDomainDTO(d.getId());
			return createdDomain;
		} catch (Exception e) {
			throw new RuntimeException();
		}
	}

	public Domain getDomain(Long domainId) {
		return acctMgr.getDomain(domainId);
	}

	public DomainDTO getDomainDTO(Long domainId) {
		return acctMgr.getDomainDTO(domainId);
	}

	public boolean createDomainRemote(CreateDomainRemoteDTO dto) {
		return acctMgr.createDomainRemote(dto);
	}

	public boolean validateToken(HttpServletRequest request, HttpServletResponse response, String token) throws Exception {
		return acctMgr.validateToken(request, response, token);
	}

	public List<DomainDTO> getDomains(boolean includeInactive) {
		return acctMgr.getDomains(includeInactive);
	}

	public List<DomainDTO> getClients(boolean includeInactive) {
		return acctMgr.getClients(includeInactive);
	}

	public DomainDTO updateDomain(UpdateDomainDTO domainDTO) throws ApheException {
		long domainId = acctMgr.updateDomain(domainDTO);
		DomainDTO updatedDomain = acctMgr.getDomainDTO(domainId);
		CreateDomainRemoteDTO createDomainRemoteDTO = authMapper.domainDTOTCreateDomainRemoteDTO(updatedDomain);
		acctMgr.updateDomainRemote(createDomainRemoteDTO);
		return updatedDomain;
	}

	public DomainDTO updateDomainDoNotPropagate(UpdateDomainDTO domainDTO) throws ApheException {
		long domainId = acctMgr.updateDomain(domainDTO);
		DomainDTO updatedDomain = acctMgr.getDomainDTO(domainId);
		return updatedDomain;
	}

	public boolean toggleDomainActiveStatus(long domainId, boolean active) throws ApheException {
		boolean updated = acctMgr.toggleDomainActiveStatus(domainId, active);
		DomainDTO updatedDomain = acctMgr.getDomainDTO(domainId);
		CreateDomainRemoteDTO createDomainRemoteDTO = authMapper.domainDTOTCreateDomainRemoteDTO(updatedDomain);
		acctMgr.updateDomainRemote(createDomainRemoteDTO);
		return updated;
	}

	public boolean toggleClientActiveStatus(long domainId, long clientId, boolean active) throws ApheException {
		boolean updated = acctMgr.toggleClientActiveStatus(domainId, clientId, active);
		DomainDTO updatedDomain = acctMgr.getDomainDTO(clientId);
		CreateDomainRemoteDTO createDomainRemoteDTO = authMapper.domainDTOTCreateDomainRemoteDTO(updatedDomain);
		acctMgr.updateDomainRemote(createDomainRemoteDTO);
		return updated;
	}


	//TODO: Is this a security hole??
	// Should this be an authenticated end point
	@Transactional
	public String generateTokenForDomain(long domainId, HttpServletRequest request, HttpServletResponse response) {
		return apheSecurityManager.authenticateUserAndSetSessionAndCookies(getLoggedInUserName(), domainId, request, response);
	}

	@Transactional
	public String generateTokenForLogin(String loginName, HttpServletRequest request, HttpServletResponse response) {
		return apheSecurityManager.switchUserAndSetSessionAndCookies(getLoggedInUserName(), loginName, request, response);
	}

}
