package com.aphe.auth.service;

import com.aphe.auth.model.a2c.AccountantClientRelation;
import com.aphe.auth.model.a2c.repo.AccountantClientRepository;
import com.aphe.auth.model.core.Accountant;
import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.Login;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.core.repo.UserRepository;
import com.aphe.auth.model.u2d.*;
import com.aphe.auth.model.u2d.repo.ClientAccessRepository;
import com.aphe.auth.model.u2d.repo.DomainAccessRepository;
import com.aphe.auth.model.u2d.repo.InvitationRepository;
import com.aphe.auth.service.dto.*;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.util.ArrayUtil;
import org.apache.commons.lang.RandomStringUtils;
import org.hibernate.validator.constraints.ValidationErrors;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service to manage DomainAccess
 * Invitations, Aceeptance of invitations and all the related business logic.
 * <p>
 * How do we transfer ownership from one user to the other???
 * <p>
 * Just SMB.
 * 1. Invite somebody as Admin.
 * 2. Toggle their role to be the owner and your own role to Admin and submit that as one separate mutation.
 * (enforce this business logic, no two owners, notion of transfer ownership...)
 * <p>
 * <p>
 * <p>
 * How do we transfer ownership between client and accountant.
 * <p>
 * Assume accountant ownership until there is one user in the SMB with ownership role.
 * When you add a new user to client... obviously, we can
 * <p>
 * Only one of the accountants has owner access.. how do we enforce that??
 * <p>
 * Add a pesudo user in the client company that represents that accountant.. with that user as the owner..
 * When you add an another accountant, they would get Admin role.
 * When you add one more.. they would get Admin role.
 * When you add a regular user.. they would get what ever they are given..
 */
@Service
@Component
public class DomainAccessManager extends BaseManager {
    private static Logger logger = LoggerFactory.getLogger(DomainAccessManager.class);

    @Autowired
    protected AuthMapper authMapper;

    @Autowired
    protected InvitationRepository invitationRepository;

    @Autowired
    protected DomainAccessRepository domainAccessRepository;

    @Autowired
    protected DomainRepository domainRepository;

    @Autowired
    protected ClientAccessRepository clientAccessRepository;

    @Autowired
    protected AccountantClientRepository accountantClientRepository;

    @Autowired
    protected UserRepository userRepository;

    @Autowired
    MailManager mailManager;

    @Autowired
    protected UserManagerTxn userManagerTxn;

    @Autowired
    OAuthIntegrationMgr oAuthIntegrationMgr;

    @Autowired
    UserLimitManager userLimitManager;

    @Transactional
    @PreAuthorize("hasPermission(#invitation.domainId, 'DOMAIN', 'CREATE_INVITATION')")
    public Invitation addInvitation(CreateInvitationDTO invitation) throws ApheDataValidationException {

        ValidationErrors errors = getValidationErrors(invitation);

        if (invitation.globalRole == GlobalRole.ROLE_OWNER) {
            errors.addMessage("globalRole", "You can not give owner role");
        } else if (invitation.globalRole == GlobalRole.ROLE_ADMIN && !authUtil.isOwnerOrAdmin(getAuthentication())) {
            errors.addMessage("globalRole", "A standard user can not invite an admin user");
        } else if (invitation.globalRole == GlobalRole.ROLE_NOACCESS) {
            errors.addMessage("domainAccess.globalRole", "Need to select an access type.");
        }

        List<Invitation> allExistingInvitations = invitationRepository.findByDomainId(invitation.domainId);
        List<Invitation> unacceptedInvitations = allExistingInvitations.stream().filter(e -> e.getStatus() == InvitationStatus.INVITED).collect(Collectors.toList());
        List<DomainAccess> existingAccess = domainAccessRepository.findByDomainId(Long.parseLong(invitation.domainId));
        long allExisitingUsers = existingAccess.size() + unacceptedInvitations.size();

        long maxUsers = userLimitManager.getMaxUsers(invitation.domainId);
        if(allExisitingUsers >= maxUsers) {
            errors.addMessage("email", "Exceeds the maximum number of users allowed.");
        }

        //Check there is an existing user.
        Login login = loginRepo.findByLoginName(invitation.email);
        if (login != null) {
            boolean isAnExisitngUser = false;
            List<DomainAccess> domainAccessList = login.getUser().getDomainAccess();
            for (DomainAccess da : domainAccessList) {
                if (da.getDomain().getId() == Long.parseLong(invitation.domainId)) {
                    isAnExisitngUser = true;
                    break;
                }
            }
            if (isAnExisitngUser) {
                errors.addMessage("email", "A user with this email address alerady exists.");
            }
        }

        //Check existing invitations..
        List<Invitation> existingInvitations = invitationRepository.findByEmailAndDomainId(invitation.email, invitation.domainId);
        List<Invitation> activeInvitations = existingInvitations.stream().filter(e -> e.getStatus() == InvitationStatus.INVITED).collect(Collectors.toList());
        if (activeInvitations != null && activeInvitations.size() > 0) {
            errors.addMessage("email", "An an existing active invitation found. Please delete it or resend the original invitation.");
        }

        Domain d = domainRepo.findById(Long.parseLong(invitation.domainId)).orElse(null);
        if (d == null || d.getId() != getCurrentDomainId()) {
            errors.addMessage("domainId", "Invalid domainId");
        }
        if (d.getAccountantRelations().size() > 0) {
            errors.addMessage("domainId", "You can not add users to client of an accountant.");
        }

        if (!(d instanceof Accountant) && invitation.clientAccess != null && invitation.clientAccess.size() > 0) {
            errors.addMessage("clientAccess", "client access can be added only for accountants");
        }

        if (d != null && d instanceof Accountant) {
            Accountant accountant = (Accountant) d;
            for (CreateClientAccessInvitationDTO cai : invitation.clientAccess) {
                boolean isValidClient = accountant.getClientRelations().stream()
                        .filter(ac -> ac.getClient().getId() == Long.parseLong(cai.clientId))
                        .findFirst()
                        .orElse(null) != null;
                if (!isValidClient) {
                    errors.addMessage("clientAccess", "Invalid client id");
                }
            }
        }

        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }
        Invitation invitationEntity = authMapper.createInvitationDTOToInvitation(invitation);
        for (ClientAccessInvitation cai : invitationEntity.getClientAccess()) {
            cai.setInvitation(invitationEntity);
        }

        invitationEntity.setCreatedDate(new Date());
        invitationEntity.setInvitationCode(RandomStringUtils.random(50, true, true));
        invitationEntity.setStatus(InvitationStatus.INVITED);

        return invitationRepository.save(invitationEntity);
    }

    @Transactional
    public InvitationDTO getInvitationByCode(String invitationCode) {
        Invitation existingInvitation = invitationRepository.findByInvitationCode(invitationCode);
        if (existingInvitation != null && existingInvitation.getStatus() == InvitationStatus.INVITED) {
            InvitationDTO invitationDTO = authMapper.invitationToInvitationDTO(existingInvitation);
            invitationDTO.existingUser = userManagerTxn.loginExists(invitationDTO.email);
            Domain d = domainRepository.findById(existingInvitation.getDomainId()).orElse(null);
            if (d != null) {
                invitationDTO.domainName = d.getName();
            }
            return invitationDTO;
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasPermission(#invitationId, 'INVITATION', 'READ_INVITATION')")
    public InvitationDTO getInvitation(String invitationId) {
        Invitation existingInvitation = invitationRepository.findById(Long.parseLong(invitationId)).orElse(null);
        if (existingInvitation != null) {
            return authMapper.invitationToInvitationDTO(existingInvitation);
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_INVITATION')")
    public List<InvitationDTO> getInvitations(String domainId) throws ApheDataValidationException {
        List<InvitationDTO> retList = new ArrayList<>();
        List<Invitation> existingInvitations = invitationRepository.findByDomainId(domainId);
        if (existingInvitations != null) {
            retList = existingInvitations.stream()
                    .filter(inv -> inv.getStatus() != InvitationStatus.ACCEPTED)
                    .map(inv -> authMapper.invitationToInvitationDTO(inv)).collect(Collectors.toList());
        }
        return retList;
    }

    @Transactional
    public Invitation isValidInvitationCode(String invitationCode) {
        Invitation inv = invitationRepository.findByInvitationCode(invitationCode);
        if (inv != null) {
            return inv;
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasPermission(#invitationId, 'INVITATION', 'DELETE_INVITATION')")
    public void deleteInvitation(String invitationId) throws ApheDataValidationException {
        Invitation inv = invitationRepository.findById(Long.parseLong(invitationId)).orElse(null);
        if (inv == null) {
            throw new ApheDataValidationException("invitationId", "Invalid invitation id");
        }
        if (inv.getStatus() == InvitationStatus.ACCEPTED) {
            throw new ApheDataValidationException("invitationId", "You can not delete an invitation that has been accepted.");
        }
        invitationRepository.delete(inv);
    }

    @Transactional
    @PreAuthorize("hasPermission(#userId, 'USER', 'UPDATE_USER')")
    public DomainAccess linkUserToInvitation(String userId, String invitationCode) throws ApheDataValidationException {
        //Could be used by the SSO, or any loggedInUser..
        //PreAuthorize checks the loggedInUser is good.
        ValidationErrors errors = new ValidationErrors();
        Invitation inv = invitationRepository.findByInvitationCode(invitationCode);
        User u = userRepo.findById(Long.parseLong(userId)).orElse(null);
        if (inv != null && inv.getStatus() == InvitationStatus.INVITED && u != null) {
            long lDomainId = inv.getDomainId();
            Domain d = domainRepo.findById(lDomainId).orElse(null);

            if (d != null) {
                boolean hasAccess = false;
                for (DomainAccess da : u.getDomainAccess()) {
                    if (da.getDomain().getId() == lDomainId) {
                        hasAccess = true;
                        break;
                    }
                }
                if (!hasAccess) {
                    DomainAccess domainAccess = new DomainAccess();
                    domainAccess.setGlobalRole(inv.getGlobalRole());
                    domainAccess.getLocalRoles().addAll(inv.getLocalRoles());
                    domainAccess.setActive(true);
                    domainAccess.setDomain(d);
                    domainAccess.setUser(u);
                    domainAccess = domainAccessRepository.save(domainAccess);
                    u.getDomainAccess().add(domainAccess);

                    inv.setStatus(InvitationStatus.ACCEPTED);
                    inv.setAcceptedDate(new Date());
                    inv.setLinkedUser(u);

                    if (d instanceof Accountant) {
                        Accountant accountant = (Accountant) d;
                        List<ClientAccess> clientAccessList = new ArrayList<>();
                        for (ClientAccessInvitation cai : inv.getClientAccess()) {
                            Domain client = domainRepo.findById(Long.parseLong(cai.getClientId())).orElse(null);
                            if (client != null) {
                                AccountantClientRelation theRel = accountant.getClientRelations().stream().filter(ac -> ac.getClient().getId() == client.getId()).findFirst().orElse(null);
                                if (theRel != null) {
                                    ClientAccess ca = new ClientAccess();
                                    ca.setDomainAccess(domainAccess);
                                    ca.setGlobalRole(cai.getGlobalRole());
                                    ca.getLocalRoles().addAll(cai.getLocalRoles());
                                    ca.setActive(true);
                                    ca.setClient(client);
                                    ca = clientAccessRepository.save(ca);
                                    domainAccess.getClientAccess().add(ca);
                                }
                            }
                        }
                    }
                    domainAccess = domainAccessRepository.save(domainAccess);
                    domainRepo.save(d);
                    userRepo.save(u);
                    invitationRepository.save(inv);

                    return domainAccess;
                } else {
                    errors.addMessage("email", "Invalid account");
                    throw new ApheDataValidationException(errors, "You already has access to the is account. Ask the account admin to edit exisitng access to give you additional roles.");
                }
            } else {
                errors.addMessage("invitationCode", "");
                throw new ApheDataValidationException(errors, "Invalid invitation code.");
            }
        } else {
            errors.addMessage("invitationCode", "");
            throw new ApheDataValidationException(errors, "Invalid invitation code.");
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAINACCESS')")
    public List<DomainUserDTO> getDomainUsers(String domainId) {
        List<DomainAccessDTO> domainAccessDTOList = getDomainAccessByDomain(domainId);
        List<DomainUserDTO> domainUsers = new ArrayList<>();
        for (DomainAccessDTO domainAccessDTO : domainAccessDTOList) {
            User u = userRepository.findById(domainAccessDTO.userId).orElse(null);
            if (u != null) {
                UserDTO userDTO = authMapper.userToUserDto(u);
                DomainUserDTO domainUserDTO = authMapper.userDTOToDomainUserDTO(userDTO);
                domainUserDTO.domainAccess = domainAccessDTO;
                domainUsers.add(domainUserDTO);
            }
        }
        return domainUsers;
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainAccessId, 'DOMAINACCESS', 'READ_DOMAINACCESS')")
    public DomainUserDTO getDomainUser(String domainAccessId) {
        DomainAccess domainAccess = domainAccessRepository.findById(Long.parseLong(domainAccessId)).orElse(null);
        if (domainAccess != null) {
            DomainAccessDTO domainAccessDTO = authMapper.domainAccessToDomainAccessDTO(domainAccess);
            User u = userRepository.findById(domainAccessDTO.userId).orElse(null);
            if (u != null) {
                UserDTO userDTO = authMapper.userToUserDto(u);
                DomainUserDTO domainUserDTO = authMapper.userDTOToDomainUserDTO(userDTO);
                domainUserDTO.domainAccess = domainAccessDTO;
                return domainUserDTO;
            }
        }
        return null;
    }


    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAINACCESS')")
    public List<DomainAccessDTO> getDomainAccessByDomain(String domainId) {
        List<DomainAccessDTO> domainUsers = new ArrayList<>();
        List<DomainAccess> domainAccessList = domainAccessRepository.findByDomainId(Long.parseLong(domainId));
        for (DomainAccess da : domainAccessList) {
            domainUsers.add(authMapper.domainAccessToDomainAccessDTO(da));
        }
        return domainUsers;
    }

    @Transactional
    @PreAuthorize("hasPermission(#userId, 'USER', 'READ_USER')")
    public List<DomainAccessDTO> getDomainAccessByUser(String userId) {
        List<DomainAccessDTO> accessibleDomains = new ArrayList<>();
        List<DomainAccess> roles = domainAccessRepository.findByUserId(Long.parseLong(userId));
        for (DomainAccess role : roles) {
            if (role.isActive()) {
                accessibleDomains.add(authMapper.domainAccessToDomainAccessDTO(role));
            }
        }
        return accessibleDomains;
    }

    @Transactional
//    @PreAuthorize("hasPermission(#userId, 'USER', 'READ_USER')")
    public boolean hasAccountingIntegration(String userId) {
        boolean hasAccountingIntegration = false;
        List<DomainAccess> roles = domainAccessRepository.findByUserId(Long.parseLong(userId));
        for (DomainAccess role : roles) {
            if (role.isActive()) {
                hasAccountingIntegration = oAuthIntegrationMgr.hasAccountingIntegration(role.getDomain().getId());
                if (hasAccountingIntegration) {
                    break;
                }
            }
            //check client access
            for (ClientAccess ca : role.getClientAccess()) {
                hasAccountingIntegration = oAuthIntegrationMgr.hasAccountingIntegration(ca.getClient().getId());
                if (hasAccountingIntegration) {
                    break;
                }
            }
        }
        return hasAccountingIntegration;
    }


    @Transactional
    @PreAuthorize("hasPermission(#domainAccessDTO.id, 'DOMAINACCESS', 'UPDATE_DOMAINACCESS')")
    public DomainAccess updateDomainAccess(UpdateDomainAccessDTO domainAccessDTO) throws ApheDataValidationException {

        ValidationErrors errors = getValidationErrors(domainAccessDTO);

        DomainAccess da = domainAccessRepository.findById(Long.parseLong(domainAccessDTO.id)).orElse(null);
        if (da == null) {
            errors.addMessage("id", "Invalid accessId");
        }

        boolean ownerTransfer = (domainAccessDTO.globalRole == GlobalRole.ROLE_OWNER && da.getGlobalRole() != GlobalRole.ROLE_OWNER)
                || (domainAccessDTO.globalRole != GlobalRole.ROLE_OWNER && da.getGlobalRole() == GlobalRole.ROLE_OWNER);
        if (ownerTransfer) {
            errors.addMessage("domainAccess.globalRole", "You can not add/remove owner role. You can only transfer owner role.");
        } else if ((domainAccessDTO.globalRole == GlobalRole.ROLE_ADMIN || da.getGlobalRole() == GlobalRole.ROLE_ADMIN) && !authUtil.isOwnerOrAdmin(getAuthentication())) {
            errors.addMessage("domainAccess.globalRole", "A standard user can not add/remove admin access");
        }

        //Lcoal role validations.
        validateLocalRoles(domainAccessDTO.localRoles, errors, null);

        //Other client access validations..
        Map<String, List<UpdateClientAccessDTO>> clientAcccessGreaterThanOne = domainAccessDTO.clientAccess.stream()
                .collect(Collectors.groupingBy(clientAccessDTO -> clientAccessDTO.clientId))
                .entrySet().stream()
                .filter(x -> x.getValue().size() > 1)
                .collect(Collectors.toMap(x -> x.getKey(), x -> x.getValue()));
        if (clientAcccessGreaterThanOne.size() > 0) {
            for (String clientId : clientAcccessGreaterThanOne.keySet()) {
                errors.addMessage("clientAccess." + clientId, "ClientAccess has been specified more than once for clientId " + clientId);
            }
        }

        //client access global role can be Noaccess, Admin, User only.
        for (UpdateClientAccessDTO dto : domainAccessDTO.clientAccess) {
            //id is valid.
            if (Long.parseLong(dto.id) > 0) {
                ClientAccess theExistingAccess = da.getClientAccess().stream()
                        .filter(ca -> ca.getClient().getId() == Long.parseLong(dto.clientId)).findFirst().orElse(null);
                if (theExistingAccess == null) {
                    errors.addMessage("domainAccess.clientAccess." + dto.id, "Invalid client access " + dto.id);
                }
            }

            //ClientId is valid.
            Accountant theAccountant = (Accountant) da.getDomain();
            AccountantClientRelation theClientRel = theAccountant.getClientRelations().stream()
                    .filter(theRel -> theRel.getClient().getId() == Long.parseLong(dto.clientId)).findFirst().orElse(null);
            if (theClientRel == null) {
                errors.addMessage("domainAccess.clientAccess." + dto.clientId, "Invalid clientId " + dto.clientId);
            }

            //Global role is valid
            if (dto.globalRole == GlobalRole.ROLE_OWNER) {
                errors.addMessage("domainAccess.clientAccess." + dto.clientId + ".globalRole", "Invalid access type for clientId " + dto.clientId);
            }

            //lcoal roles are valid -- No duplicate local roles in each of client access.
            validateLocalRoles(dto.localRoles, errors, Long.parseLong(dto.clientId));
        }


        List<UpdateClientAccessDTO> deleteClientAccess = domainAccessDTO.clientAccess.stream()
                .filter(ca -> Long.parseLong(ca.id) > 0 && ca.globalRole == GlobalRole.ROLE_NOACCESS).collect(Collectors.toList());

        List<UpdateClientAccessDTO> addNewClientAccess = domainAccessDTO.clientAccess.stream()
                .filter(ca -> Long.parseLong(ca.id) <= 0 && ca.globalRole != GlobalRole.ROLE_NOACCESS).collect(Collectors.toList());
        for (UpdateClientAccessDTO caDto : addNewClientAccess) {
            ClientAccess theExistingAccess = da.getClientAccess().stream()
                    .filter(ca -> ca.getClient().getId() == Long.parseLong(caDto.clientId)).findFirst().orElse(null);
            if (theExistingAccess != null) {
                errors.addMessage("clientAccess." + caDto.clientId, "Exisitng client access exists for clientId " + caDto.clientId);
            }
        }

        List<UpdateClientAccessDTO> updateExisting = domainAccessDTO.clientAccess.stream().collect(Collectors.toList());
        updateExisting.removeAll(deleteClientAccess);
        updateExisting.removeAll(addNewClientAccess);

        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }

        //All validations done. Let's begin updating the system, but store old access and old client access.
        boolean hasClientAccessChanges = false;
        boolean hasDomainAccessChanges = da.getGlobalRole() != domainAccessDTO.globalRole || !ArrayUtil.listsAreEqual(da.getLocalRoles(), domainAccessDTO.localRoles);

        GlobalRole oldGlobalRole = da.getGlobalRole();
        List<LocalRole> oldLocalRoles = da.getLocalRoles();

        da.setGlobalRole(domainAccessDTO.globalRole);
        da.setLocalRoles(new ArrayList<>(domainAccessDTO.localRoles));

        HashMap<Long, HashMap<String, Object>> clientAccessChanges = new HashMap<>();
        for (ClientAccess ca : da.getClientAccess()) {
            String oldClientGlobalRole = ca.getGlobalRole().getRoleLabel();
            List<String> oldClientLocalRoles = ca.getLocalRoles().stream().map(lr -> lr.getRoleLabel()).collect(Collectors.toList());
            String clientName = ca.getClient().getName();
            HashMap<String, Object> clientAccessMap = new HashMap<>();
            clientAccessMap.put("clientName", clientName);
            clientAccessMap.put("oldAccessType", oldClientGlobalRole);
            clientAccessMap.put("oldRoles", oldClientLocalRoles);
            clientAccessMap.put("newAccessType", "No Access");
            clientAccessMap.put("newRoles", new ArrayList<String>());
            clientAccessChanges.put(ca.getClient().getId(), clientAccessMap);
        }

        //delete the ones that should be deleted.
        for (UpdateClientAccessDTO caDto : deleteClientAccess) {
            ClientAccess theClientAccess = da.getClientAccess().stream()
                    .filter(ca -> ca.getId() == Long.parseLong(caDto.id)).findFirst().orElse(null);
            if (theClientAccess != null) {
                hasClientAccessChanges = true;
                da.getClientAccess().remove(theClientAccess);
                clientAccessRepository.delete(theClientAccess);
            }
        }

        //add the new ones.
        for (UpdateClientAccessDTO dto : addNewClientAccess) {
            ClientAccess newCa = authMapper.updateClientAccessDTOToClientAccess(dto);
            Domain theClient = domainRepo.findById(Long.parseLong(dto.clientId)).orElse(null);
            if (theClient != null) {
                hasClientAccessChanges = true;
                newCa.setClient(theClient);
                newCa.setDomainAccess(da);
                newCa.setActive(true);
                newCa = clientAccessRepository.save(newCa);
                da.getClientAccess().add(newCa);
            }
        }

        //Update the remaining.
        for (UpdateClientAccessDTO dto : updateExisting) {
            ClientAccess theClientAccess = da.getClientAccess().stream()
                    .filter(ca -> ca.getId() == Long.parseLong(dto.id)).findFirst().orElse(null);
            if (theClientAccess != null) {
                theClientAccess.setDomainAccess(da);
                theClientAccess.setGlobalRole(dto.globalRole);
                hasClientAccessChanges = hasClientAccessChanges || (theClientAccess.getGlobalRole() != dto.globalRole);
                List<LocalRole> list = dto.localRoles;
                hasClientAccessChanges = hasClientAccessChanges || (!ArrayUtil.listsAreEqual(theClientAccess.getLocalRoles(), dto.localRoles));
                if (list != null) {
                    theClientAccess.setLocalRoles(new ArrayList<LocalRole>(list));
                } else {
                    theClientAccess.setLocalRoles(new ArrayList<LocalRole>());
                }
                theClientAccess = clientAccessRepository.save(theClientAccess);
            }
        }

        for (ClientAccess ca : da.getClientAccess()) {
            String newClientGlobalRole = ca.getGlobalRole().getRoleLabel();
            List<String> newClientLocalRoles = ca.getLocalRoles().stream().map(lr -> lr.getRoleLabel()).collect(Collectors.toList());
            String clientName = ca.getClient().getName();

            HashMap<String, Object> clientAccessMap = clientAccessChanges.get(ca.getClient().getId());
            if (clientAccessMap == null) {
                clientAccessMap = new HashMap<>();
                clientAccessMap.put("clientName", clientName);
                clientAccessMap.put("oldAccessType", "No Access");
                clientAccessMap.put("oldRoles", new ArrayList<String>());
                clientAccessChanges.put(ca.getClient().getId(), clientAccessMap);
            }
            clientAccessMap.put("newAccessType", newClientGlobalRole);
            clientAccessMap.put("newRoles", newClientLocalRoles);
        }


        if (hasDomainAccessChanges) {
            notifyUserUpdated(da, oldGlobalRole, oldLocalRoles);
            notifyTeamUserUpdaed(da, oldGlobalRole, oldLocalRoles);
        }

        if (hasClientAccessChanges) {
            notifyUserClientAccessChanged(da, clientAccessChanges);
            notifyTeamClientAccessChanged(da, clientAccessChanges);
        }


        return domainAccessRepository.save(da);
    }

    private void validateLocalRoles(List<LocalRole> localRoles, ValidationErrors errors, Long clientId) {
        Map<LocalRole, Long> greaterThanOnce = localRoles.stream()
                .collect(Collectors.groupingBy(localRole -> localRole, Collectors.counting()))
                .entrySet().stream()
                .filter(x -> x.getValue() > 1)
                .collect(Collectors.toMap(x -> x.getKey(), x -> x.getValue()));
        if (greaterThanOnce.size() > 0) {
            for (LocalRole localRole : greaterThanOnce.keySet()) {
                if (clientId == null) {
                    errors.addMessage("localRoles", "AppRole " + localRole.getRoleName() + " has been specified more than once.");
                } else {
                    errors.addMessage("clientAccess." + clientId + ".localRoles", "AppRole " + localRole.getRoleName() + " has been specified more than once for clientId " + clientId);
                }
            }
        }
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainAccessId, 'DOMAINACCESS', 'UPDATE_DOMAINACCESS')")
    public void toggelDomainAccess(String domainAccessId, boolean isActive) throws ApheDataValidationException {

        ValidationErrors errors = new ValidationErrors();

        DomainAccess da = domainAccessRepository.findById(Long.parseLong(domainAccessId)).orElse(null);
        if (da == null) {
            throw new ApheDataValidationException("id", "Invalid domainAccessId");
        }

        if (da.getGlobalRole() == GlobalRole.ROLE_OWNER) {
            errors.addMessage("globalRole", "You can not activate/inactivate a owner role");
        } else if (da.getGlobalRole() == GlobalRole.ROLE_ADMIN && !authUtil.isOwnerOrAdmin(getAuthentication())) {
            errors.addMessage("globalRole", "A standard user can not activate/inactivate an admin user");
        }

        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }

        da.setActive(isActive);

        notifyUserToggled(da, isActive);
        notifyTeamUserToggled(da, isActive);

        domainAccessRepository.save(da);
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainAccessId, 'DOMAINACCESS', 'UPDATE_DOMAINACCESS')")
    public void deleteDomainAccess(String domainAccessId) throws ApheDataValidationException {

        ValidationErrors errors = new ValidationErrors();

        DomainAccess da = domainAccessRepository.findById(Long.parseLong(domainAccessId)).orElse(null);
        if (da == null) {
            throw new ApheDataValidationException("id", "Invalid domainAccessId");
        }

        User u = da.getUser();
        if (da.getGlobalRole() == GlobalRole.ROLE_OWNER) {
            errors.addMessage("globalRole", "You can not delete owner role");
        } else if (da.getGlobalRole() == GlobalRole.ROLE_ADMIN && !authUtil.isOwnerOrAdmin(getAuthentication())) {
            errors.addMessage("globalRole", "A standard user can not delete an admin user");
        }

        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }
        u.getDomainAccess().remove(da);

        Domain d = da.getDomain();
        if (d instanceof Accountant) {
            clientAccessRepository.deleteAll(da.getClientAccess());
            da.getClientAccess().clear();

        }

        notifyUserDeleted(da);
        notifyTeamUserDeleted(da);

        domainAccessRepository.delete(da);
        userRepo.save(u);
    }


    @Transactional
    public void notifyUserNewInvitation(String invitationId) throws ApheDataValidationException {
        InvitationDTO invDto = getInvitation(invitationId);
        if (invDto != null) {
            Invitation inv = invitationRepository.findById(invDto.id).orElse(null);
            if (inv != null && inv.getStatus() == InvitationStatus.INVITED) {
                User loggedInUser = getLoggedInUser();
                Domain d = domainRepo.findById(inv.getDomainId()).orElse(null);

                Map<String, Object> mailAttributes = new HashMap<>();
                mailAttributes.put("INVITATION_CODE", inv.getInvitationCode());
                mailAttributes.put("DOMAIN_NAME", d.getName());
                mailAttributes.put("ACCESS_CHANGE_TYPE", "Inv");

                User modifiedUser = buildUserFromInvitation(inv);

                mailManager.notifyUserChanged(modifiedUser, loggedInUser, mailAttributes);
            }
        }
    }

    @Transactional
    public void notifyTeamNewInvitation(String invitationId) throws ApheDataValidationException {
        InvitationDTO invDto = getInvitation(invitationId);
        if (invDto != null) {
            Invitation inv = invitationRepository.findById(invDto.id).orElse(null);
            if (inv != null && inv.getStatus() == InvitationStatus.INVITED) {
                User loggedInUser = getLoggedInUser();
                Domain d = domainRepo.findById(inv.getDomainId()).orElse(null);

                User ownerUser = getOwnerUser(d);

                if (ownerUser == null) {
                    //TODO: error condition.
                }
                List<User> adminUsers = getTeamManagerUsers(d.getId());

                User modifyingUser = getLoggedInUser();
                User modifiedUser = buildUserFromInvitation(inv);

                Map<String, Object> mailAttributes = new HashMap<>();
                mailAttributes.put("DOMAIN_NAME", d.getName());
                mailAttributes.put("ACCESS_CHANGE_TYPE", "Inv");

                mailManager.notifyTeamUserChanged(ownerUser, adminUsers, modifiedUser, modifyingUser, mailAttributes);
            }
        }
    }

    @Transactional
    public void notifyUserAdded(DomainAccess da) throws ApheDataValidationException {
        Map<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", da.getDomain().getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", "Add");
        mailManager.notifyUserChanged(da.getUser(), null, mailAttributes);
    }

    @Transactional
    public void notifyTeamUserAdded(DomainAccess da) throws ApheDataValidationException {
        Domain d = da.getDomain();
        User ownerUser = getOwnerUser(d);
        if (ownerUser == null) {
            //TODO: error condition.
        }
        List<User> adminUsers = getTeamManagerUsers(d.getId());
        User modifyingUser = getLoggedInUser();
        User modifiedUser = da.getUser();

        Map<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", d.getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", "Add");

        mailManager.notifyTeamUserChanged(ownerUser, adminUsers, modifiedUser, modifyingUser, mailAttributes);
    }


    @Transactional
    public void notifyUserUpdated(DomainAccess da, GlobalRole oldUserType, List<LocalRole> oldRoles) throws ApheDataValidationException {
        Map<String, Object> mailAttributes = getOldAndNewRoles(da, oldUserType, oldRoles);
        mailAttributes.put("DOMAIN_NAME", da.getDomain().getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", "Mod");
        mailManager.notifyUserChanged(da.getUser(), null, mailAttributes);
    }

    @Transactional
    public void notifyTeamUserUpdaed(DomainAccess da, GlobalRole oldUserType, List<LocalRole> oldRoles) throws ApheDataValidationException {
        Domain d = da.getDomain();
        User ownerUser = getOwnerUser(d);
        if (ownerUser == null) {
            //TODO: error condition.
        }
        List<User> adminUsers = getTeamManagerUsers(d.getId());
        User modifyingUser = getLoggedInUser();
        User modifiedUser = da.getUser();

        Map<String, Object> mailAttributes = getOldAndNewRoles(da, oldUserType, oldRoles);
        mailAttributes.put("DOMAIN_NAME", d.getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", "Mod");

        mailManager.notifyTeamUserChanged(ownerUser, adminUsers, modifiedUser, modifyingUser, mailAttributes);
    }

    @Transactional
    public void notifyUserToggled(DomainAccess da, boolean newActiveState) throws ApheDataValidationException {
        HashMap<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", da.getDomain().getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", newActiveState ? "Res" : "Sus");
        mailManager.notifyUserChanged(da.getUser(), null, mailAttributes);
    }

    @Transactional
    public void notifyTeamUserToggled(DomainAccess da, boolean newActiveState) throws ApheDataValidationException {
        Domain d = da.getDomain();
        User ownerUser = getOwnerUser(d);
        if (ownerUser == null) {
            //TODO: error condition.
        }
        List<User> adminUsers = getTeamManagerUsers(d.getId());
        User modifyingUser = getLoggedInUser();
        User modifiedUser = da.getUser();

        HashMap<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", d.getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", newActiveState ? "Res" : "Sus");
        mailManager.notifyTeamUserChanged(ownerUser, adminUsers, modifiedUser, modifyingUser, mailAttributes);
    }


    @Transactional
    public void notifyUserDeleted(DomainAccess da) throws ApheDataValidationException {
        HashMap<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", da.getDomain().getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", "Del");
        mailManager.notifyUserChanged(da.getUser(), null, mailAttributes);
    }

    @Transactional
    public void notifyTeamUserDeleted(DomainAccess da) throws ApheDataValidationException {
        Domain d = da.getDomain();
        User ownerUser = getOwnerUser(d);
        if (ownerUser == null) {
            //TODO: error condition.
        }
        List<User> adminUsers = getTeamManagerUsers(d.getId());
        User modifyingUser = getLoggedInUser();
        User modifiedUser = da.getUser();

        HashMap<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", d.getName());
        mailAttributes.put("ACCESS_CHANGE_TYPE", "Del");
        mailManager.notifyTeamUserChanged(ownerUser, adminUsers, modifiedUser, modifyingUser, mailAttributes);
    }

    @Transactional
    public void notifyUserClientAccessChanged(DomainAccess da, Map<Long, HashMap<String, Object>> clientAccessChanges) throws ApheDataValidationException {
        HashMap<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", da.getDomain().getName());
        mailAttributes.put("clientAccessList", clientAccessChanges.values());
        mailManager.notifyUserClientAccessChanged(da.getUser(), getLoggedInUser(), mailAttributes);
    }

    @Transactional
    public void notifyTeamClientAccessChanged(DomainAccess da, Map<Long, HashMap<String, Object>> clientAccessChanges) throws ApheDataValidationException {
        Domain d = da.getDomain();
        User ownerUser = getOwnerUser(d);
        if (ownerUser == null) {
            //TODO: error condition.
        }
        List<User> adminUsers = getTeamManagerUsers(d.getId());
        User modifyingUser = getLoggedInUser();
        User modifiedUser = da.getUser();

        HashMap<String, Object> mailAttributes = new HashMap<>();
        mailAttributes.put("DOMAIN_NAME", d.getName());
        mailAttributes.put("clientAccessList", clientAccessChanges.values());
        mailManager.notifyTeamClientAccessChanged(ownerUser, adminUsers, modifiedUser, modifyingUser, mailAttributes);
    }


    @NotNull
    private User buildUserFromInvitation(Invitation inv) {
        User modifiedUser = new User();
        modifiedUser.setFirstName(inv.getFirstName());
        modifiedUser.setLastName(inv.getLastName());
        modifiedUser.setEmail(inv.getEmail());
        return modifiedUser;
    }

    @Nullable
    protected User getOwnerUser(Domain d) {
        User ownerUser = getDomainOwnerUser(d.getId());
        if (ownerUser == null) {
            Domain ownerAcct = getOwnerAccountant(d.getId());
            if (ownerAcct != null) {
                ownerUser = getDomainOwnerUser(ownerAcct.getId());
            }
        }
        return ownerUser;
    }

    protected User getDomainOwnerUser(long domainId) {
        Domain d = domainRepo.findById(domainId).orElse(null);
        if (d != null) {
            List<DomainAccess> domainAccessList = domainAccessRepository.findByDomainId(domainId);
            DomainAccess ownerAccess = domainAccessList.stream().filter(da -> da.getGlobalRole() == GlobalRole.ROLE_OWNER).findFirst().orElse(null);
            if (ownerAccess != null) {
                return ownerAccess.getUser();
            }
        }
        return null;
    }

    protected List<User> getTeamManagerUsers(long domainId) {
        Domain d = domainRepo.findById(domainId).orElse(null);
        if (d != null) {
            List<DomainAccess> domainAccessList = domainAccessRepository.findByDomainId(domainId);
            List<User> adminUsers = domainAccessList.stream().filter(da -> da.getGlobalRole() == GlobalRole.ROLE_ADMIN || da.getLocalRoles().contains(LocalRole.ROLE_TEAM_MANAGER))
                    .map(da -> da.getUser())
                    .collect(Collectors.toList());
            return adminUsers;
        }
        return Collections.emptyList();
    }

    protected Domain getOwnerAccountant(long domainId) {
        Domain d = domainRepo.findById(domainId).orElse(null);
        if (d != null) {
            AccountantClientRelation ownerAccountant = d.getAccountantRelations().stream()
                    .filter(ac -> ac.isOwner()).findFirst().orElse(null);
            return ownerAccountant.getAccountant();
        }
        return null;
    }

    @NotNull
    private Map<String, Object> getOldAndNewRoles(DomainAccess da, GlobalRole oldUserType, List<LocalRole> oldRoles) {
        Map<String, Object> mailAttributes = new HashMap<>();
        List<String> oldRoleStrings = new ArrayList<>();
        if (oldUserType == GlobalRole.ROLE_USER) {
            oldRoleStrings = oldRoles.stream().map(lr -> lr.getRoleLabel()).collect(Collectors.toList());
        }
        String oldUserTypeStrting = oldUserType.getRoleLabel();

        List<String> newRoleStrings = new ArrayList<>();
        if (da.getGlobalRole() == GlobalRole.ROLE_USER) {
            newRoleStrings = da.getLocalRoles().stream().map(lr -> lr.getRoleLabel()).collect(Collectors.toList());
        }
        String newUserTypeStrting = da.getGlobalRole().getRoleLabel();

        mailAttributes.put("NEW_USER_TYPE", newUserTypeStrting);
        mailAttributes.put("NEW_ROLES", newRoleStrings);
        mailAttributes.put("OLD_USER_TYPE", oldUserTypeStrting);
        mailAttributes.put("OLD_ROLES", oldRoleStrings);
        return mailAttributes;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public void moveDomainsToAccountant(List<String> domainIds, String accountantId) {
        logger.error("Moving domains domainIds={} to accountant accounantId={}", ArrayUtil.stringListToString(domainIds), accountantId );

        Accountant ac = (Accountant) domainRepository.findById(Long.parseLong(accountantId)).orElse(null);
        if(ac == null) {
            throw new RuntimeException("accountant id not found");
        }

        for(String domainId : domainIds) {
            long domainId1 = Long.parseLong(domainId);
            Domain d = domainRepo.findById(domainId1).orElse(null);
            if(d == null) {
                throw new RuntimeException("domain not found");
            }

            //Delete all existing accesses to this domain.
            List<DomainAccess> allDomainAccess = domainAccessRepository.findByDomainId(domainId1);
            allDomainAccess.stream().forEach( da -> {
                domainAccessRepository.delete(da);
            });

            //Create a new accountant client relation between this domain and the accountnat.
            AccountantClientRelation accountantClientRelation = new AccountantClientRelation();
            accountantClientRelation.setAccountant(ac);
            accountantClientRelation.setClient(d);
            accountantClientRelation.setActive(true);
            accountantClientRelation.setOwner(true);
            AccountantClientRelation savedEntity = accountantClientRepository.save(accountantClientRelation);


            //Add client access to all the owner, admin, clent mgnt users of this accountant account.
            List<DomainAccess> clientMgmtAccessList = domainAccessRepository.findByDomainId(ac.getId()).stream()
                    .filter(da->da.getDomain().getId() == ac.getId() &&
                            (da.getGlobalRole() == GlobalRole.ROLE_OWNER || da.getGlobalRole() == GlobalRole.ROLE_ADMIN || da.getLocalRoles().contains(LocalRole.ROLE_CLIENT_MANAGER)))
                    .collect(Collectors.toList());
            for(DomainAccess theOwnerAccess : clientMgmtAccessList) {
                ClientAccess clientAccess = new ClientAccess();
                clientAccess.setGlobalRole(GlobalRole.ROLE_ADMIN);
                clientAccess.setActive(true);
                clientAccess.setDomainAccess(theOwnerAccess);
                clientAccess.setClient(d);
                clientAccess = clientAccessRepository.save(clientAccess);
                theOwnerAccess.getClientAccess().add(clientAccess);
                theOwnerAccess = domainAccessRepository.save(theOwnerAccess);
            }
        }
    }
}
