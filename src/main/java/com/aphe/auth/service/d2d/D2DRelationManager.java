package com.aphe.auth.service.d2d;

import com.aphe.auth.model.d2d.*;
import com.aphe.auth.model.d2d.repo.D2DInvitationRepository;
import com.aphe.auth.model.d2d.repo.D2DRelationRepository;
import com.aphe.auth.service.AuthMapper;
import com.aphe.auth.service.BaseManager;
import com.aphe.auth.service.d2d.dto.D2DRelationDTO;
import com.aphe.auth.service.d2d.dto.D2DRelationStatusDTO;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.util.DateUtil;
import org.hibernate.validator.constraints.ValidationErrors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service to manage D2D Relations. create from invitations, read, delete.
 */
@Service
@Component
public class D2DRelationManager extends BaseManager {
    private static Logger logger = LoggerFactory.getLogger(D2DRelationManager.class);

    @Autowired
    protected AuthMapper authMapper;

    @Autowired
    D2DRelationRepository d2DRelationRepository;

    @Autowired
    D2DInvitationRepository d2DInvitationRepository;

    @Value("${aphe.invitations.domainInvitationExpiryMinutes}")
    private int expiryMinutes;
//    private int expiryMinutes = 10080;


    @Transactional
    @PreAuthorize("hasPermission(#targetDomainId, 'DOMAIN', 'CREATE_D2DRELATION')")
    public D2DRelation addD2DRelation(Long targetDomainId, Long targetSubEntityId, String invitationCode) throws ApheDataValidationException {
        ValidationErrors errors = new ValidationErrors();

        D2DInvitation invitaiton = d2DInvitationRepository.findByInvitationCode(invitationCode);
        if(invitaiton == null || invitaiton.getStatus() != D2DInvitationStatus.INVITED) {
            throw new ApheDataValidationException("invitationCode", "Invalid invitation code");
        }
        Date createTime = invitaiton.getCreatedDate();
        Date expirationDate = DateUtil.addMinutes(createTime, expiryMinutes);
        if(expirationDate.before(new Date())) {
            errors.addMessage("invitationCode", "Invitation has expired. Please ask the sender to re-invite.");
        }
        if(targetDomainId == invitaiton.getDomainId()) {
            errors.addMessage("targetDomainId", "Target domain and source domain can not be same.");
        }

        D2DRelation existingRelation = d2DRelationRepository.findBySourceDomainIdAndSourceSubEntityIdAndRelationType(invitaiton.getDomainId(), invitaiton.getSubEntityId(), invitaiton.getRelationType());
        if(existingRelation != null) {
            errors.addMessage("invitationCode", "This relation can not be created. The source is already linked to a different domain.");
        }
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }

        //Make sure there is no existing relation..
        D2DRelation d2DRelation = new D2DRelation();
        d2DRelation.setSourceDomainId(invitaiton.getDomainId());
        d2DRelation.setSourceSubEntityId(invitaiton.getSubEntityId());
        d2DRelation.setTargetDomainId(targetDomainId);
        d2DRelation.setTargetSubEntityId(targetSubEntityId);
        d2DRelation.setRelationType(invitaiton.getRelationType());
        d2DRelation.setActive(true);
        d2DRelation = d2DRelationRepository.save(d2DRelation);

        invitaiton.setStatus(D2DInvitationStatus.ACCEPTED);
        invitaiton.setAcceptedDate(new Date());
        invitaiton.setLinkedDomainId(targetDomainId);
        invitaiton.setLinkedSubEntityId(targetSubEntityId);
        d2DInvitationRepository.save(invitaiton);
        return d2DRelation;
    }


    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_D2DRELATION')")
    public List<D2DRelationDTO> getD2DRelations(Long domainId, D2DRelationType relationType) throws ApheDataValidationException {
        List<D2DRelation> retList = d2DRelationRepository.findBySourceDomainIdAndRelationType(domainId, relationType);
        if(retList != null) {
            return retList.stream().map(d2d->authMapper.d2DRelationToD2DRelationDTO(d2d)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_D2DRELATION')")
    public D2DRelationDTO getD2DRelation(Long domainId, Long subEntityId, D2DRelationType relationType) throws ApheDataValidationException {
        D2DRelation rel = d2DRelationRepository.findBySourceDomainIdAndSourceSubEntityIdAndRelationType(domainId, subEntityId, relationType);
        if(rel != null){
            return authMapper.d2DRelationToD2DRelationDTO(rel);
        }
        return null;
    }


    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_D2DRELATION')")
    public List<D2DRelationStatusDTO> getD2DRelationStatus(Long domainId, D2DRelationType relationType, List<Long> subEntityIds) throws ApheDataValidationException {
        HashMap<Long, D2DRelationStatusDTO> retVal = new HashMap<>();
        for(Long s : subEntityIds) {
            D2DRelationStatusDTO rel = new D2DRelationStatusDTO();
            rel.id = s;
            rel.sourceDomainId = domainId;
            rel.sourceSubEntityId = s;
            rel.relationType = relationType;
            rel.relationStatus =  D2DRelationStatus.NotConnected;
            retVal.put(s, rel);
        }

        if(subEntityIds.size()  == 0) {
            return retVal.values().stream().collect(Collectors.toList());
        }

        List<D2DRelation> relations = d2DRelationRepository.findBySourceDomainIdAndRelationTypeAndSourceSubEntityIdIn(domainId, relationType, subEntityIds);

        for(D2DRelation rel : relations) {
            D2DRelationStatusDTO relStatusDTO = retVal.get(rel.getSourceSubEntityId());
            if(relStatusDTO != null) {
                relStatusDTO.relationId = rel.getId();
                relStatusDTO.relationStatus = D2DRelationStatus.Connected;
            }
        }

        List<Long> notConnectedSubEntityIds = retVal.values().stream()
                .filter(rel->rel.relationStatus == D2DRelationStatus.NotConnected)
                .map(rel->rel.sourceSubEntityId)
                .collect(Collectors.toList());

        if(notConnectedSubEntityIds.size() > 0) {
            List<D2DInvitation> invitaitons = d2DInvitationRepository.findBySourceDomainIdAndRelationTypeAndSubEntityIdIn(domainId, relationType, notConnectedSubEntityIds);
            if(invitaitons.size() > 0) {
                for(Long subEntityId : notConnectedSubEntityIds) {
                    List<D2DInvitation> subEntityInvitations = invitaitons.stream()
                            .filter(inv->inv.getSubEntityId() == subEntityId)
                            .collect(Collectors.toList());


                    if(subEntityInvitations.size() > 0) {
                        Collections.sort(subEntityInvitations, new Comparator<D2DInvitation>() {
                            @Override
                            public int compare(D2DInvitation o1, D2DInvitation o2) {
                                return (int) (o2.getId() - o1.getId());
                            }
                        });
                        D2DInvitation theInvitation = subEntityInvitations.get(0);

                        logger.debug("theiNVATION ID IS = " + theInvitation.getId());

                        D2DRelationStatus relationStatus = null;
                        Long relId = null;
                        if(theInvitation.getStatus() == D2DInvitationStatus.ACCEPTED) {
                            relationStatus = D2DRelationStatus.DisConnected;
                        } else if (theInvitation.getStatus() == D2DInvitationStatus.INVITED){
                            relationStatus = D2DRelationStatus.Invited;
                            relId = theInvitation.getId();
                        } else if (theInvitation.getStatus() == D2DInvitationStatus.EXPIRED){
                            relationStatus = D2DRelationStatus.InvitationExpired;
                            relId = theInvitation.getId();
                        }
                        D2DRelationStatusDTO relStatusDTO = retVal.get(subEntityId);
                        if(relationStatus != null && relStatusDTO != null) {
                            relStatusDTO.relationStatus = relationStatus;
                            relStatusDTO.relationId = relId;
                        }
                    }
                }
            }
        }
        return retVal.values().stream().collect(Collectors.toList());
    }

    @Transactional
    @PreAuthorize("hasPermission(#targetDomainId, 'DOMAIN', 'READ_D2DRELATION')")
    public List<D2DRelationDTO> getTargetD2DRelations(Long targetDomainId, D2DRelationType relationType) throws ApheDataValidationException {
        List<D2DRelation> retList = d2DRelationRepository.findByTargetDomainIdAndRelationType(targetDomainId, relationType);
        if(retList != null) {
            return retList.stream().map(d2d->authMapper.d2DRelationToD2DRelationDTO(d2d)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }


    @Transactional
    @PreAuthorize("hasPermission(#targetDomainId, 'DOMAIN', 'READ_D2DRELATION')")
    public D2DRelationDTO getTargetD2DRelation(Long targetDomainId, Long targetSubEntityId, D2DRelationType relationType) throws ApheDataValidationException {
        D2DRelation rel = d2DRelationRepository.findByTargetDomainIdAndTargetSubEntityIdAndRelationType(targetDomainId, targetSubEntityId, relationType);
        if(rel != null){
            return authMapper.d2DRelationToD2DRelationDTO(rel);
        }
        return null;
    }



    @Transactional
    @PreAuthorize("hasPermission(#relationId, 'D2DRELATION', 'DELETE_D2DRELATION')")
    public void deleteD2DRelation(String relationId) throws ApheDataValidationException {
        D2DRelation relation = d2DRelationRepository.findById(Long.parseLong(relationId)).orElse(null);
        if (relation == null) {
            throw new ApheDataValidationException("relationId", "Invalid D2D relation id");
        }
        d2DRelationRepository.delete(relation);
    }
}
