package com.aphe.auth.service.d2d;

import com.aphe.auth.model.core.User;
import com.aphe.auth.service.BaseMailManager;
import com.aphe.common.mail.Email;
import com.aphe.common.mail.MailService;
import com.aphe.common.util.JSONUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Component
public class D2DInvitationMailManager extends BaseMailManager {

	@Value("${aphe.product.url}")
	private String productURL;

	@Value("${aphe.product.appName}")
	private String productName;

	@Value("${aphe.product.acceptDomainInvitationPath}")
	private String acceptDomainInvitationPath;

	@Value("${aphe.product.signInPath}")
	private String signInPath;

	@Value("${aphe.email.notifyTargetD2DRelationChanged.templateName}")
	private String notifyTargetD2DRelationChangedTemplateName;

	@Value("${aphe.email.notifySourceD2DRelationChanged.templateName}")
	private String notifySourceD2DRelationChangedTemplateName;

	@Autowired
	protected MailService mailService;

	@Autowired
	protected JSONUtils jsonUtil;

	public void notifyTargetD2DRelationChanged(User modifiedUser, User modifyingUser, Map<String, Object> otherAttributes) {

		String toEmail = modifiedUser.getEmail();
		String firstName = modifiedUser.getFirstName();
		String lastName = modifiedUser.getLastName();

		Object invitation_code = otherAttributes.get("INVITATION_CODE");
		String invitationCode = invitation_code != null ? (String) invitation_code : "" ;

		HashMap<String, String> params = new HashMap<>();
		params.put("email", toEmail);
		params.put("code", invitationCode);
		StringBuffer queryString = mailService.concatParams(params);

		Map<String, Object> mailAttributes = new HashMap<>();

		// Set Message specific properties.
		String inviteUserURL = productURL + acceptDomainInvitationPath + "?" + queryString.toString();
		mailAttributes.put("INVITATION_URL", inviteUserURL);

		mailAttributes.put("RECIPIENT_USER_FIRST_NAME", firstName);
		mailAttributes.put("RECIPIENT_USER_LAST_NAME", lastName);
		mailAttributes.put("RECIPIENT_USER_NAME", buildFullName(firstName, lastName));
		mailAttributes.put("RECIPIENT_USER_EMAIL", toEmail);

		if(modifyingUser != null) {
			mailAttributes.put("MODIFYING_USER_FIRST_NAME", modifyingUser.getFirstName());
			mailAttributes.put("MODIFYING_USER_LAST_NAME", modifyingUser.getLastName());
			mailAttributes.put("MODIFYING_USER_NAME", buildFullName(modifyingUser.getFirstName(), modifyingUser.getLastName()));
			mailAttributes.put("MODIFYING_USER_EMAIL", modifyingUser.getEmail());
		}

		mailAttributes.putAll(otherAttributes);

		addUserProperties(modifiedUser, mailAttributes);

		Email m = new Email(notifyTargetD2DRelationChangedTemplateName);

		String changeType = (String) mailAttributes.get("ACCESS_CHANGE_TYPE");
		if(changeType == null) {
			changeType = "";
		}
		String subject = String.format("Regarding your access to %s", mailAttributes.get("DOMAIN_NAME"));
		switch (changeType) {
			case "Inv": {
				subject = String.format("You have been invited to manage %s's account", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Add": {
				subject = String.format("You have been added as a user to manage %s's account", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Mod": {
				subject = String.format("Your access to %s's account has been changed", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Sus": {
				subject = String.format("Your access to %s's account has been suspended", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Res": {
				subject = String.format("Your access to %s's account has been restored", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Del": {
				subject = String.format("Your access to %s's account has been removed", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
		}

		m.setSubject(subject);
		m.addTo(firstName, lastName, toEmail);
		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);



	}

	public void notifyTeamUserChanged(User owner, List<User> adminUsers, User modifiedUser, User modifyingUser, Map<String, Object> otherAttributes) {
		String firstName = owner.getFirstName();
	}
}
