package com.aphe.auth.service.d2d;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.d2d.D2DInvitation;
import com.aphe.auth.model.d2d.D2DInvitationStatus;
import com.aphe.auth.model.d2d.D2DRelation;
import com.aphe.auth.model.d2d.D2DRelationType;
import com.aphe.auth.model.d2d.repo.D2DInvitationRepository;
import com.aphe.auth.model.d2d.repo.D2DRelationRepository;
import com.aphe.auth.service.AuthMapper;
import com.aphe.auth.service.BaseManager;
import com.aphe.auth.service.UserManagerTxn;
import com.aphe.auth.service.d2d.dto.CreateD2DInvitationDTO;
import com.aphe.auth.service.d2d.dto.D2DInvitationDTO;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import org.apache.commons.lang.RandomStringUtils;
import org.hibernate.validator.constraints.ValidationErrors;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service to manage D2DRelations. create, validate and delete.
 */
@Service
@Component
public class D2DInvitationManagerTxn extends BaseManager {
    private static Logger logger = LoggerFactory.getLogger(D2DInvitationManagerTxn.class);

    @Autowired
    protected AuthMapper authMapper;

    @Autowired
    protected D2DRelationRepository d2DRelationRepository;

    @Autowired
    protected D2DInvitationRepository d2DInvitationRepository;

    @Autowired
    D2DInvitationMailManager d2DInvitationMailManager;

    @Autowired
    protected DomainRepository domainRepository;

    @Autowired
    protected UserManagerTxn userManagerTxn;

    @Transactional
    @PreAuthorize("hasPermission(#invitation.domainId, 'DOMAIN', 'CREATE_D2DINVITATION')")
    public D2DInvitation addInvitation(CreateD2DInvitationDTO invitation) throws ApheDataValidationException {
        //Do dto Level validation checks
        ValidationErrors errors = getValidationErrors(invitation);

        //Check there is an existing relation
        D2DRelation existingRelation = d2DRelationRepository.findBySourceDomainIdAndSourceSubEntityIdAndRelationType(invitation.domainId, invitation.subEntityId, invitation.relationType);
        if (existingRelation != null) {
            errors.addMessage("subEntityId", "A relation of specified type already exists for this entity");
        }

        //Check there is an existing unexpired invitation
        List<D2DInvitation> existingInvitations = d2DInvitationRepository.findByDomainIdAndSubEntityIdAndRelationType(invitation.domainId, invitation.subEntityId, invitation.relationType);
        List<D2DInvitation> activeInvitations = existingInvitations.stream().filter(e -> e.getStatus() == D2DInvitationStatus.INVITED).collect(Collectors.toList());
        if (activeInvitations != null && activeInvitations.size() > 0) {
            errors.addMessage("subEntityId", "An an existing active invitation found. Please delete it or resend the original invitation.");
        }

        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }

        //create and save the d2d invitation.
        D2DInvitation invitationEntity = authMapper.createD2DInvitationDTOToD2DInvitation(invitation);
        invitationEntity.setCreatedDate(new Date());
        invitationEntity.setInvitationCode(RandomStringUtils.random(50, true, true));
        invitationEntity.setStatus(D2DInvitationStatus.INVITED);
        return d2DInvitationRepository.save(invitationEntity);
    }

    @Transactional
    public D2DInvitationDTO getInvitationByCode(String invitationCode) {
        D2DInvitation existingInvitation = d2DInvitationRepository.findByInvitationCode(invitationCode);
        if (existingInvitation != null) {
            D2DInvitationDTO d2DInvitationDTO = authMapper.d2DInvitationToD2DInvitationDTO(existingInvitation);
            d2DInvitationDTO.existingUser = userManagerTxn.loginExists(d2DInvitationDTO.emailAddress);
            Domain d = domainRepository.findById(Long.parseLong(existingInvitation.getDomainId())).orElse(null);
            if (d != null) {
                d2DInvitationDTO.domainName = d.getName();
            }
            return d2DInvitationDTO;
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasPermission(#invitationId, 'D2DINVITATION', 'READ_D2DINVITATION')")
    public D2DInvitationDTO getInvitation(String invitationId) {
        D2DInvitation existingInvitation = d2DInvitationRepository.findById(Long.parseLong(invitationId)).orElse(null);
        if (existingInvitation != null) {
            return authMapper.d2DInvitationToD2DInvitationDTO(existingInvitation);
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_D2DINVITATION')")
    public List<D2DInvitationDTO> getInvitations(String domainId, D2DRelationType relationType) throws ApheDataValidationException {
        List<D2DInvitation> existingInvitations = d2DInvitationRepository.findByDomainIdAndRelationType(domainId, relationType);
        if (existingInvitations != null) {
            return existingInvitations.stream().map(inv -> authMapper.d2DInvitationToD2DInvitationDTO(inv)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Transactional
    @PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_D2DINVITATION')")
    public List<D2DInvitationDTO> getInvitations(String domainId, String subEntityId, D2DRelationType relationType) throws ApheDataValidationException {
        List<D2DInvitation> existingInvitations = d2DInvitationRepository.findByDomainIdAndSubEntityIdAndRelationType(domainId, subEntityId, relationType);
        if (existingInvitations != null) {
            return existingInvitations.stream().map(inv -> authMapper.d2DInvitationToD2DInvitationDTO(inv)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Transactional
    @PreAuthorize("hasPermission(#invitationId, 'D2DINVITATION', 'DELETE_D2DINVITATION')")
    public void deleteInvitation(String invitationId) throws ApheDataValidationException {
        D2DInvitation inv = d2DInvitationRepository.findById(Long.parseLong(invitationId)).orElse(null);
        if (inv == null) {
            throw new ApheDataValidationException("invitationId", "Invalid invitation id");
        }
        if (inv.getStatus() == D2DInvitationStatus.ACCEPTED) {
            throw new ApheDataValidationException("invitationId", "You can not delete an invitation that has been accepted.");
        }
        d2DInvitationRepository.delete(inv);
    }

    @Transactional
    @PreAuthorize("hasPermission(#invitationId, 'D2DINVITATION', 'CREATE_D2DINVITATION')")
    public boolean resetInvitationCode(String invitationId) throws ApheDataValidationException {
        D2DInvitation inv = d2DInvitationRepository.findById(Long.parseLong(invitationId)).orElse(null);
        if (inv != null && (inv.getStatus() == D2DInvitationStatus.INVITED ||inv.getStatus() == D2DInvitationStatus.EXPIRED)) {
            inv.setInvitationCode(RandomStringUtils.random(50, true, true));
            inv.setStatus(D2DInvitationStatus.INVITED);
            d2DInvitationRepository.save(inv);
            return true;
        }
        return false;
    }


    @Transactional
    @PreAuthorize("hasPermission(#invitationId, 'D2DINVITATION', 'CREATE_D2DINVITATION')")
    public void notifyUserNewD2DInvitation(String invitationId) throws ApheDataValidationException {
        D2DInvitation inv = d2DInvitationRepository.findById(Long.parseLong(invitationId)).orElse(null);
        if (inv != null && inv.getStatus() == D2DInvitationStatus.INVITED) {

            User loggedInUser = getLoggedInUser();
            Domain d = domainRepo.findById(Long.parseLong(inv.getDomainId())).orElse(null);

            Map<String, Object> mailAttributes = new HashMap<>();
            mailAttributes.put("INVITATION_CODE", inv.getInvitationCode());
            mailAttributes.put("SOURCE_DOMAIN_NAME", d.getName());
            mailAttributes.put("CHANGE_TYPE", "Inv");
            mailAttributes.put("REL_TYPE", inv.getRelationType().name());

            mailAttributes.put("TARGET_DOMAIN_NAME", inv.getName());

            User modifiedUser = buildUserFromInvitation(inv);

            d2DInvitationMailManager.notifyTargetD2DRelationChanged(modifiedUser,loggedInUser, mailAttributes);

        }
    }

    @NotNull
    private User buildUserFromInvitation(D2DInvitation inv) {
        User modifiedUser = new User();
        modifiedUser.setFirstName(inv.getName());
        modifiedUser.setLastName(" ");
        modifiedUser.setEmail(inv.getEmailAddress());
        return modifiedUser;
    }

}
