package com.aphe.auth.service.d2d.dto;

import com.aphe.auth.model.d2d.D2DRelationType;

public class D2DRelationDTO {

    public String id;

    public String sourceDomainId;

    public String sourceSubEntityId;

    public String targetDomainId;

    public String targetSubEntityId;

    public D2DRelationType relationType;

    public boolean isActive;

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }
}
