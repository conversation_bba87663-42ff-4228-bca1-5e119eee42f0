package com.aphe.auth.service.d2d.dto;

import com.aphe.auth.model.d2d.D2DRelationType;
import org.hibernate.validator.constraints.TrimLength;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class CreateD2DInvitationDTO {

    @NotBlank
    public String domainId;

    @NotBlank
    public String subEntityId;

    @NotNull
    public D2DRelationType relationType;

    @TrimLength(min = 2, max = 100)
    @NotBlank
    @Email
    public String emailAddress;

    @TrimLength(min = 1, max = 100)
    @NotBlank
    public String name;

}
