package com.aphe.auth.service.d2d;

import com.aphe.auth.model.d2d.D2DInvitation;
import com.aphe.auth.service.BaseManager;
import com.aphe.auth.service.d2d.dto.CreateD2DInvitationDTO;
import com.aphe.auth.service.d2d.dto.D2DInvitationDTO;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * non transactional orchestration service to manage D2DRelations. create, validate and delete.
 */
@Service
@Component
public class D2DInvitationManager extends BaseManager {
    private static Logger logger = LoggerFactory.getLogger(D2DInvitationManager.class);

    @Autowired
    D2DInvitationManagerTxn d2DInvitationManagerTxn;

    public D2DInvitationDTO addInvitation(CreateD2DInvitationDTO invitation) throws ApheDataValidationException {
        D2DInvitation inv = d2DInvitationManagerTxn.addInvitation(invitation);
        if(inv != null) {
            d2DInvitationManagerTxn.notifyUserNewD2DInvitation(Long.toString(inv.getId()));
            return d2DInvitationManagerTxn.getInvitation(Long.toString(inv.getId()));
        }else {
            return null;
        }
    }

    public boolean resendDomainInvitation(String invitationId) throws ApheDataValidationException {
        boolean resetSuccess = d2DInvitationManagerTxn.resetInvitationCode(invitationId);
        if(resetSuccess) {
            d2DInvitationManagerTxn.notifyUserNewD2DInvitation(invitationId);
            return true;
        }
        return false;
    }

}
