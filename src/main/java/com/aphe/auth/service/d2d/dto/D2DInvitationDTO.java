package com.aphe.auth.service.d2d.dto;

import com.aphe.auth.model.d2d.D2DInvitationStatus;
import com.aphe.auth.model.d2d.D2DRelationType;

import java.util.Date;

public class D2DInvitationDTO {

    public String id;

    public String domainId;

    public String subEntityId;

    public D2DRelationType relationType;

    public String emailAddress;

    public String name;

    public String invitationCode;

    public D2DInvitationStatus status;

    public Date createdDate;

    public Date acceptedDate;

    public String linkedDomainId;

    public String linkedSubEntityId;

    public boolean existingUser;

    public String domainName;

}
