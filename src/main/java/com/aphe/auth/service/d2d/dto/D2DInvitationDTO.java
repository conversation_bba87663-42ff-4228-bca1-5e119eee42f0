package com.aphe.auth.service.d2d.dto;

import com.aphe.auth.model.d2d.D2DInvitationStatus;
import com.aphe.auth.model.d2d.D2DRelationType;

import java.util.Date;

public class D2DInvitationDTO {

    public String id;

    public Long domainId;

    public Long subEntityId;

    public D2DRelationType relationType;

    public String emailAddress;

    public String name;

    public String invitationCode;

    public D2DInvitationStatus status;

    public Date createdDate;

    public Date acceptedDate;

    public Long linkedDomainId;

    public Long linkedSubEntityId;

    public boolean existingUser;

    public String domainName;

}
