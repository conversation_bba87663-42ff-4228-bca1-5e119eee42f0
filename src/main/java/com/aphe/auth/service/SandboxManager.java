package com.aphe.auth.service;

import com.aphe.auth.service.dto.CreateDomainDTO;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.auth.service.dto.DomainType;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.github.javafaker.Faker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Locale;

@Component
public class SandboxManager extends BaseManager {

	@Autowired
	protected  AccountManagerTxn accountManagerTxn;

	public void seedAccount(DomainDTO createdDomain, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException, ApheException, ApheForbiddenException {
		if (createdDomain.domainType == DomainType.A) {
			Faker faker = new Faker(new Locale(Locale.US.getCountry()));
			for (int i = 0; i < 2; i++) {
				CreateDomainDTO client = createAFakeDomain(faker);
				accountManagerTxn.addClient(Long.toString(createdDomain.id), client);
			}
		}
	}

	private CreateDomainDTO createAFakeDomain(Faker faker) {
		CreateDomainDTO input = new CreateDomainDTO();
		input.domainType = DomainType.B;
		input.name = faker.company().name().replaceAll("[^A-Za-z0-9\\-& ]", "");
		input.firstName = faker.name().firstName();
		input.lastName = faker.name().lastName();

		return input;
	}

}
