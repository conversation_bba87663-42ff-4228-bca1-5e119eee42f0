package com.aphe.auth.service;

import com.aphe.auth.model.u2d.repo.ClientAccessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * Service to manage ClientAccess, client access rows and all the related business logic.
 */
@Service
@Component
public class ClientAccessManager extends BaseManager {
    private static Logger logger = LoggerFactory.getLogger(ClientAccessManager.class);

    @Autowired
    protected AuthMapper authMapper;

    @Autowired
    protected ClientAccessRepository clientAccessRepository;

    @Autowired
    protected DomainAccessManager domainAccessManager;

//    /**
//     * Global Roles for client access can only be 1. Admin or Standard user .. how do we implement Owner transfer??
//     * In case of ER/EE access there could be more than 1 role needed and user has to pick role to join that company.
//     */
//    @Transactional
//    @PreAuthorize("hasPermission(#clientAccessDTO.accountantId, 'ACCOUNTANT', 'CREATE_CLIENTACCESS')")
//    public ClientAccess addClientAccess(CreateClientAccessDTO clientAccessDTO) throws ApheDataValidationException {
//
//        User user = userRepo.findById(clientAccessDTO.userId).orElse(null);
//        Domain client = domainRepo.findById(clientAccessDTO.clientId).orElse(null);
//        Accountant accountant = (Accountant) domainRepo.findById(clientAccessDTO.accountantId).orElse(null);
//
//        ValidationErrors validationErrors = new ValidationErrors();
//
//        if (user == null) {
//            validationErrors.addMessage("userId", "Invalid user");
//        }
//        if (client == null) {
//            validationErrors.addMessage("clientId", "Invalid client");
//        }
//        if (accountant == null) {
//            validationErrors.addMessage("accountantId", "Invalid accountant");
//        }
//
//        List<ClientAccess> currentAccess = clientAccessRepository.findByAccountantIdAndUserIdAndClientId(accountant.getId(), user.getId(), client.getId());
//
//        if (currentAccess != null && currentAccess.size() > 0) {
//            throw new ApheDataValidationException("userId", "User has already access to this client. Please update existing access to add/remove roles");
//        }
//
//        boolean isClient = false;
//        for (AccountantClientRelation clientRelation : accountant.getClientRelations()) {
//            if (clientRelation.getClient().getId() == clientAccessDTO.clientId) {
//                isClient = true;
//                break;
//            }
//        }
//        if (!isClient) {
//            validationErrors.addMessage("clientId", "Invalid clientId");
//        }
//
//
//        boolean isUser = false;
//        for (DomainAccessDTO userAccessList : domainAccessManager.getDomainAccessByDomain(Long.toString(clientAccessDTO.accountantId))) {
//            if (userAccessList.userId == clientAccessDTO.userId) {
//                isUser = true;
//                break;
//            }
//        }
//        if (!isUser) {
//            validationErrors.addMessage("clientId", "Invalid userId");
//        }
//
//        if (clientAccessDTO.globalRole == null) {
//            validationErrors.addMessage("globalRole", "Invalid global role. Please add a global role.");
//        }
//
//        if (validationErrors.getMessages().size() > 0) {
//            throw new ApheDataValidationException(validationErrors, "Invalid Input");
//        }
//
//        ClientAccess clientAccess = authMapper.createClientAccessDTOToClientAccess(clientAccessDTO);
//        clientAccess.setClient(client);
//        clientAccess.setAccountant(accountant);
//        clientAccess.setUser(user);
//        clientAccess.setActive(true);
//
//        return clientAccessRepository.save(clientAccess);
//    }
//
//
//    @Transactional
//    @PreAuthorize("hasPermission(#accountantId, 'ACCOUNTANT', 'READ_CLIENTACCESS')")
//    public List<ClientAccessDTO> getClientAccessList(String accountantId) throws ApheDataValidationException {
//        Domain domain = domainRepo.findById(Long.parseLong(accountantId)).orElse(null);
//        if (domain == null || !(domain instanceof Accountant)) {
//            throw new ApheDataValidationException("id", "This account is not an accountant account.");
//        }
//
//        List<ClientAccess> clientAccessList = clientAccessRepository.findByAccountantId(domain.getId());
//        return buildClientAccessDTOs(clientAccessList);
//    }
//
//    @Transactional
//    @PreAuthorize("hasPermission(#accountantId, 'ACCOUNTANT', 'READ_CLIENTACCESS')")
//    public List<ClientAccessDTO> getClientAccessListByUser(String accountantId, String userId) throws ApheDataValidationException {
//        Domain domain = domainRepo.findById(Long.parseLong(accountantId)).orElse(null);
//        User user = userRepo.findById(Long.parseLong(userId)).orElse(null);
//        if (domain == null || !(domain instanceof Accountant)) {
//            throw new ApheDataValidationException("accountantId", "This account is not an accountant account.");
//        }
//        if (user != null) {
//            throw new ApheDataValidationException("userId", "Invalid user.");
//        }
//
//        List<ClientAccess> clientAccessList = clientAccessRepository.findByAccountantIdAndUserId(domain.getId(), user.getId());
//        return buildClientAccessDTOs(clientAccessList);
//    }
//
//    @Transactional
//    @PreAuthorize("hasPermission(#accountantId, 'ACCOUNTANT', 'READ_CLIENTACCESS')")
//    public List<ClientAccessDTO> getClientAccessListByClient(String accountantId, String clientId) throws ApheDataValidationException {
//
//        Domain domain = domainRepo.findById(Long.parseLong(accountantId)).orElse(null);
//        Domain client = domainRepo.findById(Long.parseLong(clientId)).orElse(null);
//        if (domain == null || !(domain instanceof Accountant)) {
//            throw new ApheDataValidationException("accountantId", "This account is not an accountant account.");
//        }
//        if (client != null) {
//            throw new ApheDataValidationException("userId", "Invalid client.");
//        }
//
//        List<ClientAccess> clientAccessList = clientAccessRepository.findByAccountantIdAndClientId(domain.getId(), client.getId());
//        return buildClientAccessDTOs(clientAccessList);
//    }
//
//    private List<ClientAccessDTO> buildClientAccessDTOs(List<ClientAccess> clientAccessList) {
//        List<ClientAccessDTO> clientAccessDTOList = new ArrayList<>();
//        for (ClientAccess clientAccess : clientAccessList) {
//            if (clientAccess.isActive()) {
//                clientAccessDTOList.add(authMapper.clientAccessToClientAccessDTO(clientAccess));
//            }
//        }
//        return clientAccessDTOList;
//    }
//
//    @Transactional
//    @PreAuthorize("hasPermission(#accountantId, 'ACCOUNTANT', 'UPDATE_CLIENTACCESS')")
//    public void updateClientAccess(String accountantId, UpdateClientAccessDTO clientAccessDTO) throws ApheDataValidationException {
//        ValidationErrors errors = getValidationErrors(clientAccessDTO);
//
//        ClientAccess clientAccess = clientAccessRepository.findById(clientAccessDTO.id).orElse(null);
//        if (clientAccess == null || clientAccess.getAccountant().getId() != Long.parseLong(accountantId)) {
//            throw new ApheDataValidationException("id", "Invalid clientAccessId");
//        }
//
//        if (clientAccessDTO.globalRole == GlobalRole.ROLE_OWNER || clientAccess.getGlobalRole() == GlobalRole.ROLE_OWNER) {
//            errors.addMessage("globalRole", "Invalid role for client access");
//        }
//
//        if (errors.getMessages().size() > 0) {
//            throw new ApheDataValidationException(errors);
//        }
//
//        clientAccess.setGlobalRole(clientAccessDTO.globalRole);
//        clientAccess.setLocalRoles(new ArrayList<>(clientAccessDTO.localRoles));
//        clientAccess.setActive(clientAccessDTO.isActive);
//
//        clientAccessRepository.save(clientAccess);
//    }
//
//    @Transactional
//    @PreAuthorize("hasPermission(#accountantId, 'ACCOUNTANT', 'UPDATE_CLIENTACCESS')")
//    public void toggleClientAccess(String accountantId, long clientAccessId, boolean isActive) throws ApheDataValidationException {
//
//        ValidationErrors errors = new ValidationErrors();
//
//        ClientAccess clientAccess = clientAccessRepository.findById(clientAccessId).orElse(null);
//        if (clientAccess == null || clientAccess.getAccountant().getId() != Long.parseLong(accountantId)) {
//            throw new ApheDataValidationException("id", "Invalid clientAccessId");
//        }
//
//        if (clientAccess.getGlobalRole() == GlobalRole.ROLE_OWNER) {
//            errors.addMessage("globalRole", "Invalid role for client access.");
//        }
//
//        if (errors.getMessages().size() > 0) {
//            throw new ApheDataValidationException(errors);
//        }
//
//        clientAccess.setActive(isActive);
//
//        clientAccessRepository.save(clientAccess);
//    }
//
//    @Transactional
//    @PreAuthorize("hasPermission(#accountantId, 'ACCOUNTANT', 'UPDATE_CLIENTACCESS')")
//    public void deleteClientAccess(String accountantId, long clientAccessId) throws ApheDataValidationException {
//
//        ValidationErrors errors = new ValidationErrors();
//
//        ClientAccess clientAccess = clientAccessRepository.findById(clientAccessId).orElse(null);
//        if (clientAccess == null || clientAccess.getAccountant().getId() != Long.parseLong(accountantId)) {
//            throw new ApheDataValidationException("id", "Invalid clientAccessId");
//        }
//
//        if (clientAccess.getGlobalRole() == GlobalRole.ROLE_OWNER) {
//            errors.addMessage("globalRole", "Invalid role for client access.");
//        }
//
//        if (errors.getMessages().size() > 0) {
//            throw new ApheDataValidationException(errors);
//        }
//
//        clientAccessRepository.delete(clientAccess);
//    }
}
