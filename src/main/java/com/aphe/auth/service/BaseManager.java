package com.aphe.auth.service;

import com.aphe.auth.service.dto.SessionDTO;
import com.aphe.auth.model.a2c.AccountantClientRelation;
import com.aphe.auth.model.core.Accountant;
import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.Login;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.u2d.ClientAccess;
import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.core.repo.LoginRepository;
import com.aphe.auth.model.core.repo.UserRepository;
import com.aphe.auth.security.authorization.AuthUtil;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.service.CommonBaseManager;
import com.aphe.common.util.StringUtil;
import org.hibernate.validator.constraints.ValidationErrors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.util.*;

public abstract class BaseManager extends CommonBaseManager {

	@Autowired
	protected LoginRepository loginRepo;

	@Autowired
	protected UserRepository userRepo;

	@Autowired
	protected DomainRepository domainRepo;

	@Autowired
	protected Validator validator;

	@Autowired
	protected AuthUtil authUtil;

	protected Logger logger = LoggerFactory.getLogger(getClass());

	protected boolean isAccessible(Domain d) {
		long domainId = getCurrentDoaminIdAsLong();
		return d != null && (isSuperAdmin() || (domainId > 0 && domainId == d.getId()));
	}

	public Login findLogin(String loginName) {
		if (StringUtil.isEmpty(loginName))
			return null;
		Login login = loginRepo.findByLoginName(loginName);
		return login;
	}

	protected User findUserByLogin(String loginName) {
		Login l = findLogin(loginName);
		if (l != null) {
			return l.getUser();
		}
		return null;
	}

	public long findUserIdByLogin(String loginName) {
		Login l = findLogin(loginName);
		if (l != null) {
			return l.getUser().getId();
		}
		return -1;
	}

	protected User findUserByUserId(long userId) {
		return userRepo.findById(userId).orElse(null);
	}

	public User getLoggedInUser() {
		Authentication auth = getAuthentication();
		String name = auth.getName();
		Login l = findLogin(name);
		if (l != null)
			return userRepo.findById(l.getUser().getId()).orElse(null);
		return null;
	}

	public long getLoggedDomainId() {
		Authentication auth = getAuthentication();
		if (auth != null && auth.isAuthenticated()) {
			ApheUserDetails principal = (ApheUserDetails) auth.getPrincipal();
			return principal.getDomainId();
		}
		return -1;
	}

	protected Authentication getAuthentication() {
		return SecurityContextHolder.getContext().getAuthentication();
	}

	public long getParentDoaminId() {
		Authentication auth = getAuthentication();
		if (auth != null && auth.isAuthenticated()) {
			ApheUserDetails principal = (ApheUserDetails) auth.getPrincipal();
			return principal.getParentDomainId();
		}
		return -1;
	}

	public User getAuthroizedUser(String loginName) throws Exception {
		Login theLogin = findLogin(loginName);
		return getAuthroizedUser(theLogin.getUser().getId());
	}

	public User getAuthroizedUser(long userId) throws Exception {
		User u = getLoggedInUser();
		if (u == null || u.getId() != userId) {
			throw new Exception("Not Authorized");
		}
		return u;
	}

	public SessionDTO getCurrentSession() throws ApheForbiddenException {

		User u = getLoggedInUser();
		if (u == null) {
			throw new ApheForbiddenException();
		} else {

			long currentDoaminId = getCurrentDoaminIdAsLong();
			long parentDoaminId = getParentDoaminId();

			Domain currentDomain = domainRepo.findById(currentDoaminId).orElse(null);
			Domain parentDomain = domainRepo.findById(parentDoaminId).orElse(null);

			SessionDTO dto = new SessionDTO();
			dto.userId = u.getId();
			dto.loginName = u.getLogin().getLoginName();
			dto.domainId = currentDoaminId;
			dto.parentDomainId = parentDoaminId;
			dto.domainName = currentDomain != null ? currentDomain.getName() : "";
			dto.parentDomainName = parentDomain != null ? parentDomain.getName() : "";
			dto.firstName = u.getFirstName();
			dto.lastName = u.getLastName();
			dto.email = u.getEmail();
			dto.isSuperAdmin = isSuperAdmin();
			dto.isEmailConfirmed = u.getLogin().isEmailConfirmed();
			dto.domainType = currentDomain != null ? currentDomain.getDomainType() : null;
			dto.ssoPartner = u.getLogin().getSsoPartner() != null ? u.getLogin().getSsoPartner().toString() : "";

			return dto;
		}
	}

	public boolean userHasRoleInDomain(long userId, long domainId) {
		List<GlobalRole> allowedUserRole = Arrays.asList(GlobalRole.ROLE_OWNER, GlobalRole.ROLE_ADMIN, GlobalRole.ROLE_USER);

		User u = getLoggedInUser();
		if (u == null) {
			return false;
		}
		if (u.getId() == userId) {
			//Check if the user has a direct role in the domain that is being requested..

			DomainAccess theDomainAccess = u.getDomainAccess().stream().filter(da->da.getDomain().getId() == domainId && da.isActive() == true &&
					allowedUserRole.contains(da.getGlobalRole())).findFirst().orElse(null);

			return theDomainAccess != null;

//			List<DomainAccess> domainAccessList = u.getDomainAccess();
//			for (DomainAccess globalRole : domainAccessList) {
//				if (globalRole.isActive() && globalRole.getDomain().getId() == domainId) {
//					GlobalRole role = globalRole.getGlobalRole();
//					return role == GlobalRole.ROLE_OWNER || role == GlobalRole.ROLE_ADMIN || role == GlobalRole.ROLE_USER;
//				}
//			}
//			return false;
		}
		return false;
	}

	public boolean userHasClientAccess(long userId, long domainId) {
		User u = getLoggedInUser();
		if (u == null) {
			return false;
		}

		long currentDomainId = getParentDoaminId();

		boolean hasAccess = false;
		if (u.getId() == userId) {

			DomainAccess theDomainAccess = u.getDomainAccess().stream().filter(da->da.getDomain().getId() == currentDomainId && da.isActive() == true).findFirst().orElse(null);
			if(theDomainAccess != null && theDomainAccess.getDomain() instanceof Accountant) {
				Accountant accountant = (Accountant) theDomainAccess.getDomain();

				AccountantClientRelation theClientRel = accountant.getClientRelations().stream()
						.filter(ac->ac.getClient().getId() == domainId)
						.findFirst().orElse(null);

				ClientAccess theClientAccess = theDomainAccess.getClientAccess().stream().filter(ca-> ca.getClient().getId() == domainId && ca.isActive() == true).findFirst().orElse(null);
				if(theClientRel != null && theClientAccess != null) {
					hasAccess = true;
				}
			}

			//if the user doesn't have role.. lets see if the logged in domain is an accountant and if this domain is one of the clients of this domain.
//			long currentDomainId = getParentDoaminId();
//			Domain currentDomain = domainRepo.findById(currentDomainId).orElse(null);
//			if (currentDomain != null && currentDomain instanceof Accountant) {
//				Accountant accountant = (Accountant) currentDomain;
//				for (AccountantClientRelation accCliRelation : accountant.getClientRelations()) {
//					if (accCliRelation.getClient().getId() == domainId) {
//						List<ClientAccess> clientAccessList = accountant.getClientAccess();
//						for (ClientAccess ca : clientAccessList) {
//							if (ca.isActive() && ca.getUser().getId() == userId && ca.getClient().getId() == domainId) {
//								hasAccess = true;
//								break;
//							}
//						}
//						break;
//					}
//				}
//
//			}
		}
		return hasAccess;

	}

	public ValidationErrors convertConstraintViolations(Set<? extends ConstraintViolation<?>> violations) {
		Map<String, Set<String>> responseObj = new HashMap<String, Set<String>>();
		for (ConstraintViolation<?> violation : violations) {
			String propertyPath = violation.getPropertyPath().toString();
			Set<String> messages = responseObj.get(propertyPath);
			if (messages == null) {
				messages = new HashSet<String>();
				responseObj.put(propertyPath, messages);
			}
			messages.add(violation.getMessage());
		}
		// MapWrapper<String, Set<String>> mapWrapper = new MapWrapper<String, Set<String>>(responseObj);
		ValidationErrors errors = new ValidationErrors();
		errors.setMessages(responseObj);
		return errors;
	}

	public ValidationErrors getValidationErrors(Object dto) {
		Set<ConstraintViolation<Object>> constraintViolations = validator.validate(dto);
		return convertConstraintViolations(constraintViolations);
	}

}
