package com.aphe.auth.service;

import com.aphe.auth.model.a2c.AccountantClientRelation;
import com.aphe.auth.model.core.Accountant;
import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.Login;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.core.repo.*;
import com.aphe.auth.model.u2d.ClientAccess;
import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.auth.model.u2d.Invitation;
import com.aphe.auth.model.u2d.repo.DomainAccessRepository;
import com.aphe.auth.model.u2d.repo.InvitationRepository;
import com.aphe.auth.security.TokenManager;
import com.aphe.auth.service.dto.*;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.security.RecaptchaTokenValidator;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.JavaUtil;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.StringUtil;
import org.apache.commons.lang.RandomStringUtils;
import org.hibernate.validator.constraints.ValidationErrors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Component
public class UserManagerTxn extends BaseManager {
    private static final Logger logger = LoggerFactory.getLogger(UserManagerTxn.class);

    @Autowired
    protected AuthMapper authMapper;

    @Autowired
    private UserRepository userRepo;

    @Autowired
    private LoginRepository loginRepo;

    @Autowired
    private DomainRepository domainRepo;

    @Autowired
    private DomainAccessRepository domainAccessRepository;

    @Autowired
    private RecaptchaTokenValidator recaptchaTokenValidator;

    @Autowired
    BCryptPasswordEncoder passwordEncoder;

    @Autowired
    JwtUtil jwtUtil;

    @Autowired
    MailManager mailManager;

    @Autowired
    TokenManager tokenManager;

    @Autowired
    protected InvitationRepository invitationRepository;

    @Autowired
    protected ApheUserDetailsService apheUserDetailsService;


    /**
     * Will create an account with email address and will generate and send a email confirmation code Email needs to be confirmed. Also creates the right type of domain with right
     * access roles for the user who is creating the domain.
     *
     * @param
     * @return
     */
    @Transactional
    public User createUser(CreateUserDTO accountDTO) throws ApheDataValidationException {

        if (PropertiesManager.isSandbox()) {
            accountDTO.password = RandomStringUtils.random(30, true, true);
            accountDTO.password = accountDTO.password + "A!";
        }

        boolean validRecaptchaToken = recaptchaTokenValidator.verifyRecaptchaToken(accountDTO.recaptchaToken, accountDTO.ipAddress);

        ValidationErrors errors = getValidationErrors(accountDTO);
        if (!validRecaptchaToken) {
            errors.addMessage("recaptchaToken", "Invalid ReCaptcha Token");
        }
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        } else {

            // Create Login;
            Login l = new Login();
            l.setLoginName(accountDTO.getEmail());
            l.setEnabled(true);
            l.setAccountNonExpired(true);
            l.setAccountNonLocked(true);
            l.setCredentialsNonExpired(true);
            l.setHashedPassword(passwordEncoder.encode(accountDTO.password));

            if (PropertiesManager.isSandbox()) {
                l.setEmailConfirmed(true);
                l.setEmailConfirmationCode("");
            } else {
                l.setEmailConfirmed(false);
                l.setEmailConfirmationCode(RandomStringUtils.random(50, true, true));
            }

            // Create User
            User u = new User();
            u.setEmail(accountDTO.getEmail());
            u.setFirstName(accountDTO.firstName);
            u.setLastName(accountDTO.lastName);

            u.setLogin(l);
            l.setUser(u);

            userRepo.save(u);
            loginRepo.save(l);

            return u;
        }
    }

    @Transactional
    public User createSSOUser(CreateSSOUser ssoUserDTO) throws ApheDataValidationException {

        ValidationErrors errors = getValidationErrors(ssoUserDTO);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        } else {

            // Create Login;
            Login l = new Login();
            l.setLoginName(ssoUserDTO.loginName);
            l.setEnabled(true);
            l.setAccountNonExpired(true);
            l.setAccountNonLocked(true);
            l.setCredentialsNonExpired(true);
            l.setHashedPassword("SSOACCOUNT-NOPASSWORD");
            l.setSsoPartner(ssoUserDTO.ssoPartner);

            if (ssoUserDTO.isEmailConfirmed) {
                l.setEmailConfirmed(true);
                l.setEmailConfirmationCode("");
            } else {
                l.setEmailConfirmed(false);
                l.setEmailConfirmationCode(RandomStringUtils.random(50, true, true));
            }

            // Create User
            User u = new User();
            u.setEmail(ssoUserDTO.email);
            u.setFirstName(ssoUserDTO.firstName);
            u.setLastName(ssoUserDTO.lastName);

            u.setLogin(l);
            l.setUser(u);

            userRepo.save(u);
            loginRepo.save(l);

            return u;
        }
    }

    @Transactional
    public boolean loginExists(String loginName) {
        Login theLogin = loginRepo.findByLoginName(loginName);
        return theLogin != null;
    }

    @Transactional
    @PreAuthorize("hasPermission(#personalInfoDTO.id, 'USER', 'UPDATE_USER')")
    public void updateUser(PersonalInfoDTO personalInfoDTO) throws ApheDataValidationException {
        User u = userRepo.findById(personalInfoDTO.id).orElse(null);
        ValidationErrors errors = getValidationErrors(personalInfoDTO);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }

        u.setFirstName(personalInfoDTO.firstName);
        u.setLastName(personalInfoDTO.lastName);
        userRepo.save(u);
    }

    @Transactional
    @PreAuthorize("hasPermission(#ssoUserDTO.id, 'USER', 'UPDATE_USER')")
    public User updateSSOUser(CreateSSOUser ssoUserDTO) throws ApheDataValidationException {
        User u = userRepo.findById(ssoUserDTO.id).orElse(null);
        ValidationErrors errors = getValidationErrors(ssoUserDTO);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }
        if (u != null) {
            u.setFirstName(ssoUserDTO.firstName);
            u.setLastName(ssoUserDTO.lastName);
            u.setEmail(ssoUserDTO.email);


            Login l = u.getLogin();

            l.setSsoPartner(ssoUserDTO.ssoPartner);

            if (ssoUserDTO.isEmailConfirmed) {
                l.setEmailConfirmed(true);
                l.setEmailConfirmationCode("");
            } else {
                l.setEmailConfirmed(false);
                l.setEmailConfirmationCode(RandomStringUtils.random(50, true, true));
            }

            loginRepo.save(l);
            userRepo.save(u);
        }
        return u;
    }

    @Transactional
    public void sendEmailConfirmationLink(String loginName) {
        User u = findUserByLogin(loginName);
        Login theLogin = u.getLogin();

        if (PropertiesManager.isSandbox()) {
            theLogin.setEmailConfirmed(true);
            theLogin.setEmailConfirmationCode("");
        } else {
            theLogin.setEmailConfirmed(false);
            theLogin.setEmailConfirmationCode(RandomStringUtils.random(50, true, true));
            mailManager.sendEmailConfirmationEmail(u, u.getLogin().getEmailConfirmationCode());
        }

        loginRepo.save(theLogin);
    }

    @Transactional
    public boolean confirmEmail(EmailConfirmationDTO emailConfirmationDTO) throws ApheDataValidationException {

        ValidationErrors errors = getValidationErrors(emailConfirmationDTO);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors, "Invalid input");
        }

        String loginName = emailConfirmationDTO.loginName;
        String emailConfirmationCode = emailConfirmationDTO.emailConfirmationCode;

        Login theLogin = loginRepo.findByLoginName(loginName);
        if(theLogin != null) {
            if (!JavaUtil.booleanValue(theLogin.isEmailConfirmed())) {
                if (theLogin.getEmailConfirmationCode().equals(emailConfirmationCode)) {
                    theLogin.setEmailConfirmed(true);
                    theLogin.setEmailConfirmationCode("");
                    loginRepo.save(theLogin);
                    return true;
                } else {
                    throw new ApheDataValidationException("emailConfirmationCode", "Invalid code");
                }
            } else {
                return true;
            }
        } else {
            throw new ApheDataValidationException("loginName", "Invalid loginName");
        }
    }

    @Transactional
    public boolean confirmEmail(String loginName, String invitationCode) throws ApheDataValidationException {
        User u = findUserByLogin(loginName);
        Login theLogin = u.getLogin();

        Invitation inv = invitationRepository.findByInvitationCode(invitationCode);
        if (!JavaUtil.booleanValue(theLogin.isEmailConfirmed()) && inv != null) {
            if (theLogin.getLoginName().equalsIgnoreCase(inv.getEmail())) {
                theLogin.setEmailConfirmed(true);
                theLogin.setEmailConfirmationCode("");
                loginRepo.save(theLogin);
            }
        }
        return theLogin.isEmailConfirmed().booleanValue();
    }

    @Transactional
    @PreAuthorize("hasPermission(#userId, 'USER', 'READ_USER')")
    public UserDTO getUser(String userId) {
        User u = userRepo.findById(Long.parseLong(userId)).orElse(null);
        if (u != null) {
            return authMapper.userToUserDto(u);
        }
        return null;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public Map<Long, Collection<UserDTO>> getUsers(List<Long> domainIds) {
        Map<Long, Collection<UserDTO>> users = new HashMap<>();

        if (domainIds == null || domainIds.isEmpty())
            return users;

        for (Long domainId : domainIds) {
            Domain theDomain = domainRepo.findById(domainId).orElse(null);
            if (isAccessible(theDomain)) {
                Map<String, UserDTO> domainUsers = new HashMap<>();

                if(theDomain.isActive()) {
                    List<DomainAccess> roles = domainAccessRepository.findByDomainId(domainId);
                    for (DomainAccess role : roles) {
                        if (role.isActive()) {
                            UserDTO userDTO = authMapper.userToUserDto(role.getUser());
                            domainUsers.put(userDTO.email, userDTO);
                        }
                    }

                    //Add the accountant in case of clients
                    List<AccountantClientRelation> theRelations = theDomain.getAccountantRelations();
                    if (theRelations != null) {
                        for (AccountantClientRelation relation : theRelations) {
                            if(relation.isActive() && relation.getAccountant().isActive()) {
                                //Now that this firm has access to this client, let's see find the users who have active access to this firm.
                                List<DomainAccess> accRtoles = domainAccessRepository.findByDomainId(relation.getAccountant().getId());
                                for (DomainAccess role : accRtoles) {
                                    if (role.isActive()) {
                                        User accountantUser = role.getUser();
                                        // Now let's check if this user has access to this client. For now check for owner/admin access only.??
                                        List<ClientAccess> clientAccesses = role.getClientAccess();
                                        if(clientAccesses != null && clientAccesses.size()>0) {
                                            for(ClientAccess ca : clientAccesses) {
                                                if(ca.isActive() && ca.getClient().getId().longValue() == domainId.longValue() &&  (ca.getGlobalRole() == GlobalRole.ROLE_OWNER || ca.getGlobalRole() == GlobalRole.ROLE_ADMIN)) {
                                                    //This user has client access to the domain in question.
                                                    UserDTO userDTO = authMapper.userToUserDto(accountantUser);
                                                    domainUsers.put(userDTO.email, userDTO);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                users.put(domainId, domainUsers.values());
            }
        }
        return users;
    }

    @Transactional
    @PreAuthorize("hasAuthority('superadmin')")
    public List<com.aphe.common.auth.UserDTO> getUsersAndDomains() {

        List<com.aphe.common.auth.UserDTO> returnValue = new ArrayList<>();
        Iterable<User> allUsers = userRepo.findAll();
        for(User u : allUsers) {
            List<String> activeNonTestDomains = u.getDomainAccess().stream()
                    .filter(da->da.isActive())
                    .map(da->da.getDomain())
                    .filter(d->d.isActive() && (d.getIsTestAccount() == null || d.getIsTestAccount().booleanValue() == false))
                    .map(d->Long.toString(d.getId()))
                    .collect(Collectors.toList());

            List<String> clientDomains = u.getDomainAccess().stream()
                    .filter(da->da.isActive())
                    .flatMap(da->{
                        if(da.getDomain() instanceof Accountant) {
                            return da.getClientAccess().stream()
                                    .filter(ca->ca.isActive() && ca.getClient().isActive() && (ca.getClient().getIsTestAccount() == null || ca.getClient().getIsTestAccount() == false) )
                                    .map(ca->Long.toString(ca.getClient().getId()))
                                    .collect(Collectors.toList()).stream();
                        } else {
                            return null;
                        }
                    }).collect(Collectors.toList());

            List<String> allDomainIds = new ArrayList<>();
            allDomainIds.addAll(activeNonTestDomains);
            allDomainIds.addAll(clientDomains);

            com.aphe.common.auth.UserDTO userDTO1 = new com.aphe.common.auth.UserDTO();
            userDTO1.setId(u.getId());
            userDTO1.setFirstName(u.getFirstName());
            userDTO1.setLastName(u.getLastName());
            userDTO1.setEmail(u.getEmail());
            userDTO1.setDomainIds(allDomainIds);
            returnValue.add(userDTO1);
        }
        return returnValue;
    }


    @Transactional
    public void sendPasswordResetLink(String loginName) {
        User u = findUserByLogin(loginName);
        if (u != null) {
            Login theLogin = u.getLogin();
            theLogin.setPasswordResetCode(RandomStringUtils.random(50, true, true));
            mailManager.sendPasswordResetEmail(u, u.getLogin().getPasswordResetCode());
            loginRepo.save(theLogin);
        }
    }

    @Transactional
    public void changePassword(PasswordResetDTO passwordResetDTO) throws ApheDataValidationException {

        User u = findUserByLogin(passwordResetDTO.loginName);

        ValidationErrors errors = getValidationErrors(passwordResetDTO);
        if (errors.getMessages().size() > 0) {
            throw new ApheDataValidationException(errors);
        }

        if (u == null) {
            throw new ApheDataValidationException("email", "User not found.");
        }

        Login theLogin = u.getLogin();

        boolean canReset = canResetPassword(passwordResetDTO, theLogin, errors);

        if(!canReset) {
            throw new ApheDataValidationException(errors);
        } else {
            // TODO : validate the password reset bean here instead of rest layer.
            // TODO : Check the new password is not same as old password??
            theLogin.setHashedPassword(passwordEncoder.encode(passwordResetDTO.password));
            theLogin.setPasswordResetCode(null);
            loginRepo.save(theLogin);
        }
    }

    private boolean canResetPassword(PasswordResetDTO passwordResetDTO, Login theLogin, ValidationErrors errors) {

        String passwordResetCode = theLogin.getPasswordResetCode();
        String originalPassword = passwordResetDTO.originalPassword;

        boolean canReset = false;

        boolean isOriginalPasswordPresent = StringUtil.isNotEmpty(originalPassword);
        if(isOriginalPasswordPresent) {
            boolean originalPasswordMatches = passwordEncoder.matches(originalPassword, theLogin.getHashedPassword());
            if(!originalPasswordMatches) {
                errors.addMessage("originalPassword", "Invalid original password");
                canReset = false;
            } else {
                canReset = true;
            }
        }

        boolean passwordResetCodePresent = StringUtil.isNotEmpty(passwordResetCode);
        if(passwordResetCodePresent) {
            boolean passwordResetCodeMatches = passwordResetCode.equals(passwordResetDTO.passwordResetCode);
            if(!passwordResetCodeMatches) {
                errors.addMessage("code", "Invalid password reset code");
                canReset = false;
            } else {
                canReset = true;
            }
        }

        return canReset;
    }


    public Collection<UserDTO> searchUsers(String userSearchString) {
        if(StringUtil.isNotEmpty(userSearchString)) {
            List<Login> logins = loginRepo.findAll(Specification.where(LoginSpecification.textInAllColumns(userSearchString)));
            List<User> users = userRepo.findAll(Specification.where(UserSpecification.textInAllColumns(userSearchString)));
            List<UserDTO> userDTOs = logins.stream().map(l->authMapper.userToUserDto(l.getUser())).collect(Collectors.toList());
            userDTOs.addAll(users.stream().map(u->authMapper.userToUserDto(u)).collect(Collectors.toList()));
            Map<String, UserDTO> userDTOsMap = userDTOs.stream().collect( Collectors.toMap(userDTO->userDTO.globalId, userDTO->userDTO, (u1, u2)->u1));
            return userDTOsMap.values();
        }
        return new ArrayList<>();
    }



}
