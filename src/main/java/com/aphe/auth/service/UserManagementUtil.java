package com.aphe.auth.service;

import com.aphe.auth.model.core.Accountant;
import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.u2d.ClientAccess;
import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class UserManagementUtil {

	public Domain getDomain(long domainId, User u) {
		Domain theDomain = null;
		if (u != null) {
			for (DomainAccess domainGlobalRole : u.getDomainAccess()) {
				if (domainGlobalRole.isActive() && domainGlobalRole.getDomain().getId() == domainId) {
					theDomain = domainGlobalRole.getDomain();
					break;
				}
			}
		}
		return theDomain;
	}

	public Collection<GrantedAuthority> getDomainRoles(User u, long domainId) {
		Collection<GrantedAuthority> authList = new ArrayList<GrantedAuthority>();
		for (DomainAccess role : u.getDomainAccess()) {
			if (role.isActive() && role.getDomain().getId() == domainId) {
				authList.add(new SimpleGrantedAuthority(role.getGlobalRole().name()));
				List<LocalRole> localRoles = role.getLocalRoles();
				for (LocalRole localRole : role.getLocalRoles()) {
					authList.add(new SimpleGrantedAuthority(localRole.name()));
				}
			}
		}
		return authList;
	}

	public Collection<GrantedAuthority> getClientRoles(Accountant accountant, User user, long clientId) {
		Collection<GrantedAuthority> authList = new ArrayList<GrantedAuthority>();

		DomainAccess theDomainAccess = user.getDomainAccess().stream()
				.filter(da->da.getDomain().getId() == accountant.getId() && da.isActive())
				.findFirst().orElse(null);
		if(theDomainAccess != null) {
			ClientAccess theClientAccess = theDomainAccess.getClientAccess().stream()
					.filter(ca->ca.getClient().getId() == clientId && ca.isActive() == true)
					.findFirst().orElse(null);
			if(theClientAccess != null) {
				authList.add(new SimpleGrantedAuthority(theClientAccess.getGlobalRole().name()));
				authList.addAll(theClientAccess.getLocalRoles().stream().map(lr->new SimpleGrantedAuthority(lr.name())).collect(Collectors.toList()));
			}
		}


//		ClientAccess theClientAccess = accountant.getClientAccess().stream().filter(ca->ca.getClient().getId() == clientId && ca.getUser().getId() == userId).findFirst().orElse(null);
//		if (theClientAccess != null && theClientAccess.isActive()) {
//			authList.add(new SimpleGrantedAuthority(theClientAccess.getGlobalRole().name()));
//			authList.addAll(theClientAccess.getLocalRoles().stream().map(lr->new SimpleGrantedAuthority(lr.name())).collect(Collectors.toList()));

//			List<LocalRole> localRoles = theClientAccess.getLocalRoles();
//			for (LocalRole localRole : theClientAccess.getLocalRoles()) {
//				authList.add(new SimpleGrantedAuthority(localRole.name()));
//			}
//		}
		return authList;
	}

	public Collection<GrantedAuthority> getOwnerRole() {
		Collection<GrantedAuthority> authList = new ArrayList<GrantedAuthority>();
		authList.add(new SimpleGrantedAuthority(GlobalRole.ROLE_OWNER.name()));
		return authList;
	}

	public Collection<GrantedAuthority> getAdminRole() {
		Collection<GrantedAuthority> authList = new ArrayList<GrantedAuthority>();
		authList.add(new SimpleGrantedAuthority(GlobalRole.ROLE_ADMIN.name()));
		return authList;
	}
}
