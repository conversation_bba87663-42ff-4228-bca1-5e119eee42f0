package com.aphe.auth.service;

import com.aphe.auth.model.core.User;
import com.aphe.auth.service.dto.ContactDTO;
import com.aphe.common.mail.Email;
import com.aphe.common.mail.MailService;
import com.aphe.common.util.JSONUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Component
public class MailManager extends BaseMailManager {

	private static final Logger logger = LoggerFactory.getLogger(MailManager.class);

	@Value("${aphe.product.url}")
	private String productURL;

	@Value("${aphe.product.appName}")
	private String productName;

	@Value("${aphe.email.confirmEmail.templateName}")
	private String confirmEmailTemplateName;

	@Value("${aphe.email.confirmEmail.subject}")
	private String confirmEmailSubject;

	@Value("${aphe.product.emailConfirmationPath}")
	private String emailConfirmationPath;

	@Value("${aphe.email.resetPassword.templateName}")
	private String resetPasswordTemplateName;

	@Value("${aphe.email.resetPassword.subject}")
	private String resetPasswordSubject;

	@Value("${aphe.product.resetPasswordPath}")
	private String resetPasswordPath;

	@Value("${aphe.email.contactUs.templateName}")
	private String contactUsTemplateName;

	@Value("${aphe.email.contactUs.subject}")
	private String contactUsSubject;

	@Value("${aphe.product.acceptInvitationPath}")
	private String acceptInvitationPath;

	@Value("${aphe.email.notifyUserChanged.templateName}")
	private String notifyUserChangedTemplateName;

	@Value("${aphe.email.notifyUserClientAccessChanged.templateName}")
	private String notifyUserClientAccessChangedTemplateName;

	@Value("${aphe.email.notifyTeamUserChanged.templateName}")
	private String notifyTeamUserChangedTemplateName;

	@Value("${aphe.email.notifyTeamClientAccessChanged.templateName}")
	private String notifyTeamClientAccessChangedTemplateName;

	private String sysStatusTemplateName = "sysStatus";


	@Autowired
	protected MailService mailService;

	@Autowired
	protected JSONUtils jsonUtil;

	public void sendEmailConfirmationEmail(User u, String emailConfirmationCode) {

		String toEmail = u.getEmail();
		String firstName = u.getFirstName();
		String lastName = u.getLastName();

		HashMap<String, String> params = new HashMap<>();
		String loginName = u.getLogin().getLoginName();
		params.put("login", loginName);
		params.put("email", toEmail);
		params.put("code", emailConfirmationCode);
		StringBuffer queryString = mailService.concatParams(params);

		Map<String, Object> mailAttributes = new HashMap<>();

		// Set Message specific properties.
		String emailConfirmationURL = productURL + emailConfirmationPath + "?" + queryString.toString();
		mailAttributes.put("EMAIL_CONFIRMATION_CODE", emailConfirmationCode);
		mailAttributes.put("EMAIL_CONFIRMATION_URL", emailConfirmationURL);

		addUserProperties(u, mailAttributes);

		Email m = new Email(confirmEmailTemplateName);
		m.setSubject(confirmEmailSubject);
		m.addTo(firstName, lastName, toEmail);
		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		createAndSendEmail(m, loginName);
	}

	private void createAndSendEmail(Email m, String loginName) {
		String emailTemplateName = m.getTemplateName();
		Email email = mailService.createEmail(m);
		if (email != null) {
			//Send asynchronously in a separate thread
			logger.info("Created " + emailTemplateName + " for " + loginName);


			mailService.sendEmail(email.getId());
		} else {
			logger.error("Failed to create " + emailTemplateName + " for " + loginName);
		}
	}

	public void sendPasswordResetEmail(User u, String passwordResetCode) {

		String email = u.getEmail();
		String firstName = u.getFirstName();
		String lastName = u.getLastName();
		String loginName = u.getLogin().getLoginName();

		HashMap<String, String> params = new HashMap<>();
		params.put("email", email);
		params.put("code", passwordResetCode);
		StringBuffer queryString = mailService.concatParams(params);

		Map<String, Object> mailAttributes = new HashMap<>();

		// Set Message specific properties.
		String passwordResetURL = productURL + resetPasswordPath + "?" + queryString.toString();
		mailAttributes.put("PASSWORD_RESET_CODE", passwordResetCode);
		mailAttributes.put("PASSWORD_RESET_URL", passwordResetURL);

		addUserProperties(u, mailAttributes);

		Email m = new Email(resetPasswordTemplateName);
		m.setSubject(resetPasswordSubject);
		m.addTo(firstName, lastName, email);
		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		createAndSendEmail(m, loginName);
	}

	public void sendContactUsEmail(ContactDTO contactDTO) {

		String toEmail = "<EMAIL>";
		String firstName = "1099SmartFile";
		String lastName = "Support Team";

		Map<String, Object> mailAttributes = new HashMap<>();

		// Set Message specific properties.
		mailAttributes.put("C_FN", contactDTO.firstName);
		mailAttributes.put("C_LN", contactDTO.lastName);
		mailAttributes.put("C_EMAIL", contactDTO.emailAddress);
		mailAttributes.put("C_PHONE", contactDTO.phoneNumber);
		mailAttributes.put("C_ACCOUNT_NAME", contactDTO.accountName);
		mailAttributes.put("C_ACCOUNT_ID", contactDTO.accountId);
		mailAttributes.put("C_SUBJECT", contactDTO.subject);
		mailAttributes.put("C_MESSAGE", contactDTO.message);

		Email m = new Email(contactUsTemplateName);
		m.setSubject("Re: 1099SmartFile inquiry - " + contactDTO.firstName + " : " + contactDTO.subject);
		m.addTo(contactDTO.firstName, contactDTO.lastName, contactDTO.emailAddress);
		m.addCC(firstName, lastName, toEmail);
		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		createAndSendEmail(m, contactDTO.emailAddress);
	}


	public void notifyUserChanged(User modifiedUser, User modifyingUser, Map<String, Object> otherAttributes) {

		String toEmail = modifiedUser.getEmail();
		String firstName = modifiedUser.getFirstName();
		String lastName = modifiedUser.getLastName();

		Object invitation_code = otherAttributes.get("INVITATION_CODE");
		String invitationCode = invitation_code != null ? (String) invitation_code : "" ;

		HashMap<String, String> params = new HashMap<>();
		params.put("email", toEmail);
		params.put("code", invitationCode);
		StringBuffer queryString = mailService.concatParams(params);

		Map<String, Object> mailAttributes = new HashMap<>();

		// Set Message specific properties.
		String inviteUserURL = productURL + acceptInvitationPath + "?" + queryString.toString();
		mailAttributes.put("INVITATION_URL", inviteUserURL);

		mailAttributes.put("RECIPIENT_USER_FIRST_NAME", firstName);
		mailAttributes.put("RECIPIENT_USER_LAST_NAME", lastName);
		mailAttributes.put("RECIPIENT_USER_NAME", buildFullName(firstName, lastName));
		mailAttributes.put("RECIPIENT_USER_EMAIL", toEmail);

		if(modifyingUser != null) {
			mailAttributes.put("MODIFYING_USER_FIRST_NAME", modifyingUser.getFirstName());
			mailAttributes.put("MODIFYING_USER_LAST_NAME", modifyingUser.getLastName());
			mailAttributes.put("MODIFYING_USER_NAME", buildFullName(modifyingUser.getFirstName(), modifyingUser.getLastName()));
			mailAttributes.put("MODIFYING_USER_EMAIL", modifyingUser.getEmail());
		}

		mailAttributes.put("MODIFIED_USER_FIRST_NAME", firstName);
		mailAttributes.put("MODIFIED_USER_LAST_NAME", lastName);
		mailAttributes.put("MODIFIED_USER_NAME", buildFullName(firstName, lastName));
		mailAttributes.put("MODIFIED_USER_EMAIL", toEmail);

		mailAttributes.putAll(otherAttributes);

		addUserProperties(modifiedUser, mailAttributes);

		Email m = new Email(notifyUserChangedTemplateName);

		String changeType = (String) mailAttributes.get("ACCESS_CHANGE_TYPE");
		if(changeType == null) {
			changeType = "";
		}
		String subject = String.format("Regarding your access to %s", mailAttributes.get("DOMAIN_NAME"));
		switch (changeType) {
			case "Inv": {
				subject = String.format("You have been invited to manage %s's account", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Add": {
				subject = String.format("You have been added as a user to manage %s's account", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Mod": {
				subject = String.format("Your access to %s's account has been changed", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Sus": {
				subject = String.format("Your access to %s's account has been suspended", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Res": {
				subject = String.format("Your access to %s's account has been restored", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
			case "Del": {
				subject = String.format("Your access to %s's account has been removed", mailAttributes.get("DOMAIN_NAME"));
				break;
			}
		}

		m.setSubject(subject);
		m.addTo(firstName, lastName, toEmail);
		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);
	}

	public void notifyTeamUserChanged(User owner, List<User> adminUsers, User modifiedUser, User modifyingUser, Map<String, Object> otherAttributes) {

		String toEmail = owner.getEmail();
		String firstName = owner.getFirstName();
		String lastName = owner.getLastName();

		Map<String, Object> mailAttributes = new HashMap<>();
		mailAttributes.put("RECIPIENT_USER_FIRST_NAME", firstName);
		mailAttributes.put("RECIPIENT_USER_LAST_NAME", lastName);
		mailAttributes.put("RECIPIENT_USER_NAME", buildFullName(firstName, lastName));
		mailAttributes.put("RECIPIENT_USER_EMAIL", toEmail);

		mailAttributes.put("MODIFYING_USER_FIRST_NAME", modifyingUser.getFirstName());
		mailAttributes.put("MODIFYING_USER_LAST_NAME", modifyingUser.getLastName());
		mailAttributes.put("MODIFYING_USER_NAME", buildFullName(modifyingUser.getFirstName(), modifyingUser.getLastName()));
		mailAttributes.put("MODIFYING_USER_EMAIL", modifyingUser.getEmail());

		mailAttributes.put("MODIFIED_USER_FIRST_NAME", modifiedUser.getFirstName());
		mailAttributes.put("MODIFIED_USER_LAST_NAME", modifiedUser.getLastName());
		mailAttributes.put("MODIFIED_USER_NAME", buildFullName(modifiedUser.getFirstName(), modifiedUser.getLastName()));
		mailAttributes.put("MODIFIED_USER_EMAIL", modifiedUser.getEmail());

		mailAttributes.putAll(otherAttributes);

		addUserProperties(owner, mailAttributes);

		Email m = new Email(notifyTeamUserChangedTemplateName);
		m.setSubject(String.format("Security Alert: Access change to your %s's account with %s", mailAttributes.get("DOMAIN_NAME"), productName));
		m.addTo(firstName, lastName, toEmail);

		for(User adminUser : adminUsers) {
			if(adminUser.getId() == owner.getId() || adminUser.getId() == modifiedUser.getId()) {
				continue;
			}
			m.addCC(adminUser.getFirstName(), adminUser.getLastName(), adminUser.getEmail());
		}

		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);
	}


	public void notifyUserClientAccessChanged(User modifiedUser, User modifyingUser, Map<String, Object> otherAttributes) {

		String toEmail = modifiedUser.getEmail();
		String firstName = modifiedUser.getFirstName();
		String lastName = modifiedUser.getLastName();

		Map<String, Object> mailAttributes = new HashMap<>();
		mailAttributes.put("RECIPIENT_USER_FIRST_NAME", firstName);
		mailAttributes.put("RECIPIENT_USER_LAST_NAME", lastName);
		mailAttributes.put("RECIPIENT_USER_NAME", buildFullName(firstName, lastName));
		mailAttributes.put("RECIPIENT_USER_EMAIL", toEmail);

		if(modifyingUser != null) {
			mailAttributes.put("MODIFYING_USER_FIRST_NAME", modifyingUser.getFirstName());
			mailAttributes.put("MODIFYING_USER_LAST_NAME", modifyingUser.getLastName());
			mailAttributes.put("MODIFYING_USER_NAME", buildFullName(modifyingUser.getFirstName(), modifyingUser.getLastName()));
			mailAttributes.put("MODIFYING_USER_EMAIL", modifyingUser.getEmail());
		}

		mailAttributes.put("MODIFIED_USER_FIRST_NAME", firstName);
		mailAttributes.put("MODIFIED_USER_LAST_NAME", lastName);
		mailAttributes.put("MODIFIED_USER_NAME", buildFullName(firstName, lastName));
		mailAttributes.put("MODIFIED_USER_EMAIL", toEmail);

		mailAttributes.putAll(otherAttributes);

		addUserProperties(modifiedUser, mailAttributes);

		Email m = new Email(notifyUserClientAccessChangedTemplateName);

		String subject = String.format("Clients you can manage for %s have been updated", mailAttributes.get("DOMAIN_NAME"));

		m.setSubject(subject);
		m.addTo(firstName, lastName, toEmail);
		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);
	}

	public void notifyTeamClientAccessChanged(User owner, List<User> adminUsers, User modifiedUser, User modifyingUser, Map<String, Object> otherAttributes) {

		String toEmail = owner.getEmail();
		String firstName = owner.getFirstName();
		String lastName = owner.getLastName();

		Map<String, Object> mailAttributes = new HashMap<>();
		mailAttributes.put("RECIPIENT_USER_FIRST_NAME", firstName);
		mailAttributes.put("RECIPIENT_USER_LAST_NAME", lastName);
		mailAttributes.put("RECIPIENT_USER_NAME", buildFullName(firstName, lastName));
		mailAttributes.put("RECIPIENT_USER_EMAIL", toEmail);

		mailAttributes.put("MODIFYING_USER_FIRST_NAME", modifyingUser.getFirstName());
		mailAttributes.put("MODIFYING_USER_LAST_NAME", modifyingUser.getLastName());
		mailAttributes.put("MODIFYING_USER_NAME", buildFullName(modifyingUser.getFirstName(), modifyingUser.getLastName()));
		mailAttributes.put("MODIFYING_USER_EMAIL", modifyingUser.getEmail());

		mailAttributes.put("MODIFIED_USER_FIRST_NAME", modifiedUser.getFirstName());
		mailAttributes.put("MODIFIED_USER_LAST_NAME", modifiedUser.getLastName());
		mailAttributes.put("MODIFIED_USER_NAME", buildFullName(modifiedUser.getFirstName(), modifiedUser.getLastName()));
		mailAttributes.put("MODIFIED_USER_EMAIL", modifiedUser.getEmail());

		mailAttributes.putAll(otherAttributes);

		addUserProperties(owner, mailAttributes);

		Email m = new Email(notifyTeamClientAccessChangedTemplateName);
		m.setSubject(String.format("Security Alert: Access change to your %s's account with %s", mailAttributes.get("DOMAIN_NAME"), productName));
		m.addTo(firstName, lastName, toEmail);

		for(User adminUser : adminUsers) {
			if(adminUser.getId() == owner.getId() || adminUser.getId() == modifiedUser.getId()) {
				continue;
			}
			m.addCC(adminUser.getFirstName(), adminUser.getLastName(), adminUser.getEmail());
		}

		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);
	}

	public void sendSystemStatusEmail(String subject, String message) {

		String toEmail = "<EMAIL>";
		String firstName = "1099SmartFile";
		String lastName = "Support Team";

		Map<String, Object> mailAttributes = new HashMap<>();

		Email m = new Email(sysStatusTemplateName);
		m.setSubject(subject);
		m.addTo(firstName, lastName, toEmail);
		mailAttributes.put("STATUS_MESSAGE", message);
		m.setParams(jsonUtil.mapToJSONObject(mailAttributes));
		mailService.createEmail(m);
	}


}
