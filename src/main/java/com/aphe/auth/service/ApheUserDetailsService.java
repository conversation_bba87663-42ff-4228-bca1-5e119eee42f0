package com.aphe.auth.service;

import com.aphe.auth.model.core.Accountant;
import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.Login;
import com.aphe.auth.model.core.repo.LoginRepository;
import com.aphe.auth.model.d2d.D2DRelation;
import com.aphe.auth.model.d2d.repo.D2DRelationRepository;
import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.auth.security.FailedAttemptsManager;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.D2DAccess;
import com.aphe.common.util.JavaUtil;
import com.aphe.common.util.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Component
@Transactional
public class ApheUserDetailsService implements UserDetailsService {

	@Autowired
	LoginRepository loginRepo;

	@Autowired
	protected D2DRelationRepository d2DRelationRepository;

	@Autowired
	UserManagementUtil userMgmtUtil;

	@Autowired
	FailedAttemptsManager failedAttemptsManager;

	@Autowired
	private HttpServletRequest request;

	@Autowired
	private RequestUtil requestUtil;

	@Override
	public ApheUserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		Login theLogin = null;
		try {
			theLogin = loginRepo.findByLoginName(username);
		} catch (Throwable e) {
			throw e;
		}
		if (theLogin == null) {
			throw new UsernameNotFoundException("Username not found");
		} else {
			ApheUserDetails user = buildUserDetailsObjectFromLogin(theLogin);
			return user;
		}
	}

	private ApheUserDetails buildUserDetailsObjectFromLogin(Login l) {

		Collection<GrantedAuthority> authList = new ArrayList<GrantedAuthority>();
		Collection<D2DAccess> domainRelations = new ArrayList<D2DAccess>();
		long domainId = 0;
		String domainGlobalId = "";
		String parentDomainGlobalId = "";
		long parentDomainId = 0;
		String domainType = "";
		String domainName = "";
		Domain theDomain = null;

		List<DomainAccess> domainAccessList = l.getUser().getDomainAccess().stream().filter(da->da.isActive() == true && da.getDomain().isActive() == true ).collect(Collectors.toList());
		if (domainAccessList.size() == 1) {
			theDomain = domainAccessList.get(0).getDomain();
			domainId = theDomain.getId();
			domainGlobalId = theDomain.getGlobalId().toString();
			domainType = theDomain.getDomainType();
			domainName = theDomain.getDisplayName();
			if (theDomain instanceof Accountant) {
				parentDomainId = domainId;
				parentDomainGlobalId = domainGlobalId;
			}
			authList = userMgmtUtil.getDomainRoles(l.getUser(), theDomain.getId());

			//if this user (direct user or accountant user is an admin user, load it's relations in other domains as a Vendor or Employee..
			boolean isAdminUser = true;

			List<D2DRelation> d2DRelations= d2DRelationRepository.findByTargetDomainId(domainId);
			for(D2DRelation rel : d2DRelations) {
				D2DAccess d2DAccess = new D2DAccess();
				d2DAccess.setRelType(rel.getRelationType().name());
				d2DAccess.setSourceDomainId(rel.getSourceDomainId());
				d2DAccess.setTargetDomainId(rel.getTargetDomainId());
				d2DAccess.setSourceSubEntityId(rel.getSourceSubEntityId());
				d2DAccess.setTargetSubEntityId(rel.getTargetSubEntityId());
				domainRelations.add(d2DAccess);
			}
		}

		long loginId = l.getId();
		String username = l.getLoginName();
		boolean accountNonExpired = true;

		String ip = requestUtil.getIPAddress(request);
		boolean accountNonLocked = !failedAttemptsManager.isLockedOut(l.getLoginName(), ip); //JavaUtil.booleanValue(l.isAccountNonLocked());
		boolean credentialsNonExpired = JavaUtil.booleanValue(l.isCredentialsNonExpired());
		boolean enabled = JavaUtil.booleanValue(l.isEnabled());
		boolean emailConfirmed = l.isEmailConfirmed();
		String password = "";

		String loginGlobalId = l.getGlobalId().toString();
		String userGlobalId = l.getUser().getGlobalId().toString();

		password = l.getHashedPassword();
		ApheUserDetails user = new ApheUserDetails(loginId, l.getUser().getId(), domainId,
				domainName, domainType, parentDomainId, authList, username, password, accountNonExpired,
				accountNonLocked, credentialsNonExpired, enabled, emailConfirmed, domainRelations, "", false,false, loginGlobalId, userGlobalId, domainGlobalId, parentDomainGlobalId);
		return user;
	}

}
