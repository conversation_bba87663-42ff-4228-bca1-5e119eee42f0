package com.aphe.auth.service;

import com.aphe.auth.model.a2c.AccountantClientRelation;
import com.aphe.auth.model.core.Accountant;
import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.d2d.D2DRelation;
import com.aphe.auth.model.d2d.repo.D2DRelationRepository;
import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.auth.model.u2d.repo.DomainAccessRepository;
import com.aphe.auth.security.TokenManager;
import com.aphe.auth.service.dto.CreateDomainRemoteDTO;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.D2DAccess;
import com.aphe.common.security.jwt.ApheAuthContext;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.security.jwt.TokenInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Component
@Transactional
public class ApheSecurityManager extends BaseManager {

	private static Logger logger = LoggerFactory.getLogger(ApheSecurityManager.class);

	@Autowired
	protected AccountManager acctMgr;

	@Autowired
	protected UserManagementUtil userMgmtUtil;

	@Autowired
	protected JwtUtil jwtUtil;

	@Autowired
	protected TokenManager tokenManager;

	@Autowired
	protected ApheUserDetailsService userDetailsService;

	@Autowired
	protected DomainRepository domainRepository;

	@Autowired
	protected DomainAccessRepository domainAccessRepository;

	@Autowired
	protected D2DRelationRepository d2DRelationRepository;

	public List<TokenInfo> getLoggedInUsers() {
		return tokenManager.getLoggedInUsers(0, 1000);
	}

	/**
	 * 
	 * An API to initialize the security context so that all the downstream calls can run in the context of an authenticated session.
	 * 
	 * 
	 * 
	 * Concerns of security. (A token could be just for user or user and domain. A domain could be client account too.)
	 * 1. Generate token.
	 * 2. Set spring security for down stream code about currentLoggedInDomain, user, accountantId etc.
	 * 3. Add token to thread local for down stream remote calls.
	 * 4. Add token manager to validate for future requests.
	 * 
	 * 5. Set the token details in request and response.
	 * 
	 * 6. Create the domain on remote domain service. 
	 * 
	 */

	/**
	 * authenticateUserAndSetSession methods generate token and stores in the thread local, token manager. 
	 * In Addition, it also initialzies the spring security context, which is not always required sometimes.
	 * @param u
	 * @param domainId
	 * @return
	 */

	public String authenticateUserAndSetSession(User u, long domainId) {
		String loginName = u.getLogin().getLoginName();
		return authenticateUserAndSetSession(loginName, domainId);
	}

	public String authenticateUserAndSetSession(User u) {
		String loginName = u.getLogin().getLoginName();
		return authenticateUserAndSetSession(loginName, -1L);
	}

	public String authenticateLoginAndSetSession(String loginName) {
		return authenticateUserAndSetSession(loginName, -1L);
	}

	public String authenticateUserAndSetSession(String loginName, long domainId) {
		return authenticateUserAndSetSessionAndCookiesInternal(loginName, getSuperAdminLoginName(), domainId, null, null);
	}

	public String authenticateUserAndSetSessionAndCookies(User u, long domainId, HttpServletRequest request, HttpServletResponse response) {
		String loginName = u.getLogin().getLoginName();
		return authenticateUserAndSetSessionAndCookiesInternal(loginName, getSuperAdminLoginName(), domainId, request, response);
	}

	public String authenticateUserAndSetSessionAndCookies(String loginName, long domainId, HttpServletRequest request, HttpServletResponse response) {
		return authenticateUserAndSetSessionAndCookiesInternal(loginName, getSuperAdminLoginName(), domainId, request, response);
	}

	public String switchUserAndSetSessionAndCookies(String currentLoginName, String newLoginName, HttpServletRequest request, HttpServletResponse response) {
		//Check if the current user is super admin.
		if(acctMgr.isSuperAdmin()) {
			//Generate a token for the new user and set auth context. After this line, security context is switched to new domain.
			String token = authenticateUserAndSetSessionAndCookiesInternal(newLoginName.toUpperCase(), currentLoginName, -1, request, response);

			logger.warn("User switched fromUser={} toUser={} token={}", currentLoginName, newLoginName, token);

			//For the new user, if there is only 1 domain. If so, set the token for that domain, otherwise, just return the above token.
			User u = acctMgr.getLoggedInUser();
			List<DomainAccess> domainAccessList = u.getDomainAccess();
			if (domainAccessList.size() == 1) {
				Domain theDomain = domainAccessList.get(0).getDomain();
				long domainId = theDomain.getId();
				token = authenticateUserAndSetSessionAndCookiesInternal(newLoginName, currentLoginName, domainId, request, response);
			}
			return token;
		} else {
			logger.error("Unauthorized switch user attempt fromUser={} toUser={} ", currentLoginName, newLoginName);
		}
		return null;
	}

	private String authenticateUserAndSetSessionAndCookiesInternal(String loginName, String superAdminLoginName, long domainId, HttpServletRequest request, HttpServletResponse response) {
		String token = generateTokenForLoginAndDomain(loginName, domainId, superAdminLoginName);

		if(token != null) {
			ApheUserDetails userDetails = null;
			try {
				userDetails = jwtUtil.parseToken(token);
				UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
				SecurityContextHolder.getContext().setAuthentication(authentication);
				ApheAuthContext.setToken(token);
				tokenManager.addToken(token);

				if(request != null) {
					String oldToken = jwtUtil.getToken(request);
					if(oldToken != null) {
						tokenManager.removeToken(oldToken);
					}
				}
			} catch (Exception e) {
				logger.error("Error parsing token", e);
			}

			if (domainId > 0) {
				createDomainOnDomainService(domainId);
			}

			if (request != null && response != null) {
				setTokenInCookies(token, request, response);
			}
		}
		return token;
	}
	
	/**
	 * @param loginName
	 * @param domainId
	 * @return
	 */
	private String generateTokenForLoginAndDomain(String loginName, long domainId, String superAdminLoginName) {
		String token = null;
		try {
			//Then check if there is a specific domain
			if (domainId > 0) {
				token = generateTokenForDomain(domainId, superAdminLoginName);
			}else{
				//Generate the token with just the user.
				//First set auth context with just the user.
				ApheUserDetails userDetails = userDetailsService.loadUserByUsername(loginName);
				userDetails.setSuperAdminLoginName(superAdminLoginName);
				if(domainId == -99) {
					userDetails.setDomainId(0);
					userDetails.setParentDomainId(0);
					userDetails.setDomainType("");
					userDetails.getAuthorities().clear();
				}
				token = jwtUtil.generateToken(userDetails);
			}
		} catch (Exception e) {
			logger.error("Error generating token for domain", e);
		}

		return token;
	}

	/**
	 * Generates a token that is good for given domain.
	 * Ensure the logged in user has access to the domain, 
	 * 1. in the form a role in the domain
	 * 2. in the form user has a role in the accountant account and the domain is a client of accountant account
	 * 3. is the user is a super admin and has access to everything.
	 */
	private String generateTokenForDomain(long domainId, String superAdminLoginName) throws ApheException {
		String token = null;
		User u = acctMgr.getLoggedInUser();

		Domain domain = domainRepository.findById(domainId).orElse(null);

		long parentDomainId = 0;
		String parentDomainGlobalId = "";
		String domainGlobalId = "";
		Collection<GrantedAuthority> authList = new ArrayList<GrantedAuthority>();
		boolean hasAccess = false;

		if (domain != null && acctMgr.userHasRoleInDomain(u.getId(), domain.getId())) {
			domainGlobalId = domain.getGlobalId().toString();
			hasAccess = true;
			if (domain instanceof Accountant) {
				parentDomainId = domain.getId();
				parentDomainGlobalId = domain.getGlobalId().toString();
			}

			authList.addAll(userMgmtUtil.getDomainRoles(u, domainId));
		} else if (domain != null && acctMgr.userHasClientAccess(u.getId(), domain.getId())) {
			hasAccess = true;
			domainGlobalId = domain.getGlobalId().toString();
			parentDomainId = acctMgr.getParentDoaminId();
			Accountant accountant = (Accountant) domainRepository.findById(parentDomainId).orElse(null);

			if(accountant != null) {
				parentDomainGlobalId = accountant.getGlobalId().toString();
				authList.addAll(userMgmtUtil.getClientRoles(accountant, u, domainId));
			}

			//In case of superAdmin logging into parent account, this superadmin didn't really have any role in the accountant company. So we need to check for superAdmin and add owner role in the client company.
//			if (acctMgr.isSuperAdmin()) {
//				boolean hasAdminRole = false;
//				for (GrantedAuthority gAuth : authList) {
//					if (gAuth.getAuthority().toString().equalsIgnoreCase(GlobalRole.ROLE_OWNER.name())) {
//						hasAdminRole = true;
//						break;
//					}
//				}
//				if (!hasAdminRole) {
//					authList.addAll(userMgmtUtil.getOwnerRole());
//				}
//			}
		} else if (domain != null && acctMgr.isSuperAdmin()) {
			hasAccess = true;

			domainGlobalId = domain.getGlobalId().toString();
			if (domain instanceof Accountant) {
				parentDomainId = domain.getId();
				parentDomainGlobalId = domain.getGlobalId().toString();
			} else {
				parentDomainId = acctMgr.getParentDoaminId();
				Accountant accountant = (Accountant) domainRepository.findById(parentDomainId).orElse(null);
				if(accountant != null) {
					parentDomainGlobalId = accountant.getGlobalId().toString();
				}
			}
			authList.addAll(userMgmtUtil.getAdminRole());
		}

		if (hasAccess) {
			Collection<D2DAccess> domainRelations = new ArrayList<D2DAccess>();

			//if this user (direct user or accountant user is an admin user, load it's relations in other domains as a Vendor or Employee..
			boolean isAdminUser = true;

			List<D2DRelation> d2DRelations= d2DRelationRepository.findByTargetDomainId(domainId);
			for(D2DRelation rel : d2DRelations) {
				D2DAccess d2DAccess = new D2DAccess();
				d2DAccess.setRelType(rel.getRelationType().name());
				d2DAccess.setSourceDomainId(rel.getSourceDomainId());
				d2DAccess.setTargetDomainId(rel.getTargetDomainId());
				d2DAccess.setSourceSubEntityId(rel.getSourceSubEntityId());
				d2DAccess.setTargetSubEntityId(rel.getTargetSubEntityId());
				domainRelations.add(d2DAccess);
			}

			ApheUserDetails apheUserDetails = new ApheUserDetails(u.getLogin().getId(), u.getId(), domainId, domain.getDisplayName(), domain.getDomainType(), parentDomainId, authList,
					u.getLogin().getLoginName(), "", true, true, true, true, u.getLogin().isEmailConfirmed(), domainRelations, superAdminLoginName, false, false,
					u.getLogin().getGlobalId().toString(), u.getGlobalId().toString(), domainGlobalId, parentDomainGlobalId);
			try {
				token = jwtUtil.generateToken(apheUserDetails);
				if (token != null) {
					return token;
				} else {
					throw new ApheException("Error generating token.");
				}
			} catch (Exception e) {
				throw new ApheException(e);
			}
		}

		return null;

	}

	private void createDomainOnDomainService(long domainId) {
		Domain domain = domainRepository.findById(domainId).orElse(null);
		String email = findOwnerEmail(domain);

		if(email == null) {
			//There is no user owner for this domain. It must be a client of an accountant. Get the email of the owner of the accountant.
			AccountantClientRelation mainAccountantRelation = domain.getAccountantRelations().stream().filter(accountantRelation -> accountantRelation.getClient().getId() == domainId).findFirst().orElse(null);
			if(mainAccountantRelation != null) {
				Domain mainAccountant = mainAccountantRelation.getAccountant();
				email = findOwnerEmail(mainAccountant);
			}
		}

		DomainDTO createdDomain = acctMgr.getDomainDTO(domainId);
		CreateDomainRemoteDTO remoteDTO = new CreateDomainRemoteDTO();
		remoteDTO.id = createdDomain.id;
		remoteDTO.name = createdDomain.name;
		remoteDTO.firstName = createdDomain.firstName;
		remoteDTO.lastName = createdDomain.lastName;
		remoteDTO.domainType = createdDomain.domainType;
		remoteDTO.emailAddress = email;
		remoteDTO.isTestAccount = createdDomain.isTestAccount;
		boolean domainCreated = acctMgr.createDomainRemote(remoteDTO);
		if (!domainCreated) {
			logger.error("Error creating the domain on domain service");
		}
	}

	private String findOwnerEmail(Domain domain) {
		List<DomainAccess> domainAccessRelations = domainAccessRepository.findByDomainId(domain.getId());
		DomainAccess ownerAccess = domainAccessRelations.stream().filter(da -> da.getGlobalRole() == GlobalRole.ROLE_OWNER).findFirst().orElse(null);
		if(ownerAccess != null) {
			return ownerAccess.getUser().getEmail();
		}
		return null;
	}

	public void setTokenInCookies(String token, HttpServletRequest request, HttpServletResponse response) {
		jwtUtil.setToken(request, response, token);
	}


	/**
	 * MFA code...
	 */

	public void setMFANotRequiredOnToken(String token, HttpServletRequest request, HttpServletResponse response) {
		boolean mfaRequired = false;
		boolean mfaSuccess = false;
		updateMFAAttributesOnToken(token, request, response, mfaRequired, mfaSuccess);
	}

	public void setMFASuccessOnToken(String token, HttpServletRequest request, HttpServletResponse response) {
		// check if this token is still valid.
		// if valid, build ApheUserDetails and set mfaSuccess to true.
		// generate new token and it to token manager.
		// Set cookie with new token.
		// if not valid, throw exception.
		boolean mfaRequired = true;
		boolean mfaSuccess = true;
		updateMFAAttributesOnToken(token, request, response, mfaRequired, mfaSuccess);
	}

	private void updateMFAAttributesOnToken(String token, HttpServletRequest request, HttpServletResponse response, boolean mfaRequired, boolean mfaSuccess) {
		try {
			if (token != null) {
				ApheUserDetails apheUserDetails = jwtUtil.parseToken(token);
				if (apheUserDetails != null) {
					apheUserDetails.setMfaRequired(mfaRequired);
					apheUserDetails.setMfaSuccess(mfaSuccess);
					String newToken = jwtUtil.generateToken(apheUserDetails);
					if (newToken != null) {
						tokenManager.addToken(newToken);
						tokenManager.removeToken(token);
						setTokenInCookies(newToken, request, response);
					} else {
						throw new ApheException("Error generating token.");
					}
				} else {
					throw new ApheException("Error getting user details from token.");
				}
			}
		} catch (Exception e) {
			logger.error("Error setting MFA success on token.", e);
		}
	}


}
