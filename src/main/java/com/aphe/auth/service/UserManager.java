package com.aphe.auth.service;

import com.aphe.auth.model.core.User;
import com.aphe.auth.model.u2d.Invitation;
import com.aphe.auth.service.dto.*;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.security.RecaptchaTokenValidator;
import com.aphe.common.security.jwt.TokenInfo;
import com.aphe.common.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

@Service
@Component
public class UserManager extends BaseManager {
    private static final Logger logger = LoggerFactory.getLogger(UserManager.class);


    @Autowired
    protected UserManagerTxn userManagerTxn;

    @Autowired
    protected DomainAccessManager domainAccessManager;

    @Autowired
    protected AuthMapper authMapper;

    @Autowired
    protected ApheSecurityManager apheSecurityManager;

    @Autowired
    protected ApheUserDetailsService apheUserDetailsService;

    @Autowired
    private RecaptchaTokenValidator recaptchaTokenValidator;

    @Autowired
    protected RequestUtil requestUtil;



    @PreAuthorize("hasAuthority('superadmin')")
    public List<TokenInfo> getLoggedInUsers() {
        return apheSecurityManager.getLoggedInUsers();
    }


    public UserDTO createSSOUser(CreateSSOUser accountDTO) throws ApheDataValidationException {

        User createdUser = userManagerTxn.createSSOUser(accountDTO);

        if (createdUser != null) {
            apheSecurityManager.authenticateUserAndSetSession(createdUser, -1);
            if (createdUser.getLogin().isEmailConfirmed() == false) {
                userManagerTxn.sendEmailConfirmationLink(createdUser.getLogin().getLoginName());
            }
        }
        UserDTO retVal = authMapper.userToUserDto(createdUser);
        return retVal;
    }

    public UserDTO createUser(CreateUserDTO createUserDTO, HttpServletRequest request, HttpServletResponse response) throws ApheDataValidationException {
        createUserDTO.ipAddress = requestUtil.getIPAddress(request);
        User createdUser = userManagerTxn.createUser(createUserDTO);
        if (createdUser != null) {
            boolean isEmailConfirmed = userManagerTxn.confirmEmail(createdUser.getLogin().getLoginName(), createUserDTO.code);
            if(!isEmailConfirmed) {
                userManagerTxn.sendEmailConfirmationLink(createdUser.getLogin().getLoginName());
            }
            apheSecurityManager.authenticateUserAndSetSessionAndCookies(createdUser, -1, request, response);
        }
        UserDTO retVal = authMapper.userToUserDto(createdUser);
        return retVal;
    }

    public UserDTO updateSSOUser(CreateSSOUser ssoUserDTO) throws ApheDataValidationException {
        User updatedUser = userManagerTxn.updateSSOUser(ssoUserDTO);
        if (updatedUser != null) {
            if (updatedUser.getLogin().isEmailConfirmed() == false) {
                userManagerTxn.sendEmailConfirmationLink(updatedUser.getLogin().getLoginName());
            }
        }
        UserDTO retVal = authMapper.userToUserDto(updatedUser);
        return retVal;
    }


    public UserDTO getUser(String userId) {
        return userManagerTxn.getUser(userId);
    }


    public InvitationDTO addInvitation(CreateInvitationDTO invitation) throws ApheDataValidationException {
        Invitation inv = domainAccessManager.addInvitation(invitation);
        if(inv != null) {
            domainAccessManager.notifyUserNewInvitation(Long.toString(inv.getId()));
            domainAccessManager.notifyTeamNewInvitation(Long.toString(inv.getId()));
            return domainAccessManager.getInvitation(Long.toString(inv.getId()));
        }else {
            return null;
        }
    }

    public Collection<UserDTO> searchUsers(String userSearchString) {
        return userManagerTxn.searchUsers(userSearchString);
    }


}
