package com.aphe.auth.service;

import com.aphe.auth.model.core.*;
import com.aphe.auth.model.d2d.D2DInvitation;
import com.aphe.auth.model.d2d.D2DRelation;
import com.aphe.auth.model.u2d.ClientAccess;
import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.auth.model.u2d.Invitation;
import com.aphe.auth.service.d2d.dto.CreateD2DInvitationDTO;
import com.aphe.auth.service.d2d.dto.D2DInvitationDTO;
import com.aphe.auth.service.d2d.dto.D2DRelationDTO;
import com.aphe.auth.service.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Objects;
import java.util.UUID;

@Mapper(componentModel = "spring")
public interface AuthMapper {

	@Mapping(source = "login.loginName", target = "loginName")
	@Mapping(source = "login.ssoPartner", target = "ssoPartner")
	UserDTO userToUserDto(User u);


	default String map(UUID value) {
		return Objects.toString(value, null);
	}

	default UUID mapStringToUUID(String value) {
		return UUID.fromString(value);
	}

	CreateUserDTO createAccountDTOToCreateUserDTO(CreateAccountDTO u);

	CreateDomainRemoteDTO domainDTOTCreateDomainRemoteDTO(DomainDTO u);

	@Mapping(source = "domainId", target = "domainAccess.domainId")
	@Mapping(source = "id", target = "domainAccess.id")
	@Mapping(source = "globalRole", target = "domainAccess.globalRole")
	@Mapping(source = "localRoles", target = "domainAccess.localRoles")
	@Mapping(source = "clientAccess", target = "domainAccess.clientAccess")
	InvitationDTO invitationToInvitationDTO(Invitation inv);

	Invitation createInvitationDTOToInvitation(CreateInvitationDTO inv);

	@Mapping(source = "user.id", target = "userId")
	@Mapping(source = "domain.id", target = "domainId")
	@Mapping(source = "active", target = "isActive")
	DomainAccessDTO domainAccessToDomainAccessDTO(DomainAccess da);
	DomainAccess domainAccessDTOToDomainAccess(DomainAccessDTO da);


	DomainAccess domainAccessDTOToDomainAccess(UpdateDomainAccessDTO da);

	DomainUserDTO userDTOToDomainUserDTO(UserDTO dto);

//	@Mapping(source = "user.id", target = "userId")
	@Mapping(source = "client.id", target = "clientId")
//	@Mapping(source = "accountant.id", target = "accountantId")
	@Mapping(source = "active", target = "isActive")
	ClientAccessDTO clientAccessToClientAccessDTO(ClientAccess ca);
	ClientAccess clientAccessDTOToClientAccess(ClientAccessDTO ca);
	ClientAccess createClientAccessDTOToClientAccess(CreateClientAccessDTO ca);

	ClientAccess updateClientAccessDTOToClientAccess(UpdateClientAccessDTO ca);

	DomainDTO domainToDomainDTO(Domain domain);

	UserDomainDTO domainDTOToUserDomainDTO(DomainDTO dto);

	Business domainDtoToBusiness(DomainDTO dto);

	Accountant domainDtoToAccountant(DomainDTO dto);

	Consumer domainDtoToConsumer(DomainDTO dto);

	MyAccountantDTO domainToMyAccountantDTO(Domain domain);


	//D2D Mappings.
	D2DInvitation createD2DInvitationDTOToD2DInvitation(CreateD2DInvitationDTO inv);
	D2DInvitationDTO d2DInvitationToD2DInvitationDTO(D2DInvitation inv);
	D2DRelationDTO d2DRelationToD2DRelationDTO(D2DRelation d2DRelation);
	
}
	