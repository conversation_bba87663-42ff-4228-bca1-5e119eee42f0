package com.aphe.auth.service.mfa;

import com.aphe.auth.model.core.Login;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.core.UserDevice;
import com.aphe.auth.model.core.repo.LoginRepository;
import com.aphe.auth.model.core.repo.UserRepository;
import com.aphe.auth.service.BaseManager;
import com.aphe.auth.service.DomainAccessManager;
import com.aphe.auth.service.dto.MFAConfigDTO;
import com.aphe.common.error.ApheErrorCode;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.util.PropertiesManager;
import org.jboss.aerogear.security.otp.Totp;
import org.jboss.aerogear.security.otp.api.Base32;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

@Service
@Component
public class MFAManager extends BaseManager {

    private static final Logger logger = LoggerFactory.getLogger(MFAManager.class);

    public static String QR_PREFIX = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=";

    @Autowired
    protected UserRepository userRepository;

    @Autowired
    protected LoginRepository loginRepository;

    @Autowired
    protected  DomainAccessManager domainAccessManager;

    @Value("${aphe.product.appName}")
    private String APP_NAME;

    private static final long THIRTY_DAYS = 30L * 24 * 60 * 60 * 1000;

    private static final int MAX_MFA_SKIPS = 3;

    @Transactional
    public boolean requiresMFA(ApheUserDetails userDetails, String deviceFingerprint, String deviceName) {

        String loginName = userDetails.getUsername();

        //Do not require MFA in Sandbox environment
        if(PropertiesManager.isSandbox()) {
            return false;
        }

        //Get login and user.
        Login login = loginRepository.findByLoginName(loginName);
        if(login == null) {
            return false;
        }

        User user = login.getUser();
        if(user == null || userDetails.isSuperAdmin()) {
            return false;
        }

        if(user.isMfaEnabled()) {
            //If mfa is enabled, and is not a trusted device, then return true.
            List<UserDevice> userDeviceList = user.getUserDevices();
            //see if this device is trusted.
            boolean isTrusted = false;
            for(UserDevice userDevice : userDeviceList) {
                if(userDevice.getFingerprint().equalsIgnoreCase(deviceFingerprint) && userDevice.isTrusted()) {
                    //trust only if the device was first used within the last 30 days.
                    if(userDevice.getLastMFASuccess() != null && userDevice.getLastMFASuccess().after(new Date(System.currentTimeMillis() - THIRTY_DAYS))) {
                        isTrusted = true;
                        break;
                    }
                }
            }
            return isTrusted ? false : true;
        } else {
            //if mfa is not enabled but user has access to domain that has xero or qbo connection, then return true.
            boolean hasAccountingIntegration = domainAccessManager.hasAccountingIntegration(Long.toString(user.getId()));
            if(hasAccountingIntegration) {
                return true;
            }
            return false;
        }
    }


    @Transactional
    public String getQRCodeUrl(Long userId) throws ApheDataValidationException {
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            throw new ApheDataValidationException("id", "User not found");
        }

        //TODO: return this only if only MFA is not enabled. do not leak secret to the users if MFA is enabled.
        try {
            String secret = user.getTotpSecret();
            if (secret == null) {
                user = generateTotpSecret(user.getId());
            }
            return QR_PREFIX + URLEncoder.encode(String.format("otpauth://totp/%s:%s?secret=%s&issuer=%s", APP_NAME, user.getLogin().getLoginName(), user.getTotpSecret(), APP_NAME), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("Error generating QR code", e);
        }
        return null;
    }


    @Transactional
    public MFAConfigDTO getMFAConfig(Long userId) throws ApheDataValidationException {
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            throw new ApheDataValidationException("id", "User not found");
        }

        MFAConfigDTO mfaConfigDTO = new MFAConfigDTO();
        boolean mfaEnabled = user.isMfaEnabled();;
        if(user.getLogin().getSsoPartner() != null) {
            mfaEnabled = true;
        }
        mfaConfigDTO.mfaEnabled = mfaEnabled;
        mfaConfigDTO.loginName = user.getLogin().getLoginName();
        mfaConfigDTO.canSkipMFA = user.getMfaSkips() >= MAX_MFA_SKIPS ? false : true;

        //Do not leak the TOTP secret to the client. Send it only when the user is registering TOTP.
        if(!mfaEnabled) {
            mfaConfigDTO.totpURL = getQRCodeUrl(user.getId());
            mfaConfigDTO.totpSecret = user.getTotpSecret();
        }
        return mfaConfigDTO;
    }

    @Transactional
    public boolean canSkipMFA(Long userId) throws ApheDataValidationException {
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            throw new ApheDataValidationException("id", "User not found");
        }

        return user.getMfaSkips() >= MAX_MFA_SKIPS ? false : true;
    }

    @Transactional
    public void incrementSkipCounter(long userId) throws ApheDataValidationException {
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            throw new ApheDataValidationException("id", "User not found");
        }
        user.setMfaSkips(user.getMfaSkips() + 1);
        userRepository.save(user);
    }


    @Transactional
    public User generateTotpSecret(Long userId) {
        User user = userRepo.findById(userId).orElse(null);
        if(user != null && user.getTotpSecret() == null) {
            user.setTotpSecret(Base32.random());
            user = userRepo.save(user);
        }
        return user;
    }

    @Transactional
    public boolean enableAndVerifyMFA(String loginName, long code, String deviceFingerprint, String deviceName, boolean trustDevice) throws ApheDataValidationException {
        Login login = loginRepo.findByLoginName(loginName);
        if(login != null) {
            User user = login.getUser();
            Totp totp = new Totp(user.getTotpSecret());
            if (!totp.verify(Long.toString(code))) {
                throw new ApheDataValidationException("code", "Invalid code", ApheErrorCode.AUTHN_1008);
            } else {
                if(!user.isMfaEnabled()) {
                    user.setMfaEnabled(true);
                }
                //If not already in trusted devices, add it.
                List<UserDevice> userDeviceList = user.getUserDevices();
                UserDevice newUserDevice = userDeviceList.stream().filter(userDevice -> userDevice.getFingerprint().equalsIgnoreCase(deviceFingerprint)).findFirst().orElseGet(() -> {
                    UserDevice userDevice = new UserDevice();
                    userDevice.setFingerprint(deviceFingerprint);
                    userDevice.setName(deviceName);
                    userDevice.setType("?");
                    userDevice.setTrusted(false);
                    userDevice.setUser(user);
                    userDevice.setFirstUsed(new Date());
                    user.getUserDevices().add(userDevice);
                    return userDevice;
                });
                newUserDevice.setLastUsed(new Date());
                newUserDevice.setTrusted(trustDevice);
                if(trustDevice) {
                    newUserDevice.setLastMFASuccess(new Date());
                }
                userRepo.save(user);
            }
        }
        return true;
    }


}
