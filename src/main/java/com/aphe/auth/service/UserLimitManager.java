package com.aphe.auth.service;

import com.aphe.common.util.PropertiesManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Component
public class UserLimitManager {


    public static Map<String, Long> USER_LIMITS;

    static {
        USER_LIMITS = new HashMap<>();

        if (PropertiesManager.isDev()) {
            USER_LIMITS.put("311", 15L);
        }

        if (PropertiesManager.isProd()) {
            USER_LIMITS.put("12484", 20L);
            USER_LIMITS.put("12613", 20L);
        }
    }

    @Value("${aphe.invitations.maxUsers}")
    private long maxUsers;


    public long getMaxUsers(String domainId) {
        if (USER_LIMITS.containsKey(domainId)) {
            return USER_LIMITS.get(domainId);
        } else {
            return maxUsers;
        }
    }


}
