package com.aphe.auth.service;

import com.aphe.auth.insights.InsightsDTO;
import com.aphe.auth.insights.InsightsRemoteManager;
import com.aphe.auth.model.a2c.AccountantClientRelation;
import com.aphe.auth.model.a2c.repo.AccountantClientRepository;
import com.aphe.auth.model.core.*;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.core.repo.DomainSpecification;
import com.aphe.auth.model.core.repo.LoginRepository;
import com.aphe.auth.model.core.repo.UserRepository;
import com.aphe.auth.model.u2d.ClientAccess;
import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.auth.model.u2d.repo.ClientAccessRepository;
import com.aphe.auth.model.u2d.repo.DomainAccessRepository;
import com.aphe.auth.security.TokenManager;
import com.aphe.auth.service.dto.*;
import com.aphe.common.auth.AuthManager;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.security.RecaptchaTokenValidator;
import com.aphe.common.security.jwt.ApheAuthContext;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.SSLUtil;
import com.aphe.common.util.StringUtil;
import org.hibernate.validator.constraints.ValidationErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import jakarta.persistence.EntityManager;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Component
@Transactional
public class AccountManagerTxn extends BaseManager {

	@Autowired
	protected AuthMapper authMapper;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private LoginRepository loginRepo;

	@Autowired
	private DomainRepository domainRepo;

	@Autowired
	private DomainAccessRepository domainAccessRepository;

	@Autowired
	private ClientAccessRepository clientAccessRepository;

	@Autowired
	private AccountantClientRepository accountantClientRepository;

	@Autowired
	private RecaptchaTokenValidator recaptchaTokenValidator;

	@Autowired
	BCryptPasswordEncoder passwordEncoder;

	@Autowired
	AuthManager authManager;

	@Autowired
	JwtUtil jwtUtil;

	@Autowired
	MailManager mailManager;

	@Autowired
	TokenManager tokenManager;

	@Autowired
	InsightsRemoteManager insightRemoteManager;

	@Autowired
	ClientAccessManager clientAccessManager;

	@Value("${aphe.domain.domainServiceURLLocal}")
	private String domainServiceURL;

	@Value("${aphe.domain.domainResourcePath}")
	private String domainResourcePath;

	@Autowired
	EntityManager em;

	@PreAuthorize("hasAuthority('superadmin')")
	public void flush() throws ApheForbiddenException {
		em.getEntityManagerFactory().getCache().evictAll();
	}

	//Any user can create a new domain.
	@Transactional
	public Domain addDomain(CreateDomainDTO dto) throws ApheDataValidationException {

		User u = getLoggedInUser();
		// Create Domain
		Domain d = null;
		if (dto.domainType == DomainType.A) {
			d = new Accountant();
		} else if (dto.domainType == DomainType.B) {
			d = new Business();
		} else if (dto.domainType == DomainType.C) {
			d = new Consumer();
		} else {
			ValidationErrors domainValidationErrors = new ValidationErrors();
			domainValidationErrors.addMessage("domainType", "Invalid domain Type");
			throw new ApheDataValidationException(domainValidationErrors, "Invalid input");
		}

		d.setDomainType(dto.domainType.toString());
		d.setName(dto.name);
		d.setFirstName(dto.firstName);
		d.setLastName(dto.lastName);
		d.setActive(true);
		d.setIsTestAccount(dto.isTestAccount);

		DomainAccess domainAccess = new DomainAccess();
		domainAccess.setGlobalRole(GlobalRole.ROLE_OWNER);
		domainAccess.setActive(true);
		domainAccess.setDomain(d);
		domainAccess.setUser(u);

		List<DomainAccess> domainAccessList = u.getDomainAccess();
		domainAccessList.add(domainAccess);
		u.setDomainAccess(domainAccessList);

		domainAccessRepository.save(domainAccess);
		domainRepo.save(d);
		userRepo.save(u);
		return d;
	}

	@Transactional
	@PreAuthorize("hasPermission(#accountantDomainId, 'DOMAIN', 'CREATE_CLIENT')")
	public Domain addClient(Long accountantDomainId, CreateDomainDTO dto) throws ApheDataValidationException, ApheException {

		User u = getLoggedInUser();
		Domain accountantDomain = domainRepo.findById(accountantDomainId).orElse(null);

		ValidationErrors domainValidationErrors = new ValidationErrors();

		if (accountantDomain == null || !(accountantDomain instanceof Accountant)) {
			domainValidationErrors.addMessage("domainId", "Current domain is not an accountant domain");
			throw new ApheDataValidationException(domainValidationErrors, "Invalid input");
		}

		// Create Domain
		Domain d = null;
		if (dto.domainType == DomainType.B) {
			d = new Business();
		} else if (dto.domainType == DomainType.C) {
			d = new Consumer();
		} else {
			domainValidationErrors.addMessage("domainType", "Invalid domain Type");
			throw new ApheDataValidationException(domainValidationErrors, "Invalid input");
		}
		d.setDomainType(dto.domainType.toString());
		d.setName(dto.name);
		d.setFirstName(dto.firstName);
		d.setLastName(dto.lastName);
		d.setActive(true);
		d.setIsTestAccount(dto.isTestAccount);
		d = domainRepo.save(d);

		//Find the domain access of this user to the accountant domain. If not, found throw an error.
		DomainAccess theDomainAccess = u.getDomainAccess().stream()
				.filter(da->da.getDomain().getId() == accountantDomainId)
				.findFirst().orElse(null);

		if(theDomainAccess == null) {
			throw new ApheException("No domain access found");
		}

		//Now find all the users with owner access, admin access or client mgnt access to this accountant account.
		//Add a client access record to the doamin access.
		List<DomainAccess> clientMgmtAccessList = domainAccessRepository.findByDomainId(accountantDomain.getId()).stream()
				.filter(da->da.getDomain().getId() == accountantDomainId &&
						(da.getGlobalRole() == GlobalRole.ROLE_OWNER || da.getGlobalRole() == GlobalRole.ROLE_ADMIN || da.getLocalRoles().contains(LocalRole.ROLE_CLIENT_MANAGER)))
				.collect(Collectors.toList());
		for(DomainAccess theOwnerAccess : clientMgmtAccessList) {
			ClientAccess clientAccess = new ClientAccess();
			clientAccess.setGlobalRole(GlobalRole.ROLE_ADMIN);
			clientAccess.setActive(true);
			clientAccess.setDomainAccess(theOwnerAccess);
			clientAccess.setClient(d);
			clientAccess = clientAccessRepository.save(clientAccess);
			theOwnerAccess.getClientAccess().add(clientAccess);
			theOwnerAccess = domainAccessRepository.save(theOwnerAccess);
		}



		//The user who is adding gets the admin access..
		//The owner of the firm definitley get admin access.
		//Do existing admin's of the firm get admin on the newly added client?? -- SHould not..
//		addClientAdminAccess(u, (Accountant) accountantDomain, d);

//		List<DomainAccess> allUsers = domainAccessRepository.findByDomainId(accountantDomain.getId());
//		for(DomainAccess da : allUsers) {
//			//DO not add this if the user adding this client is the owner..
//			if(da.getGlobalRole() == GlobalRole.ROLE_OWNER) {
//				addClientAdminAccess(da, (Accountant) accountantDomain, d);
//			}
//		}


		Accountant accountant = (Accountant) accountantDomain;
		AccountantClientRelation accCliRelation = new AccountantClientRelation();
		accCliRelation.setClient(d);
		accCliRelation.setActive(true);
		accCliRelation.setAccountant(accountant);
		accCliRelation.setOwner(true);

		accountant.getClientRelations().add(accCliRelation);
		d.getAccountantRelations().add(accCliRelation);

		accountantClientRepository.save(accCliRelation);
		domainRepo.save(accountant);

		return d;
	}

	public boolean updateDomainRemote(CreateDomainRemoteDTO dto) {
		String resourceUrl = domainServiceURL + domainResourcePath + "/" + dto.id;
		HttpMethod method = HttpMethod.PUT;
		return createOrUpdateDomainRemote(dto, resourceUrl, method);
	}

	public boolean createDomainRemote(CreateDomainRemoteDTO dto) {
		String resourceUrl = domainServiceURL + domainResourcePath;
		HttpMethod method = HttpMethod.POST;
		return createOrUpdateDomainRemote(dto, resourceUrl, method);
	}

	private boolean createOrUpdateDomainRemote(CreateDomainRemoteDTO dto, String resourceUrl, HttpMethod method) {
		try {
			if (PropertiesManager.isDev()) {
				SSLUtil.turnOffSslChecking();
			}

			RestTemplate restTemplate = new RestTemplate();
			restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));

			String token = ApheAuthContext.getToken();

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.add(JwtUtil.AUTH_HEADER_NAME, JwtUtil.TOKEN_PREFIX + token);

			HttpEntity<CreateDomainRemoteDTO> httpEntity = new HttpEntity<>(dto, headers);
			ResponseEntity<DomainDTO> response = restTemplate.exchange(resourceUrl, method, httpEntity, DomainDTO.class);
			if (response.getStatusCode() == HttpStatus.OK) {
				return true;
			}
		} catch (Exception e) {
			logger.error("Error creating domain on domain service", e);
			return false;
		} finally {
			try {
				// SSLUtil.turnOnSslChecking();
			} catch (Exception e) {
				logger.error("Error turning on SSL cert checking", e);
			}
		}
		return false;
	}

	//Domain read is a basic operation. Everybody has access to reading their domains.
	@Transactional
	public List<DomainDTO> getDomains(boolean includeInactive) {

		User u = getLoggedInUser();

		if (u != null) {
			List<DomainDTO> domains = new ArrayList<DomainDTO>();
			for (DomainAccess domainAccess : u.getDomainAccess()) {
				if(domainAccess.isActive() && (domainAccess.getDomain().isActive() || includeInactive)) {
					domains.add(authMapper.domainToDomainDTO(domainAccess.getDomain()));
				}
			}
			return domains;
		}
		return Collections.emptyList();
	}

	@Transactional
	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAIN')")
	public Domain getDomain(Long domainId) {
		Domain d = domainRepo.findById(domainId).orElse(null);
		return d;
	}


	@Transactional
	public boolean toggleDomainActiveStatus(long domainId, boolean isActive) {
		Domain d = domainRepo.findById(domainId).orElse(null);
		if(d != null) {
			d.setActive(isActive);
			domainRepo.save(d);
			return true;
		} else {
			return false;
		}
	}

	@Transactional
	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAIN')")
	public DomainDTO getDomainDTO(Long domainId) {
		Domain d = domainRepo.findById(domainId).orElse(null);
		if (d != null) {
			DomainDTO dto = new DomainDTO();
			dto.id = d.getId();
			dto.name = d.getName();
			dto.firstName = d.getFirstName();
			dto.lastName = d.getLastName();
			dto.isTestAccount = d.getIsTestAccount();
			dto.isActive = d.isActive();

			if (d instanceof Accountant) {
				dto.domainType = DomainType.A;
			} else if (d instanceof Business) {
				dto.domainType = DomainType.B;
			} else if (d instanceof Consumer) {
				dto.domainType = DomainType.C;
			} else {
				throw new RuntimeException("Invalid domain type");
			}
			return dto;
		}
		return null;
	}


	//No authorization. Returning read on the clients who has access.
	//TODO: enforce security on this end point. Right now this is being used by billingMgr of domain service
	// to calculate tier pricing and needs to be called from client context.
	//@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAIN')")
	public List<DomainDTO> getClients(boolean includeInactive) {
//		long loggedInDomainId = getCurrentDoaminIdAsLong();
		long loggedInDomainId = getParentDoaminId();
		User loggedInUser = getLoggedInUser();

		DomainAccess theDomainAccess = loggedInUser.getDomainAccess().stream()
				.filter(da->da.isActive() && da.getDomain().getId() == loggedInDomainId)
				.findFirst().orElse(null);
		if(theDomainAccess != null) {
			Accountant accountant = (Accountant)theDomainAccess.getDomain();

			List<Domain> theClients = accountant.getClientRelations().stream()
					.filter(ac->ac.isActive() || includeInactive)
					.map(ac->ac.getClient()).collect(Collectors.toList());

			Map<Long, Boolean> activeStatus = new HashMap<>();
			for (AccountantClientRelation rel : accountant.getClientRelations()) {
				activeStatus.put(rel.getClient().getId(), rel.isActive());
			}

			List<DomainDTO> clients = theDomainAccess.getClientAccess().stream()
					.filter(ca->ca.isActive() && theClients.contains(ca.getClient()))
					.map(ca -> authMapper.domainToDomainDTO(ca.getClient()))
					.collect(Collectors.toList());

			clients.stream().forEach(d->d.isActive=activeStatus.get(d.id));

			addInsights(clients);

			return clients;
		} else if(isSuperAdmin()) {
			long domainId = getLoggedDomainId();
			Domain d = domainRepo.findById(domainId).orElse(null);
			if(d != null && d instanceof Accountant) {
				Accountant accountant = (Accountant) d;
				List<DomainDTO> clients = accountant.getClientRelations().stream()
						.filter(ac->ac.isActive() || includeInactive)
						.map(ac->authMapper.domainToDomainDTO(ac.getClient()))
						.collect(Collectors.toList());

				addInsights(clients);

				return clients;
			}
		}

//		Domain domain = domainRepo.findById(loggedInDomainId).orElse(null);
//		if (domain != null && domain instanceof Accountant) {
//
//			List<DomainDTO> clients = clientAccessRepository.findByAccountantIdAndUserId(loggedInDomainId, getLoggedInUserId()).stream()
//					.filter(ca -> ca.isActive() == true)
//					.map(ca -> authMapper.domainToDomainDTO(ca.getClient())).collect(Collectors.toList());
//			return clients;
//		}

		return Collections.emptyList();
	}

	private void addInsights(List<DomainDTO> clients) {
		List<String> clientIds = clients.stream().map(c->Long.toString(c.id)).collect(Collectors.toList());
		List<String> insights = new ArrayList<>();
		insights.add("tin");
		insights.add("draftCount");
		insights.add("processingCount");
		insights.add("acceptedCount");
		insights.add("totalCount");

		List<InsightsDTO> insightsDTOs = insightRemoteManager.getInsightsRemote(clientIds, insights);
		if(insightsDTOs != null) {
			for (InsightsDTO insightsDTO : insightsDTOs) {
				DomainDTO client = clients.stream().filter(c->Long.toString(c.id).equalsIgnoreCase(insightsDTO.domainId)).findFirst().orElse(null);
				if(client != null) {
					client.insights = insightsDTO.insights;
				}
			}
		}
	}

	@Transactional
	public boolean toggleClientActiveStatus(long domainId, long clientId, boolean isActive) {
		Domain d = domainRepo.findById(domainId).orElse(null);
		if(d != null && d instanceof Accountant) {
			Accountant accountant = (Accountant) d;
			AccountantClientRelation acr = accountant.getClientRelations().stream().filter(ac->ac.getClient().getId() == clientId).findFirst().orElse(null);
			acr.setActive(isActive);
			acr.getClient().setActive(isActive);
			domainRepo.save(acr.getClient());
			accountantClientRepository.save(acr);
			return true;
		} else {
			return false;
		}
	}

	@Transactional
	@PreAuthorize("hasRole('CLIENT_MANAGER') or hasRole('ADMIN') or hasRole('OWNER')")
	public List<DomainDTO> getAllClients() {
		long loggedInDomainId = getCurrentDomainId();
		User loggedInUser = getLoggedInUser();

		DomainAccess theDomainAccess = loggedInUser.getDomainAccess().stream()
				.filter(da->da.isActive() && da.getDomain().getId() == loggedInDomainId)
				.findFirst().orElse(null);
		if(theDomainAccess != null) {
			Accountant accountant = (Accountant)theDomainAccess.getDomain();

			List<DomainDTO> theClients = accountant.getClientRelations().stream()
					.map(ac -> authMapper.domainToDomainDTO(ac.getClient()))
					.collect(Collectors.toList());

			return theClients;
		}
		return Collections.emptyList();
	}

	@Transactional
	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAIN')")
	public MyAccountantDTO getMyAccountant(String domainId, String accountantId) {
		Domain theDomain = domainRepo.findById(Long.parseLong(domainId)).orElse(null);
		if(theDomain != null) {
			long accountantIdLong = Long.parseLong(accountantId);

			AccountantClientRelation theAccountantClientRel = theDomain.getAccountantRelations().stream()
					.filter(ac->ac.getAccountant().getId() == accountantIdLong)
					.findFirst().orElse(null);
			if(theAccountantClientRel != null) {
				return authMapper.domainToMyAccountantDTO(theAccountantClientRel.getAccountant());
			}
		}
		return null;
	}

	@Transactional
	@PreAuthorize("hasPermission(#domainId, 'DOMAIN', 'READ_DOMAIN')")
	public List<MyAccountantDTO> getMyAccountants(String domainId) {
		Domain domain = domainRepo.findById(Long.parseLong(domainId)).orElse(null);
		List<MyAccountantDTO> retVal = new ArrayList<>();
		if(domain != null) {
			for(AccountantClientRelation rel : domain.getAccountantRelations()) {
				MyAccountantDTO e = authMapper.domainToMyAccountantDTO(rel.getAccountant());
				e.isOwner = rel.isOwner();
				retVal.add(e);
			}
		}
		return retVal;
	}

	@Transactional
	@PreAuthorize("hasPermission(#domainDTO.id, 'DOMAIN', 'UPDATE_DOMAIN')")
	public long updateDomain(UpdateDomainDTO domainDTO) throws ApheException {

		Domain currentDomain = domainRepo.findById(Long.parseLong(domainDTO.id)).orElse(null);

		if (isAccessible(currentDomain)) {
			if(domainDTO.name != null) {
				currentDomain.setName(domainDTO.name);
			}
			if(domainDTO.firstName != null) {
				currentDomain.setFirstName(domainDTO.firstName);
			}
			if(domainDTO.lastName != null) {
				currentDomain.setLastName(domainDTO.lastName);
			}
			if(domainDTO.isTestAccount != null) {
				if(domainDTO.isTestAccount.booleanValue() != currentDomain.getIsTestAccount().booleanValue() && !isSuperAdmin()) {
					throw new ApheForbiddenException("Not authorized to mark test accounts.");
				}
				currentDomain.setIsTestAccount(domainDTO.isTestAccount);
			}
			Domain savedDomain = domainRepo.save(currentDomain);
			return savedDomain.getId();
		} else {
			throw new ApheDataValidationException(new ValidationErrors(), "Invalid input");
		}

	}

	public boolean validateToken(HttpServletRequest request, HttpServletResponse response, String token) throws Exception {
		if (token != null) {
			boolean validToken = tokenManager.validateToken(token);
			if (!validToken) {
				jwtUtil.eraseToken(request, response);
			}
			return validToken;
		}
		return false;
	}

	public List<DomainDTO> searchDomains(String searchString) {
		if(StringUtil.isNotEmpty(searchString)) {
			List<Domain> domains = domainRepo.findAll(Specification.where(DomainSpecification.textInAllColumns(searchString)));
			List<DomainDTO> domainDTOS = domains.stream().map(d -> authMapper.domainToDomainDTO(d)).collect(Collectors.toList());
			return domainDTOS;
		}
		return new ArrayList<>();
	}


	@Transactional
	@PreAuthorize("hasAuthority('superadmin')")
	public Map<Long, Collection<String>> getAccountants(List<Long> domainIds) {
		Map<Long, Collection<String>> accountants = new HashMap<>();

		if (domainIds == null || domainIds.isEmpty())
			return accountants;

		for (Long domainId : domainIds) {
			Domain theDomain = domainRepo.findById(domainId).orElse(null);
			if (isAccessible(theDomain)) {
				List<String> domainAccountants = new ArrayList<>();

				if(theDomain.isActive()) {
					List<AccountantClientRelation> theRelations = theDomain.getAccountantRelations();
					for (AccountantClientRelation relation : theRelations) {
						if(relation.isActive() && relation.getAccountant().isActive()) {
							domainAccountants.add(Long.toString(relation.getAccountant().getId()));
						}
					}
				}
				accountants.put(domainId, domainAccountants);
			}
		}
		return accountants;
	}
}
