package com.aphe.auth.insights;

import com.aphe.common.auth.AuthManager;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.PropertiesManager;
import com.aphe.common.util.RestTemplateRequestLogger;
import com.aphe.common.util.SSLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class InsightsRemoteManager {

    private static Logger logger = LoggerFactory.getLogger(InsightsRemoteManager.class);

    @Value("${aphe.domain.domainServiceURLLocal}")
    private String domainServiceURL;

    @Value("${aphe.domain.insightsResourcePath}")
    private String insightsResourcePath;

    @Autowired
    AuthManager authManager;

    public List<InsightsDTO> getInsightsRemote(List<String> domainIds, List<String> insights) {

        String token = authManager.getTokenForSystemUser();

        try {
            if (PropertiesManager.isDev()) {
                SSLUtil.turnOffSslChecking();
            }

            RestTemplate restTemplate = new RestTemplate();
            restTemplate.setInterceptors(Collections.singletonList(new RestTemplateRequestLogger()));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add(JwtUtil.AUTH_HEADER_NAME, JwtUtil.TOKEN_PREFIX + token);
            HttpEntity<?> httpEntity = new HttpEntity<>(headers);

            String resourceUrl = domainServiceURL + "" + insightsResourcePath;

            String urlTemplate = UriComponentsBuilder.fromHttpUrl(resourceUrl)
                    .queryParam("domainIds", "{domainIds}")
                    .queryParam("insights", "{insights}")
                    .encode()
                    .toUriString();

            Map<String, String> vars = new HashMap<>();
            vars.put("domainIds", String.join(",", domainIds));
            vars.put("insights", String.join(",", insights));

            ParameterizedTypeReference<List<InsightsDTO>> responseType = new ParameterizedTypeReference<List<InsightsDTO>>() {};
            ResponseEntity<List<InsightsDTO>> response = restTemplate.exchange(urlTemplate, HttpMethod.GET, httpEntity, responseType, vars);
            if (response.getStatusCode() == HttpStatus.OK) {
                List<InsightsDTO> insightsDTOS = response.getBody();
                return insightsDTOS;
            }
        } catch (Exception e) {
            logger.error("Error getting insights from domain service", e);
            return null;
        } finally {
            try {
                // SSLUtil.turnOnSslChecking();
                //logout the system user
                authManager.deleteToken(token);
            } catch (Exception e) {
                logger.error("Error turning on SSL cert checking", e);
            }
        }
        return null;
    }
}
