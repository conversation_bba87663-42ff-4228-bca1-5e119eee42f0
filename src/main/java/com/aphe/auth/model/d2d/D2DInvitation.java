package com.aphe.auth.model.d2d;

import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "d2dinvitations")
public class D2DInvitation extends BaseEntity {

	@Column(name = "S_DOMAIN_ID")
	private Long domainId;

	@Column(name = "S_SUB_ENTITY_ID")
	private Long subEntityId;

	@Enumerated(EnumType.STRING)
	@Column(name = "RELATIONTYPE")
	private D2DRelationType relationType;

	private String emailAddress;

	@Column(nullable = false)
	private String name;

	private String invitationCode;

	@Enumerated(EnumType.STRING)
	private D2DInvitationStatus status;

	private Date createdDate;

	private Date acceptedDate;

	@Column(name = "LINKED_DOMAIN_ID")
	private Long linkedDomainId;

	@Column(name = "LINKED_SUB_ENTITY_ID")
	private Long linkedSubEntityId;


	public D2DInvitation() {
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Long getSubEntityId() {
		return subEntityId;
	}

	public void setSubEntityId(Long subEntityId) {
		this.subEntityId = subEntityId;
	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getInvitationCode() {
		return invitationCode;
	}

	public void setInvitationCode(String invitationCode) {
		this.invitationCode = invitationCode;
	}

	public D2DInvitationStatus getStatus() {
		return status;
	}

	public void setStatus(D2DInvitationStatus status) {
		this.status = status;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getAcceptedDate() {
		return acceptedDate;
	}

	public void setAcceptedDate(Date acceptedDate) {
		this.acceptedDate = acceptedDate;
	}

	public Long getLinkedDomainId() {
		return linkedDomainId;
	}

	public void setLinkedDomainId(Long linkedDomainId) {
		this.linkedDomainId = linkedDomainId;
	}

	public Long getLinkedSubEntityId() {
		return linkedSubEntityId;
	}

	public void setLinkedSubEntityId(Long linkedSubEntityId) {
		this.linkedSubEntityId = linkedSubEntityId;
	}

	public D2DRelationType getRelationType() {
		return relationType;
	}

	public void setRelationType(D2DRelationType relationType) {
		this.relationType = relationType;
	}
}
