package com.aphe.auth.model.d2d.repo;

import com.aphe.auth.model.d2d.D2DRelation;
import com.aphe.auth.model.d2d.D2DRelationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface D2DRelationRepository extends JpaRepository<D2DRelation, Long> {

    List<D2DRelation> findBySourceDomainId(String domainId);

    List<D2DRelation> findBySourceDomainIdAndRelationType(String domainId, D2DRelationType relationType);

    D2DRelation findBySourceDomainIdAndSourceSubEntityIdAndRelationType(String domainId, String subEntityId, D2DRelationType relationType);

    @Query("SELECT r FROM D2DRelation r WHERE r.sourceDomainId=:domainId AND r.relationType=:relationType AND r.sourceSubEntityId IN :subEntityIds")
    List<D2DRelation> findBySourceDomainIdAndRelationTypeAndSourceSubEntityIdIn(String domainId, D2DRelationType relationType, List<String> subEntityIds);

    List<D2DRelation> findByTargetDomainId(String domainId);

    List<D2DRelation> findByTargetDomainIdAndRelationType(String domainId, D2DRelationType relationType);

    D2DRelation findByTargetDomainIdAndTargetSubEntityIdAndRelationType(String domainId, String subEntityId, D2DRelationType relationType);

}