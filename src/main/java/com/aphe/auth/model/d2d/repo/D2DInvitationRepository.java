package com.aphe.auth.model.d2d.repo;

import com.aphe.auth.model.d2d.D2DInvitation;
import com.aphe.auth.model.d2d.D2DRelationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface D2DInvitationRepository extends JpaRepository<D2DInvitation, Long> {

    List<D2DInvitation> findByDomainIdAndSubEntityIdAndRelationType(Long domainId, Long subEntityId, D2DRelationType relationType);

    @Query("SELECT i FROM D2DInvitation i WHERE i.domainId=:domainId AND i.relationType=:relationType AND i.subEntityId IN :subEntityIds")
    List<D2DInvitation> findBySourceDomainIdAndRelationTypeAndSubEntityIdIn(Long domainId, D2DRelationType relationType, List<Long> subEntityIds);

    List<D2DInvitation> findByDomainIdAndRelationType(Long domainId, D2DRelationType relationType);

    D2DInvitation findByInvitationCode(String invitationCode);

}