package com.aphe.auth.model.d2d;

import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;

@Entity
@Table(name = "d2drelations")
public class D2DRelation extends BaseEntity {

    @Column(name = "S_DOMAIN_ID")
    private Long sourceDomainId;

    @Column(name = "S_SUB_ENTITY_ID")
    private Long sourceSubEntityId;

    @Column(name = "T_DOMAIN_ID")
    private Long targetDomainId;

    @Column(name = "T_SUB_ENTITY_ID")
    private Long targetSubEntityId;

    @Enumerated(EnumType.STRING)
    @Column(name = "RELATIONTYPE")
    private D2DRelationType relationType;

    private boolean isActive;


    public Long getSourceDomainId() {
        return sourceDomainId;
    }

    public void setSourceDomainId(Long sourceDomainId) {
        this.sourceDomainId = sourceDomainId;
    }

    public Long getSourceSubEntityId() {
        return sourceSubEntityId;
    }

    public void setSourceSubEntityId(Long sourceSubEntityId) {
        this.sourceSubEntityId = sourceSubEntityId;
    }

    public Long getTargetDomainId() {
        return targetDomainId;
    }

    public void setTargetDomainId(Long targetDomainId) {
        this.targetDomainId = targetDomainId;
    }

    public Long getTargetSubEntityId() {
        return targetSubEntityId;
    }

    public void setTargetSubEntityId(Long targetSubEntityId) {
        this.targetSubEntityId = targetSubEntityId;
    }

    public D2DRelationType getRelationType() {
        return relationType;
    }

    public void setRelationType(D2DRelationType relationType) {
        this.relationType = relationType;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }
}
