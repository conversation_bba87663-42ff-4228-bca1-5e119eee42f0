package com.aphe.auth.model.d2d;

import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;

@Entity
@Table(name = "d2drelations")
public class D2DRelation extends BaseEntity {

    @Column(name = "S_DOMAIN_ID")
    private String sourceDomainId;

    @Column(name = "S_SUB_ENTITY_ID")
    private String sourceSubEntityId;

    @Column(name = "T_DOMAIN_ID")
    private String targetDomainId;

    @Column(name = "T_SUB_ENTITY_ID")
    private String targetSubEntityId;

    @Enumerated(EnumType.STRING)
    @Column(name = "RELATIONTYPE")
    private D2DRelationType relationType;

    private boolean isActive;


    public String getSourceDomainId() {
        return sourceDomainId;
    }

    public void setSourceDomainId(String sourceDomainId) {
        this.sourceDomainId = sourceDomainId;
    }

    public String getSourceSubEntityId() {
        return sourceSubEntityId;
    }

    public void setSourceSubEntityId(String sourceSubEntityId) {
        this.sourceSubEntityId = sourceSubEntityId;
    }

    public String getTargetDomainId() {
        return targetDomainId;
    }

    public void setTargetDomainId(String targetDomainId) {
        this.targetDomainId = targetDomainId;
    }

    public String getTargetSubEntityId() {
        return targetSubEntityId;
    }

    public void setTargetSubEntityId(String targetSubEntityId) {
        this.targetSubEntityId = targetSubEntityId;
    }

    public D2DRelationType getRelationType() {
        return relationType;
    }

    public void setRelationType(D2DRelationType relationType) {
        this.relationType = relationType;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }
}
