package com.aphe.auth.model.a2c;

import com.aphe.auth.model.core.Accountant;
import com.aphe.auth.model.core.Domain;
import com.aphe.common.model.BaseEntity;

import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "accountantclientrelations")
public class AccountantClientRelation extends BaseEntity {

	@ManyToOne
	private Domain client;

	@ManyToOne
	private Accountant accountant;

	private boolean isOwner;

	private boolean isActive;

	public AccountantClientRelation() {
	}

	public Domain getClient() {
		return client;
	}

	public void setClient(Domain client) {
		this.client = client;
	}

	public Accountant getAccountant() {
		return accountant;
	}

	public void setAccountant(Accountant accountant) {
		this.accountant = accountant;
	}

	public boolean isOwner() {
		return isOwner;
	}

	public void setOwner(boolean owner) {
		isOwner = owner;
	}

	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean active) {
		isActive = active;
	}
}
