package com.aphe.auth.model.util;

import com.aphe.common.auth.GlobalRole;
import com.aphe.common.util.StringUtil;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Converter
public class GlobalRoleListConverter implements AttributeConverter<List<GlobalRole>, String> {

	@Override
  public String convertToDatabaseColumn(List<GlobalRole> list) {
		if(list != null) {
			List<String> stringValues = new ArrayList<>();
			for(GlobalRole role : list){
				stringValues.add(role.name());
			}
			return String.join(",", stringValues);
		}
		return "";
  }

	@Override
	public List<GlobalRole> convertToEntityAttribute(String joined) {
		if(!StringUtil.isEmpty(joined)) {
			List<String> stringValues = Arrays.asList(joined.split(","));
			List<GlobalRole> roles = new ArrayList<>();
			for (String role : stringValues) {
				GlobalRole globalRole = GlobalRole.lookupByName(role);
				if(globalRole != null) {
					roles.add(globalRole);
				}
			}
			return roles;
		}
		return Collections.emptyList();
	}

}