package com.aphe.auth.model.util;

import com.aphe.common.auth.LocalRole;
import com.aphe.common.util.StringUtil;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Converter
public class LocalRoleListConverter implements AttributeConverter<List<LocalRole>, String> {

	@Override
  public String convertToDatabaseColumn(List<LocalRole> list) {
		if(list != null) {
			List<String> stringValues = new ArrayList<>();
			for (LocalRole role : list) {
				stringValues.add(role.name());
			}
			return String.join(",", stringValues);
		}
		return "";

  }

	@Override
	public List<LocalRole> convertToEntityAttribute(String joined) {
		if(!StringUtil.isEmpty(joined)) {
			List<String> stringValues = Arrays.asList(joined.split(","));
			List<LocalRole> roles = new ArrayList<>();
			for (String role : stringValues) {
				LocalRole e = LocalRole.lookupByName(role);
				if(e!=null) {
					roles.add(e);
				}
			}
			return roles;
		}
		return Collections.emptyList();
	}

}