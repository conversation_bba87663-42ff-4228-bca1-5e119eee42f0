package com.aphe.auth.model.u2d.repo;

import com.aphe.auth.model.u2d.ClientAccess;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ClientAccessRepository extends JpaRepository<ClientAccess, Long> {

//    public List<ClientAccess> findByAccountantId(long accountantId);

//    public List<ClientAccess> findByAccountantIdAndClientId(long accountantId, long clientId);

//    public List<ClientAccess> findByAccountantIdAndUserId(long accountantId, long userId);

//    public List<ClientAccess> findByAccountantIdAndUserIdAndClientId(long accountantId, long userId, long clientId);

    public List<ClientAccess> findByDomainAccessId(long domainAccessId);

    public List<ClientAccess> findByDomainAccessIdAndClientId(long domainAccessId, long clientId);

}