package com.aphe.auth.model.u2d;

import com.aphe.auth.model.util.LocalRoleListConverter;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;
import java.util.List;

@Entity
@Table(name = "clientaccessinvitations")
public class ClientAccessInvitation extends BaseEntity {

	@ManyToOne
	private Invitation invitation;

	private String clientId;

	@Enumerated(EnumType.STRING)
	private GlobalRole globalRole;

	@Convert(converter = LocalRoleListConverter.class)
	@Enumerated(EnumType.STRING)
	private List<LocalRole> localRoles;


	public ClientAccessInvitation() {
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public GlobalRole getGlobalRole() {
		return globalRole;
	}

	public void setGlobalRole(GlobalRole globalRole) {
		this.globalRole = globalRole;
	}

	public List<LocalRole> getLocalRoles() {
		return localRoles;
	}

	public void setLocalRoles(List<LocalRole> localRoles) {
		this.localRoles = localRoles;
	}

	public Invitation getInvitation() {
		return invitation;
	}

	public void setInvitation(Invitation invitation) {
		this.invitation = invitation;
	}
}

