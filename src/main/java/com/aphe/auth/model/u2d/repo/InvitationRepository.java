package com.aphe.auth.model.u2d.repo;

import com.aphe.auth.model.u2d.Invitation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface InvitationRepository extends JpaRepository<Invitation, Long> {

    public Invitation findByInvitationCode(String invitationCode);

    public List<Invitation> findByDomainId(String domainId);

    public List<Invitation> findByEmailAndDomainId(String email, String domainId);

}