package com.aphe.auth.model.u2d;

import com.aphe.auth.model.core.User;
import com.aphe.auth.model.util.LocalRoleListConverter;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "invitations")
public class Invitation extends BaseEntity {

	@Column(nullable = false)
	private String firstName;

	@Column(nullable = false)
	private String lastName;

	private String middleName;

	private String email;

	private String invitationCode;

	private User linkedUser;

	@Enumerated(EnumType.STRING)
	private InvitationStatus status;

	private Date createdDate;

	private Date acceptedDate;

	private String domainId;

	@Enumerated(EnumType.STRING)
	private GlobalRole globalRole;

	@Convert(converter = LocalRoleListConverter.class)
	@Enumerated(EnumType.STRING)
	private List<LocalRole> localRoles = new ArrayList<>();

	@OneToMany(mappedBy = "invitation", cascade = CascadeType.ALL)
	private List<ClientAccessInvitation> clientAccess = new ArrayList<>(0);

	public Invitation() {
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getInvitationCode() {
		return invitationCode;
	}

	public void setInvitationCode(String invitationCode) {
		this.invitationCode = invitationCode;
	}

	public User getLinkedUser() {
		return linkedUser;
	}

	public void setLinkedUser(User linkedUser) {
		this.linkedUser = linkedUser;
	}

	public InvitationStatus getStatus() {
		return status;
	}

	public void setStatus(InvitationStatus status) {
		this.status = status;
	}

	public Date getAcceptedDate() {
		return acceptedDate;
	}

	public void setAcceptedDate(Date acceptedDate) {
		this.acceptedDate = acceptedDate;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getDomainId() {
		return domainId;
	}

	public void setDomainId(String domainId) {
		this.domainId = domainId;
	}

	public GlobalRole getGlobalRole() {
		return globalRole;
	}

	public void setGlobalRole(GlobalRole globalRole) {
		this.globalRole = globalRole;
	}

	public List<LocalRole> getLocalRoles() {
		return localRoles;
	}

	public void setLocalRoles(List<LocalRole> localRoles) {
		this.localRoles = localRoles;
	}

	public List<ClientAccessInvitation> getClientAccess() {
		return clientAccess;
	}

	public void setClientAccess(List<ClientAccessInvitation> clientAccess) {
		this.clientAccess = clientAccess;
	}
}
