package com.aphe.auth.model.u2d;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.util.LocalRoleListConverter;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "clientaccess")
public class ClientAccess extends BaseEntity {

//    @ManyToOne
//    private Accountant accountant;
//
//    @OneToOne
//    private User user;

    @ManyToOne
    private DomainAccess domainAccess;

    @OneToOne
    private Domain client;

    @Enumerated(EnumType.STRING)
    private GlobalRole globalRole;

    @Convert(converter = LocalRoleListConverter.class)
    @Enumerated(EnumType.STRING)
    private List<LocalRole> localRoles = new ArrayList<>();

    private boolean isActive;

    public ClientAccess() {
    }

    public Domain getClient() {
        return client;
    }

    public void setClient(Domain domain) {
        this.client = domain;
    }

//    public User getUser() {
//        return user;
//    }
//
//    public void setUser(User user) {
//        this.user = user;
//    }

    public GlobalRole getGlobalRole() {
        return globalRole;
    }

    public void setGlobalRole(GlobalRole globalRole) {
        this.globalRole = globalRole;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

//    public Accountant getAccountant() {
//        return accountant;
//    }
//
//    public void setAccountant(Accountant accountant) {
//        this.accountant = accountant;
//    }

    public List<LocalRole> getLocalRoles() {
        return localRoles;
    }

    public void setLocalRoles(List<LocalRole> localRoles) {
        this.localRoles = localRoles;
    }

    public DomainAccess getDomainAccess() {
        return domainAccess;
    }

    public void setDomainAccess(DomainAccess domainAccess) {
        this.domainAccess = domainAccess;
    }
}
