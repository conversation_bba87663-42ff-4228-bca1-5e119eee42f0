package com.aphe.auth.model.u2d;

import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.util.LocalRoleListConverter;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "domainaccess")
public class DomainAccess extends BaseEntity {

    @ManyToOne
    private User user;

    @OneToOne
    private Domain domain;

    @Enumerated(EnumType.STRING)
    private GlobalRole globalRole;

    @Convert(converter = LocalRoleListConverter.class)
    @Enumerated(EnumType.STRING)
    private List<LocalRole> localRoles = new ArrayList<>();

    private boolean isActive;

    @OneToMany(mappedBy = "domainAccess")
    private List<ClientAccess> clientAccess = new ArrayList<>(0);

    public DomainAccess() {
    }

    public Domain getDomain() {
        return domain;
    }

    public void setDomain(Domain domain) {
        this.domain = domain;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public GlobalRole getGlobalRole() {
        return globalRole;
    }

    public void setGlobalRole(GlobalRole globalRole) {
        this.globalRole = globalRole;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public List<LocalRole> getLocalRoles() {
        return localRoles;
    }

    public void setLocalRoles(List<LocalRole> localRoles) {
        this.localRoles = localRoles;
    }

    public List<ClientAccess> getClientAccess() {
        return clientAccess;
    }

    public void setClientAccess(List<ClientAccess> clientAccess) {
        this.clientAccess = clientAccess;
    }
}
