package com.aphe.auth.model.core;

import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.common.model.BaseEntity;
import com.aphe.common.persistence.AttributeEncryptor;
import org.jboss.aerogear.security.otp.api.Base32;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "users")
public class User extends BaseEntity {

	@Column(nullable = false)
	private String firstName;

	@Column(nullable = false)
	private String lastName;

	private String middleName;

	private String email;

	@Convert(converter = AttributeEncryptor.class)
	private String totpSecret;

	private boolean mfaEnabled;

	private int mfaSkips;

	@OneToOne(fetch = FetchType.EAGER, mappedBy = "user")
	private Login login;

	@OneToMany(mappedBy = "user")
	private List<DomainAccess> domainAccess = new ArrayList<>(0);

	@OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
	private List<UserDevice> userDevices = new ArrayList<>(0);

	public User() {
		this.totpSecret = Base32.random();
	}

	public Login getLogin() {
		return login;
	}

	public void setLogin(Login login) {
		this.login = login;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public List<DomainAccess> getDomainAccess() {
		return domainAccess;
	}

	public void setDomainAccess(List<DomainAccess> domainAccess) {
		this.domainAccess = domainAccess;
	}

	public List<UserDevice> getUserDevices() {
		return userDevices;
	}

	public void setUserDevices(List<UserDevice> userDevices) {
		this.userDevices = userDevices;
	}

	public String getTotpSecret() {
		return totpSecret;
	}

	public void setTotpSecret(String totpSecret) {
		this.totpSecret = totpSecret;
	}

	public boolean isMfaEnabled() {
		return mfaEnabled;
	}

	public void setMfaEnabled(boolean mfaEnabled) {
		this.mfaEnabled = mfaEnabled;
	}

	public int getMfaSkips() {
		return mfaSkips;
	}

	public void setMfaSkips(int mfaSkips) {
		this.mfaSkips = mfaSkips;
	}
}
