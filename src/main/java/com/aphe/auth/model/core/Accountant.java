package com.aphe.auth.model.core;

import com.aphe.auth.model.a2c.AccountantClientRelation;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * 
 *         Accountant is a concrete class to represent an Accountant.
 * 
 *
 */
@Entity
@Table(name = "accountants")
@DiscriminatorValue("A")
public class Accountant extends Business {

	@OneToMany(mappedBy = "accountant")
	private List<AccountantClientRelation> clientRelations = new ArrayList<>(0);

//	@OneToMany(mappedBy = "accountant")
//	private List<ClientAccess> clientAccess = new ArrayList<>(0);
//
	public Accountant() {
	}

//	public List<ClientAccess> getClientAccess() {
//		return clientAccess;
//	}
//
//	public void setClientAccess(List<ClientAccess> clientAccess) {
//		this.clientAccess = clientAccess;
//	}

	public List<AccountantClientRelation> getClientRelations() {
		return clientRelations;
	}

	public void setClientRelations(List<AccountantClientRelation> clientRelations) {
		this.clientRelations = clientRelations;
	}
}
