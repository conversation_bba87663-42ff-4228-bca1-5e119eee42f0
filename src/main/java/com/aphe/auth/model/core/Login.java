package com.aphe.auth.model.core;

import com.aphe.common.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;

import jakarta.persistence.*;

/**
 * 
 * <AUTHOR>
 *
 *         Login represents the login information of a user. The system can have many users with roles in many domains. But login allows the user to enter into the system with
 *         valid credentials.
 */
@Entity
@Table(name = "logins")
public class Login extends BaseEntity {
	
	@Column(unique = true)
	private String loginName;

	@Column(nullable = false)
	private String hashedPassword;

	@Column(nullable = false)
	private Boolean accountNonExpired;

	@Column(nullable = false)
	private Boolean accountNonLocked;

	@Column(nullable = false)
	private Boolean credentialsNonExpired;

	@Column(nullable = false)
	private Boolean enabled;

	@Column(nullable = false)
	private Boolean emailConfirmed;

	@Column
	private String emailConfirmationCode;

	@Column
	private String passwordResetCode;

	@Column
	@Enumerated(EnumType.STRING)
	private SSOPartner ssoPartner; //We need to remove this too. This should probably move to User if at all...

	@OneToOne	
	@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
	@JsonIdentityReference(alwaysAsId = true)
	private User user;

	public Login() {
	}

	public String getHashedPassword() {
		return hashedPassword;
	}

	public Boolean isAccountNonExpired() {
		return accountNonExpired;
	}

	public Boolean isAccountNonLocked() {
		return accountNonLocked;
	}

	public Boolean isCredentialsNonExpired() {
		return credentialsNonExpired;
	}

	public Boolean isEnabled() {
		return enabled;
	}

	public void setHashedPassword(String hashedPassword) {
		this.hashedPassword = hashedPassword;
	}

	public void setAccountNonExpired(Boolean accountNonExpired) {
		this.accountNonExpired = accountNonExpired;
	}

	public void setAccountNonLocked(Boolean accountNonLocked) {
		this.accountNonLocked = accountNonLocked;
	}

	public void setCredentialsNonExpired(Boolean credentialsNonExpired) {
		this.credentialsNonExpired = credentialsNonExpired;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public Boolean isEmailConfirmed() {
		return emailConfirmed;
	}

	public void setEmailConfirmed(Boolean emailConfirmed) {
		this.emailConfirmed = emailConfirmed;
	}

	public String getEmailConfirmationCode() {
		return emailConfirmationCode;
	}

	public void setEmailConfirmationCode(String emailConfirmationCode) {
		this.emailConfirmationCode = emailConfirmationCode;
	}

	public String getPasswordResetCode() {
		return passwordResetCode;
	}

	public void setPasswordResetCode(String passwordResetCode) {
		this.passwordResetCode = passwordResetCode;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public SSOPartner getSsoPartner() {
		return ssoPartner;
	}

	public void setSsoPartner(SSOPartner ssoPartner) {
		this.ssoPartner = ssoPartner;
	}
}
