package com.aphe.auth.model.core.repo;

import com.aphe.auth.model.core.Domain;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DomainRepository extends JpaRepository<Domain, Long> {

    @Query("SELECT d FROM Domain d WHERE d.id IN :domainIds")
    public List<Domain> findByIdIn(@Param("domainIds") List<Long> domainIds);

    List<Domain> findAll(Specification<Domain> spec);
}