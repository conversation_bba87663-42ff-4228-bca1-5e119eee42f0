package com.aphe.auth.model.core.repo;

import com.aphe.auth.model.core.User;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UserRepository extends JpaRepository<User, Long> {

    @Query("SELECT u FROM User u WHERE u.id IN :userIds")
    public List<User> findByIdIn(@Param("userIds") List<Long> userIds);

    List<User> findAll(Specification<User> spec);

}