package com.aphe.auth.model.core.repo;

import com.aphe.auth.model.core.Domain;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class DomainSpecification {

    public static Specification<Domain> textInAllColumns(String text) {

        // if text is a proper integer convert to int.
        String idText = "0";
         if (text.matches("\\d+")) {
             idText = text;
         }
        String searchText = "";
        if (!text.contains("%")) {
            searchText = "%" + text + "%";
        }
        final String theFinalSearchText = searchText;
        final String theFinalIdText = idText;

        return new Specification<Domain>() {
            @Override
            public Predicate toPredicate(Root<Domain> root, CriteriaQuery<?> cq, CriteriaBuilder builder) {
//                return builder.or(root.getModel().getDeclaredSingularAttributes().stream().filter(a -> {
//                    if (a.getJavaType().getSimpleName().equalsIgnoreCase("string")) {
//                        return true;
//                    } else {
//                        return false;
//                    }
//                }).map(a -> builder.like(root.get(a.getName()), finalText)).toArray(Predicate[]::new));

                return builder.or(
                        builder.like(root.get("lastName"), theFinalSearchText),
                        builder.like(root.get("firstName"), theFinalSearchText),
                        builder.like(root.get("name"), theFinalSearchText),
                        builder.like(root.get("globalId"), theFinalSearchText),
                        builder.equal(root.get("id"), Integer.parseInt(theFinalIdText))
                );
            }
        };
    }

}



