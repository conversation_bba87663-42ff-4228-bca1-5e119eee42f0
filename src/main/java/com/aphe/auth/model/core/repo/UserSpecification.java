package com.aphe.auth.model.core.repo;

import com.aphe.auth.model.core.User;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class UserSpecification {

    public static Specification<User> textInAllColumns(String text) {

        if (!text.contains("%")) {
            text = "%" + text + "%";
        }
        final String finalText = text;

        return new Specification<User>() {
            @Override
            public Predicate toPredicate(Root<User> root, CriteriaQuery<?> cq, CriteriaBuilder builder) {
//                return builder.or(root.getModel().getDeclaredSingularAttributes().stream().filter(a -> {
//                    if (a.getJavaType().getSimpleName().equalsIgnoreCase("string")) {
//                        return true;
//                    } else {
//                        return false;
//                    }
//                }).map(a -> builder.like(root.get(a.getName()), finalText)).toArray(Predicate[]::new));

                return builder.or(
                        builder.like(root.get("firstName"), finalText),
                        builder.like(root.get("lastName"), finalText),
                        builder.like(root.get("middleName"), finalText),
                        builder.like(root.get("email"), finalText),
                        builder.like(root.get("globalId"), finalText)
                );
            }
        };
    }

}



