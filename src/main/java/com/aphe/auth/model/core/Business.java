package com.aphe.auth.model.core;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 
 * <AUTHOR>
 * 
 *  Business is a concrete class to represent an SMB or any other type of business entity.
 * 
 *
 */
@Entity
@Table(name = "businesses")
@DiscriminatorValue("B")
public class Business extends Domain {

	public Business() {
	}

}
