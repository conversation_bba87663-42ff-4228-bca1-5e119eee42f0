package com.aphe.auth.model.core;

public enum OAuthIntegrationPartner {
	QBDT("Quickbooks Desktop", "Intuit", "qbdt.jpeg", "file", "files"),
	QBO("Quickbooks Online", "Intuit", "qbdt.jpeg", "company", "companies"),
	Xero("Xero", "Xero", "qbdt.jpeg", "organization", "organizations"),
	FreshBooks("FreshBooks", "FreshBooks" ,"qbdt.jpeg", "business", "businesses"),
	ZohoBooks("ZohoBooks", "Zoho", "zoho.jpeg", "organization", "organizations"),
	Wave("Wave", "Wave", "wave.jpeg", "business", "businesses"),
	BQECORE("CORE", "BQE", "bqecore.jpeg", "company", "companies"),
	<PERSON>("Bill.com", "Bill.com", "bill.svg", "company", "companies"),
	Excel("Excel", "Excel", "qbdt.jpeg", "file", "files"),
	;


	private String productName;
	private String company;
	private String logoName;
	private String orgRef;
	private String orgsRef;

	OAuthIntegrationPartner(String productName, String company, String logoName, String orgRef, String orgsRef) {
		this.setProductName(productName);
		this.setCompany(company);
		this.setLogoName(logoName);
		this.setOrgRef(orgRef);
		this.setOrgsRef(orgsRef);
	}


	public static OAuthIntegrationPartner getFromName(String s) {
		for(OAuthIntegrationPartner partner : values()) {
			if(partner.name().equalsIgnoreCase(s)) {
				return partner;
			}
		}
		return null;
	}
	
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getLogoName() {
		return logoName;
	}

	public void setLogoName(String logoName) {
		this.logoName = logoName;
	}

	public String getOrgRef() {
		return orgRef;
	}

	public void setOrgRef(String orgRef) {
		this.orgRef = orgRef;
	}

	public String getOrgsRef() {
		return orgsRef;
	}

	public void setOrgsRef(String orgsRef) {
		this.orgsRef = orgsRef;
	}
}
