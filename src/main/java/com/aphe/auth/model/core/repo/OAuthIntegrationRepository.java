package com.aphe.auth.model.core.repo;

import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.OAuthIntegrationPartner;
import com.aphe.common.data.repository.ApheCustomRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public interface OAuthIntegrationRepository extends JpaRepository<OAuthIntegration, Long>, ApheCustomRepository<OAuthIntegration> {

	public List<OAuthIntegration> findByDomainId(String domainId);

	public List<OAuthIntegration> findByIdIn(List<Long> idss);

	public List<OAuthIntegration> findByDomainIdAndPartner(String domainId, OAuthIntegrationPartner partner);
	
	@Query("select a from OAuthIntegration a where a.refreshTokenExpiryDate <= :expiryDate and a.connected=1 order by a.domainId asc")
	public List<OAuthIntegration> findAllWithRefreshTokenDateBefore(@Param("expiryDate") Date expiryDate);

}
