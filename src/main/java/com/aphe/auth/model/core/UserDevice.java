package com.aphe.auth.model.core;

import com.aphe.common.model.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "userdevices")
public class UserDevice extends BaseEntity {

    @ManyToOne
    private User user;

    @Column(nullable = false)
    private String fingerprint;

    @Column(nullable = false)
    private String name;

    private String type;

    @Column(nullable = false)
    private boolean isTrusted;

    @Column(nullable = false)
    private Date firstUsed;

    @Column(nullable = false)
    private Date lastUsed;

    @Column(nullable = false)
    private Date lastMFASuccess;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getFingerprint() {
        return fingerprint;
    }

    public void setFingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getFirstUsed() {
        return firstUsed;
    }

    public void setFirstUsed(Date firstUsed) {
        this.firstUsed = firstUsed;
    }

    public Date getLastUsed() {
        return lastUsed;
    }

    public void setLastUsed(Date lastUsed) {
        this.lastUsed = lastUsed;
    }

    public boolean isTrusted() {
        return isTrusted;
    }

    public void setTrusted(boolean trusted) {
        isTrusted = trusted;
    }

    public Date getLastMFASuccess() {
        return lastMFASuccess;
    }

    public void setLastMFASuccess(Date lastMFASuccess) {
        this.lastMFASuccess = lastMFASuccess;
    }
}
