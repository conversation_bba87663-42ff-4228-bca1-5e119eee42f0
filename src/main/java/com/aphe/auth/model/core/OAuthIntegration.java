package com.aphe.auth.model.core;

import com.aphe.common.model.BaseEntity;
import com.aphe.common.persistence.AttributeEncryptor;
import org.eclipse.persistence.annotations.Cache;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "oauthintegrations")
@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true)
public class OAuthIntegration extends BaseEntity {

	@Column(name = "DOMAIN_ID")
	private Long domainId;

	@Enumerated(EnumType.STRING)
	@Column(name = "PARTNER")
	private OAuthIntegrationPartner partner;

	// realmId for QBO and QBDT, tenant_id For Xero
	@Column(name = "ACCOUNT_ID")
	@Convert(converter = AttributeEncryptor.class)
	private String accountId;

	@Column(name = "ACCOUNT_NAME")
	private String accountName;

	@Column(name = "ACCESS_TOKEN")
	@Convert(converter = AttributeEncryptor.class)
	private String accessToken;

	@Column(name = "ACCESS_TOKEN_EXPIRESIN")
	private long accessTokenExpiresIn;

	@Column(name = "ACCESS_TOKEN_EXPIRY_DATE")
	private Date accessTokenExpiryDate;

	@Column(name = "REFRESH_TOKEN")
	@Convert(converter = AttributeEncryptor.class)
	private String refreshToken;

	@Column(name = "REFRESH_TOKEN_EXPIRESIN")
	private long refreshTokenExpiresIn;

	@Column(name = "REFRESH_TOKEN_EXPIRY_DATE")
	private Date refreshTokenExpiryDate;

	@Column(name = "CONNECTED_DATE")
	private Date connectedDate;

	@Column(name = "UPDATED_DATE")
	private Date updatedDate;

	@Column(name = "XERO_CONNECTION_ID")
	private String xeroConnectionId;

	@Column(name = "XEROI_D_TOKEN")
	private String xeroIdToken;

	@Column(name = "XERO_TENANT_TYPE")
	private String xeroTenantType;

	@Column(name = "CONNECTED")
	private boolean connected;

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public OAuthIntegrationPartner getPartner() {
		return partner;
	}

	public void setPartner(OAuthIntegrationPartner partner) {
		this.partner = partner;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public long getAccessTokenExpiresIn() {
		return accessTokenExpiresIn;
	}

	public void setAccessTokenExpiresIn(long accessTokenExpiresIn) {
		this.accessTokenExpiresIn = accessTokenExpiresIn;
	}

	public String getRefreshToken() {
		return refreshToken;
	}

	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}

	public long getRefreshTokenExpiresIn() {
		return refreshTokenExpiresIn;
	}

	public void setRefreshTokenExpiresIn(long refreshTokenExpiresIn) {
		this.refreshTokenExpiresIn = refreshTokenExpiresIn;
	}

	public String getXeroIdToken() {
		return xeroIdToken;
	}

	public void setXeroIdToken(String xeroIdToken) {
		this.xeroIdToken = xeroIdToken;
	}

	public String getXeroTenantType() {
		return xeroTenantType;
	}

	public void setXeroTenantType(String xeroTenantType) {
		this.xeroTenantType = xeroTenantType;
	}

	public String getXeroConnectionId() {
		return xeroConnectionId;
	}

	public void setXeroConnectionId(String xeroConnectionId) {
		this.xeroConnectionId = xeroConnectionId;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public Date getConnectedDate() {
		return connectedDate;
	}

	public void setConnectedDate(Date connectedDate) {
		this.connectedDate = connectedDate;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public Date getAccessTokenExpiryDate() {
		return accessTokenExpiryDate;
	}

	public void setAccessTokenExpiryDate(Date accessTokenExpiryDate) {
		this.accessTokenExpiryDate = accessTokenExpiryDate;
	}

	public Date getRefreshTokenExpiryDate() {
		return refreshTokenExpiryDate;
	}

	public void setRefreshTokenExpiryDate(Date refreshTokenExpiryDate) {
		this.refreshTokenExpiryDate = refreshTokenExpiryDate;
	}

	public boolean isConnected() {
		return connected;
	}

	public void setConnected(boolean connected) {
		this.connected = connected;
	}
}
