package com.aphe.auth.model.core;

import com.aphe.auth.model.a2c.AccountantClientRelation;
import com.aphe.auth.service.dto.DomainType;
import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * 
 *         Domain is a loosely defined concept encompassing a certain traits. It could be a Company, A sub division of a company, A person's account etc. Can be one of the two
 *         types. Business, Consumer. Business can be a Firm/Accountant.
 *         
 *         The Auth service is responsible for managing which user has access to which domain. 
 *         To that end, this service should manage accountant/client relationship too.
 * 
 *
 */
@Entity
@Table(name = "domains")
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "DTYPE")
public abstract class Domain extends BaseEntity {

	@Column(name = "DTYPE", nullable = false, insertable = false, updatable = false)
	private String domainType;

	@Column(nullable = false)
	private String name;

	@Column(nullable = false)
	private boolean isActive;

	@Column
	private String firstName;

	@Column
	private String lastName;

	@Column(name = "ISTESTACCOUNT")
	private Boolean isTestAccount;


	@OneToMany(mappedBy = "client")
	private List<AccountantClientRelation> accountantRelations = new ArrayList<>(0);

	public String getDisplayName() {
		String displayName = "";
		if (domainType != DomainType.C.name()) {
			displayName = name;
		} else {
			displayName = (firstName + " " + lastName).trim();
		}
		return displayName;
	}

	public Domain() {
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDomainType() {
		return domainType;
	}

	public void setDomainType(String domainType) {
		this.domainType = domainType;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public List<AccountantClientRelation> getAccountantRelations() {
		return accountantRelations;
	}

	public void setAccountantRelations(List<AccountantClientRelation> accountantRelations) {
		this.accountantRelations = accountantRelations;
	}

	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean active) {
		isActive = active;
	}

	public Boolean getIsTestAccount() {
		return isTestAccount;
	}

	public void setIsTestAccount(Boolean isTestAccount) {
		this.isTestAccount = isTestAccount;
	}

}
