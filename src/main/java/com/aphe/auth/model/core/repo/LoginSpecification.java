package com.aphe.auth.model.core.repo;

import com.aphe.auth.model.core.Login;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

public class LoginSpecification {

    public static Specification<Login> textInAllColumns(String text) {

        if (!text.contains("%")) {
            text = "%" + text + "%";
        }
        final String finalText = text;

        return new Specification<Login>() {
            @Override
            public Predicate toPredicate(Root<Login> root, CriteriaQuery<?> cq, CriteriaBuilder builder) {
//                return builder.or(root.getModel().getDeclaredSingularAttributes().stream().filter(a -> {
//                    if (a.getJavaType().getSimpleName().equalsIgnoreCase("string")) {
//                        return true;
//                    } else {
//                        return false;
//                    }
//                }).map(a -> builder.like(root.get(a.getName()), finalText)).toArray(Predicate[]::new));

                return builder.or(
                        builder.like(root.get("loginName"), finalText),
                        builder.like(root.get("globalId"), finalText)
                );
            }
        };
    }

}



