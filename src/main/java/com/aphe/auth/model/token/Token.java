package com.aphe.auth.model.token;

import java.io.Serializable;
import java.time.Instant;

public class Token implements Serializable {
    private String jwt;
    private Instant expiry;

    public Token() {}

    public Token(String jwt, Instant expiry) {
        this.jwt = jwt;
        this.expiry = expiry;
    }

    public String getJwt() { return jwt; }
    public void setJwt(String jwt) { this.jwt = jwt; }

    public Instant getExpiry() { return expiry; }
    public void setExpiry(Instant expiry) { this.expiry = expiry; }
}