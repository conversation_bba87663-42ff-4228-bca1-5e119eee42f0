package com.aphe.auth.config;

import com.aphe.auth.security.JwtAuthenticationFilter;
import com.aphe.auth.security.JwtAuthorizationFilter;
import com.aphe.auth.security.TokenManager;
import com.aphe.auth.service.mfa.MFAManager;
import com.aphe.common.logging.filter.ApheMDCFilter;
import com.aphe.common.logging.filter.ApheResponseHeadersFilter;
import com.aphe.common.security.ApheCorsFilter;
import com.aphe.common.security.ApheRequestStatsFilter;
import com.aphe.common.security.RecaptchaTokenValidator;
import com.aphe.common.security.jwt.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import java.util.Set;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig {
	
	@Autowired
	LogoutSuccessHandler logoutSuccessHandler;

	@Autowired
	JwtUtil jwtUtil;
	
	@Autowired
	TokenManager tokenManager;
	
	@Autowired
	RecaptchaTokenValidator recaptchaTokenValidator;

	@Autowired
	MFAManager mfaManager;
	
	@Value("${aphe.auth.validRecaptchaToken}")
	private String validRecaptchaToken;

	@Value("${aphe.auth.validUserName}")
	private String validUserName;
	
	@Value("#{'${aphe.auth.allowedHosts}'.split(',')}") 
	private Set<String> allowedHosts;

	@Autowired
	private AuthenticationConfiguration authConfig;
	
//	@Override
//	protected void configure(HttpSecurity http) throws Exception {
//
//		http.csrf().disable();
//		http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
//        http.authorizeRequests()
//        .antMatchers(HttpMethod.OPTIONS).permitAll()
//        .antMatchers("/logs/**").denyAll()
//        .antMatchers("/access/**").permitAll()
//        .antMatchers("/rs/api/accounts/**").permitAll()
//		.antMatchers("/rs/api/cspreport/**").permitAll()
//        .antMatchers("/badbadbadfeed/**").hasAuthority("superadmin")
//        .antMatchers("/**").authenticated()
//        .and()
//        .exceptionHandling().authenticationEntryPoint(new ApheAuthenticationEntryPoint())
//        .and()
//
//
//        .addFilterBefore(new ApheCorsFilter(allowedHosts), ChannelProcessingFilter.class)
//        .addFilterAfter(new ApheMDCFilter(), ApheCorsFilter.class)
//        .addFilterAfter(new ApheResponseHeadersFilter(), ApheMDCFilter.class)
//        .addFilterAfter(new ApheRequestStatsFilter(), ApheResponseHeadersFilter.class)
//
//        .addFilterAt(new JwtAuthenticationFilter(authenticationManager(), jwtUtil, tokenManager, recaptchaTokenValidator, mfaManager, validRecaptchaToken, validUserName), UsernamePasswordAuthenticationFilter.class)
//        .addFilterAfter(new JwtAuthorizationFilter(authenticationManager(), jwtUtil, tokenManager), UsernamePasswordAuthenticationFilter.class);
//
//		http.logout().logoutUrl("/access/logout").invalidateHttpSession(true).logoutSuccessHandler(logoutSuccessHandler);
//
//	}

	@Bean
	public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {

		http.csrf(csrf -> csrf.disable());
		http.sessionManagement(sess -> sess.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
		http.authorizeHttpRequests((authz) -> authz
						.requestMatchers(HttpMethod.OPTIONS).permitAll()
						.requestMatchers("/logs/**").denyAll()
						.requestMatchers("/access/**").permitAll()
						.requestMatchers("/rs/api/accounts/**").permitAll()
						.requestMatchers("/rs/api/cspreport/**").permitAll()
						.requestMatchers("/badbadbadfeed/health/**").permitAll()
						.requestMatchers("/badbadbadfeed/**").hasAuthority("superadmin")
						.requestMatchers("/rs/api/**").authenticated()
						.requestMatchers("/graphql/**").authenticated()
						.requestMatchers("/**").authenticated()
//						.anyRequest().authenticated()
				)
				.addFilterBefore(new ApheCorsFilter(allowedHosts), ChannelProcessingFilter.class)
				.addFilterAfter(new ApheMDCFilter(), ApheCorsFilter.class)
				.addFilterAfter(new ApheResponseHeadersFilter(), ApheMDCFilter.class)
				.addFilterAfter(new ApheRequestStatsFilter(), ApheResponseHeadersFilter.class)

				.addFilterAt(new JwtAuthenticationFilter(authenticationManager(authConfig), jwtUtil, tokenManager, recaptchaTokenValidator, mfaManager, validRecaptchaToken, validUserName), UsernamePasswordAuthenticationFilter.class)
				.addFilterAfter(new JwtAuthorizationFilter(authenticationManager(authConfig), jwtUtil, tokenManager), UsernamePasswordAuthenticationFilter.class);

		http.logout(
				logout -> logout.logoutUrl("/access/logout")
						.invalidateHttpSession(true)
						.logoutSuccessHandler(logoutSuccessHandler)
		);

		return http.build();
	}

	@Bean
	public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
		return authenticationConfiguration.getAuthenticationManager();
	}

	@Bean
	public BCryptPasswordEncoder passwordEncoder() {
		BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
		return bCryptPasswordEncoder;
	}

//	@Bean
//	public UserDetailsService userDetailsService() {
//		// Implementation for retrieving user details (e.g., from a database)
//		return userDetailsService;
//	}

//	@Override
//	public void configure(AuthenticationManagerBuilder auth) throws Exception {
//		auth.userDetailsService(userDetailsService).passwordEncoder(passwordEncoder());
//	}

}