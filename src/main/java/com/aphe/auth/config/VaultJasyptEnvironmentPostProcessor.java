package com.aphe.auth.config;

import com.aphe.common.vault.JasyptLoader;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
public class VaultJasyptEnvironmentPostProcessor implements EnvironmentPostProcessor, Ordered {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        JasyptLoader.loadJasypt("auth", environment);
    }

    @Override
    public int getOrder() {
        // Run early, but after config file processing
        return Ordered.HIGHEST_PRECEDENCE + 10;
    }
}
