package com.aphe.auth.security;

import java.io.IOException;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class JwtAuthenticationSuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

	private RequestCache requestCache = new HttpSessionRequestCache();

	@Override
	public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {

		if ("application/json".equals(request.getHeader("Content-Type"))) {
			//FOR JSON requests, write JSON response.
			//			response.getWriter().print("{\"responseCode\":\"SUCCESS\"}");
			//			response.getWriter().flush();
			//			clearAuthenticationAttributes(request);
		} else {
			//Handle traditional way 
			//super.onAuthenticationSuccess(request, response, authentication);

			//TODO : why are we doing this?
			SavedRequest savedRequest = requestCache.getRequest(request, response);
			if (savedRequest == null) {
				clearAuthenticationAttributes(request);
				return;
			}

			//TODO : why are we doing this?
			String targetUrlParam = getTargetUrlParameter();
			if (isAlwaysUseDefaultTargetUrl() || (targetUrlParam != null && StringUtils.hasText(request.getParameter(targetUrlParam)))) {
				requestCache.removeRequest(request, response);
				clearAuthenticationAttributes(request);
				return;
			}

			//TODO : why are we doing this?
			clearAuthenticationAttributes(request);
		}
	}

	public void setRequestCache(RequestCache requestCache) {
		this.requestCache = requestCache;
	}

}
