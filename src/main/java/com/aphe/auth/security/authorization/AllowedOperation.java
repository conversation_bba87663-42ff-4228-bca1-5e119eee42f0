package com.aphe.auth.security.authorization;

import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class AllowedOperation {

    private TargetObjectType targetObjectType;
    private Permission permission;

    private List<GlobalRole> allowedGlobalRoles;
    private List<LocalRole> allowedLocalRoles;
    private List<String> allowedRoles;

    public AllowedOperation() {
    }

    public AllowedOperation(TargetObjectType type, Permission per, GlobalRole globalRole, LocalRole... localRoles) {
        this.targetObjectType = type;
        this.permission = per;
        this.allowedGlobalRoles = Arrays.asList(globalRole);
        this.allowedLocalRoles = Arrays.asList(localRoles);
        this.allowedRoles = buildAllowedRoles(allowedGlobalRoles, allowedLocalRoles);
    }
    public AllowedOperation(TargetObjectType type, Permission per, LocalRole... localRoles) {
        this.targetObjectType = type;
        this.permission = per;
        this.allowedGlobalRoles = Arrays.asList();
        this.allowedLocalRoles = Arrays.asList(localRoles);
        this.allowedRoles = buildAllowedRoles(allowedGlobalRoles, allowedLocalRoles);
    }

    public AllowedOperation(TargetObjectType type, Permission per, List<GlobalRole> globalRoles, List<LocalRole> localRoles) {
        this.targetObjectType = type;
        this.permission = per;
        this.allowedGlobalRoles = globalRoles;
        this.allowedLocalRoles = localRoles;
        this.allowedRoles = buildAllowedRoles(allowedGlobalRoles, allowedLocalRoles);
    }

    private List<String> buildAllowedRoles(List<GlobalRole> allowedGlobalRoles, List<LocalRole> allowedLocalRoles) {
        List<String> roleStrings = new ArrayList<>();
        for(GlobalRole globalRole : allowedGlobalRoles){
            roleStrings.add(globalRole.name());
        }
        for(LocalRole localRole : allowedLocalRoles){
            roleStrings.add(localRole.name());
        }
        return roleStrings;
    }


    public TargetObjectType getTargetObjectType() {
        return targetObjectType;
    }

    public void setTargetObjectType(TargetObjectType targetObjectType) {
        this.targetObjectType = targetObjectType;
    }

    public Permission getPermission() {
        return permission;
    }

    public void setPermission(Permission permission) {
        this.permission = permission;
    }

    public List<GlobalRole> getAllowedGlobalRoles() {
        return allowedGlobalRoles;
    }

    public void setAllowedGlobalRoles(List<GlobalRole> allowedGlobalRoles) {
        this.allowedGlobalRoles = allowedGlobalRoles;
    }

    public List<LocalRole> getAllowedLocalRoles() {
        return allowedLocalRoles;
    }

    public void setAllowedLocalRoles(List<LocalRole> allowedLocalRoles) {
        this.allowedLocalRoles = allowedLocalRoles;
    }

    public List<String> getAllowedRoles() {
        return allowedRoles;
    }

    public void setAllowedRoles(List<String> allowedRoles) {
        this.allowedRoles = allowedRoles;
    }
}
