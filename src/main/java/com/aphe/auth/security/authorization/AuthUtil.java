package com.aphe.auth.security.authorization;

import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.security.ApheUserDetails;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AuthUtil {

    //TODO: Move most of the get currentDomain etc methods over here??

    public boolean isOwnerOrAdmin(Authentication auth) {
        return isOwner(auth) || isAdmin(auth);
    }

    public boolean isAdmin(Authentication auth) {
        return hasRole(auth, GlobalRole.ROLE_ADMIN.name());
    }

    public boolean isOwner(Authentication auth) {
        return hasRole(auth, GlobalRole.ROLE_OWNER.name());
    }

    public boolean isSuperAdmin(Authentication auth) {
        return hasRole(auth, "superadmin");
    }

    public boolean hasRole(Authentication auth, String roleName) {
        if (auth != null && auth.isAuthenticated()) {
            ApheUserDetails principal = (ApheUserDetails) auth.getPrincipal();
            for (GrantedAuthority authority : principal.getAuthorities()) {
                if (authority.getAuthority().equalsIgnoreCase(roleName)) {
                    return true;
                }
            }
        }
        return false;
    }


    public boolean hasRole(DomainAccess domainAccess, List<GlobalRole> globalRoleList, List<LocalRole> localRoleList) {
        if (domainAccess != null && domainAccess.isActive()) {

            if (domainAccess.getGlobalRole() == GlobalRole.ROLE_OWNER || domainAccess.getGlobalRole() == GlobalRole.ROLE_ADMIN) {
                return true;
            }

            if (globalRoleList.contains(domainAccess.getGlobalRole())) {
                return true;
            }

            for (LocalRole localRole : localRoleList) {
                if (domainAccess.getLocalRoles().contains(localRole)) {
                    return true;
                }
            }

        }
        return false;
    }

}
