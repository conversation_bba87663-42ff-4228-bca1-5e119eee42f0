package com.aphe.auth.security.authorization;

import com.aphe.auth.model.a2c.AccountantClientRelation;
import com.aphe.auth.model.core.Domain;
import com.aphe.auth.model.core.OAuthIntegration;
import com.aphe.auth.model.core.User;
import com.aphe.auth.model.core.repo.DomainRepository;
import com.aphe.auth.model.core.repo.OAuthIntegrationRepository;
import com.aphe.auth.model.core.repo.UserRepository;
import com.aphe.auth.model.d2d.D2DInvitation;
import com.aphe.auth.model.d2d.D2DRelation;
import com.aphe.auth.model.d2d.repo.D2DInvitationRepository;
import com.aphe.auth.model.d2d.repo.D2DRelationRepository;
import com.aphe.auth.model.u2d.*;
import com.aphe.auth.model.u2d.repo.ClientAccessRepository;
import com.aphe.auth.model.u2d.repo.DomainAccessRepository;
import com.aphe.auth.model.u2d.repo.InvitationRepository;
import com.aphe.common.auth.GlobalRole;
import com.aphe.common.auth.LocalRole;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.util.ArrayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;

@Component
public class AphePermissionEvaluator implements PermissionEvaluator {

    private static final Logger logger = LoggerFactory.getLogger(AphePermissionEvaluator.class);

    private static List<AllowedOperation> allowedOperations = new ArrayList<>();

    static {

        allowedOperations.add(new AllowedOperation(TargetObjectType.USER, Permission.READ, LocalRole.ROLE_TEAM_MEMBER, LocalRole.ROLE_TEAM_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.USER, Permission.UPDATE, GlobalRole.ROLE_USER));

        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAIN, Permission.READ, GlobalRole.ROLE_USER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAIN, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));

        allowedOperations.add(new AllowedOperation(TargetObjectType.INVITATION, Permission.CREATE, LocalRole.ROLE_TEAM_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.INVITATION, Permission.READ, LocalRole.ROLE_TEAM_MEMBER, LocalRole.ROLE_TEAM_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.INVITATION, Permission.UPDATE, LocalRole.ROLE_TEAM_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.INVITATION, Permission.DELETE, LocalRole.ROLE_TEAM_MEMBER));

        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAINACCESS, Permission.CREATE, LocalRole.ROLE_TEAM_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAINACCESS, Permission.READ, LocalRole.ROLE_TEAM_MEMBER, LocalRole.ROLE_TEAM_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAINACCESS, Permission.UPDATE, LocalRole.ROLE_TEAM_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.DOMAINACCESS, Permission.DELETE, LocalRole.ROLE_TEAM_MEMBER));

        allowedOperations.add(new AllowedOperation(TargetObjectType.CLIENT, Permission.CREATE, LocalRole.ROLE_CLIENT_MANAGER, LocalRole.ROLE_CLIENT_REP));
        //TODO: Read of clients in the context of client access only....
        allowedOperations.add(new AllowedOperation(TargetObjectType.CLIENT, Permission.READ, LocalRole.ROLE_CLIENT_MANAGER, LocalRole.ROLE_CLIENT_REP));

        allowedOperations.add(new AllowedOperation(TargetObjectType.CLIENTACCESS, Permission.CREATE, LocalRole.ROLE_CLIENT_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.CLIENTACCESS, Permission.READ, LocalRole.ROLE_CLIENT_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.CLIENTACCESS, Permission.UPDATE, LocalRole.ROLE_CLIENT_MANAGER));
        allowedOperations.add(new AllowedOperation(TargetObjectType.CLIENTACCESS, Permission.DELETE, LocalRole.ROLE_CLIENT_MANAGER));

        //TODO: this should be more generic role. Not just 1099 prepare.. or it could be prepare and someother things too.
        allowedOperations.add(new AllowedOperation(TargetObjectType.INTEGRATION, Permission.CREATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.INTEGRATION, Permission.READ, LocalRole.ROLE_1099_PREPARE, LocalRole.ROLE_1099_SUBMIT));
        allowedOperations.add(new AllowedOperation(TargetObjectType.INTEGRATION, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.INTEGRATION, Permission.DELETE, LocalRole.ROLE_1099_PREPARE));


        //TODO: for now we are treating all D2D Invitations as of smae type. As we expand them to EmployeeEmployee relation,
        // or VendorCustomer relation, we need to introduce new customer centric entities and model access rules around them.
        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DINVITATION, Permission.CREATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DINVITATION, Permission.READ, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DINVITATION, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DINVITATION, Permission.DELETE, LocalRole.ROLE_1099_PREPARE));

        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DRELATION, Permission.CREATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DRELATION, Permission.READ, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DRELATION, Permission.UPDATE, LocalRole.ROLE_1099_PREPARE));
        allowedOperations.add(new AllowedOperation(TargetObjectType.D2DRELATION, Permission.DELETE, LocalRole.ROLE_1099_PREPARE));

    }

    @Autowired
    protected UserRepository userRepository;

    @Autowired
    protected DomainRepository domainRepository;

    @Autowired
    protected InvitationRepository invitationRepository;

    @Autowired
    protected OAuthIntegrationRepository oAuthIntegrationRepository;

    @Autowired
    protected D2DInvitationRepository d2DInvitationRepository;

    @Autowired
    protected D2DRelationRepository d2DRelationRepository;

    @Autowired
    protected AuthUtil authUtil;

    @Autowired
    protected DomainAccessRepository domainAccessRepository;

    @Autowired
    protected ClientAccessRepository clientAccessRepository;

    // targetId and targetType reach to a context. A user context or domain context. Now in this context, does this user has permission to perform
    // the required action.

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {

        ApheUserDetails principal = (ApheUserDetails) authentication.getPrincipal();

        logger.info("Checking access of user_id={} on domain_id={} with parent_domain_id={} for entity_type={} and entity_id={} for permission={}",
                principal.getUserId(), principal.getDomainId(), principal.getParentDomainId(), targetType, targetId, permission);

        TargetObjectType context = TargetObjectType.DOMAIN;
        boolean hasPermission = false;
        String reason = "";
        String targetDomain = "";
        AllowedOperation theOperation = null;
        List<String> requiredRoles = new ArrayList<>();

        //return true all the time for superadmin
        if (authUtil.isSuperAdmin(authentication)) {
            hasPermission = true;
            reason = "SuperAdmin";
        } else {
            try {
                if (targetId instanceof Long) {
                    targetId = Long.toString((Long) targetId);
                }

                //Figure out the entity type and operation we are trying to do.
                TargetObjectType targetObject = TargetObjectType.valueOf(targetType);

                if (permission != null) {
                    theOperation = findOperation((String)permission);
                    if (theOperation != null) {
                        if (targetId instanceof List) {
                            switch (targetObject) {
                                case USER: {
                                    targetDomain = getTargetDomainForUsers((List<String>) targetId, principal);
                                    break;
                                }
                            }
                        } else if (targetId instanceof String) {
                            switch (targetObject) {
                                case USER: {
                                    context = TargetObjectType.USER;
                                    targetDomain = (String) targetId;
                                    break;
                                }
                                case DOMAIN: {
                                    targetDomain = (String) targetId;
                                    break;
                                }
                                case INVITATION: {
                                    targetDomain = getTargetDomainForInvitation((String) targetId);
                                    break;
                                }
                                case DOMAINACCESS: {
                                    targetDomain = getTargetDomainForDomainAccess((String) targetId);
                                    break;
                                }
                                case CLIENT: {
                                    targetDomain = getTargetDomainForClients(Arrays.asList((String) targetId), principal);
                                    break;
                                }
                                case CLIENTACCESS: {
                                    targetDomain = getTargetDomainForClientAccess((String) targetId);
                                    break;
                                }
                                case INTEGRATION: {
                                    targetDomain = getTargetDomainForOAuthIntegration((String) targetId);
                                    break;
                                }
                                case D2DINVITATION: {
                                    targetDomain = getTargetDomainForD2DInvitation((String) targetId);
                                    break;
                                }
                                case D2DRELATION: {
                                    targetDomain = getTargetDomainForD2DRelation((String) targetId);
                                    break;
                                }
                            }
                        }


                        //if user context..
                        if(context == TargetObjectType.USER) {
                            //A user has permission to update thier info. (Update roles requires a diff entity)
                            if (targetDomain.equalsIgnoreCase(Long.toString(principal.getUserId()))) {
                                reason = "SelfUpdate";
                                hasPermission = true;

                            }
                        } else if(context == TargetObjectType.DOMAIN) {
                            if (targetDomain.equalsIgnoreCase(Long.toString(principal.getDomainId()))) {
                                if (authUtil.isOwnerOrAdmin(authentication)) {
                                    reason = "OwnerOrAdmin";
                                    hasPermission = true;
                                } else {
                                    for (String requiredRole : theOperation.getAllowedRoles()) {
                                        if (authUtil.hasRole(authentication, requiredRole)) {
                                            reason = "HasRequiredRole";
                                            hasPermission = true;
                                            break;
                                        }
                                    }
                                    if(!hasPermission) {
                                        reason = "HasNoRequiredRole";
                                    }
                                }
                            } else {
                                reason = "InvalidTargetDomain";
                            }
                        } else {
                            reason = "HasNoContext";
                        }

                            //TODO: Not sure we should allow things beyond current domain.. We added this because, get domain DTO get domain by domainId,
                            // which is outside of the current domain. Just because we have access domainAccessRepo, we shouldn't be using it.
//                            List<DomainAccess> domainAccessList = domainAccessRepository.findByUserId(principal.getUserId());
//                            DomainAccess theDomainAccess = null;
//                            for(DomainAccess da : domainAccessList) {
//                                if(da.isActive() && targetDomain.equalsIgnoreCase(Long.toString(da.getDomain().getId()))) {
//                                    theDomainAccess = da;
//                                    break;
//                                }
//                            }
//                            if(theDomainAccess != null) {
//                                if(authUtil.hasRole(theDomainAccess, theOperation.getAllowedGlobalRoles(), theOperation.getAllowedLocalRoles())){
//                                    hasPermission = true;
//                                    reason = "HasRequiredRole";
//                                } else{
//                                    reason = "HasNoRequiredRole";
//                                }
//                            }else {
//                                reason = "NoRoleInTargetDomain";
//                            }

                    } else {
                        reason = "NoAccessRulesFound";
                    }
                } else {
                    reason = "UnknownPermissionOrEntity";
                }
            } catch (Exception e) {
                logger.error("Error evaluating permissions", e);
                reason = e.getClass().getName();
            }
        }
        if(!hasPermission) {
            logger.info("Access check result={}, reason={} have_roles={} target_domain={} required_roles={}", (hasPermission ? "Granted" : "Denied"), reason, principal.getAuthorities(), targetDomain, (theOperation != null ? theOperation.getAllowedRoles() : ""));
        }
        return hasPermission;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        //For now do not support this type of permission evaluation...
        logger.error("Error evaluating permissions. role=UnsupportedEvaluation");
        return false;
    }

    private AllowedOperation findOperation(String permission) {
        String[] parts = permission.split("_");
        Permission permissionNeeded = Permission.valueOf(parts[0]);
        TargetObjectType targetObject = TargetObjectType.valueOf(parts[1]);

        AllowedOperation theOperation = null;
        for (AllowedOperation allowedOperation : allowedOperations) {
            if (allowedOperation.getTargetObjectType() == targetObject && allowedOperation.getPermission() == permissionNeeded) {
                theOperation = allowedOperation;
                break;
            }
        }
        return theOperation;
    }

    private String getTargetDomainForOAuthIntegration(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        OAuthIntegration oAuthInt = oAuthIntegrationRepository.findById(longTargetId).orElse(null);
        if (oAuthInt != null) {
            targetDomain = oAuthInt.getDomainId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForInvitation(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        Invitation invitation = invitationRepository.findById(longTargetId).orElse(null);
        if (invitation != null) {
            targetDomain = invitation.getDomainId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForDomainAccess(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        DomainAccess domainAccess = domainAccessRepository.findById(longTargetId).orElse(null);
        if (domainAccess != null) {
            targetDomain = domainAccess.getDomain().getId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForClientAccess(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        ClientAccess clientAccess = clientAccessRepository.findById(longTargetId).orElse(null);
        if (clientAccess != null) {
            targetDomain = clientAccess.getDomainAccess().getDomain().getId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForD2DInvitation(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        D2DInvitation entity = d2DInvitationRepository.findById(longTargetId).orElse(null);
        if (entity != null) {
            targetDomain = entity.getDomainId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForD2DRelation(String targetId) {
        String targetDomain = "-1";
        long longTargetId = Long.parseLong(targetId);
        D2DRelation entity = d2DRelationRepository.findById(longTargetId).orElse(null);
        if (entity != null) {
            targetDomain = entity.getSourceDomainId().toString();
        }
        return targetDomain;
    }

    private String getTargetDomainForClients(List<String> clientIds, ApheUserDetails principal) {
        //Client is nothing but a domain.
        //Each of this client should have the loggedInDomain as the accountant.
        //LoggedInUser has direct access to the client. Because of explicit client access
        // or loggedInUser is a client manager.

        final long loggedInDomainId = principal.getDomainId();
        final long loggedInUserId = principal.getUserId();
        String targetDomain = "-1";

        List<Long> listTargetIds = ArrayUtil.stringListToLongList(clientIds);
        Set<String> domainIds = new HashSet<>();

        List<Domain> domains = domainRepository.findByIdIn(listTargetIds);
        for (Domain d : domains) {
            if(d != null) {
                boolean hasAccountantAccess = false;
                List<AccountantClientRelation> acctClientRelations = d.getAccountantRelations();
                for(AccountantClientRelation relation : acctClientRelations) {
                    if(relation.getAccountant().getId() == loggedInDomainId) {
                        hasAccountantAccess = true;
                        break;
                    }
                }

                if(hasAccountantAccess) {
                    domainIds.add(Long.toString(loggedInDomainId));
                } else {
                    domainIds.add(Long.toString(-1L));
                }
            }
        }
        //if only 1 domain.. use that as the target domain to check down stream..
        if (domainIds.size() == 1) {
            targetDomain = domainIds.iterator().next();
        }


        return targetDomain;
    }

    // Operation on a list of users, has to be in the context of domain.
    // If all users belong to a domian return that, otherwise return no domain.
    private String getTargetDomainForUsers(List<String> userIds, ApheUserDetails principal) {
        //This userId should have a role in the logged in domain.
        //The userId is same as loggedInUser, or loggedInUser is team manager.
        final long loggedInDomainId = principal.getDomainId();
        final long loggedInUserId = principal.getUserId();
        String targetDomain = "0";

        List<Long> listTargetIds = ArrayUtil.stringListToLongList(userIds);
        List<User> users = userRepository.findByIdIn(listTargetIds);
        Set<String> domainIds = new HashSet<>();
        for (User u : users) {

            if(u != null) {
                boolean hasRoleInDomain = false;
                for(DomainAccess role : u.getDomainAccess()) {
                    Long id = role.getDomain().getId();
                    if(id == loggedInDomainId) {
                        hasRoleInDomain = true;
                        break;
                    }
                }
                if(hasRoleInDomain) {
                    domainIds.add(Long.toString(loggedInDomainId));
                } else {
                    domainIds.add(Long.toString(0L));
                }
            }
        }
        //if only 1 domain.. use that as the target domain to check down stream..
        if (domainIds.size() == 1) {
            targetDomain = domainIds.iterator().next();
        }
        return targetDomain;
    }




    /**
     * What is the best (performant, easy to read, easy to scale, decoupled from domain) way to implement Roles and Permissions
     *
     * A Graph is made of domain entity as the root node and everything hangs of it.
     *
     * Root node has one child entity of certain type -- You specify what roles can have what permissions on it.
     * check by rootNodeId
     * check by childNodeId
     * find the parent and check if you have that role.
     *
     * Root node has a child node and the child node has sub child node.
     * check by rootNodeId
     * check by childNodeId
     * check by subChildNodeId
     * find the parent and check if you have that role.
     *
     *
     * Root node has children and there is a notion of you can do only have access to certain nodes. (Almost admin access on those nodes and child nodes.)
     *  Another use case is you can do certain things on certain nodes... (it needs a full blown access control system), but as long as we delegate it another system, we don't need to care.
     *
     * Check only by childNodeId or childNodeId list.
     * find the parent node and make sure if this belongs to this root node.
     * using the next access control list, see if the user has full access to it.
     * if the next access control is a full blown roles and permission, then we have to run it through full system.
     *
     *
     * Root node has children and is fully owned by the parent node.
     * Check childNodeId or childNodeId list.
     * Check by rootNodeId
     * Just check if all the children to single parent and if the user has role on the parent node.
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     */

}
