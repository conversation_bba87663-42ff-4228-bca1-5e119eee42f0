package com.aphe.auth.security.authorization;

public enum TargetObjectType {
    USER, //Update user's first name and last name, email etc. Only allowed by the user and nobody else.
    DOMAIN, // Any body can create a domain, other aspects are controlled by various roles.
    INVITATION, //Vehicles to link users to domains.
    DOMAINACCESS, // Team member can create and update things.
    CLIENT, // CLIENT_REP can create a client.. gets admin access as part of that creation.. client manager can read,upddate,activate or inactivate clients
    CLIENTACCESS, // Only client manager can create new ones that are not part of initial client creation
    INTEGRATION, //
    D2DINVITATION, // 1099 prepares
    D2DRELATION, // 1099 prepares
   ;
}
