package com.aphe.auth.security;

import com.aphe.auth.service.mfa.MFAManager;
import com.aphe.common.security.ApheAuthenticationFilter;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.RecaptchaTokenValidator;
import com.aphe.common.security.jwt.InvalidRecaptchaToken;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.util.RequestUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;

import java.io.IOException;

public class JwtAuthenticationFilter extends ApheAuthenticationFilter {

	public static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

	JwtUtil jwtUtil;
	TokenManager tokenManager;
	RecaptchaTokenValidator recaptchaTokenValidator;
	String validRecaptchaToken;
	String validUserName;
	RequestUtil requestUtil;
	MFAManager mfaManager;

	public JwtAuthenticationFilter(AuthenticationManager authenticationManager, JwtUtil jwtUtil, TokenManager tokenManager, RecaptchaTokenValidator recaptchaTokenValidator, MFAManager mfaManager,
			String validRecaptchaToken, String validUserName) {
		super(authenticationManager);
		this.jwtUtil = jwtUtil;
		this.tokenManager = tokenManager;
		this.recaptchaTokenValidator = recaptchaTokenValidator;
		this.mfaManager = mfaManager;
		this.validRecaptchaToken = validRecaptchaToken;
		this.validUserName = validUserName;
		setFilterProcessesUrl("/access/login");
		setAuthenticationFailureHandler(new JwtAuthenticationFailureHandler());
		setAuthenticationSuccessHandler(new JwtAuthenticationSuccessHandler());
		requestUtil = new RequestUtil();
	}

	@Override
	protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse res, FilterChain chain, Authentication auth) throws IOException, ServletException {

		ApheUserDetails user = (ApheUserDetails) auth.getPrincipal();

		boolean loginOnly = new Boolean(request.getParameter("loginOnly"));
		if(loginOnly) {
			user.setDomainType("");
			user.setDomainId(0);
			user.setParentDomainId(0);
		}

//		/**
//		 * To not allow login on sandbox accounts (Except for sanity testing accounts), even if credentials passed are right, do not generate a token
//		 * and set them in cookies or headers, unless the user is a super admin or user is sanity test user.
//		 *
//		 */
//		if (PropertiesManager.isSandbox() && !user.isSuperAdmin()) {
//			if (!user.getUsername().contains(validUserName)) {
//				return;
//			}
//		}

		/**
		 * Verify recaptcha right users and environments.
		 */
		verifyRecaptchaToken(request, user);

		//Get device fingerprint from the request
		String deviceFingerprint = request.getParameter("deviceFingerprint");
		String deviceName = request.getParameter("deviceName");
		//TODO: See if this one of the trusted devices??
		boolean requireMFA = mfaManager.requiresMFA(user, deviceFingerprint, deviceName);

		if(!loginOnly && requireMFA) {
			user.setMfaRequired(true);
			user.setMfaSuccess(false);
		} else {
			user.setMfaRequired(false);
			user.setMfaSuccess(false);
		}

		String token;
		try {
			token = jwtUtil.generateToken(user);
			tokenManager.addToken(token);
			jwtUtil.setToken(request, res, token);
		} catch (Exception e) {
			throw new ServletException(e.getMessage());
		}

		getSuccessHandler().onAuthenticationSuccess(request, res, auth);

	}

	private void verifyRecaptchaToken(HttpServletRequest request, ApheUserDetails user) {
		boolean validRecaptcha = false;
		String recaptchaToken = null;
		recaptchaToken = request.getParameter("recaptchaToken");

		if (user.isSuperAdmin()) {
			validRecaptcha = true;
		} else {
			String ipAddress = requestUtil.getIPAddress(request);
			validRecaptcha = recaptchaTokenValidator.verifyRecaptchaToken(recaptchaToken, ipAddress);
		}

		if (!validRecaptcha) {
			throw new InvalidRecaptchaToken("Invalid ReCaptcha Key");
		}
	}

}