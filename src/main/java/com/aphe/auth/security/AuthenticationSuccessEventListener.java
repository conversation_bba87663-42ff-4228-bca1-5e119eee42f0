package com.aphe.auth.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.stereotype.Component;

import com.aphe.common.security.ApheUserDetails;

@Component
public class AuthenticationSuccessEventListener implements ApplicationListener<AuthenticationSuccessEvent> {

	public static final Logger logger = LoggerFactory.getLogger(AuthenticationSuccessEventListener.class);

	@Autowired
	FailedAttemptsManager failedAttemptsManager;

	public void onApplicationEvent(AuthenticationSuccessEvent e) {

		UsernamePasswordAuthenticationToken authToken = (UsernamePasswordAuthenticationToken) e.getSource();
		ApheUserDetails principal = (ApheUserDetails) authToken.getPrincipal();
		if(principal == null || !principal.isEmailConfirmed()) {
			throw new BadCredentialsException("Email not verified");	
		}
		
		WebAuthenticationDetails auth = (WebAuthenticationDetails) e.getAuthentication().getDetails();
		String remoteIp = "unknown";
		if (auth != null) {
			remoteIp = auth.getRemoteAddress();
		}

		failedAttemptsManager.clearFailedRecord(e.getAuthentication().getName(), remoteIp);
	}
}