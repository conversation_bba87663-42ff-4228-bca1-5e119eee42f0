package com.aphe.auth.security;

import com.aphe.auth.model.token.Token;
import com.aphe.common.security.ApheUserDetails;
import com.aphe.common.security.jwt.JwtUtil;
import com.aphe.common.security.jwt.TokenInfo;
import com.aphe.common.tasks.GenericBackgroundTask;
import org.jobrunr.jobs.annotations.Recurring;
import org.jobrunr.scheduling.BackgroundJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TokenManager extends GenericBackgroundTask {

    Logger logger = LoggerFactory.getLogger(TokenManager.class);

    @Autowired
    private JwtUtil jwtUtil;

    private static final String TOKEN_KEY_PREFIX = "token:";
    private static final String INDEX_KEY = "token:index";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${spring.jwt.expirationMinutes}")
    private int expirationMinutes;

    @Transactional
    public boolean validateToken(String jwt) {
        String tokenHash = jwtUtil.sha256(jwt);
        String redisKey = TOKEN_KEY_PREFIX + tokenHash;

        // Check if token exists in Redis
        Object tokenString = redisTemplate.opsForValue().get(redisKey);
        if (tokenString == null) {
            redisTemplate.opsForSet().remove(INDEX_KEY, tokenHash);
            return false;
        }

        //Get ttl
        long ttl = redisTemplate.getExpire(redisKey);
        Instant expiry = Instant.now().plusSeconds(ttl);
        if (expiry.isBefore(Instant.now())) {
            // Token has expired, remove it
            redisTemplate.delete(redisKey);
            redisTemplate.opsForSet().remove(INDEX_KEY, tokenHash);
            return false;
        }

        // Update the TTL
        redisTemplate.opsForValue().set(redisKey, jwt, Duration.ofMinutes(expirationMinutes));

        return true;
    }

    public void addToken(String token) {
        String tokenHash = jwtUtil.sha256(token);
        String redisKey = TOKEN_KEY_PREFIX + tokenHash;
        redisTemplate.opsForValue().set(redisKey, token, Duration.ofMinutes(expirationMinutes));
        redisTemplate.opsForSet().add(INDEX_KEY, tokenHash);
    }

    @Transactional
    public void removeToken(String token) {
        if (token == null || token.isEmpty()) {
            return; // No token to remove
        }
        String tokenHash = jwtUtil.sha256(token);
        redisTemplate.delete(TOKEN_KEY_PREFIX + tokenHash);
        redisTemplate.opsForSet().remove(INDEX_KEY, tokenHash);
    }


    public List<TokenInfo> getLoggedInUsers(int page, int size) {
        Set<Object> allHashes = redisTemplate.opsForSet().members(INDEX_KEY);

        if (allHashes == null) return List.of();

        List<String> sortedHashes = allHashes.stream().map(Object::toString).sorted().collect(Collectors.toList());

        int from = page * size;
        int to = Math.min(from + size, sortedHashes.size());
        if (from >= to) return List.of();

        List<Token> tokens = new ArrayList<>();
        List<String> currentPage = sortedHashes.subList(from, to);

        return currentPage.stream()
                .map(hash -> {
                    String redisKey = TOKEN_KEY_PREFIX + hash;
                    // Check if token exists in Redis
                    Object tokenString = redisTemplate.opsForValue().get(redisKey);
                    long ttl = redisTemplate.getExpire(redisKey);
                    Token token = new Token((String) tokenString, Instant.now().plusSeconds(ttl));
                    return token;
                })
                .filter(token -> token != null && token.getExpiry().isAfter(Instant.now()))
                .map((token -> {
                    try {
                        ApheUserDetails userDetails = jwtUtil.parseToken(token.getJwt());
                        if (userDetails.getDomainId() > 0) {
                            Date expiryDate = Date.from(token.getExpiry());
                            // Last access time is token expiry time minus the expiration duration
                            Date lastAccessTime = Date.from(token.getExpiry().minus(Duration.ofMinutes(expirationMinutes)));
                            return new TokenInfo(userDetails, expiryDate, lastAccessTime);
                        }
                    } catch (Exception e) {
                    }
                    return null;
                }))
                .filter(tokenInfo -> tokenInfo != null).collect(Collectors.toList());
    }

    @Recurring(id = "authservice-token-cleanup-job", cron = "0 50 * * * *")
    public void cleanupExpiredTokens() {
        long startTime = System.currentTimeMillis();

        boolean pauseTasks = isPauseTasks("authservice-token-cleanup-job");
        if(pauseTasks) {
            return;
        }
        BackgroundJob.enqueue(() -> {
            cleanupExpiredTokensInternal();
        });
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        logger.info("authservice-token-cleanup-job completed in: " + duration + " milliseconds");
    }

    public void cleanupExpiredTokensInternal() {
        // Get all tokens from the index.
        Set<Object> allHashes = redisTemplate.opsForSet().members(INDEX_KEY);
        // Go through each token hash and check if it has expired.
        if (allHashes == null || allHashes.isEmpty()) {
            logger.info("No tokens to clean up.");
            return;
        }

        allHashes.forEach(hash -> {
            String redisKey = TOKEN_KEY_PREFIX + hash;
            // Check if token exists in Redis
            Object tokenString = redisTemplate.opsForValue().get(redisKey);
            if (tokenString == null) {
                // Token does not exist, remove from index
                redisTemplate.opsForSet().remove(INDEX_KEY, hash);
            }
        });
    }

}
