package com.aphe.auth.security;

import com.aphe.common.security.jwt.ApheAuthContext;
import com.aphe.common.security.jwt.JwtUtil;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

public class JwtAuthorizationFilter extends BasicAuthenticationFilter {

	JwtUtil jwtUtil;

	TokenManager tokenManager;

	public JwtAuthorizationFilter(AuthenticationManager authenticationManager, JwtUtil jwtUtil, TokenManager tokenManager) {
		super(authenticationManager);
		this.jwtUtil = jwtUtil;
		this.tokenManager = tokenManager;
	}

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {

		String token = jwtUtil.getToken(request);

		try {
			if (token == null) {
				return;
			} else {
				UsernamePasswordAuthenticationToken authentication = getAuthentication(token);
				SecurityContextHolder.getContext().setAuthentication(authentication);
			}

		} catch (Exception e) {
			logger.error("Error validating token", e);
		} finally {
			chain.doFilter(request, response);
		}
	}

	protected UsernamePasswordAuthenticationToken getAuthentication(String token) throws Exception {
		if (token != null && tokenManager.validateToken(token)) {
			UserDetails userDetails = jwtUtil.parseToken(token);
			if (userDetails != null) {
				ApheAuthContext.setToken(token);
				return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
			}
			return null;
		}
		return null;
	}

}
