package com.aphe.auth.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.stereotype.Component;

@Component
public class AuthenticationFailureListener implements ApplicationListener<AuthenticationFailureBadCredentialsEvent> {

	public static final Logger logger = LoggerFactory.getLogger(AuthenticationFailureListener.class);

	@Autowired
	FailedAttemptsManager failedAttemptsManager;

	@Override
	public void onApplicationEvent(AuthenticationFailureBadCredentialsEvent e) {
		WebAuthenticationDetails auth = (WebAuthenticationDetails) e.getAuthentication().getDetails();
		String remoteIp = "unknown";
		if (auth != null) {
			remoteIp = auth.getRemoteAddress();
		}

		failedAttemptsManager.recordFailedAttempt(e.getAuthentication().getName(), remoteIp);
	}
}
