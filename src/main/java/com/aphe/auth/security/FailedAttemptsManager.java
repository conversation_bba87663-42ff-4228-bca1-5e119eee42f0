package com.aphe.auth.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

public class FailedAttemptsManager {

	public static final Logger logger = LoggerFactory.getLogger(FailedAttemptsManager.class);
	
	private static final String REDIS_KEY_PREFIX_USER = "failed_attempts:user:";
	private static final String REDIS_KEY_PREFIX_IP = "failed_attempts:ip:";

	private final int MAX_USER_ATTEMPTS;
	private final int MAX_IP_ATTEMPTS;
	private final int RESET_IP_ATTEMPTS;
	private final int DECREMENT_IP_ATTEMPTS;
	private final int USER_LOCKOUT_PERIOD;
	private final int IP_LOCKOUT_PERIOD;

	@Autowired
	private StringRedisTemplate redisTemplate;

	public FailedAttemptsManager(int maxUser, int maxIP, int resetIp, int decrementIp, int userLockout, int ipLockout) {
		this.MAX_USER_ATTEMPTS = maxUser;
		this.MAX_IP_ATTEMPTS = maxIP;
		this.RESET_IP_ATTEMPTS = resetIp;
		this.DECREMENT_IP_ATTEMPTS = decrementIp;
		this.USER_LOCKOUT_PERIOD = userLockout;
		this.IP_LOCKOUT_PERIOD = ipLockout;
	}

	public void recordFailedAttempt(String username, String ip) {
		String userKey = REDIS_KEY_PREFIX_USER + username;
		String ipKey = REDIS_KEY_PREFIX_IP + ip;
		
		redisTemplate.opsForValue().increment(userKey);
		redisTemplate.opsForValue().increment(ipKey);
		
		// Set expiration
		redisTemplate.expire(userKey, USER_LOCKOUT_PERIOD, TimeUnit.MINUTES);
		redisTemplate.expire(ipKey, IP_LOCKOUT_PERIOD, TimeUnit.MINUTES);
	}

	public void clearFailedRecord(String username, String ip) {
		String userKey = REDIS_KEY_PREFIX_USER + username;
		String ipKey = REDIS_KEY_PREFIX_IP + ip;
		
		// Delete user attempts
		redisTemplate.delete(userKey);
		
		// Get current IP attempts
		String ipAttemptsStr = redisTemplate.opsForValue().get(ipKey);
		int ipAttempts = ipAttemptsStr != null ? Integer.parseInt(ipAttemptsStr) : 0;
		
		// Calculate new IP attempts
		if (ipAttempts > RESET_IP_ATTEMPTS) {
			ipAttempts = RESET_IP_ATTEMPTS;
		} else {
			ipAttempts = Math.max(0, ipAttempts - DECREMENT_IP_ATTEMPTS);
		}
		
		// Update IP attempts if not zero
		if (ipAttempts > 0) {
			redisTemplate.opsForValue().set(ipKey, String.valueOf(ipAttempts));
			redisTemplate.expire(ipKey, IP_LOCKOUT_PERIOD, TimeUnit.MINUTES);
		} else {
			redisTemplate.delete(ipKey);
		}
	}

	public boolean isLockedOut(String username, String ip) {
		String userKey = REDIS_KEY_PREFIX_USER + username;
		String ipKey = REDIS_KEY_PREFIX_IP + ip;
		
		String userAttemptsStr = redisTemplate.opsForValue().get(userKey);
		String ipAttemptsStr = redisTemplate.opsForValue().get(ipKey);
		
		int userAttempts = userAttemptsStr != null ? Integer.parseInt(userAttemptsStr) : 0;
		int ipAttempts = ipAttemptsStr != null ? Integer.parseInt(ipAttemptsStr) : 0;
		
		return (userAttempts >= MAX_USER_ATTEMPTS) || (ipAttempts >= MAX_IP_ATTEMPTS);
	}
}
