package com.aphe.auth.security;

import com.aphe.common.security.jwt.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler;
import org.springframework.stereotype.Component;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class ApheLogoutSuccessHandler extends SimpleUrlLogoutSuccessHandler {

	@Autowired
	TokenManager tokenManager;

	@Autowired
	JwtUtil jwtUtil;

	@Override
	public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
		// super.onLogoutSuccess(request, response, authentication);
		response.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
		String token = jwtUtil.getToken(request);
		tokenManager.removeToken(token);
		jwtUtil.eraseToken(request, response);
	}


}