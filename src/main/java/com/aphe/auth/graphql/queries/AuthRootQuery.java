package com.aphe.auth.graphql.queries;

import com.aphe.auth.service.AccountManagerTxn;
import com.aphe.auth.service.AuthMapper;
import com.aphe.auth.service.DomainAccessManager;
import com.aphe.auth.service.UserManager;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.auth.service.dto.SearchResultsDTO;
import com.aphe.auth.service.dto.SessionDTO;
import com.aphe.auth.service.dto.UserDTO;
import com.aphe.common.error.ApheErrorCode;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.security.jwt.TokenInfo;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
@DgsComponent
public class AuthRootQuery {

    private static final Logger logger = LoggerFactory.getLogger(AuthRootQuery.class);

    @Autowired
    protected AccountManagerTxn accountManager;

    @Autowired
    protected UserManager userManager;

    @Autowired
    protected DomainAccessManager domainAccessManager;

    @Autowired
    protected AuthMapper authMapper;

    @DgsQuery
    public boolean flush() throws Exception {
        try {
            accountManager.flush();
            return true;
        } catch (Exception e) {
            String message = "Error getting current session";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsQuery
    public SearchResultsDTO searchUsersAndDomains(String userSearchString, String domainSearchString) throws ApheException {
        if (!accountManager.isSystemUser()) {
            throw new ApheForbiddenException(ApheErrorCode.AUTHZ_1001.name(), "Only authorized users can perform this action");
        }
        Collection<UserDTO> users = userManager.searchUsers(userSearchString);
        List<DomainDTO> domains = accountManager.searchDomains(domainSearchString);
        SearchResultsDTO dto = new SearchResultsDTO();
        dto.users = users;
        dto.domains = domains.stream().map(domainDTO -> authMapper.domainDTOToUserDomainDTO(domainDTO)).collect(Collectors.toList());
        return dto;
    }

    @DgsQuery
    public List<TokenInfo> loggedInUsers() throws ApheException {
        if (!accountManager.isSystemUser()) {
            throw new ApheForbiddenException(ApheErrorCode.AUTHZ_1001.name(), "Only authorized users can perform this action");
        }
        return userManager.getLoggedInUsers();
    }

    @DgsQuery
    public UserDTO user() throws Exception {
        try {
            long userId = userManager.getLoggedInUserId();
            return userManager.getUser(Long.toString(userId));
        } catch (Exception e) {
            String message = "Error getting user";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsQuery
    public SessionDTO session() throws Exception {
        try {
            return accountManager.getCurrentSession();
        } catch (Exception e) {
            String message = "Error getting current session";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsQuery
    public DomainDTO domain() throws Exception {
        try {
            return accountManager.getDomainDTO(accountManager.getCurrentDomainId());
        } catch (Exception e) {
            String message = "Error getting domain";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsQuery
    public DomainDTO accountant() throws Exception {
        try {
            return accountManager.getDomainDTO(accountManager.getCurrentDomainId());
        } catch (Exception e) {
            String message = "Error getting accountant";
            logger.error(message, e);
            throw e;
        }
    }
}