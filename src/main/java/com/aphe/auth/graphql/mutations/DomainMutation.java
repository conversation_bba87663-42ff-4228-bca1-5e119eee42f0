package com.aphe.auth.graphql.mutations;

import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.dto.CreateDomainDTO;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.auth.service.dto.UpdateDomainDTO;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import com.netflix.graphql.dgs.internal.DgsWebMvcRequestData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
@DgsComponent
public class DomainMutation {

    private static final Logger logger = LoggerFactory.getLogger(DomainMutation.class);

    @Autowired
    private AccountManager accountManager;

    @DgsMutation
    public boolean addDomain(@InputArgument("input") CreateDomainDTO createDomainDTO, DgsDataFetchingEnvironment dfe) throws Exception {
        DgsWebMvcRequestData requestData = (DgsWebMvcRequestData) dfe.getDgsContext().getRequestData();
        ServletWebRequest webRequest = (ServletWebRequest) requestData.getWebRequest();
        HttpServletRequest request = webRequest.getRequest();
        HttpServletResponse response = webRequest.getResponse();
        try {
            DomainDTO domainDTO = accountManager.addDomain(createDomainDTO, request, response);
            return true;
        } catch (Exception e) {
            String message = "Error deleting domain access.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean addClient(@InputArgument("input") CreateDomainDTO createDomainDTO, DgsDataFetchingEnvironment dfe) throws Exception {
        DgsWebMvcRequestData requestData = (DgsWebMvcRequestData) dfe.getDgsContext().getRequestData();
        ServletWebRequest webRequest = (ServletWebRequest) requestData.getWebRequest();
        HttpServletRequest request = webRequest.getRequest();
        HttpServletResponse response = webRequest.getResponse();
        try {
            Long parentDomainID = accountManager.getParentDomainId();
            DomainDTO domainDTO = accountManager.addClient(parentDomainID, createDomainDTO, request, response);
            return true;
        } catch (Exception e) {
            String message = "Error deleting domain access.";
            logger.error(message, e);
            throw e;
        }
    }


    @DgsMutation
    public boolean updateDomain(@InputArgument("input") UpdateDomainDTO updateDomainDTO) throws Exception {
        try {
            DomainDTO domainDTO = accountManager.updateDomain(updateDomainDTO);
            return true;
        } catch (Exception e) {
            String message = "Error deleting domain access.";
            logger.error(message, e);
            throw e;
        }
    }
}