package com.aphe.auth.graphql.mutations;

import com.aphe.auth.service.d2d.D2DInvitationManager;
import com.aphe.auth.service.d2d.D2DInvitationManagerTxn;
import com.aphe.auth.service.d2d.dto.CreateD2DInvitationDTO;
import com.aphe.auth.service.d2d.dto.D2DInvitationDTO;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class D2DInvitationMutation{

    private static final Logger logger = LoggerFactory.getLogger(D2DInvitationMutation.class);

    @Autowired
    protected D2DInvitationManager d2DInvitationManager;

    @Autowired
    protected D2DInvitationManagerTxn d2DInvitationManagerTxn;


    @DgsMutation
    public D2DInvitationDTO inviteDomain(@InputArgument("input") CreateD2DInvitationDTO invitation) throws Exception {
        try {
            D2DInvitationDTO dto = d2DInvitationManager.addInvitation(invitation);
            return dto;
        } catch (Exception e) {
            String message = "Error creating a domain invitation.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean resendDomainInvitation(@InputArgument("id") String invitationId) throws Exception {
        try {
            boolean success = d2DInvitationManager.resendDomainInvitation(invitationId);
            return success;
        } catch (Exception e) {
            String message = "Error resending the domain invitation.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean deleteDomainInvitation(@InputArgument("id") String invitationId) throws Exception {
        try {
            d2DInvitationManagerTxn.deleteInvitation(invitationId);
            return true;
        } catch (Exception e) {
            String message = "Error getting D2D Invitations";
            logger.error(message, e);
            throw e;
        }
    }
}