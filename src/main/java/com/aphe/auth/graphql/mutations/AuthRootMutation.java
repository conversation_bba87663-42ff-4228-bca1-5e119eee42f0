package com.aphe.auth.graphql.mutations;

import com.aphe.auth.model.u2d.DomainAccess;
import com.aphe.auth.model.u2d.Invitation;
import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.ApheSecurityManager;
import com.aphe.auth.service.DomainAccessManager;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import com.netflix.graphql.dgs.internal.DgsWebMvcRequestData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
@DgsComponent
public class AuthRootMutation {

    private static final Logger logger = LoggerFactory.getLogger(AuthRootMutation.class);

    @Autowired
    private AccountManager accountManager;

    @Autowired
    private DomainAccessManager domainAccessManager;

    @Autowired
    protected ApheSecurityManager apheSecurityManager;


    @DgsMutation
    public boolean acceptUserInvite(@InputArgument("invitationCode") String invitationCode, DgsDataFetchingEnvironment dfe) throws Exception {
        DgsWebMvcRequestData requestData = (DgsWebMvcRequestData) dfe.getDgsContext().getRequestData();
        ServletWebRequest webRequest = (ServletWebRequest) requestData.getWebRequest();
        HttpServletRequest request = webRequest.getRequest();
        HttpServletResponse response = webRequest.getResponse();

        Invitation inv = domainAccessManager.isValidInvitationCode(invitationCode);
        if (inv != null) {
            DomainAccess da = domainAccessManager.linkUserToInvitation(Long.toString(domainAccessManager.getLoggedInUserId()), invitationCode);
            String theDomainToken = apheSecurityManager.authenticateUserAndSetSessionAndCookies(domainAccessManager.getLoggedInUser(), Long.parseLong(inv.getDomainId()), request, response);
            domainAccessManager.notifyUserAdded(da);
            domainAccessManager.notifyTeamUserAdded(da);
        }
        return true;
    }

    @DgsMutation
    public boolean toggleDomainActiveStatus(@InputArgument("domainId") String domainId, @InputArgument("active") boolean isActive) throws Exception {
        try {
            return accountManager.toggleDomainActiveStatus(Long.parseLong(domainId), isActive);
        } catch (Exception e) {
            String message = "Error toggling domain active status.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean toggleClientActiveStatus(@InputArgument("clientId") String clientId, @InputArgument("active") boolean isActive) throws Exception {
        try {
            long domainId = Long.parseLong(accountManager.getCurrentDoaminId());
            return accountManager.toggleClientActiveStatus(domainId, Long.parseLong(clientId), isActive);
        } catch (Exception e) {
            String message = "Error toggling client active status.";
            logger.error(message, e);
            throw e;
        }
    }
}