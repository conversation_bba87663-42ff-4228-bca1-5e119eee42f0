package com.aphe.auth.graphql.mutations;

import com.aphe.auth.service.d2d.D2DRelationManager;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class D2DRelationMutation {

    private static final Logger logger = LoggerFactory.getLogger(D2DRelationMutation.class);

    @Autowired
    protected D2DRelationManager d2DRelationManager;

    @DgsMutation
    public Boolean acceptDomainInvitation(@InputArgument("targetDomainId") String domainId,
                                          @InputArgument("targetSubEntityId") String subEntityId,
                                          @InputArgument("code") String code) throws Exception {
        try {
            d2DRelationManager.addD2DRelation(domainId, subEntityId, code);
            return true;
        } catch (Exception e) {
            String message = "Error creating a domain to domain relation.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean deleteD2DRelation(@InputArgument("id") String relationId) throws ApheDataValidationException {
        try {
            d2DRelationManager.deleteD2DRelation(relationId);
            return true;
        } catch (Exception e) {
            String message = "Error getting D2D Invitations";
            logger.error(message, e);
            throw e;
        }
    }
}