package com.aphe.auth.graphql.mutations;

import com.aphe.auth.service.dto.OAuthIntegrationClientDTO;
import com.aphe.auth.service.dto.OAuthTokenDTO;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class OAuthIntegrationRootMutation {

    private static final Logger logger = LoggerFactory.getLogger(OAuthIntegrationRootMutation.class);

    @Autowired
    OAuthIntegrationMgr integrationMgr;

    @DgsMutation
    public boolean hardRefreshTokens(@InputArgument("partner") String partner) throws Exception {
        try {
            integrationMgr.hardRefreshTokens(partner);
            return true;
        } catch (Exception e) {
            String message = "Error hard refreshing tokens";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean refreshTokens(@InputArgument("partner") String partner) throws Exception {
        try {
            OAuthIntegrationClientDTO dto = integrationMgr.refreshTokens(partner);
            //TODO: Error handling.. the false should be thrown as an exception with error code or with a generic message.
            if (dto != null) {
                return true;
            } else {
                throw new RuntimeException("Unknown error occured when refreshing tokens.");
            }
        } catch (Exception e) {
            String message = "Error refreshing tokens";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean disconnectOAuthIntegration(@InputArgument("partner") String partner) throws Exception {
        try {
            Long domainId = integrationMgr.getCurrentDomainId();
            return integrationMgr.disconnect(domainId, partner);
        } catch (Exception e) {
            String message = "Error disconnecting accounting integration";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean connectOAuthAccount(@InputArgument("partner") String partner, @InputArgument("accountId") String accountId, @InputArgument("accountName") String accountName) throws Exception {
        try {
            Long domainId = integrationMgr.getCurrentDomainId();
            return integrationMgr.connectOAuthAccount(domainId, partner, accountId, accountName);
        } catch (Exception e) {
            String message = "Error disconnecting accounting integration";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean updateOAuthToken(@InputArgument("partner") String partner, @InputArgument("tokenData") OAuthTokenDTO oAuthTokenDTO) throws Exception {
        try {
            Long domainId = integrationMgr.getCurrentDomainId();
            return integrationMgr.updateOAuthToken(domainId, partner, oAuthTokenDTO);
        } catch (Exception e) {
            String message = "Error disconnecting accounting integration";
            logger.error(message, e);
            throw e;
        }
    }

}