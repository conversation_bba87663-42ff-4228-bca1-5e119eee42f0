package com.aphe.auth.graphql.mutations;

import com.aphe.auth.service.DomainAccessManager;
import com.aphe.auth.service.UserManager;
import com.aphe.auth.service.dto.CreateInvitationDTO;
import com.aphe.auth.service.dto.InvitationDTO;
import com.aphe.auth.service.dto.UpdateDomainAccessDTO;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@Component
@DgsComponent
public class DomainAccessMutation {

    private static final Logger logger = LoggerFactory.getLogger(DomainAccessMutation.class);

    @Autowired
    protected UserManager userManager;

    @Autowired
    protected DomainAccessManager domainAccessManager;

    @DgsMutation
    public boolean inviteUser(@InputArgument("input") CreateInvitationDTO invitation) throws Exception {
        try {
            InvitationDTO dto = userManager.addInvitation(invitation);
            return true;
        } catch (Exception e) {
            String message = "Error inviting user.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean resendInvitation(@InputArgument("invitationId") String invitationId) throws Exception {
        try {
            domainAccessManager.notifyUserNewInvitation(invitationId);
            return true;
        } catch (Exception e) {
            String message = "Error resending the invitation email.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean deleteInvitation(@InputArgument("invitationId") String invitationId) throws Exception {
        try {
            domainAccessManager.deleteInvitation(invitationId);
            return true;
        } catch (Exception e) {
            String message = "Error deleting the invitation.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean updateDomainAccess(@InputArgument("input") UpdateDomainAccessDTO dto) throws Exception {
        try {
            domainAccessManager.updateDomainAccess(dto);
            return true;
        } catch (Exception e) {
            String message = "Error updating domain access.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean toggleDomainAccess(@InputArgument("domainAccessId") String domainAccessId, @InputArgument("active") boolean isActive) throws Exception {
        try {
            domainAccessManager.toggelDomainAccess(domainAccessId, isActive);
            return true;
        } catch (Exception e) {
            String message = "Error deleting domain access.";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsMutation
    public boolean deleteDomainAccess(@InputArgument("domainAccessId") String domainAccessId) throws Exception {
        try {
            domainAccessManager.deleteDomainAccess(domainAccessId);
            return true;
        } catch (Exception e) {
            String message = "Error deleting domain access.";
            logger.error(message, e);
            throw e;
        }
    }


    @DgsMutation
    public boolean moveDomainsToAccountant(@InputArgument("domainIds") ArrayList<String> domainIds, @InputArgument("accountantId") String accountantId) throws Exception {
        try {
            domainAccessManager.moveDomainsToAccountant(domainIds, accountantId);
            return true;
        } catch (Exception e) {
            String message = "Error deleting domain access.";
            logger.error(message, e);
            throw e;
        }
    }

}