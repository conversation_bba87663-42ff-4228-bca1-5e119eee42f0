package com.aphe.auth.graphql.resolvers;

import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.UserManager;
import com.aphe.auth.service.dto.DomainAccessDTO;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.auth.service.dto.UserDTO;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class DomainAccessResolver{

    @Autowired
    protected UserManager userManager;

    @Autowired
    protected AccountManager accountManager;

    public UserDTO getUser(DgsDataFetchingEnvironment dfe) {
        DomainAccessDTO d = dfe.getSource();
        return userManager.getUser(Long.toString(d.userId));
    }

    public DomainDTO getDomain(DgsDataFetchingEnvironment dfe) {
        DomainAccessDTO d = dfe.getSource();
        return accountManager.getDomainDTO(d.domainId);
    }

}
