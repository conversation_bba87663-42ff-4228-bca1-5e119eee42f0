package com.aphe.auth.graphql.resolvers;

import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.UserManager;
import com.aphe.auth.service.dto.ClientAccessDTO;
import com.aphe.auth.service.dto.DomainDTO;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsData;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DgsComponent
public class ClientAccessResolver  {

    @Autowired
    protected UserManager userManager;

    @Autowired
    protected AccountManager accountManager;

//    public DomainDTO getAccountant(ClientAccessDTO d) {
//        return accountManager.getDomainDTO(Long.toString(d.accountantId));
//    }

    @DgsData(parentType = "ClientAccess", field = "client")
    public DomainDTO client(DgsDataFetchingEnvironment dfe) {
        ClientAccessDTO d = dfe.getSource();
        return accountManager.getDomainDTO(d.clientId);
    }

//    public UserDTO getUser(ClientAccessDTO d) {
//        return userManager.getUser(Long.toString(d.userId));
//    }
//
//    public DomainDTO getDomain(DomainAccessDTO d) {
//        return accountManager.getDomainDTO(Long.toString(d.domainId));
//    }

}
