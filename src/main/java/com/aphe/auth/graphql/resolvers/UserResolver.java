package com.aphe.auth.graphql.resolvers;

import com.aphe.auth.service.AccountManager;
import com.aphe.auth.service.AuthMapper;
import com.aphe.auth.service.dto.DomainDTO;
import com.aphe.auth.service.dto.MFAConfigDTO;
import com.aphe.auth.service.dto.UserDomainDTO;
import com.aphe.auth.service.mfa.MFAManager;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsData;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import com.netflix.graphql.dgs.InputArgument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@DgsComponent
public class UserResolver {

    @Autowired
    protected AuthMapper authMapper;

    @Autowired
    protected AccountManager accountManager;

    @Autowired
    protected MFAManager mfaManager;

    @DgsData(parentType = "User", field = "userDomains")
    public List<UserDomainDTO> userDomains(DgsDataFetchingEnvironment dfe, @InputArgument boolean includeInactive) {
        List<DomainDTO> domains = accountManager.getDomains(includeInactive);
        return domains.stream().map(d->authMapper.domainDTOToUserDomainDTO(d)).collect(Collectors.toList());
    }

    @DgsData(parentType = "User", field = "mfaConfig")
    public MFAConfigDTO mfaConfig(DgsDataFetchingEnvironment dfe) throws ApheDataValidationException {
        return mfaManager.getMFAConfig(accountManager.getLoggedInUserId());
    }

}
