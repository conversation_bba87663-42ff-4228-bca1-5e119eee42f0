package com.aphe.auth.graphql.resolvers;

import com.aphe.auth.model.d2d.D2DRelationType;
import com.aphe.auth.service.*;
import com.aphe.auth.service.d2d.D2DInvitationManagerTxn;
import com.aphe.auth.service.d2d.D2DRelationManager;
import com.aphe.auth.service.d2d.dto.D2DInvitationDTO;
import com.aphe.auth.service.d2d.dto.D2DRelationDTO;
import com.aphe.auth.service.d2d.dto.D2DRelationStatusDTO;
import com.aphe.auth.service.dto.*;
import com.aphe.auth.sso.OAuthIntegrationMgr;
import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.netflix.graphql.dgs.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@DgsComponent
public class DomainResolver {

    private static final Logger logger = LoggerFactory.getLogger(DomainResolver.class);

    @Autowired
    AuthMapper authMapper;

    @Autowired
    protected AccountManagerTxn accountManagerTxn;

    @Autowired
    protected DomainAccessManager domainAccessManager;

    @Autowired
    protected ClientAccessManager clientAccessManager;

    @Autowired
    protected OAuthIntegrationMgr integrationMgr;

    @Autowired
    protected UserManagerTxn userManagerTxn;

    @Autowired
    protected D2DInvitationManagerTxn d2DInvitationManagerTxn;

    @Autowired
    protected D2DRelationManager d2DRelationManager;

    @DgsData(parentType = "Domain", field = "accountants")
    public List<MyAccountantDTO> accountants(DgsDataFetchingEnvironment dfe) {
        DomainDTO d = dfe.getSource();
        return accountManagerTxn.getMyAccountants(Long.toString(d.id));
    }

    @DgsData(parentType = "Domain", field = "clients")
    public List<DomainDTO> clients(DgsDataFetchingEnvironment dfe, @InputArgument boolean includeInactive) {
        return accountManagerTxn.getClients(includeInactive);
    }

    @DgsData(parentType = "Domain", field = "allClients")
    public List<DomainDTO> allClients(DgsDataFetchingEnvironment dfe) {
        return accountManagerTxn.getAllClients();
    }

    @DgsData(parentType = "Domain", field = "domainUser")
    public DomainUserDTO domainUser(DgsDataFetchingEnvironment dfe, @InputArgument String domainAccessId) {
        DomainDTO d = dfe.getSource();
        return domainAccessManager.getDomainUser(domainAccessId);
    }

    @DgsData(parentType = "Domain", field = "domainUsers")
    public List<DomainUserDTO> domainUsers(DgsDataFetchingEnvironment dfe) throws ApheException {
        DomainDTO d = dfe.getSource();
        return domainAccessManager.getDomainUsers(Long.toString(d.id));
    }

    @DgsData(parentType = "Domain", field = "invitations")
    public List<InvitationDTO> invitations(DgsDataFetchingEnvironment dfe) throws ApheException {
        try {
            DomainDTO d = dfe.getSource();
            return domainAccessManager.getInvitations(Long.toString(d.id));
        } catch (Exception e) {
            String message = "Error getting invitations";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsData(parentType = "Domain", field = "oAuthIntegration")
    public OAuthIntegrationClientDTO oAuthIntegration(DgsDataFetchingEnvironment dfe, @InputArgument String partner) {
        DomainDTO d = dfe.getSource();
        return integrationMgr.getAccountingIntegrationDTO(d.id, partner);
    }

    @DgsData(parentType = "Domain", field = "oAuthTokenData")
    public OAuthTokenDTO oAuthTokenData(DgsDataFetchingEnvironment dfe, @InputArgument String partner) {
        DomainDTO d = dfe.getSource();
        return integrationMgr.getOAuthTokenDTO(d.id, partner);
    }

//    @DgsData.List({
//            @DgsData(parentType = "Domain", field = "oAuthIntegrations"),
//            @DgsData(parentType = "UserDomain", field = "oAuthIntegrations")
//    })
//    public List<OAuthIntegrationClientDTO> oAuthIntegrations(DgsDataFetchingEnvironment dfe) {
//        DomainDTO d = dfe.getSource();
//        return integrationMgr.getAccountingIntegrationDTOs(Long.toString(d.id));
//    }

    @DgsData(parentType = "Domain", field = "oAuthAccounts")
    public List<PartnerAccountDTO> oAuthAccounts(DgsDataFetchingEnvironment dfe, @InputArgument String partner) {
        DomainDTO d = dfe.getSource();
        return integrationMgr.getOAuthAccounts(d.id, partner);
    }

    @DgsData(parentType = "Domain", field = "domainInvitationsByType")
    public List<D2DInvitationDTO> domainInvitationsByType(DgsDataFetchingEnvironment dfe, @InputArgument D2DRelationType relationType) throws ApheDataValidationException {
        try {
            DomainDTO d = dfe.getSource();
            return d2DInvitationManagerTxn.getInvitations(d.id, relationType);
        } catch (Exception e) {
            String message = "Error getting D2D Invitations by type";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsData(parentType = "Domain", field = "domainInvitationsByEntity")
    public List<D2DInvitationDTO> domainInvitationsByEntity(DgsDataFetchingEnvironment dfe, @InputArgument Long subEntityId, @InputArgument D2DRelationType relationType) throws ApheDataValidationException {
        try {
            DomainDTO d = dfe.getSource();
            return d2DInvitationManagerTxn.getInvitations(d.id, subEntityId, relationType);
        } catch (Exception e) {
            String message = "Error getting D2D Invitations by entity";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsData(parentType = "Domain", field = "domainRelationStatusByTypeAndEntities")
    public List<D2DRelationStatusDTO> domainRelationStatusByTypeAndEntities(DgsDataFetchingEnvironment dfe, @InputArgument D2DRelationType relationType, @InputArgument List<Long> subEntityIds) throws ApheDataValidationException {
        try {
            DomainDTO d = dfe.getSource();
            return d2DRelationManager.getD2DRelationStatus(d.id, relationType, subEntityIds);
        } catch (Exception e) {
            String message = "Error getting Domain relation status";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsData(parentType = "Domain", field = "domainRelationsByType")
    public List<D2DRelationDTO> domainRelationsByType(DgsDataFetchingEnvironment dfe, @InputArgument D2DRelationType relationType) throws ApheDataValidationException {
        try {
            DomainDTO d = dfe.getSource();
            return d2DRelationManager.getD2DRelations(d.id, relationType);
        } catch (Exception e) {
            String message = "Error getting D2D relations by type";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsData(parentType = "Domain", field = "domainRelationByEntity")
    public D2DRelationDTO domainRelationByEntity(DgsDataFetchingEnvironment dfe, @InputArgument Long subEntityId, @InputArgument D2DRelationType relationType) throws ApheDataValidationException {
        try {
            DomainDTO d = dfe.getSource();
            return d2DRelationManager.getD2DRelation(d.id, subEntityId, relationType);
        } catch (Exception e) {
            String message = "Error getting D2D relation by entity";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsData(parentType = "Domain", field = "targetDomainRelationsByType")
    public List<D2DRelationDTO> targetDomainRelationsByType(DgsDataFetchingEnvironment dfe, @InputArgument D2DRelationType relationType) throws ApheDataValidationException {
        try {
            DomainDTO d = dfe.getSource();
            return d2DRelationManager.getTargetD2DRelations(d.id, relationType);
        } catch (Exception e) {
            String message = "Error getting D2D relations by type for target domain id";
            logger.error(message, e);
            throw e;
        }
    }

    @DgsData(parentType = "Domain", field = "targetDomainRelationByEntity")
    public D2DRelationDTO targetDomainRelationByEntity(DgsDataFetchingEnvironment dfe, @InputArgument Long subEntityId, @InputArgument D2DRelationType relationType) throws ApheDataValidationException {
        try {
            DomainDTO d = dfe.getSource();
            return d2DRelationManager.getTargetD2DRelation(d.id, subEntityId, relationType);
        } catch (Exception e) {
            String message = "Error getting D2D relations by entity for target domain id";
            logger.error(message, e);
            throw e;
        }
    }

}
