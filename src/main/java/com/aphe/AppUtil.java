package com.aphe;

import java.util.List;
import java.util.Map;

public class AppUtil {

    public static final String GS_COMPANY_ID = "12484";
//    public static final String GS_COMPANY_ID = "311";

    public static boolean isClientOfGS(String domainId, Map<String, List<String>> accountantsByDomain) {
        boolean isClientOfGS = false;

        if(domainId.equalsIgnoreCase(GS_COMPANY_ID)) {
            isClientOfGS = true;
        } else {
            if (accountantsByDomain != null) {
                List<String> accountantIds = accountantsByDomain.get(domainId);
                if (accountantIds != null && accountantIds.contains(GS_COMPANY_ID)) {
                    isClientOfGS = true;
                }
            }
        }
        return isClientOfGS;
    }
}
