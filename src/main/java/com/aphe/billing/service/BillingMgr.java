package com.aphe.billing.service;

import com.aphe.billing.dto.*;
import com.aphe.billing.model.*;
import com.aphe.billing.model.enums.TNNService;
import com.aphe.billing.repo.BillingTransactionRepo;
import com.aphe.billing.repo.PurchaseTransactionRepo;
import com.aphe.common.error.ApheErrorCode;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.service.CommonBaseManager;
import com.aphe.common.util.DateUtil;
import com.aphe.common.util.StringUtil;
import com.aphe.contractor.services.FilingManager;
import com.aphe.contractor.services.TINMatchRequestManager;
import com.aphe.contractor.services.W9RequestManager;
import com.aphe.domain.service.DomainMgr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import jakarta.transaction.Transactional;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class BillingMgr extends CommonBaseManager {

	@Autowired
	PurchaseTransactionRepo txnRepo;

	@Autowired
	BillingTransactionRepo billingTxnRepo;

	@Autowired
    SMBPricingMgr smbPricingMgr;

	@Autowired
	AccountantPricingMgr accountantPricingMgr;

	@Autowired
	FilingManager filingMgr;

	@Autowired
	TINMatchRequestManager tinMatchRequestManager;

	@Autowired
	W9RequestManager w9RequestManager;

	@Autowired
	TNNContractMgr tnnContractMgr;

	@Autowired
	SQProcessor sqProcessor;

	@Autowired
	PayPalProcessor payPalProcessor;

	@Autowired
	BillingMapper billingMapper;

	@Autowired
	DomainMgr domainMgr;

	@Transactional
	public boolean capturePayment(ChargeWithNonceDTO dto) throws Exception {
		String chargeDesc = "1099SF";
		if(StringUtil.isNotEmpty(dto.chargeDesc)) {
			chargeDesc = chargeDesc + "-" +dto.chargeDesc;
			chargeDesc = chargeDesc.replaceAll("[^a-zA-Z0-9-& ]", "");
			if(chargeDesc.length() > 20) {
				chargeDesc = chargeDesc.substring(0,20);
			}
		}
		dto.chargeDesc = chargeDesc;
		TransactionData data = null;
		if(dto.paymentPartner == PaymentParner.SQUARE) {
			data = sqProcessor.capturePayment(dto);
		} else if(dto.paymentPartner == PaymentParner.PAYPAL) {
			data = payPalProcessor.capturePayment(dto);
		} else {
			throw new ApheException(ApheErrorCode.BILL_1001);
		}
		if (data != null) {
			recordCharge(data);
			return true;
		} else {
			return false;
		}
	}

	@PreAuthorize("hasAuthority('superadmin')")
	@Transactional
	public boolean requestRefund(RefundDTO dto) throws Exception {
		String chargeDesc = "1099SF";
		if(StringUtil.isNotEmpty(dto.reason)) {
			chargeDesc = chargeDesc + "-" +dto.reason;
			chargeDesc = chargeDesc.replaceAll("[^a-zA-Z0-9-& ]", "");
			if(chargeDesc.length() > 20) {
				chargeDesc = chargeDesc.substring(0,20);
			}
		}
		dto.reason = chargeDesc;
		RefundData data = null;

		BillingTransaction billingTransaction = billingTxnRepo.findByGlobalId(dto.id);
		if(billingTransaction != null) {
			//extract a substring from the description that is in enclosed in a pair of parentheses
			String regEx = "\\(([^)]+)\\)";
			java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regEx);
			java.util.regex.Matcher matcher = pattern.matcher(billingTransaction.getDescription());
			String paymentId = null;
			if (matcher.find()) {
				paymentId = matcher.group(1);
			}
			PurchaseTransaction purchaseTransaction = txnRepo.findByPaymentId(paymentId);

			//check if the requestedAmount and processingFee match to the refundAmount
			if(dto.requestedAmount - dto.processingFee != dto.refundAmount) {
				throw new ApheException(ApheErrorCode.BILL_1001);
			}

			//If this is null, this must be old payment.. let's assume payment partner is square
			if(purchaseTransaction == null || purchaseTransaction.getPaymentParner() == PaymentParner.SQUARE) {
				data = sqProcessor.requestRefund(dto, paymentId);
			} else if(purchaseTransaction.getPaymentParner() == PaymentParner.PAYPAL) {
				data = payPalProcessor.requestRefund(dto, paymentId);
			} else {
				throw new ApheException(ApheErrorCode.BILL_1001);
			}
		}
		if (data != null) {
			recordRefund(data);
			return true;
		} else {
			return false;
		}
	}


	@Transactional
	public void recordCharge(TransactionData transactionData) {
//	private void recordCharge(Instant createTime, Long transactionAmount, String currency, String description, String referenceId, String paymentId) {
		PurchaseTransaction txn = new PurchaseTransaction();
		txn.setPaymentParner(transactionData.paymentPartner);
		txn.setDomainId(getBillingDomainId());
		txn.setCreateTime(Date.from(transactionData.createTime));
		txn.setStatus(PurchaseTransactionStatus.Accepted);
		txn.setReferenceId(transactionData.referenceId);
		txn.setPaymentId(transactionData.paymentId);
		txn.setAmount(transactionData.transactionAmount);
		txn.setCurrency(transactionData.currency);
		txn.setDescription(transactionData.description);
		txnRepo.save(txn);

		long currentRunningTotal = getRunningTotal(getBillingDomainId());
		long runningTotal = currentRunningTotal + txn.getAmount();

		// Add a new entry into billing transactions table.
		BillingTransaction billingTransaction = new BillingTransaction();
		billingTransaction.setDomainId(txn.getDomainId());
		billingTransaction.setTransactionTime(new Date());
		billingTransaction.setTransactionType(BillingTransactionType.PurchaseCredit);
		billingTransaction.setTransactionAmount(txn.getAmount());
		billingTransaction.setDescription(txn.getDescription() + "(" + transactionData.paymentId + ")");
		billingTransaction.setRunningTotal(runningTotal);

		billingTxnRepo.save(billingTransaction);
	}

	@Transactional
//	private void recordRefund(Instant createTime, Long transactionAmount, String currency, String description, String referenceId, String paymentId, PurchaseTransactionStatus status) {
	public void recordRefund(RefundData refundData) {
		PurchaseTransaction txn = new PurchaseTransaction();
		txn.setPaymentParner(refundData.paymentPartner);
		txn.setDomainId(getBillingDomainId());
		txn.setCreateTime(Date.from(refundData.createTime));
		txn.setStatus(refundData.status);
		txn.setReferenceId(refundData.referenceId);
		txn.setPaymentId(refundData.refundId);
		txn.setAmount(refundData.transactionAmount);
		txn.setCurrency(refundData.currency);
		txn.setDescription(refundData.description);
		txnRepo.save(txn);

		long currentRunningTotal = getRunningTotal(getBillingDomainId());
		long runningTotal = currentRunningTotal + txn.getAmount();

		// Add a new entry into billing transactions table.
		BillingTransaction billingTransaction = getRefundTransaction(refundData, txn, runningTotal);
		billingTxnRepo.save(billingTransaction);

		if(refundData.processingFee > 0) {
			// Add a new entry into billing transactions table for processing fee.
			runningTotal = runningTotal - refundData.processingFee;
			BillingTransaction billingTransactionFee = getProcessingFeeTransaction(refundData, txn, runningTotal);
			billingTxnRepo.save(billingTransactionFee);
		}


	}
	private BillingTransaction getRefundTransaction(RefundData refundData, PurchaseTransaction txn, long runningTotal) {
		BillingTransaction billingTransaction = new BillingTransaction();
		billingTransaction.setDomainId(txn.getDomainId());
		billingTransaction.setTransactionTime(new Date());
		billingTransaction.setTransactionType(BillingTransactionType.PurchaseCredit);
		billingTransaction.setTransactionAmount(txn.getAmount());
		billingTransaction.setDescription(txn.getDescription() + " (" + refundData.refundId + ")");
		billingTransaction.setRunningTotal(runningTotal);
		return billingTransaction;
	}
	private BillingTransaction getProcessingFeeTransaction(RefundData refundData, PurchaseTransaction txn, long runningTotal) {
		BillingTransaction billingTransactionFee = new BillingTransaction();
		billingTransactionFee.setDomainId(txn.getDomainId());
		billingTransactionFee.setTransactionTime(new Date());
		billingTransactionFee.setTransactionType(BillingTransactionType.ServiceCharge);
		billingTransactionFee.setTransactionAmount(refundData.processingFee);
		billingTransactionFee.setDescription("Processing Fee for " + txn.getDescription() + " (" + refundData.refundId + ")");
		billingTransactionFee.setRunningTotal(runningTotal);
		return billingTransactionFee;
	}


	/**
	 * Adds promo credits that are active at the time of company creation.
	 * 
	 */
	@PreAuthorize("hasAuthority('superadmin')")
	@Transactional
	public boolean addPromoCredits(long credits, String description) {
		return addPromoCreditsPrivate(credits, description);
	}

	public boolean addPromoCreditsPrivate(long credits, String description) {
		Long billingDomainId = getBillingDomainId();

		long currentRunningTotal = getRunningTotal(getBillingDomainId());
		long runningTotal = currentRunningTotal + credits;

		BillingTransaction billingTransaction = new BillingTransaction();
		billingTransaction.setDomainId(billingDomainId);
		billingTransaction.setTransactionTime(new Date());
		billingTransaction.setTransactionType(BillingTransactionType.PromoCredit);
		billingTransaction.setTransactionAmount(credits);
		billingTransaction.setDescription(description);
		billingTransaction.setRunningTotal(runningTotal);
		billingTxnRepo.save(billingTransaction);
		return true;
	}

	private long getRunningTotal(Long billingDomainId) {

		Pageable pageable = PageRequest.of(0, 1, Sort.Direction.DESC, "id");

		Page<BillingTransaction> topPage = billingTxnRepo.findByDomainId(billingDomainId, pageable);
		List<BillingTransaction> topTransactions = topPage.getContent();

		if (topTransactions != null && topTransactions.size() > 0) {
			return topTransactions.get(0).getRunningTotal();
		}
		return 0;
	}

	@Transactional
	public List<BillingTransactionDTO> getBillingTransactions() {
		return getBillingTransactions(getBillingDomainId());
	}

	@Transactional
	public List<BillingTransactionDTO> getBillingTransactions(Long domainId) {
		if (isDomainAccessible(domainId)) {
			List<BillingTransaction> byDomainId = billingTxnRepo.findByDomainId(domainId);
			Set<Long> accountIds = byDomainId.stream().map(BillingTransaction::getReferenceId).collect(Collectors.toUnmodifiableSet());

			Map<Long, String> accountNames = domainMgr.getDomainNames(accountIds.stream().toList());

			List<BillingTransactionDTO> dtoList = byDomainId.stream()
					.map(billingTransaction -> {
						BillingTransactionDTO billingTransactionDTO = billingMapper.toBillingTransactionDTO(billingTransaction);
						billingTransactionDTO.accountName = accountNames.get(billingTransaction.getReferenceId());
						return billingTransactionDTO;
					})
					.collect(Collectors.toList());
			dtoList.sort((o1, o2) -> o2.transactionTime.compareTo(o1.transactionTime));
			return dtoList;
		} else {
			return Collections.emptyList();
		}
	}

	@Transactional
	public long getAvailableCredits() {
		return getAvailableCredits(getBillingDomainId());
	}

	@Transactional
	public long getAvailableCredits(Long domainId) {
		if (isDomainAccessible(domainId))
			return getRunningTotal(domainId);
		else
			return 0;
	}

	@Transactional
	public boolean useCredits(Long numberOfCredits, String description) {
		// Checks if there are enough credits.
		Long billingDomainId = getBillingDomainId();
		Long txnAmount = numberOfCredits;
		Long availableAmount = getRunningTotal(billingDomainId);

		if (availableAmount < txnAmount) {
			logger.info("Not enough funds to file for domainId={} creditsNeeded={} creditsAvaialable={} description={}", getCurrentDomainId(), txnAmount, availableAmount,
					description);
			return false;
		} else {
			// create a new billing transaction with enough details and deduct the credits..
			long runningTotal = availableAmount - txnAmount;

			BillingTransaction billingTransaction = new BillingTransaction();
			billingTransaction.setDomainId(billingDomainId);
			billingTransaction.setTransactionTime(new Date());
			billingTransaction.setTransactionType(BillingTransactionType.ServiceCharge);
			billingTransaction.setTransactionAmount(txnAmount);
			billingTransaction.setReferenceId(getCurrentDomainId());
			billingTransaction.setDescription(description);
			billingTransaction.setRunningTotal(runningTotal);

			billingTxnRepo.save(billingTransaction);
			return true;
		}
	}

	/**
	 * List of products and amounts. Need to be aware of the
	 * 
	 * @return
	 */

	//TODO: how is billing supposed to work when client user tried to submit the filings for a client???
	// Client user should not be able to charge
	// What kind of tiered pricing can they get??
	// Do we restrict
	// The whole notion of wholesale vs retail comes up... (who is the owner of the account???)
	// If you are not the owner, you should not be able to charge or add new produts?? how does that work for one time charges
	public InvoiceDTO generateInvoice(List<ChargeItemDTO> items) {

		Long billingDomainId = getBillingDomainId();
		boolean isAccountant = isAccountant();
		List<Long> payerIds = filingMgr.getPayerIdsForAllClients(billingDomainId);


		/**
		 * Support for special contracts...
		 * A contract has a start date and end date, allocated number of fed filings, state filings, print copies etc. An additional
		 * service of each type has some additional pricing.
		 * If there are multiple contracts, use the one that is expiring soon. If there are no services left on the soon expiring one, use the next soon
		 * expiring one.
		 *
		 * If there are no contracts or contract ended before the season start date
		 * 	-- if we are in the season and apply season pricing, and look at all the current year filings.
		 8  -- If not in season, apply regular pricing, by setting previous filed quantity to 0.
		 *
		 * If there are contracts, apply the contract pricing.
		 * 	-- if the contract is in effect, get all the filings since the contract started, (all years, all corrections etc..) and use them as previous quantity.
		 * 	-- If the contract is not in effect,
		 * 		-- if we are in seasson, use the current year filings since contract end date, and use them as previous quantity.
		 * 		-- if we are not in season, set previous filed quantity to 0.
		 */
		TNNContract lastContract = tnnContractMgr.getTNNContract(billingDomainId);

		Date now = new Date();
		Date contractStartDate = lastContract != null ? Date.from(lastContract.getContractStartTime().atStartOfDay(ZoneId.systemDefault()).toInstant()) : null;
		Date contractEndDate = lastContract != null ? DateUtil.addEndOfDay(Date.from(lastContract.getContractEndTime().atStartOfDay(ZoneId.systemDefault()).toInstant())) : null;
//		Date seasonStartDate = Date.from(SEASON_START_DATE.atStartOfDay(ZoneId.systemDefault()).toInstant());
//		Date seasonEndDate = DateUtil.addEndOfDay(Date.from(SEASON_END_DATE.atStartOfDay(ZoneId.systemDefault()).toInstant()));
//		boolean isInSeason = now.after(seasonStartDate) && now.before(seasonEndDate);
		Date seasonStartDate = Date.from(smbPricingMgr.getSeasonStartDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
		boolean isInSeason = smbPricingMgr.inSeason();

		if(lastContract == null || contractEndDate.before(seasonStartDate)) {
			//Just bill as usual.. tiered pricing... lookup all the filings for the current year if we are still in the season..
			//If we are not in the season, assume previous filing quantity as zero and pass the cart to the pricing engine.
			if(isInSeason) {
				for(ChargeItemDTO item : items) {
					if(item.service == TNNService.FED_EFILE) {
						item.previousQuantity = filingMgr.getFedSubsForAllClients(payerIds, seasonStartDate, now, true);
					}
					if(item.service == TNNService.STATE_EFILE) {
						item.previousQuantity = filingMgr.getStateSubsForAllClients(payerIds, seasonStartDate, now, true);
					}
					if(item.service == TNNService.RECIPIENT_PRINT_DELIVERY) {
						item.previousQuantity = filingMgr.getPrintSubsForAllClients(payerIds, seasonStartDate, now, true);
					}
					if(item.service == TNNService.TIN_MATCH) {
						item.previousQuantity = tinMatchRequestManager.getTINMatchRequestsForAllClients(payerIds, seasonStartDate);
					}
					if(item.service == TNNService.W9) {
						item.previousQuantity = w9RequestManager.getW9RequestsForAllClients(payerIds, seasonStartDate);
					}
				}
			}
			InvoiceDTO invoiceDTO = generateInvoice(items, isAccountant, false, null);
			return invoiceDTO;
		} else {
			//Check if this contract is effective now
			if(contractStartDate.before(now) && contractEndDate.after(now)) {
				//This contract is effective now... use the contract pricing
				//Get the filings submitted between these dates.... and set the previosuly filed qty and pass it to the pricing engine along with contract...
				for(ChargeItemDTO item : items) {
					if(item.service == TNNService.FED_EFILE) {
						item.previousQuantity = filingMgr.getFedSubsForAllClients(payerIds, contractStartDate, contractEndDate, false);
					}
					if(item.service == TNNService.STATE_EFILE) {
						item.previousQuantity = filingMgr.getStateSubsForAllClients(payerIds, contractStartDate, contractEndDate, false);
					}
					if(item.service == TNNService.RECIPIENT_PRINT_DELIVERY) {
						item.previousQuantity = filingMgr.getPrintSubsForAllClients(payerIds, contractStartDate, contractEndDate, false);
					}
					if(item.service == TNNService.TIN_MATCH) {
						item.previousQuantity = tinMatchRequestManager.getTINMatchRequestsForAllClients(payerIds, contractStartDate);
					}
					if(item.service == TNNService.W9) {
						item.previousQuantity = w9RequestManager.getW9RequestsForAllClients(payerIds, contractStartDate);
					}

				}
				InvoiceDTO invoiceDTO = generateInvoice(items, isAccountant, false, lastContract);
				return invoiceDTO;
			} else {
				//This contract is not effective now... but it ended during the season... so only count the filings submitted after contract end date
				if(isInSeason) {
					for(ChargeItemDTO item : items) {
						if(item.service == TNNService.FED_EFILE) {
							item.previousQuantity = filingMgr.getFedSubsForAllClients(payerIds, contractEndDate, now, true);
						}
						if(item.service == TNNService.STATE_EFILE) {
							item.previousQuantity = filingMgr.getStateSubsForAllClients(payerIds, contractEndDate, now, true);
						}
						if(item.service == TNNService.RECIPIENT_PRINT_DELIVERY) {
							item.previousQuantity = filingMgr.getPrintSubsForAllClients(payerIds, contractEndDate, now, true);
						}
						if(item.service == TNNService.TIN_MATCH) {
							item.previousQuantity = tinMatchRequestManager.getTINMatchRequestsForAllClients(payerIds, contractEndDate);
						}
						if(item.service == TNNService.W9) {
							item.previousQuantity = w9RequestManager.getW9RequestsForAllClients(payerIds, contractEndDate);
						}
					}
				}
				InvoiceDTO invoiceDTO = generateInvoice(items, isAccountant, false, null);
				return invoiceDTO;
			}
		}
	}

	public InvoiceDTO generateInvoice(List<ChargeItemDTO> chargeItemDTOS, boolean isAccountant, boolean seasonPricing, TNNContract lastContract) {
		if(isAccountant) {
			return accountantPricingMgr.generateInvoice(chargeItemDTOS, seasonPricing, lastContract);
		} else {
			return smbPricingMgr.generateInvoice(chargeItemDTOS, seasonPricing, lastContract);
		}
	}


}
