package com.aphe.billing.rs;

import com.aphe.billing.dto.InvoiceDTO;
import com.aphe.billing.service.BillingMgr;
import com.aphe.common.error.exceptions.ApheException;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/rs/api/pricing/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Billing Information", description = "API to calcualte pricing")
public class PricingController {

	private static Logger logger = LoggerFactory.getLogger(PricingController.class);

	@Autowired
	BillingMgr billingMgr;

	@PostMapping(path = "calculator/")
	public InvoiceDTO getInvoiceDetails(@RequestBody ChargeDetailsInput chargeDetailsInput) throws Exception {
		try {InvoiceDTO invoice = billingMgr.generateInvoice(chargeDetailsInput.services, chargeDetailsInput.accountant, true, null);
			return invoice;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error calculating price."));
		}
	}
}
