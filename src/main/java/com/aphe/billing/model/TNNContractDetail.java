package com.aphe.billing.model;

import com.aphe.billing.model.enums.TNNService;
import com.aphe.common.model.BaseEntity;

/**
 * Table to maintain various billing contract details.
 */
//@Entity
//@Table(name = "B_TNNContractDetails")
//@Cache(alwaysRefresh = true, refreshOnlyIfNewer = true)
public class TNNContractDetail extends BaseEntity {

    private TNNService service;
    private Long contractLimit;
    private Long additionalPrice;

    public TNNContractDetail() {
    }

    public TNNService getService() {
        return service;
    }

    public void setService(TNNService service) {
        this.service = service;
    }

    public Long getContractLimit() {
        return contractLimit;
    }

    public void setContractLimit(Long contractLimit) {
        this.contractLimit = contractLimit;
    }

    public Long getAdditionalPrice() {
        return additionalPrice;
    }

    public void setAdditionalPrice(Long additionalPrice) {
        this.additionalPrice = additionalPrice;
    }
}
