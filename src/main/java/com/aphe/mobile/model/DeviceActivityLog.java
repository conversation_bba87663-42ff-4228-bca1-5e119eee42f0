package com.aphe.mobile.model;

import com.aphe.common.model.BaseEntity;

import jakarta.persistence.*;
import java.util.Date;


@Entity
@Table(name = "m_deviceactivitylogs")
@NamedQuery(name = "DeviceActivityLog.findByDeviceActivityLogByDevice",
        query = "SELECT dl FROM DeviceActivityLog dl WHERE dl.device=:device order by dl.activityDate DESC")
public class DeviceActivityLog extends BaseEntity {

    @ManyToOne
    private Device device;

    private Date activityDate;

    @Enumerated(EnumType.STRING)
    private DeviceActivityType activityType;

    private String description;

    @Column(columnDefinition = "JSON")
    private String metadata;

    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    public Date getActivityDate() {
        return activityDate;
    }

    public void setActivityDate(Date activityDate) {
        this.activityDate = activityDate;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public DeviceActivityType getActivityType() {
        return activityType;
    }

    public void setActivityType(DeviceActivityType activityType) {
        this.activityType = activityType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}