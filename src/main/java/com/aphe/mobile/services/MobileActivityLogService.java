package com.aphe.mobile.services;

import com.aphe.mobile.model.Device;
import com.aphe.mobile.model.DeviceActivityLog;
import com.aphe.mobile.model.DeviceActivityType;
import com.aphe.mobile.repo.DeviceActivityLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Transactional
public class MobileActivityLogService {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(MobileActivityLogService.class);

    @Autowired
    private DeviceActivityLogRepository deviceActivityLogRepository;

    @Async("applicationTaskExecutor")
    public void logActivity(Device device, DeviceActivityType activityType, String description, String metadataJson) {
        try {
            DeviceActivityLog log = new DeviceActivityLog();
            log.setDevice(device);
            log.setActivityDate(new Date());
            log.setActivityType(activityType);
            log.setDescription(description);
            log.setMetadata(metadataJson);
            deviceActivityLogRepository.save(log);
        } catch (Throwable e) {
            logger.error("Error logging activity: ", e);
        }
    }
}
