package com.aphe.employee.rs;

import com.aphe.common.error.exceptions.ApheDataValidationException;
import com.aphe.common.error.exceptions.ApheException;
import com.aphe.employee.dto.EmployeeDTO;
import com.aphe.employee.model.Gender;
import com.aphe.employee.model.JobTitle;
import com.aphe.employee.services.WorkerMgr;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(path = "/rs/api/workers/", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Workers", description = "API to manage Workers in a business.")
public class WorkerController {

	private static Logger logger = LoggerFactory.getLogger(WorkerController.class);

	@Autowired
	WorkerMgr workerMgr;

	@GetMapping
	public List<EmployeeDTO> getWorkers() throws Exception {
		try {
			Long domainId = workerMgr.getCurrentDomainId();
			List<EmployeeDTO> workers = (List<EmployeeDTO>) workerMgr.getEmployees(domainId, 1000, 0).data;
			if (workers != null) {
				return workers;
			} else {
				throw new RuntimeException("Error loading the workers");
			}
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting workers"));
		}
	}

	@GetMapping(path = "{workerId}/")
	public EmployeeDTO getWorker(@PathVariable("workerId") Long employeeId) throws Exception {
		try {
			EmployeeDTO worker = workerMgr.getEmployee(employeeId);
			if (worker != null) {
				return worker;
			} else {
				throw new RuntimeException("Error getting worker");
			}
		} catch (ApheDataValidationException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting worker by workerId"));
		}
	}

	@PutMapping
	public EmployeeDTO addWorker(EmployeeDTO employeeDTO) throws Exception {
		try {
			EmployeeDTO e = workerMgr.addEmployee(employeeDTO);
			if (e != null) {
				return e;
			} else {
				throw new RuntimeException("Error adding employee");
			}
		} catch (ApheDataValidationException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error adding a worker"));
		}
	}

	@PostMapping
	public EmployeeDTO updateWorker(EmployeeDTO employeeDTO) throws Exception {
		try {
			EmployeeDTO e = workerMgr.updateEmployee(employeeDTO);
			if (e != null) {
				return e;
			} else {
				throw new RuntimeException("Error updating employee");
			}
		} catch (ApheDataValidationException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error updating a worker"));
		}
	}

	@DeleteMapping(path = "{workerId}/")
	public void deleteWorker(@PathVariable("workerId") String workerId) throws Exception {
		try {
			boolean deleted = workerMgr.deleteWorker(workerId);
			if (!deleted) {
				throw new RuntimeException("Error deleting the worker");
			}
		} catch (ApheDataValidationException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error deleting a worker by workerId "));
		}
	}

	@DeleteMapping(path = "org/{workerId}/")
	public void deleteWorkerOrg(@PathVariable("workerId") String workerId) throws Exception {
		try {
			boolean deleted = workerMgr.deleteWorkerOrg(workerId);
			if (!deleted) {
				throw new RuntimeException("Error deleting the worker");
			}
		} catch (ApheDataValidationException e) {
			throw e;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error getting org size of a worker"));
		}
	}

	@Value("${aphe.fileUploadDir}")
	private String fileUploadDir;

	@PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public String addWorkers(@RequestParam("file") MultipartFile file) throws ApheException {
		try {
			Path targetLocation = Paths.get(fileUploadDir).toAbsolutePath().normalize();
			try {
				Files.createDirectories(targetLocation);
			} catch (Exception ex) {
				throw new ApheException("Could not create the directory where the uploaded files will be stored.");
			}

			Path filePath = targetLocation.resolve(StringUtils.cleanPath(file.getOriginalFilename()));
			try {
				Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
			} catch (IOException e) {
				throw new ApheException("Could not store file.");
			}

			String failedLines = bulkAddWorkers(filePath.toAbsolutePath().toString());
			return failedLines;
		} catch (Exception e) {
			logger.error("Error", e);
			throw new ApheException(new Exception("Error bulk adding workers by uploadin file "));
		}

	}

	private String bulkAddWorkers(String fileUploadLocation) {
		BufferedReader br = null;
		String line = "";
		String cvsSplitBy = ",";

		List<String[]> workerLines = new ArrayList<>();
		List<String[]> failedLines = new ArrayList<>();
		boolean ignoreFirstLine = true;
		try {
			br = new BufferedReader(new FileReader(fileUploadLocation));
			while ((line = br.readLine()) != null) {
				if (ignoreFirstLine) {
					ignoreFirstLine = false;
					continue;
				}
				workerLines.add(line.split(cvsSplitBy));
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

		Long domainId = workerMgr.getCurrentDomainId();
		for (String[] workerLine : workerLines) {
			try {
				EmployeeDTO dto = new EmployeeDTO();
				dto.setFirstName(workerLine[0]);
				dto.setMiddleName(workerLine[1]);
				dto.setLastName(workerLine[2]);
				dto.setDomainId(domainId);
				dto.setJobTitle(JobTitle.valueOf(workerLine[3]));
				EmployeeDTO manager = workerMgr.getWorkerByFirstName(workerMgr.getCurrentDomainId(), workerLine[4]);
				dto.setManagerId(manager != null ? manager.getId() : 0);
				dto.setGender(Gender.valueOf(workerLine[5]));
				dto.setLocation(workerLine[6]);

				EmployeeDTO addedEmployee = workerMgr.addEmployee(dto);
				if (addedEmployee == null) {
					failedLines.add(workerLine);
				}

			} catch (Exception e) {
				failedLines.add(workerLine);
			}
		}

		StringBuffer response = new StringBuffer();
		for (String[] failedLine : failedLines) {
			String s = String.join(", ", failedLine);
			response.append(s).append(System.lineSeparator());
		}

		return response.toString();
	}

}
