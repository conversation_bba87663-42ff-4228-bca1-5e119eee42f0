package com.aphe.employee.dto;

import com.aphe.employee.model.Gender;
import com.aphe.employee.model.JobTitle;
import com.aphe.employee.model.Race;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.TrimLength;

public class EmployeeDTO {

	private Long domainId;

	private long id;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	private String firstName;

	@TrimLength(min = 0, max = 100)
	private String middleName;

	@NotBlank
	@TrimLength(min = 1, max = 100)
	private String lastName;

	private long managerId;

	@NotNull
	private Gender gender;

	private Race race;

	@NotBlank
	private String location;

	@NotNull
	private JobTitle jobTitle;

	private long orgSize;

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public long getManagerId() {
		return managerId;
	}

	public void setManagerId(long managerId) {
		this.managerId = managerId;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public Race getRace() {
		return race;
	}

	public void setRace(Race race) {
		this.race = race;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public JobTitle getJobTitle() {
		return jobTitle;
	}

	public void setJobTitle(JobTitle jobTitle) {
		this.jobTitle = jobTitle;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public long getOrgSize() {
		return orgSize;
	}

	public void setOrgSize(long orgSize) {
		this.orgSize = orgSize;
	}
}
