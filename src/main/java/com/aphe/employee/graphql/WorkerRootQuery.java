package com.aphe.employee.graphql;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.graphql.PagedResult;
import com.aphe.employee.dto.EmployeeDTO;
import com.aphe.employee.services.WorkerMgr;
import com.netflix.graphql.dgs.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@DgsComponent
public class WorkerRootQuery {

    private static final Logger logger = LoggerFactory.getLogger(WorkerRootQuery.class);

    @Autowired
    WorkerMgr workerMgr;

    @DgsQuery
    public List<EmployeeDTO> workers() throws Exception {
        try {
            Long domainId = workerMgr.getCurrentDomainId();
            return (List<EmployeeDTO>) workerMgr.getEmployees(domainId, 1000, 0).data;
        } catch (Exception e) {
            String message = "Error getting workers";
            logger.error(message, e);
            throw new Exception(message);
        }
    }

    @DgsData(parentType = "Query", field = "workersWithPageMetadata")
    public PagedResult<EmployeeDTO> workersWithPageMetadata(
            @InputArgument("pageSize") Integer pageSize,
            @InputArgument("pageNumber") Integer pageNumber,
            DgsDataFetchingEnvironment dfe) throws ApheException {
        if (pageSize == null) {
            pageSize = 1000;
        }
        if (pageNumber == null) {
            pageNumber = 0;
        }
        return workerMgr.getEmployees(workerMgr.getCurrentDomainId(), pageSize, pageNumber);
    }


    @DgsQuery
    public EmployeeDTO worker(@InputArgument("id") Long workerId) throws Exception {
        try {
            return workerMgr.getEmployee(workerId);
        } catch (Exception e) {
            String message = "Error getting worker by id";
            logger.error(message, e);
            throw new Exception(message);
        }
    }
}