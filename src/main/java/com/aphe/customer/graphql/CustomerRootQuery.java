package com.aphe.customer.graphql;

import com.aphe.customer.services.CustomerManager;
import com.aphe.customer.services.dto.CustomerDTO;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@DgsComponent
public class CustomerRootQuery {

	private static final Logger logger = LoggerFactory.getLogger(CustomerRootQuery.class);

	@Autowired
	CustomerManager customerManager;

	@DgsQuery
	public List<CustomerDTO> customers() throws Exception {
		try {
			return customerManager.getCustomers(customerManager.getCurrentDomainId());
		} catch (Exception e) {
			logger.error("Error getting customers. " + e.getMessage(), e);
			throw e;
		}
	}
}