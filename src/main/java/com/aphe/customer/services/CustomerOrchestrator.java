package com.aphe.customer.services;

import com.aphe.common.error.exceptions.ApheException;
import com.aphe.common.error.exceptions.ApheForbiddenException;
import com.aphe.common.service.CommonBaseManager;
import com.aphe.customer.model.Customer;
import com.aphe.customer.services.dto.CreateCustomerDTO;
import com.aphe.customer.services.dto.CustomerDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@Component
public class CustomerOrchestrator extends CommonBaseManager {

    @Autowired
    protected CustomerManager customerManager;

    @Autowired
    protected CustomerMapper customerMapper;

    public CustomerDTO createCustomer(CreateCustomerDTO dto) throws ApheForbiddenException, ApheException {
        Customer c = customerManager.createCustomer(customerManager.getCurrentDomainId(), dto);
        if (c != null) {
            return customerMapper.toCustomerDTO(c);
        }
        return null;
    }

}
