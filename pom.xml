<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.aphe</groupId>
		<artifactId>aphespringbootpom</artifactId>
		<version>1</version>
		<relativePath>../../poms/aphespringbootpom/pom.xml</relativePath>
	</parent>

	<groupId>com.aphe</groupId>
	<artifactId>springboot-common</artifactId>
	<version>1.0-SNAPSHOT</version>
	<name>springboot-common</name>
	<description>Project for common classes between auth,domain and other services</description>

	<properties>
		<java.version>17</java.version>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<org.mapstruct.version>1.3.1.Final</org.mapstruct.version>
		<log4j2.version>2.17.1</log4j2.version>
		<commons-lang3.version>3.12.0</commons-lang3.version>
		<jobrunr.version>7.5.1</jobrunr.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jersey</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>3.0.5</version>
		</dependency>

		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-maven-plugin</artifactId>
			<version>3.0.3</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>

				<exclusions>
					<exclusion>
						<groupId>org.hibernate</groupId>
						<artifactId>hibernate-entitymanager</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.hibernate</groupId>
						<artifactId>hibernate-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.flywaydb</groupId>
						<artifactId>flyway-core</artifactId>
					</exclusion>
				</exclusions>

		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-mysql</artifactId>
		</dependency>

		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.eclipse.persistence</groupId>
			<artifactId>org.eclipse.persistence.jpa</artifactId>
			<version>4.0.5</version>
		</dependency>

		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.9.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.json/json -->
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20180813</version>
		</dependency>


		<!-- JSON binding library by google -->
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1.1</version>
		</dependency>

		<!-- JSON binding library by google for MailUtil -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>

		<!-- Apache Commons
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.2</version>
		</dependency -->

		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.3</version>
		</dependency>

		<!-- Object Lab Kit for business day calculations -->
		<dependency>
			<groupId>net.objectlab.kit</groupId>
			<artifactId>datecalc-common</artifactId>
			<version>1.2.0</version>
		</dependency>
		<dependency>
			<groupId>net.objectlab.kit</groupId>
			<artifactId>datecalc-joda</artifactId>
			<version>1.2.0</version>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${org.mapstruct.version}</version>
		</dependency>

		<!-- GraphQL related stuff -->
		<dependency>
			<groupId>com.netflix.graphql.dgs</groupId>
			<artifactId>graphql-dgs-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.netflix.graphql.dgs.codegen</groupId>
			<artifactId>graphql-dgs-codegen-client-core</artifactId>
			<version>5.1.17</version>
		</dependency>
		<dependency>
			<groupId>com.netflix.graphql.dgs</groupId>
			<artifactId>graphql-dgs-extended-scalars</artifactId>
		</dependency>


		<!-- https://mvnrepository.com/artifact/com.github.martinwithaar/encryptor4j -->
		<dependency>
			<groupId>com.github.martinwithaar</groupId>
			<artifactId>encryptor4j</artifactId>
			<version>0.1</version>
		</dependency>

		<dependency>
			<groupId>com.sendinblue</groupId>
			<artifactId>sib-api-v3-sdk</artifactId>
			<version>3.0.1</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>io.swagger</groupId>
					<artifactId>swagger-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.squareup.okio</groupId>
					<artifactId>okio</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.sentry</groupId>
			<artifactId>sentry-logback</artifactId>
			<version>1.7.30</version>
		</dependency>

		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>1.7.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-mail -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>

		<dependency>
			<groupId>org.thymeleaf</groupId>
			<artifactId>thymeleaf-spring6</artifactId>
		</dependency>
		<dependency>
			<groupId>nz.net.ultraq.thymeleaf</groupId>
			<artifactId>thymeleaf-layout-dialect</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api -->
		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>2.3.1</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/com.github.xeroapi/xero-java -->
		<dependency>
			<groupId>com.github.xeroapi</groupId>
			<artifactId>xero-java</artifactId>
			<version>12.0.0</version>
		</dependency>


		<!-- Lo4j  -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.1</version>
		</dependency>

		<!-- Adding explicit commons-code to resolve the dependencies -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>

		<!-- Lob integration -->
 		<dependency>
			<groupId>com.lob</groupId>
			<artifactId>lob-java</artifactId>
			<version>10.0.0</version>
		</dependency>

		<!-- Intuit developer API -->
		<!-- data jar -->
		<dependency>
			<groupId> com.intuit.quickbooks-online </groupId>
			<artifactId>ipp-v3-java-data </artifactId>
			<version>6.2.3</version>
			<exclusions>
				<exclusion>
					<groupId>io.swagger</groupId>
					<artifactId>swagger-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.ant</groupId>
					<artifactId>ant</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun</groupId>
					<artifactId>tools</artifactId>
				</exclusion>
<!--				<exclusion>-->
<!--					<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
<!--					<artifactId>jackson-jaxrs-base</artifactId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
<!--					<artifactId>jackson-jaxrs-json-provider</artifactId>-->
<!--				</exclusion>-->
			</exclusions>
		</dependency>

		<!-- devkit jar with dependencies -->
		<dependency>
			<groupId>com.intuit.quickbooks-online</groupId>
			<artifactId>ipp-v3-java-devkit</artifactId>
			<version>6.2.3</version>
			<exclusions>
				<exclusion>
					<groupId>io.swagger</groupId>
					<artifactId>swagger-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
<!--				<exclusion>-->
<!--					<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
<!--					<artifactId>jackson-jaxrs-base</artifactId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
<!--					<artifactId>jackson-jaxrs-json-provider</artifactId>-->
<!--				</exclusion>-->
				<exclusion>
					<groupId>com.sun</groupId>
					<artifactId>tools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>asm</groupId>
					<artifactId>asm</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- oauth jar with dependencies -->
		<dependency>
			<groupId>com.intuit.quickbooks-online</groupId>
			<artifactId>oauth2-platform-api</artifactId>
			<version>6.2.3</version>
			<exclusions>
				<exclusion>
					<groupId>io.swagger</groupId>
					<artifactId>swagger-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun</groupId>
					<artifactId>tools</artifactId>
				</exclusion>
<!--				<exclusion>-->
<!--					<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
<!--					<artifactId>jackson-jaxrs-base</artifactId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
<!--					<artifactId>jackson-jaxrs-json-provider</artifactId>-->
<!--				</exclusion>-->
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.github.javafaker</groupId>
			<artifactId>javafaker</artifactId>
			<version>1.0.2</version>
			<exclusions>
				<exclusion>
					<groupId>org.yaml</groupId>
					<artifactId>snakeyaml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.aphe</groupId>
			<artifactId>freshbooks-sdk</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.zoho.books</groupId>
			<artifactId>books-java</artifactId>
			<version>1.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.aphe</groupId>
			<artifactId>wave-sdk</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.aphe</groupId>
			<artifactId>bqecore-sdk</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.aphe</groupId>
			<artifactId>bdc-sdk</artifactId>
			<version>1.0.0</version>
		</dependency>

		<!-- Dependencies for inlining css for html email templates -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.13.1</version>
		</dependency>
		<dependency>
			<groupId>net.sourceforge.cssparser</groupId>
			<artifactId>cssparser</artifactId>
			<version>0.9.29</version>
		</dependency>
		<!-- End of dependencies for inlining css for html email templates -->

		<!-- Lombok - avoid boilerplate code -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.22</version>
		</dependency>

		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client-gson</artifactId>
			<version>1.41.2</version>
		</dependency>

		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>4.8.0</version>
		</dependency>

		<dependency>
			<groupId>org.jboss.aerogear</groupId>
			<artifactId>aerogear-otp-java</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing</artifactId>
		</dependency>

		<dependency>
			<groupId>org.jobrunr</groupId>
			<artifactId>jobrunr-spring-boot-3-starter</artifactId>
			<version>${jobrunr.version}</version>
		</dependency>

		<dependency>
			<groupId>com.bettercloud</groupId>
			<artifactId>vault-java-driver</artifactId>
			<version>5.1.0</version>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.netflix.graphql.dgs</groupId>
				<artifactId>graphql-dgs-platform-dependencies</artifactId>
				<!-- The DGS BOM/platform dependency. This is the only place you set version of DGS -->
				<version>9.2.2</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2023.0.1</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<!-- Exclude spring boot maven plugin for this jar -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>com.github.ulisesbocchio</groupId>
				<artifactId>jasypt-maven-plugin</artifactId>
				<version>3.0.3</version>
			</plugin>
		</plugins>
	</build>
</project>
