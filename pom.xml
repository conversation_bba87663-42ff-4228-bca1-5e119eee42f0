<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.aphe</groupId>
		<artifactId>aphespringbootpom</artifactId>
		<version>1</version>
		<relativePath>../../poms/aphespringbootpom/pom.xml</relativePath>
	</parent>

<!-- mvn build-helper:parse-version versions:set \
  -DnewVersion=\${parsedVersion.majorVersion}.\${parsedVersion.minorVersion}.\${parsedVersion.nextIncrementalVersion}-\${parsedVersion.qualifier}
 -->
	<artifactId>auth</artifactId>
	<version>1.0.80</version>
	<packaging>jar</packaging>
	<name>auth</name>
	<description>Service responsible for creating users, roles, shell accounts and for managing roles and permissions of these users in their shell accounts</description>

	<properties>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.aphe</groupId>
			<artifactId>springboot-common</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.jboss.aerogear</groupId>
			<artifactId>aerogear-otp-java</artifactId>
			<version>1.0.0</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.flywaydb</groupId>
				<artifactId>flyway-maven-plugin</artifactId>
				<configuration>
					<user>root</user>
					<password></password>
					<url>*******************************************************</url>
				</configuration>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.3.1.Final</version>
						</path>
					</annotationProcessorPaths>
					<compilerArgs>
						<compilerArg>
							-Amapstruct.suppressGeneratorTimestamp=true
						</compilerArg>
						<compilerArg>
							-Amapstruct.suppressGeneratorVersionInfoComment=true
						</compilerArg>
					</compilerArgs>
				</configuration>
			</plugin>

			<!-- This plugin is used to parse the version number and set it as a property -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>parse-version</id>
						<goals>
							<goal>parse-version</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>versions-maven-plugin</artifactId>
				<version>2.18.0</version>
			</plugin>

		</plugins>
	</build>

	<profiles>
		<profile>
			<id>bump-patch</id>
			<activation>
				<property>
					<name>bumpPatch</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>versions-maven-plugin</artifactId>

						<executions>
							<execution>
								<goals>
									<goal>set</goal>
								</goals>
								<phase>validate</phase>
								<configuration>
									<newVersion>${parsedVersion.majorVersion}.${parsedVersion.minorVersion}.${parsedVersion.nextIncrementalVersion}</newVersion>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<scm>
		<url>**************:apheliontech/auth.git</url>
	</scm>
</project>
