<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.aphe</groupId>
		<artifactId>aphespringbootpom</artifactId>
		<version>1</version>
		<relativePath>../../poms/aphespringbootpom/pom.xml</relativePath>
	</parent>


	<artifactId>domain</artifactId>
	<version>1.0.95</version>
	<packaging>jar</packaging>
	<name>Domain Service</name>
	<description>Service responsible for maintaing the details of a domain entity.</description>

	<properties>
		<java.version>17</java.version>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<okhttp3.version>4.9.1</okhttp3.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.aphe</groupId>
			<artifactId>springboot-common</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.beanio/beanio -->
		<dependency>
			<groupId>org.beanio</groupId>
			<artifactId>beanio</artifactId>
			<version>2.1.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.beanio/beanio -->
		<dependency>
			<groupId>com.aphe</groupId>
			<artifactId>pdfgen</artifactId>
			<version>0.0.1-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>javax.mail</groupId>
					<artifactId>mailapi</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- 		<dependency>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-zuul</artifactId>
                    <version>2.2.3.RELEASE</version>
                </dependency> -->

		<!-- https://mvnrepository.com/artifact/com.marcosbarbero.cloud/spring-cloud-zuul-ratelimit -->
		<!-- 		<dependency>
                    <groupId>com.marcosbarbero.cloud</groupId>
                    <artifactId>spring-cloud-zuul-ratelimit</artifactId>
                    <version>2.4.1.RELEASE</version>
                </dependency> -->

		<!-- Square -->
		<dependency>
			<groupId>com.squareup</groupId>
			<artifactId>square</artifactId>
			<version>31.0.0.20230720</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Paypal -->
		<dependency>
			<groupId>com.paypal.sdk</groupId>
			<artifactId>checkout-sdk</artifactId>
			<version>LATEST</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/net.lingala.zip4j/zip4j -->
		<dependency>
			<groupId>net.lingala.zip4j</groupId>
			<artifactId>zip4j</artifactId>
			<version>2.11.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.opencsv/opencsv -->
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>4.1</version>
		</dependency>

		<!--Web service client -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web-services</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.hierynomus</groupId>
			<artifactId>sshj</artifactId>
			<version>0.27.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api -->
		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>2.3.1</version>
		</dependency>

		<!-- JAXB API only -->
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>4.0.0</version>
		</dependency>

		<!-- JAXB Implementation -->
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-impl</artifactId>
			<version>3.0.0</version>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
		</dependency>

		<dependency>
			<groupId>com.eatthepath</groupId>
			<artifactId>pushy</artifactId>
			<version>0.15.4</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>3.0.0</version>
				<executions>
					<execution>
						<id>copy-mainroot</id>
						<phase>initialize</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<copy todir="${basedir}/src/main/resources/schema/acctint">
									<fileset dir="${basedir}/src/main/resources/schema">
										<include name="*.graphqls" />
									</fileset>
								</copy>
								<copy todir="${basedir}/src/main/resources/schema/billing">
									<fileset dir="${basedir}/src/main/resources/schema">
										<include name="*.graphqls" />
									</fileset>
								</copy>
								<copy todir="${basedir}/src/main/resources/schema/contractor">
									<fileset dir="${basedir}/src/main/resources/schema">
										<include name="*.graphqls" />
									</fileset>
								</copy>
								<copy todir="${basedir}/src/main/resources/schema/customer">
									<fileset dir="${basedir}/src/main/resources/schema">
										<include name="*.graphqls" />
									</fileset>
								</copy>
								<copy todir="${basedir}/src/main/resources/schema/domain">
									<fileset dir="${basedir}/src/main/resources/schema">
										<include name="*.graphqls" />
									</fileset>
								</copy>
								<copy todir="${basedir}/src/main/resources/schema/efs">
									<fileset dir="${basedir}/src/main/resources/schema">
										<include name="*.graphqls" />
									</fileset>
								</copy>
								<copy todir="${basedir}/src/main/resources/schema/employee">
									<fileset dir="${basedir}/src/main/resources/schema">
										<include name="*.graphqls" />
									</fileset>
								</copy>
							</target>
						</configuration>
					</execution>

					<execution>
						<id>delete-mainroot</id>
						<phase>process-sources</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<delete>
									<fileset dir="${basedir}/src/main/resources/schema" includes="*/mainroot.graphqls" />
								</delete>
							</target>
						</configuration>
					</execution>

				</executions>
			</plugin>



			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.flywaydb</groupId>
				<artifactId>flyway-maven-plugin</artifactId>
				<configuration>
					<user>root</user>
					<password></password>
					<url>*********************************************************</url>
					<placeholderReplacement>false</placeholderReplacement>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<forceJavacCompilerUse>true</forceJavacCompilerUse>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.3.1.Final</version>
						</path>
					</annotationProcessorPaths>
					<compilerArgs>
						<compilerArg>
							-Amapstruct.suppressGeneratorTimestamp=true
						</compilerArg>
						<compilerArg>
							-Amapstruct.suppressGeneratorVersionInfoComment=true
						</compilerArg>
					</compilerArgs>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>target/generated-graphql</source>
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>io.github.deweyjose</groupId>
				<artifactId>graphqlcodegen-maven-plugin</artifactId>
				<version>1.30</version>
				<executions>
					<execution>
						<id>acctint</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<schemaPaths>
								<param>src/main/resources/schema/acctint/mainroot.graphqls</param>
								<param>src/main/resources/schema/acctint/root.graphqls</param>
								<param>src/main/resources/schema/acctint/types.graphqls</param>
							</schemaPaths>
							<generateClientApi>true</generateClientApi>
							<packageName>com.aphe.acctint</packageName>
							<outputDir>${basedir/target/generated-graphql}</outputDir>
							<addGeneratedAnnotation>true</addGeneratedAnnotation>
						</configuration>
					</execution>
					<execution>
						<id>billing</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<schemaPaths>
								<param>src/main/resources/schema/billing/mainroot.graphqls</param>
								<param>src/main/resources/schema/billing/root.graphqls</param>
								<param>src/main/resources/schema/billing/types.graphqls</param>
							</schemaPaths>
							<generateClientApi>true</generateClientApi>
							<packageName>com.aphe.billing</packageName>
							<outputDir>${basedir/target/generated-graphql}</outputDir>
							<addGeneratedAnnotation>true</addGeneratedAnnotation>
						</configuration>
					</execution>
					<execution>
						<id>contractor</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<schemaPaths>
								<param>src/main/resources/schema/contractor/mainroot.graphqls</param>
								<param>src/main/resources/schema/contractor/root.graphqls</param>
								<param>src/main/resources/schema/contractor/types.graphqls</param>
							</schemaPaths>
							<generateClientApi>true</generateClientApi>
							<packageName>com.aphe.contractor</packageName>
							<outputDir>${basedir/target/generated-graphql}</outputDir>
							<addGeneratedAnnotation>true</addGeneratedAnnotation>
						</configuration>
					</execution>
					<execution>
						<id>customer</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<schemaPaths>
								<param>src/main/resources/schema/customer/mainroot.graphqls</param>
								<param>src/main/resources/schema/customer/root.graphqls</param>
								<param>src/main/resources/schema/customer/types.graphqls</param>
							</schemaPaths>
							<generateClientApi>true</generateClientApi>
							<packageName>com.aphe.customer</packageName>
							<outputDir>${basedir/target/generated-graphql}</outputDir>
							<addGeneratedAnnotation>true</addGeneratedAnnotation>
						</configuration>
					</execution>
					<execution>
						<id>domain</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<schemaPaths>
								<param>src/main/resources/schema/domain/mainroot.graphqls</param>
								<param>src/main/resources/schema/domain/root.graphqls</param>
								<param>src/main/resources/schema/domain/types.graphqls</param>
							</schemaPaths>
							<generateClientApi>true</generateClientApi>
							<packageName>com.aphe.domain</packageName>
							<outputDir>${basedir/target/generated-graphql}</outputDir>
							<addGeneratedAnnotation>true</addGeneratedAnnotation>
						</configuration>
					</execution>
					<execution>
						<id>efs</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<schemaPaths>
								<param>src/main/resources/schema/efs/mainroot.graphqls</param>
								<param>src/main/resources/schema/efs/root.graphqls</param>
								<param>src/main/resources/schema/efs/types.graphqls</param>
							</schemaPaths>
							<generateClientApi>true</generateClientApi>
							<packageName>com.aphe.efs</packageName>
							<outputDir>${basedir/target/generated-graphql}</outputDir>
							<addGeneratedAnnotation>true</addGeneratedAnnotation>
						</configuration>
					</execution>
					<execution>
						<id>employee</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<schemaPaths>
								<param>src/main/resources/schema/employee/mainroot.graphqls</param>
								<param>src/main/resources/schema/employee/root.graphqls</param>
								<param>src/main/resources/schema/employee/types.graphqls</param>
							</schemaPaths>
							<generateClientApi>true</generateClientApi>
							<packageName>com.aphe.employee</packageName>
							<outputDir>${basedir/target/generated-graphql}</outputDir>
							<addGeneratedAnnotation>true</addGeneratedAnnotation>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- tag::wsdl[] -->
			<plugin>
				<groupId>org.jvnet.jaxb2.maven2</groupId>
				<artifactId>maven-jaxb2-plugin</artifactId>
				<version>0.14.0</version>
				<executions>
					<execution>
						<goals>
							<goal>generate</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<schemaLanguage>WSDL</schemaLanguage>
					<generatePackage>com.aphe.trclients.nd.wsdl</generatePackage>
					<schemas>
						<schema>
<!--							<url>https://test.apps.nd.gov/tax/ElectronicUpload/?wsdl</url>-->
							<url>https://apps.nd.gov/tax/ElectronicUpload/?wsdl</url>
						</schema>
					</schemas>
				</configuration>
			</plugin>
			<!-- end::wsdl[] -->


			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>parse-version</id>
						<goals>
							<goal>parse-version</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>versions-maven-plugin</artifactId>
				<version>2.18.0</version>
			</plugin>

		</plugins>
	</build>

	<profiles>
		<profile>
			<id>bump-patch</id>
			<activation>
				<property>
					<name>bumpPatch</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>versions-maven-plugin</artifactId>

						<executions>
							<execution>
								<goals>
									<goal>set</goal>
								</goals>
								<phase>validate</phase>
								<configuration>
									<newVersion>${parsedVersion.majorVersion}.${parsedVersion.minorVersion}.${parsedVersion.nextIncrementalVersion}</newVersion>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>


</project>
