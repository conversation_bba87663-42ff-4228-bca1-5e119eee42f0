cd ../springboot-common
git pull
mvn clean install -D skipTests

cd ../auth
git pull

# First bump the version
mvn validate -DbumpPatch

# Build and install the project
mvn clean install

# check the build status
if [ $? -ne 0 ]; then
    echo "Build failed. Exiting."

    # Revert the version bump
    git checkout pom.xml
    exit 1
fi

echo "Build successful. Proceeding with the next steps."

# Commit the version bump
git add pom.xml
git commit -m "Bump Patch"
git push

scp target/auth-*.jar <EMAIL>:/tmp/

# Check if the scp command was successful
if [ $? -ne 0 ]; then
    echo "File transfer failed. Exiting."
    exit 1
fi

echo "File transfer completed successfully. Now building the Docker image on the remote server."

#Find the version number of the jar from the name pattern for auht-1.0.0.jar 
VERSION=$(ls target/auth-*.jar | sed -E 's/.*auth-([0-9]+\.[0-9]+\.[0-9]+)\.jar/\1/')
echo "Version: $VERSION"


#Run a command over SSH to build the Docker image and wait for it to complete
ssh <EMAIL> "cd ~/projects/dockerimages/springkube && ./buildscript.sh auth $VERSION"
if [ $? -ne 0 ]; then
    echo "Docker image build failed with error code $?"
    exit 1
fi

echo "Docker image built successfully."


