package com.aphe.wavesdk;

import com.aphe.wavesdk.connections.BusinessConnection;
import com.aphe.wavesdk.model.Business;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@NoArgsConstructor
public class WaveData {
    private BusinessConnection businesses;
    private Business business;

    public List<Business> getBusinesses() {
        return businesses.getNodes();
    }
    public boolean hasMoreBusinesses() {
        return businesses.hasMorePages();
    }

}
