package com.aphe.bdcsdk.model;


import com.aphe.bdcsdk.model.enums.VendorAccountType;
import com.aphe.bdcsdk.model.enums.PaymentType;
import com.aphe.bdcsdk.model.enums.TaxIdType;

import java.math.BigDecimal;
import java.util.Date;

public class Vendor {
    public String entity;
    public String id;
    public int isActive;
    public String name;
    public String shortName;
    public String nameOnCheck;
    public String companyName;
    public String accNumber;
    public String taxId;
    public int taxIdType;

    public TaxIdType getTaxIdType() {
        return TaxIdType.getByCode(taxIdType);
    }

    public boolean track1099;
    public String address1;
    public String address2;
    public String address3;
    public String address4;
    public String addressCity;
    public String addressState;
    public String addressZip;
    public String addressCountry;
    public String email;
    public String fax;
    public String phone;


    public int payBy;

    public PaymentType getPayBy() {
        return PaymentType.getByCode(payBy);
    }

    public String paymentEmail;
    public String paymentPhone;
    public String description;
    public Date createdTime;  // "2016-09-20T16:39:41.000+0000",
    public Date updatedTime;  // "2016-12-13T18:46:45.000+0000",
    public String mergedIntoId;
    public String contactFirstName;
    public String contactLastName;
    public int accountType;

    public VendorAccountType getAccountType() {
        return VendorAccountType.getByCode(accountType);
    }

    public BigDecimal balance;
    public Date lastBalanceUpdate;

}