package com.aphe.bdcsdk.model.enums;

public enum VendorAccountType {
    NONE(0, "None"),
    BUSINESS(1, "Business"),
    PERSONAL(2, "Personal");

    int code;
    String name;

    private VendorAccountType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static VendorAccountType getByCode(final int byCode) {
        for (final VendorAccountType accountType : values())
            if (accountType.code == byCode)
                return accountType;
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
