package com.aphe.bdcsdk.model.enums;

public enum PaymentType {
    Check(0, "Check"),
    ACH(1, "ACH"),
    RPPS(2, "RPPS"),
    PayPal(3, "PayPal"),
    Other(4, "Other"),
    IntlEPmt(5, "IntlEPmt"),
    Amex(6, "Amex"),
    VCard(7, "VCard"),
    Wallet(11, "Wallet"),
    CreditCard(12, "CreditCard");

    int code;
    String name;

    private PaymentType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PaymentType getByCode(final int byCode)
    {
        for (final PaymentType accountType : values())
            if (accountType.code == byCode)
                return accountType;
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
