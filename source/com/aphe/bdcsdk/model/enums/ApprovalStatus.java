package com.aphe.bdcsdk.model.enums;

public enum ApprovalStatus {
    UNASSIGNED(0, "Unassigned"),
    ASSIGNED(1, "Assigned"),
    APPROVING(4, "Approving"),
    APPROVED(3, "Approved"),
    DENIED(5, "Denied");

    int code;
    String name;

    private ApprovalStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ApprovalStatus getByCode(final int byCode) {
        for (final ApprovalStatus accountType : values())
            if (accountType.code == byCode)
                return accountType;
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
