package com.aphe.bdcsdk.query.filters;

import com.google.common.base.Joiner;
import org.json.JSONObject;

import java.util.List;


public class NotInFilter implements Filter {
    String field;
    List<String> values;

    public NotInFilter(String field, List<String> values) {
        this.field = field;
        this.values = values;
    }

    public JSONObject build() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("field", field);
        jsonObject.put("op", "nin");
        jsonObject.put("value",  Joiner.on(",").join(this.values));
        return jsonObject;
    }
}

//curl --request GET \
//        --url 'https://base_url/api/activity?where=firstNameLIKE%22cha%22' \
//        --header 'accept: application/json' \
//        --header 'authorization: Bearer [YOUR_ACCESS_TOKEN]'
//
//curl --request GET \
//        --url 'https://base_url/api/activity?where=isActive%3DtrueANDid%3D%22d13250e6-0947-48ca-9540-14e2fa324d1b%22' \
//        --header 'accept: application/json' \
//        --header 'authorization: Bearer [YOUR_ACCESS_TOKEN]'
//
//
//curl --request GET \
//        --url 'https://base_url/api/activity?where=isActive%3DtrueORid%3D%22d13250e6-0947-48ca-9540-14e2fa324d1b%22' \
//        --header 'accept: application/json' \
//        --header 'authorization: Bearer [YOUR_ACCESS_TOKEN]'