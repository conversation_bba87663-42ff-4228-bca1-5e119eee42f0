package com.aphe.bqecoresdk.api;

import com.aphe.bqecoresdk.query.QueryBuilder;
import com.aphe.bqecoresdk.exceptions.BQECoreException;
import com.aphe.bqecoresdk.model.Company;
import com.aphe.bqecoresdk.util.BQCCoreEntityManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;
import java.util.List;

public class CompanyManager extends BQCCoreEntityManager<Company> {

    public CompanyManager(String baseURL, String accessToken) {
        super(baseURL, accessToken);
    }

    @Override
    public List<Company> getList(List<QueryBuilder> builders) throws BQECoreException {
        throw new RuntimeException("Get list not supported on company");
    }

    @Override
    protected String getOnePath(String id) {
        return "company";
    }

    @Override
    protected String getListPath() {
        throw new RuntimeException("Get list not supported on company");
    }

    @Override
    protected List<Company> convertToList(ObjectMapper mapper, String json) throws BQECoreException {
        try {
            return Arrays.asList(mapper.readValue(json, Company[].class));
        } catch (JsonProcessingException e) {
            throw new BQECoreException(e.getMessage());
        }
    }

    @Override
    protected Company convertToOne(ObjectMapper mapper, String json) throws BQECoreException {
        try {
            return mapper.readValue(json, Company.class);
        } catch (JsonProcessingException e) {
            throw new BQECoreException(e.getMessage());
        }
    }
}
