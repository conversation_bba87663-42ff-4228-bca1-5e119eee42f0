package com.aphe.bqecoresdk.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

public class RestTemplateRequestLogger implements ClientHttpRequestInterceptor {

	private final Logger log = LoggerFactory.getLogger(this.getClass());

	@Override
	public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) {
		ClientHttpResponse response = null;
		HttpStatus responseCode = null;
		long t1 = System.currentTimeMillis();
		try{
			response = execution.execute(request, body);
			responseCode = response.getStatusCode();
		}catch (Exception e) {
			log.error("<PERSON>rror executing rest template call", e);
		}finally{
			long t2 = System.currentTimeMillis();
			long diff = t2 - t1;
			log.info("req_dir=Outbound req_uri={} req_method={} req_res_code={} req_res_time={} req_start_time={} req_end_time={}",
					request.getURI(), request.getMethod(), responseCode, diff, t1, t2);
		}
		return response;
	}

}