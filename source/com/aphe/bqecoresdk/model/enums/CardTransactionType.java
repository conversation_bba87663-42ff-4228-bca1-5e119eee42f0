package com.aphe.bqecoresdk.model.enums;

public enum CardTransactionType {
    PURCHASE(0, "Card transaction type is purchase"),
    REFUND(1, "Card transaction type is refund"),
    BILL_PAYMENT(2, "Card transaction type is bill payment");

    int code;
    String name;

    private CardTransactionType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CardTransactionType getByCode(final int code)
    {
        for (final CardTransactionType vendorType : values())
            if (vendorType.code == code)
                return vendorType;
        return null;
    }


}
