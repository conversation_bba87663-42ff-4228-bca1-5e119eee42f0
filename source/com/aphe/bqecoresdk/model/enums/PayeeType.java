package com.aphe.bqecoresdk.model.enums;

public enum PayeeType {
    NO_PAYEE(0, "<PERSON><PERSON>or is of type vendor"),
    CLIENT(6, "<PERSON><PERSON><PERSON> is of type contract employee"),
    EMPLOYEE(9, "<PERSON><PERSON>or is of type outside consultant"),
    PROJEC<PERSON>(16, "<PERSON><PERSON><PERSON> is of type outside consultant"),
    <PERSON><PERSON>D<PERSON>(29, "<PERSON><PERSON><PERSON> is of type outside consultant"),
    CHECK(59, "<PERSON><PERSON><PERSON> is of type outside consultant"),
    CREDIT_CARD(66, "<PERSON><PERSON><PERSON> is of type outside consultant"),
    EMPLOYEE_GROUP(74, "<PERSON><PERSON>or is of type outside consultant"),
    CONTACT(76, "<PERSON><PERSON>or is of type outside consultant"),
    <PERSON><PERSON>DOR_GROUP(136, "<PERSON><PERSON>or is of type outside consultant"),
    <PERSON><PERSON><PERSON>NT_GROUP(137, "<PERSON><PERSON>or is of type outside consultant"),
    CLIENT_CONTACT(149, "<PERSON><PERSON>or is of type outside consultant");


    int code;
    String name;

    private PayeeType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PayeeType getByCode(final int code)
    {
        for (final PayeeType vendorType : values())
            if (vendorType.code == code)
                return vendorType;
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
