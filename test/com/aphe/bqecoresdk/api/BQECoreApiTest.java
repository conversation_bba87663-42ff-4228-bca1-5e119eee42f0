package com.aphe.bqecoresdk.api;

import com.aphe.bqecoresdk.model.Account;
import com.aphe.bqecoresdk.model.Bill;
import com.aphe.bqecoresdk.model.Check;
import com.aphe.bqecoresdk.model.CreditCard;
import com.aphe.bqecoresdk.query.ExpandQueryBuilder;
import com.aphe.bqecoresdk.query.FilterQueryBuilder;
import com.aphe.bqecoresdk.query.QueryBuilder;
import com.aphe.bqecoresdk.query.SortQueryBuilder;
import com.aphe.bqecoresdk.query.filters.FilterOperator;
import org.junit.jupiter.api.Test;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BQECoreApiTest {

    public static final SimpleDateFormat ISO_8601_FORMAT = new SimpleDateFormat("YYYY-MM-dd'T'HH:mm:ss.sssZ");
    private static String baseURL = "https://api.bqecore.com/api/";

    private static String accessToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6IjI4MzQ0Qzg0RUNBMzdBRjM1MzVDQjQ3QUEyMzJERDIwOEZGQ0QxMDgiLCJ0eXAiOiJKV1QiLCJ4NXQiOiJLRFJNaE95amV2TlRYTFI2b2pMZElJXzgwUWcifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bq0mYY475PjHkPceP0CmX__mss6b9K_nzqDDvT02NfQeOtyhlrisqBM5Oh2ZtLCm6eWLjcGJH66kt-myAtBS3QAITV6Q0AGpmsjhbdFmIl37CcOYL_0FGC-Hs9gSMdJWVbc1SDvc-gUuragKVGvw2X5Nao7itmaHj7mUlFRJ8FdMsAqxLU7Qyb85yke5ypC-_t2os_I0_yevPYnlPoUAxVEATfL157nkFKLf6XJBw-xg4GI_SAHGXljtLVqHBhmJ8gb0CCOoAUv7AR4e213rs9aJgIRs4bCJ0nA5eeUYK08MtV8zW4eKzxBFNfU-XmjJLnW8skjIrQtBSBYouwY_Dg";

    private void printAccounts(List<Account> accounts) {
        accounts.stream().forEach(account -> {
            System.out.printf("%s----%s---%s---%s---%s\n", account.id, account.name, account.code, account.lastUpdated, account.customFields != null ? account.customFields.size() : "?");
        });
    }

    @Test
    void testPaginationFilter() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<Account> accounts = accountManager.getListAll(new ArrayList<>());
        printAccounts(accounts);
    }


    @Test
    void testPaginationSingleSort() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("lastUpdated", SortQueryBuilder.SortOrder.DESC));
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testPaginationDoubleSort() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("lastUpdated", SortQueryBuilder.SortOrder.DESC).add("code", SortQueryBuilder.SortOrder.DESC));
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }


    @Test
    void testPaginationSortExpand() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("lastUpdated", SortQueryBuilder.SortOrder.DESC).add("code", SortQueryBuilder.SortOrder.DESC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testEqualsBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addEquals("isActive", "true");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testEqualsString() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("lastUpdated", SortQueryBuilder.SortOrder.DESC).add("code", SortQueryBuilder.SortOrder.DESC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addEquals("id", "fbd00f3f-657a-4d9e-b01f-fbfa322c417b");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testNotEqualsString() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("lastUpdated", SortQueryBuilder.SortOrder.DESC).add("code", SortQueryBuilder.SortOrder.DESC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addNotEquals("id", "fbd00f3f-657a-4d9e-b01f-fbfa322c417b");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }


    @Test
    void testEqualsInt() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("lastUpdated", SortQueryBuilder.SortOrder.DESC).add("code", SortQueryBuilder.SortOrder.DESC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().addEquals("code", "6238");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testEqualsIntAndBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("lastUpdated", SortQueryBuilder.SortOrder.DESC).add("code", SortQueryBuilder.SortOrder.DESC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.OR)
                .addEquals("code", "6238")
                .addEquals("isActive", "true");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testGreaterThanAndBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addGreatThan("code", "66900");

        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testGreaterThanOREqaualAndBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addGreatThanOrEqual("code", "6238")
                .addEquals("isActive", "true");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testLessThanAndBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addLessThan("code", "6238")
                .addEquals("isActive", "true");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testLessThanOREqualsAndBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addLessThanOrEqual("code", "6238")
                .addEquals("isActive", "true");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }



    @Test
    void testInAndBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addIn("code", Arrays.asList("6238", "6245"))
                .addEquals("isActive", "true");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }



    @Test
    void testNotInAndBoolean() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addNotIn("code", Arrays.asList("6238", "6245"))
                .addEquals("isActive", "true");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testLike() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addLike("code", "623");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }

    @Test
    void testNotLike() throws Exception {
        AccountManager accountManager = new AccountManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        queryBuilders.add(new SortQueryBuilder("code", SortQueryBuilder.SortOrder.ASC));
        queryBuilders.add(new ExpandQueryBuilder("customFields"));
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addNotLike("code", "6238");
        queryBuilders.add(filterQueryBuilder);
        List<Account> accounts = accountManager.getListAll(queryBuilders);
        printAccounts(accounts);
    }
    @Test
    void testGetBills() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken);
        List<Bill> bills = billManager.getListAll(new ArrayList<>());
        printBills(bills);
//        System.out.println(ISO_8601_FORMAT.format(new Date()));
    }

    private void printBills(List<Bill> bills) {
        for (Bill bill : bills) {
            System.out.printf("%s--%s---%s\n", bill.id, ISO_8601_FORMAT.format(bill.date), bill.number);
        }
    }


    @Test
    void testDateGreaterThan() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addGreatThan("date", "2021-01-01");
        queryBuilders.add(filterQueryBuilder);
//        queryBuilders.add(new SortQueryBuilder("date", SortQueryBuilder.SortOrder.ASC));
        List<Bill> bills = billManager.getListAll(queryBuilders);
        printBills(bills);
    }

    @Test
    void testDateGreaterThanEqual() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addGreatThanOrEqual("date", "2022-03-30");
        queryBuilders.add(filterQueryBuilder);
//        queryBuilders.add(new SortQueryBuilder("date", SortQueryBuilder.SortOrder.ASC));
        List<Bill> bills = billManager.getListAll(queryBuilders);
        printBills(bills);
    }


    @Test
    void testDateInBetween() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addBetween("date", "2022-02-28", "2022-03-29");
        queryBuilders.add(filterQueryBuilder);
//        queryBuilders.add(new SortQueryBuilder("date", SortQueryBuilder.SortOrder.ASC));
        List<Bill> bills = billManager.getListAll(queryBuilders);
        printBills(bills);
    }

    @Test
    void testDateInBetweenInclusive() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addBetweenInclusive("date", "2022-03-01", "2022-03-30");
        queryBuilders.add(filterQueryBuilder);
//        queryBuilders.add(new SortQueryBuilder("date", SortQueryBuilder.SortOrder.ASC));
        List<Bill> bills = billManager.getListAll(queryBuilders);
        printBills(bills);
    }

    @Test
    void testNestedQuery() throws Exception {
        BillManager billManager = new BillManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addBetweenInclusive("date", "2022-03-01", "2022-03-30")
                .addGreatThanOrEqual("number", "1001");
        queryBuilders.add(filterQueryBuilder);
//        queryBuilders.add(new SortQueryBuilder("date", SortQueryBuilder.SortOrder.ASC));
        List<Bill> bills = billManager.getListAll(queryBuilders);
        printBills(bills);
    }

    private void printCards(List<CreditCard> car) {
        for (CreditCard creditCard : car) {
            System.out.printf("%s--%s---%s\n", creditCard.id, creditCard.date, creditCard.amount);
        }
    }


    @Test
    void testGetCreditCards() throws Exception {
        CreditCardManager creditCardManager = new CreditCardManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addBetweenInclusive("date", "2021-01-01", "2021-12-31");
//        queryBuilders.add(filterQueryBuilder);
//        queryBuilders.add(new SortQueryBuilder("date", SortQueryBuilder.SortOrder.ASC));
        List<CreditCard> cards = creditCardManager.getListAll(queryBuilders);
        printCards(cards);
    }


    private void printChecks(List<Check> cards) {
        for (Check check : cards) {
            System.out.printf("%s--%s---%s\n", check.id, check.date, check.amount);
        }
    }

    @Test
    void testGetChecks() throws Exception {
        CheckManager checkManager = new CheckManager(baseURL, accessToken);
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder().setOperator(FilterOperator.AND)
                .addBetweenInclusive("date", "2021-01-01", "2021-12-31");
        queryBuilders.add(filterQueryBuilder);
//        queryBuilders.add(new SortQueryBuilder("date", SortQueryBuilder.SortOrder.ASC));
        List<Check> checks = checkManager.getListAll(queryBuilders);
        printChecks(checks);
    }


}


