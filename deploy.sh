#!/bin/bash

# Take environment as an argument
if [ -z "$1" ]; then
    echo "Usage: $0 <environment>"
    exit 1
fi

ENV=$1

#Find the version number of the jar from the name pattern for auht-1.0.0.jar 
VERSION=$(ls target/domain-*.jar | sed -E 's/.*domain-([0-9]+\.[0-9]+\.[0-9]+)\.jar/\1/')


echo "Deploying domain application version $VERSION to environment $ENV"

cd ~/projects/utils/kubescripts

echo "Running migration script for domain application in $ENV environment with version $VERSION"
./migrate.sh domain $ENV $VERSION migrate
if [ $? -ne 0 ]; then
    echo "Migration failed. Exiting."
    exit 1
fi

echo ""
echo ""
echo "Migration completed successfully. Now deploying the application."
./deploy.sh domain $ENV $VERSION

