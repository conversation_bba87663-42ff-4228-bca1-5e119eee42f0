<!doctype html>
<html style="display: flex; justify-content: center; background-color: rgb(245, 245, 245)">   
 <head></head>
 <body style="font-family: &quot;Roboto&quot;, &quot;sans-serif&quot;; font-size: 18px; border: 1px solid rgb(42, 55, 144); border-spacing: 0px; background-color: rgb(255, 255, 255)">  
  <meta charset="UTF-8"> 
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
  <link href="http://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" type="text/css">       
  <div> 
   <div style="display: flex; flex-grow: 1; background-color: rgb(42, 55, 144); border-radius: 0px; border-collapse: separate; padding: 20px; text-align: center; justify-content: center"> 
    <div style="width: 100%; text-align: center; font-size: 36px; color: rgb(255, 255, 255)"> <strong> <span>1099SmartFile</span> </strong> 
    </div> 
   </div> 
  </div>  
  <div style="font-size: 24px; font-weight: bold; font-size: 18px; text-align: center; padding: 10px; text-align: left">
    Hello, <span>John</span>, 
  </div> 
  <div style="font-size: 18px; text-align: center; padding: 10px; text-align: left">
    The following 1099 forms couldn't be processed by our system. Please review the failure reason, fix errors and submit them again. You will not be charged for re-submissions. Please don't hesitate to contact us if you have any questions. You can find the latest status of all your filings from the filings tab of the app at <a href="">https://app.1099smartfile.com/filings</a> 
  </div> 
  <div style="font-size: 18px; text-align: center; padding: 10px; text-align: left"> 
   <div> 
    <table style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px"> 
     <tbody>
      <tr> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Payee Name</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Payee Address</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Form Type</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Correction Type</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Fed eFile Status</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">State Filing Status</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Email Status</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Print Status</th> 
       <th style="border: 1px solid rgb(42, 55, 144); background-color: rgb(42, 55, 144); color: rgb(255, 255, 255); border-spacing: 0px">Form Data</th> 
      </tr> 
     </tbody>
    </table> 
   </div> 
  </div>    
  <div> 
   <div style="font-size: 18px; text-align: center; padding: 10px; text-align: left">
     Best Regards,
    <br> <span>1099SmartFile Support Team</span> 
   </div> 
   <div style="text-align: center; font-size: 14px; padding: 10px"> <a href="https://app.1099smartfile.com"> <span>https://app.1099smartfile.com</span> </a> 
    <br> <span>412 Arlewood Ct</span> <span>San Ramon</span> <span>CA</span> <span>94582</span> 
    <br> <span><EMAIL></span> 
   </div> 
   <div style="text-align: center; font-size: 14px; padding: 10px">
     This email was sent to <span></span> 
    <br> You received this email because you are registered with <span>1099SmartFile</span> and have an active account. If you wish not to receive these emails, please log in into your account and deactivate your account. 
   </div> 
   <div style="display: flex; flex-grow: 1; background-color: rgb(42, 55, 144); border-radius: 0px; border-collapse: separate; padding: 5px; justify-content: center"> 
    <div style="width: 100%; text-align: center; font-size: 14px; color: rgb(255, 255, 255)">
      © 2023 <span>1099SmartFile</span> 
    </div> 
   </div> 
  </div>   
 </body>
</html>