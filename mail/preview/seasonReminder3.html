<!doctype html>
<html style="display: flex; justify-content: center; background-color: rgb(245, 245, 245)">   
 <head></head>
 <body style="font-family: &quot;Roboto&quot;, &quot;sans-serif&quot;; font-size: 18px; border: 1px solid rgb(42, 55, 144); border-spacing: 0px; background-color: rgb(255, 255, 255)">  
  <meta charset="UTF-8"> 
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
  <link href="http://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" type="text/css">       
  <div> 
   <div style="display: flex; flex-grow: 1; background-color: rgb(42, 55, 144); border-radius: 0px; border-collapse: separate; padding: 20px; text-align: center; justify-content: center"> 
    <div style="width: 100%; text-align: center; font-size: 36px; color: rgb(255, 255, 255)"> <strong> <span>1099SmartFile</span> </strong> 
    </div> 
   </div> 
  </div>  
  <div style="font-size: 18px; text-align: center; padding: 10px; text-align: center"> 
   <div style="text-align: center; font-size: 30px; padding: 40px"> <strong> It's just 7 days to file your 1099 forms </strong> 
    <br> 
    <p style="display: inline; font-weight: bold; color: red">Due date: Jan 31<sup>st</sup></p> 
   </div> 
  </div> 
  <div style="font-size: 18px; text-align: center; padding: 10px"> <a href="https://app.1099smartfile.com" style="color: rgb(255, 255, 255); text-decoration: none; text-align: center; font-size: 30px; padding: 5px 10px; border-radius: 4px; text-align: center; font-size: 36px; background-color: rgb(42, 55, 144); color: rgb(255, 255, 255)"> Prepare your forms </a> 
  </div> 
  <div style="font-size: 18px; text-align: center; padding: 10px; text-align: left"> 
   <table id="deadlines" style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none"> 
    <tbody>
     <tr>
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px">&nbsp;</td>
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px"></td>
     </tr> 
     <tr> 
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px"> Print &amp; Mail Cutoff Time<br> <span style="font-size: 12px">(to have recipient copies postmarked by 31<sup>st</sup>)</span> </td> 
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px"> <p style="display: inline; font-weight: bold; color: red"> <span style="white-space: nowrap">Jan 26<sup>th</sup> 2023 &nbsp;&nbsp;</span> <span style="white-space: nowrap">3:00 PM PST</span> </p> </td> 
     </tr> 
     <tr>
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px">&nbsp;</td>
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px"></td>
     </tr> 
     <tr> 
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px"> eFile Cutoff Time<br> <span style="font-size: 12px">(to eFile with IRS on time)</span> </td> 
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px"> <p style="display: inline; font-weight: bold; color: red"> <span style="white-space: nowrap">Jan 31<sup>st</sup> 2023 &nbsp;&nbsp;</span> <span style="white-space: nowrap">12:00 noon PST</span> </p> </td> 
     </tr> 
     <tr>
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px">&nbsp;</td>
      <td style="border: 1px solid rgb(42, 55, 144); border-spacing: 0px; border: none; padding-right: 15px"></td>
     </tr> 
    </tbody>
   </table> 
  </div> 
  <div style="font-size: 18px; text-align: center; padding: 10px; text-align: justify">
    If you paid more than $600 to independent contractors during the course of the year 2022, you are required to report those payments to the IRS and issue a paper copy of 1099 form to the recipients that is postmarked by Jan 31<sup>st</sup>. 
   <br> 
   <br> This year, take care of all your 1099 form filing needs with 1099SmartFile. With 1099SmartFile, you can bulk import your filing data from Excel or QuickBooks, and use our full service experience to eFile with the IRS and issue paper copies to your recipients. 
  </div> 
  <div> 
   <br> 
  </div> 
  <div style="font-size: 18px; text-align: center; padding: 10px"> <a href="https://app.1099smartfile.com" style="color: rgb(255, 255, 255); text-decoration: none; text-align: center; font-size: 30px; padding: 5px 10px; border-radius: 4px; text-align: center; font-size: 36px; background-color: rgb(42, 55, 144); color: rgb(255, 255, 255)"> Get started </a> 
   <br> 
  </div>    
  <div> 
   <div style="font-size: 18px; text-align: center; padding: 10px; text-align: left">
     Best Regards,
    <br> <span>1099SmartFile Support Team</span> 
   </div> 
   <div style="text-align: center; font-size: 14px; padding: 10px"> <a href="https://app.1099smartfile.com"> <span>https://app.1099smartfile.com</span> </a> 
    <br> <span>412 Arlewood Ct</span> <span>San Ramon</span> <span>CA</span> <span>94582</span> 
    <br> <span><EMAIL></span> 
   </div> 
   <div style="text-align: center; font-size: 14px; padding: 10px">
     This email was sent to <span></span> 
    <br> You received this email because you are registered with <span>1099SmartFile</span> and have an active account. If you wish not to receive these emails, please log in into your account and deactivate your account. 
   </div> 
   <div style="display: flex; flex-grow: 1; background-color: rgb(42, 55, 144); border-radius: 0px; border-collapse: separate; padding: 5px; justify-content: center"> 
    <div style="width: 100%; text-align: center; font-size: 14px; color: rgb(255, 255, 255)">
      © 2023 <span>1099SmartFile</span> 
    </div> 
   </div> 
  </div>   
 </body>
</html>