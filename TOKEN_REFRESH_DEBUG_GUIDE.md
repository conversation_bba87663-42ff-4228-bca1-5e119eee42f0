# Token Refresh Job Debug Guide

## Overview
This guide provides logging identifiers and search patterns to debug token refresh job issues in production with multiple nodes.

## Log Search Identifiers

### Primary Search Patterns

#### 1. **TOKEN_REFRESH_ORCHESTRATOR** - Main orchestration logs
```bash
# Search for all orchestrator activity
grep "TOKEN_REFRESH_ORCHESTRATOR" /path/to/logs

# Search for specific run
grep "TOKEN_REFRESH_ORCHESTRATOR.*RunId: refresh:run:12345" /path/to/logs

# Search for failures
grep "TOKEN_REFRESH_ORCHESTRATOR.*FAILED" /path/to/logs
```

#### 2. **TOKEN_REFRESH_JOB** - Individual chunk processing logs
```bash
# Search for all job activity
grep "TOKEN_REFRESH_JOB" /path/to/logs

# Search for specific chunk
grep "TOKEN_REFRESH_JOB.*ChunkId: chunk:abcd1234" /path/to/logs

# Search for integration processing
grep "TOKEN_REFRESH_JOB.*Integration:" /path/to/logs
```

#### 3. **TOKEN_REFRESH_RESULT_STORE** - Redis operations logs
```bash
# Search for all result store activity
grep "TOKEN_REFRESH_RESULT_STORE" /path/to/logs

# Search for Redis failures
grep "TOKEN_REFRESH_RESULT_STORE.*FAILED" /path/to/logs
```

### Node-Specific Debugging

#### Find which node processed what
```bash
# Search by node hostname
grep "Node: prod-auth-node-1" /path/to/logs

# Search by node for specific run
grep "RunId: refresh:run:12345.*Node: prod-auth-node-1" /path/to/logs
```

### Run-Specific Debugging

#### Track a complete run from start to finish
```bash
# Replace 'refresh:run:12345' with actual run ID
RUN_ID="refresh:run:12345"

# 1. Orchestrator start
grep "TOKEN_REFRESH_ORCHESTRATOR.*RunId: $RUN_ID.*STARTING" /path/to/logs

# 2. Chunk enqueueing
grep "TOKEN_REFRESH_ORCHESTRATOR.*RunId: $RUN_ID.*Enqueueing chunk" /path/to/logs

# 3. Chunk processing
grep "TOKEN_REFRESH_JOB.*RunId: $RUN_ID" /path/to/logs

# 4. Completion status
grep "TOKEN_REFRESH_JOB.*RunId: $RUN_ID.*completion status" /path/to/logs
```

### Integration-Specific Debugging

#### Track specific integration processing
```bash
# Replace with actual integration ID
INTEGRATION_ID="12345"

# Find which chunk processed this integration
grep "TOKEN_REFRESH_JOB.*Integration: $INTEGRATION_ID" /path/to/logs

# Find the processing details
grep "TOKEN_REFRESH_JOB.*Integration: $INTEGRATION_ID.*PROCESSING" /path/to/logs

# Find the result
grep "TOKEN_REFRESH_JOB.*Integration: $INTEGRATION_ID.*(SUCCESS|FAILED)" /path/to/logs
```

## Common Failure Patterns

### 1. **Orchestrator Failures**
```bash
# No integrations found
grep "TOKEN_REFRESH_ORCHESTRATOR.*No OAuth integrations found" /path/to/logs

# Database query issues
grep "TOKEN_REFRESH_ORCHESTRATOR.*Found.*OAuth integrations.*query took" /path/to/logs

# Job enqueueing failures
grep "TOKEN_REFRESH_ORCHESTRATOR.*FAILED to enqueue chunk" /path/to/logs
```

### 2. **Job Processing Failures**
```bash
# Integration not found in database
grep "TOKEN_REFRESH_JOB.*No integration data found" /path/to/logs

# Domain lookup failures
grep "TOKEN_REFRESH_JOB.*Domain.*not found" /path/to/logs

# Token refresh failures
grep "TOKEN_REFRESH_JOB.*FAILED.*Token refresh" /path/to/logs

# Authentication issues
grep "TOKEN_REFRESH_JOB.*Error cleaning authentication" /path/to/logs
```

### 3. **Redis/Result Store Failures**
```bash
# Redis connection issues
grep "TOKEN_REFRESH_RESULT_STORE.*FAILED" /path/to/logs

# Chunk counting issues
grep "TOKEN_REFRESH_RESULT_STORE.*Expected chunks key not found" /path/to/logs

# Status saving failures
grep "TOKEN_REFRESH_RESULT_STORE.*FAILED to save token status" /path/to/logs
```

## Performance Analysis

### Timing Analysis
```bash
# Database query performance
grep "TOKEN_REFRESH_ORCHESTRATOR.*query took.*ms" /path/to/logs

# Domain lookup performance
grep "TOKEN_REFRESH_JOB.*Domain lookup took.*ms" /path/to/logs

# Token refresh performance
grep "TOKEN_REFRESH_JOB.*Token refresh attempt completed in.*ms" /path/to/logs

# Redis operation performance
grep "TOKEN_REFRESH_RESULT_STORE.*took.*ms" /path/to/logs
```

### Success/Failure Rates
```bash
# Chunk completion summary
grep "TOKEN_REFRESH_JOB.*COMPLETED chunk processing" /path/to/logs

# Individual integration results
grep "TOKEN_REFRESH_JOB.*(SUCCESS|FAILED):" /path/to/logs
```

## Multi-Node Analysis

### Node Distribution
```bash
# See which nodes are processing chunks
grep "TOKEN_REFRESH_JOB.*STARTING chunk processing" /path/to/logs | grep -o "Node: [^,]*" | sort | uniq -c

# Check for node-specific failures
grep "TOKEN_REFRESH_JOB.*FAILED" /path/to/logs | grep -o "Node: [^,]*" | sort | uniq -c
```

### Chunk Distribution
```bash
# See chunk processing distribution
grep "TOKEN_REFRESH_JOB.*STARTING chunk processing" /path/to/logs | grep -o "with [0-9]* integration IDs" | sort | uniq -c
```

## MDC (Mapped Diagnostic Context) Fields

The following MDC fields are set for correlation:

- **runId**: Unique identifier for each orchestration run
- **chunkId**: Unique identifier for each chunk processing
- **nodeId**: Hostname/identifier of the processing node
- **operation**: Type of operation (token-refresh-orchestrator, token-refresh-chunk)

### Using MDC for Correlation
```bash
# If your logging framework supports MDC, you can search by:
grep "runId=refresh:run:12345" /path/to/logs
grep "chunkId=chunk:abcd1234" /path/to/logs
grep "nodeId=prod-auth-node-1" /path/to/logs
```

## Troubleshooting Checklist

### When Job Fails to Start
1. Check orchestrator initialization: `grep "TOKEN_REFRESH_ORCHESTRATOR.*Initialized" /path/to/logs`
2. Check cron trigger: `grep "TOKEN_REFRESH_ORCHESTRATOR.*STARTING token refresh" /path/to/logs`
3. Check database connectivity: Look for query timing logs

### When Chunks Don't Process
1. Check job enqueueing: `grep "TOKEN_REFRESH_ORCHESTRATOR.*Successfully enqueued chunk" /path/to/logs`
2. Check JobRunr queue: Look for JobRunr-specific logs
3. Check chunk initialization: `grep "TOKEN_REFRESH_JOB.*STARTING chunk processing" /path/to/logs`

### When Individual Integrations Fail
1. Check integration data: `grep "TOKEN_REFRESH_JOB.*Prepared integration" /path/to/logs`
2. Check domain lookup: `grep "TOKEN_REFRESH_JOB.*Found domain" /path/to/logs`
3. Check token refresh: `grep "TOKEN_REFRESH_JOB.*Hard refresh completed" /path/to/logs`

### When Summary Report Doesn't Send
1. Check chunk completion: `grep "TOKEN_REFRESH_JOB.*All chunks completed" /path/to/logs`
2. Check summary job enqueueing: `grep "TOKEN_REFRESH_JOB.*Successfully enqueued summary report" /path/to/logs`

## Example Debug Session

```bash
# 1. Find the latest run
LATEST_RUN=$(grep "TOKEN_REFRESH_ORCHESTRATOR.*STARTING token refresh" /path/to/logs | tail -1 | grep -o "RunId: [^,]*" | cut -d' ' -f2)

# 2. Check orchestrator completion
grep "TOKEN_REFRESH_ORCHESTRATOR.*RunId: $LATEST_RUN.*COMPLETED" /path/to/logs

# 3. Check how many chunks were created
grep "TOKEN_REFRESH_ORCHESTRATOR.*RunId: $LATEST_RUN.*Will create.*chunks" /path/to/logs

# 4. Check chunk processing
grep "TOKEN_REFRESH_JOB.*RunId: $LATEST_RUN.*STARTING chunk" /path/to/logs

# 5. Check completion status
grep "TOKEN_REFRESH_JOB.*RunId: $LATEST_RUN.*completion status" /path/to/logs

# 6. Check for any failures
grep "TOKEN_REFRESH.*RunId: $LATEST_RUN.*FAILED" /path/to/logs
```

This comprehensive logging should help you identify exactly where the token refresh job is failing in your production environment.
