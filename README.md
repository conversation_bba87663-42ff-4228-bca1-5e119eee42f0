# EFile-1099: Back-end capabbility to file a 1099 form using FIRE system.

## How to Use it
1. Read the rest API docs.
1. Read the GraphQL APIs


## How to add support for a new form
1. Follow the instructions in pdfgenutil to add a support for PDF form.

1. Identify the fields we need to collect from the User to a file this form. The elements needed to fill the PDF form and elements needed to file using FIRE

1. Create a new input object with this form specific fields. Even these fields belong to payer or payee, store them at the filing data level, to keep Payer and Payee simple.

1. Update the FilingDataMapper to all the mapping from dtoToJSON and jsonToDTO (in jsonToDTO, the DTO can be either customer facing FilingDTO or EFS side, FilingDataDTO.

1. Ensure Rest and GraphQL APIs are working and the filing is getting stored and read properly back to UI on the TNN side.

1. Add new FilingDataDTO for this form and add new EFSFilingxxx.java for this form.

1. Make sure FilingsManager rest end point is storing and retrieving the form correctly.

1. Enable the Enums for these forms FilingReturnType of tnn and efs.

1. Update the DataGenUtil to update A record, B record and so on..

1. Finally generate the payload needed to generate the PDF form.
